plugins {
    id("org.gradle.toolchains.foojay-resolver-convention") version "0.5.0"
}
rootProject.name = "bill-payment-service"
include("application")

// APPS
include("apps:me-poupe")
include("apps:friday")
include("apps:motorola")

// MODULES
include("modules:manual-entry")
include("modules:pfm")
include("modules:push-notification")
include("modules:chatbot-ai")
include("modules:event-api")
include("modules:openfinance")
include("modules:in-app-subscription-coupon")
include("modules:investment-goals")
findProject(":modules:chatbot-ai")?.name = "chatbot-ai"
findProject(":modules:openfinance")?.name = "openfinance"