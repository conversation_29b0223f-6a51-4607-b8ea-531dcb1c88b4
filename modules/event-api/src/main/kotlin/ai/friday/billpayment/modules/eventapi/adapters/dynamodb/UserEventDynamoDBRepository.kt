package ai.friday.billpayment.modules.eventapi.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.USER_EVENT_TABLE_NAME
import ai.friday.billpayment.app.integrations.UserEvent
import ai.friday.billpayment.app.integrations.UserEventRepository
import ai.friday.billpayment.app.integrations.UserEventSource
import ai.friday.billpayment.app.integrations.UserEventType
import ai.friday.billpayment.modules.eventapi.EventAPI
import ai.friday.morning.date.isoDateTimeFormat
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped

@EventAPI
class UserEventDynamoDBRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) : UserEventRepository {

    override fun save(userEvent: UserEvent) {
        val formattedTimestamp = userEvent.timestamp.format(isoDateTimeFormat)

        val entity = UserEventEntity().apply {
            partitionKey = "${userEvent.entityType}#${userEvent.entityId}"
            scanKey = formattedTimestamp
            gSIndex1PrimaryKey = userEvent.event
            gSIndex1ScanKey = formattedTimestamp
            gSIndex2PrimaryKey = userEvent.correlationId
            gSIndex2ScanKey = formattedTimestamp
            entityId = userEvent.entityId
            entityType = userEvent.entityType
            source = userEvent.source
            timestamp = formattedTimestamp
            event = userEvent.event
            metadata = userEvent.metadata
            correlationId = userEvent.correlationId
        }

        dynamoDbDAO.save(entity)
    }
}

@DynamoDBTable(tableName = USER_EVENT_TABLE_NAME)
class UserEventEntity {

    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    var gSIndex1PrimaryKey: String? = null

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    var gSIndex1ScanKey: String? = null

    @DynamoDBIndexHashKey(attributeName = "GSIndex2PrimaryKey", globalSecondaryIndexName = "GSIndex2")
    var gSIndex2PrimaryKey: String? = null

    @DynamoDBIndexRangeKey(attributeName = "GSIndex2ScanKey", globalSecondaryIndexName = "GSIndex2")
    var gSIndex2ScanKey: String? = null

    @DynamoDBAttribute(attributeName = "EntityId")
    lateinit var entityId: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "EntityType")
    lateinit var entityType: UserEventType

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Source")
    lateinit var source: UserEventSource

    @DynamoDBAttribute(attributeName = "Timestamp")
    lateinit var timestamp: String

    @DynamoDBAttribute(attributeName = "Event")
    lateinit var event: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.M)
    @DynamoDBAttribute(attributeName = "Metadata")
    lateinit var metadata: Map<String, String>

    @DynamoDBAttribute(attributeName = "CorrelationId")
    lateinit var correlationId: String
}