package ai.friday.billpayment.modules.eventapi.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.integrations.UserEvent
import ai.friday.billpayment.app.integrations.UserEventService
import ai.friday.billpayment.app.integrations.UserEventSource
import ai.friday.billpayment.app.integrations.UserEventType
import ai.friday.billpayment.modules.eventapi.EventAPI
import ai.friday.morning.date.dateTimeFormat
import io.micronaut.context.annotation.Property
import java.time.ZonedDateTime
import java.util.UUID
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@EventAPI
open class SQSUserEventHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sqs.queues.userEvent") private val queueName: String,
    private val userEventService: UserEventService,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    queueName,
    consumers = 4,
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val message = parseObjectFrom<UserEventMessage>(m.body())

        userEventService.save(message.toUserEvent())
        logger.info(Markers.append("body", message), "SQSUserEventHandler")

        return SQSHandlerResponse(true)
    }

    override fun handleError(m: Message, e: Exception): SQSHandlerResponse {
        logger.error(Markers.append("body", m), "SQSUserEventHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSUserEventHandler::class.java)
    }
}

data class UserEventMessage(
    val entityId: String,
    val entityType: UserEventType,
    val source: UserEventSource,
    val event: String,
    val timestamp: String,
    val metadata: Map<String, String>,
    val correlationId: String = UUID.randomUUID().toString(),
) {
    fun toUserEvent(): UserEvent {
        return UserEvent(
            entityId = entityId,
            entityType = entityType,
            source = source,
            event = event,
            timestamp = ZonedDateTime.parse(timestamp, dateTimeFormat),
            metadata = metadata,
            correlationId = correlationId,
        )
    }
}