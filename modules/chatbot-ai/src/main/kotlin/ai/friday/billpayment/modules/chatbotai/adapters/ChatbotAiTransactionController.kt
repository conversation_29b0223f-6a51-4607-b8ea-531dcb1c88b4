package ai.friday.billpayment.modules.chatbotai.adapters

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.StandardHttpResponses
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.LimitType
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.modules.chatbotai.ChatbotAI
import ai.friday.billpayment.modules.chatbotai.app.AuthorizationDetails
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransactionService
import arrow.core.getOrElse
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Delete
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/chatbotAI/transaction")
@Version("2")
@ChatbotAI
class ChatbotAiTransactionController(
    private val chatbotAiTransactionService: ChatbotAiTransactionService,
    private val accountService: AccountService,
    private val walletService: WalletService,
) {

    @Get("/{transactionId}")
    fun findTransaction(
        authentication: Authentication,
        @PathVariable transactionId: String,
    ): HttpResponse<*> {
        val accountId = AccountId(authentication.name)
        val wallet = walletService.findPrimaryWallet(accountId)
        val walletMember = wallet.getActiveMember(accountId)

        val markers = Markers
            .append("transactionId", transactionId)
            .andAppend("accountId", accountId)
            .andAppend("walletId", wallet.id)
        val logName = "ChatbotAiTransactionController#findTransaction"

        return try {
            val transaction = chatbotAiTransactionService.findTransaction(transactionId, wallet.id, walletMember).getOrElse {
                logger.error(markers.andAppend("error", it), logName)
                return it.handleResponse("Erro ao buscar transação")
            }
            logger.info(markers.andAppend("transaction", transaction), logName)
            HttpResponse.ok(transaction)
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            HttpResponse.serverError("Não foi possível encontrar a transação")
        }
    }

    @Post("/{transactionId}")
    fun confirmTransaction(
        authentication: Authentication,
        @PathVariable transactionId: String,
        @Body body: AuthorizationDetails,
    ): HttpResponse<*> {
        val accountId = AccountId(authentication.name)
        val account = try {
            accountService.findAccountById(accountId)
        } catch (e: AccountNotFoundException) {
            return StandardHttpResponses.accountNotFound()
        }

        val markers = Markers
            .append("transactionId", transactionId)
            .andAppend("accountId", accountId)
            .andAppend("msisdn", account.mobilePhone)
            .andAppend("body", body)
        val logName = "ChatbotAiTransactionController#confirmTransaction"

        return try {
            chatbotAiTransactionService.confirmTransaction(transactionId, account, body).getOrElse {
                logger.error(markers.andAppend("error", it), logName)
                return it.handleResponse("Erro ao confirmar transação")
            }
            logger.info(markers, logName)
            HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            HttpResponse.serverError("Não foi possível confirmar a transação")
        }
    }

    @Delete("/{transactionId}")
    fun cancelTransaction(
        authentication: Authentication,
        @PathVariable transactionId: String,
    ): HttpResponse<*> {
        val accountId = AccountId(authentication.name)

        val account = try {
            accountService.findAccountById(accountId)
        } catch (e: AccountNotFoundException) {
            return StandardHttpResponses.accountNotFound()
        }

        val markers = Markers
            .append("transactionId", transactionId)
            .andAppend("accountId", accountId)
            .andAppend("msisdn", account.mobilePhone)
        val logName = "ChatbotAiTransactionController#cancelTransaction"

        return try {
            chatbotAiTransactionService.cancelTransaction(transactionId, account.mobilePhone).getOrElse {
                logger.error(markers.andAppend("error", it), logName)
                return it.handleResponse("Erro ao cancelar transação")
            }
            logger.info(markers, logName)
            HttpResponse.ok<Unit>()
        } catch (e: Exception) {
            logger.error(
                markers.andAppend("error", e.localizedMessage),
                logName,
                e,
            )
            HttpResponse.serverError("Não foi possível cancelar a transação")
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ChatbotAiTransactionController::class.java)
    }
}

data class ConfirmTransactionTO(
    val billsIds: List<String>,
)

fun ChatbotAiTransactionError.handleResponse(serverErrorText: String) = when (this) {
    ChatbotAiTransactionError.TransactionNotFound -> HttpResponse.notFound<Unit>()
    ChatbotAiTransactionError.IllegalTransactionState -> HttpResponse.status<Unit>(HttpStatus.CONFLICT)
    is ChatbotAiTransactionError.SweepingLimitExceeded -> {
        val response = when (this.limitType) {
            LimitType.DAILY -> ResponseTO(code = "40031", message = "Limite diário excedido")
            LimitType.WEEKLY -> ResponseTO(code = "40032", message = "Limite semanal excedido")
            LimitType.MONTHLY -> ResponseTO(code = "40033", message = "Limite mensal excedido")
            LimitType.YEARLY -> ResponseTO(code = "40034", message = "Limite anual excedido")
            LimitType.GLOBAL -> ResponseTO(code = "40035", message = "Limite global excedido")
            LimitType.TRANSACTION -> ResponseTO(code = "40036", message = "Limite por transação excedido")
            LimitType.UNKNOWN -> ResponseTO(code = "40037", message = "Limite excedido")
        }
        HttpResponse.unprocessableEntity<ResponseTO>().body(response)
    }
    is ChatbotAiTransactionError.UnknownError -> HttpResponse.serverError(serverErrorText)
}