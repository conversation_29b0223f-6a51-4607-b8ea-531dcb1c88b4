package ai.friday.billpayment.modules.inAppSubscriptionCoupon.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponCode
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.inAppSubscriptionCoupon
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test

class InAppSubscriptionCouponRedeemDbRepositoryTest {
    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDAO = DynamoDbDAO(dynamoDB)

    private val inAppSubscriptionCouponRedeemRepository = InAppSubscriptionCouponRedeemDbRepository(dynamoDbDAO = dynamoDAO)

    private val couponId = CouponId("fake coupon code")
    private val accountId = AccountId("fake account id")

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Nested
    @DisplayName("Create")
    inner class Create {
        @Test
        fun `deve conseguir criar um redeem`() {
            val coupon =
                inAppSubscriptionCoupon(couponId = couponId)

            inAppSubscriptionCouponRedeemRepository.createCouponRedeem(accountId, coupon, getZonedDateTime().plusDays(30))
            val result = inAppSubscriptionCouponRedeemRepository.findAllBy(coupon.id).single()

            result.shouldNotBeNull()
            result.couponId.value shouldBe "fake coupon code"
            result.offerGroup shouldBe null
            result.accountId.value shouldBe "fake account id"
        }
    }

    @Nested
    @DisplayName("Find")
    inner class Find {
        @Test
        fun `deve conseguir conseguir buscar um cupom por id`() {
            val coupon =
                inAppSubscriptionCoupon(
                    couponId = CouponId("CUPOM1"),
                    offerGroup = "fake_offer_group",
                )

            val redeem = inAppSubscriptionCouponRedeemRepository.createCouponRedeem(accountId, coupon, getZonedDateTime().plusDays(30))
            val result1 = inAppSubscriptionCouponRedeemRepository.findOrNull(redeem.id)

            result1.shouldNotBeNull()
            result1.couponId.value shouldBe "CUPOM1"
            result1.accountId.value shouldBe "fake account id"
            result1.offerGroup shouldBe "fake_offer_group"
        }

        @Test
        fun `deve conseguir conseguir buscar um cupom por id do cupom`() {
            val coupon =
                inAppSubscriptionCoupon(
                    couponId = CouponId("CUPOM1"),
                    offerGroup = "fake_offer_group",
                )

            inAppSubscriptionCouponRedeemRepository.createCouponRedeem(accountId, coupon, getZonedDateTime().plusDays(30))
            val result1 = inAppSubscriptionCouponRedeemRepository.findAllBy(coupon.id)

            result1.shouldNotBeNull()
            result1.size shouldBe 1
            result1[0].accountId.value shouldBe "fake account id"
            result1[0].couponId.value shouldBe "CUPOM1"
            result1[0].offerGroup shouldBe "fake_offer_group"
        }

        @Test
        fun `deve conseguir conseguir buscar resgates do usuario`() {
            val coupon1 = inAppSubscriptionCoupon(code = CouponCode("CUPOM1"))
            val coupon2 = inAppSubscriptionCoupon(code = CouponCode("CUPOM2"))
            val coupon3 = inAppSubscriptionCoupon(code = CouponCode("CUPOM3"))
            val coupon4 = inAppSubscriptionCoupon(code = CouponCode("CUPOM4"))

            inAppSubscriptionCouponRedeemRepository.createCouponRedeem(accountId, coupon1, getZonedDateTime().plusDays(30))
            inAppSubscriptionCouponRedeemRepository.createCouponRedeem(accountId, coupon2, getZonedDateTime().plusDays(30))
            inAppSubscriptionCouponRedeemRepository.createCouponRedeem(accountId, coupon3, getZonedDateTime().plusDays(30))
            inAppSubscriptionCouponRedeemRepository.createCouponRedeem(AccountId(), coupon4, getZonedDateTime().plusDays(30))
            val result = inAppSubscriptionCouponRedeemRepository.findAllBy(accountId)

            result.shouldNotBeNull()
            result.size shouldBe 3
            result[0].couponId.value shouldBe coupon1.id.value
            result[1].couponId.value shouldBe coupon2.id.value
            result[2].couponId.value shouldBe coupon3.id.value
        }
    }
}