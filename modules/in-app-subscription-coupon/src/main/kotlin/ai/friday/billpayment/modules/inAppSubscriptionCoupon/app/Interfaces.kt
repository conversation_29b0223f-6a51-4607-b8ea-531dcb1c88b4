package ai.friday.billpayment.modules.inAppSubscriptionCoupon.app

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.account.AccountId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import java.time.Period
import java.time.ZonedDateTime
import java.util.UUID

interface InAppSubscriptionCouponRepository {
    fun create(coupon: CreateInAppSubscriptionCouponCommand): InAppSubscriptionCoupon

    fun findAll(): List<InAppSubscriptionCoupon>

    fun findEnabledOrNull(code: CouponCode): InAppSubscriptionCoupon?

    fun find(code: CouponCode): List<InAppSubscriptionCoupon>

    fun findOrNull(id: CouponId): InAppSubscriptionCoupon?
    fun redeem(couponId: CouponId): Either<CouponError, InAppSubscriptionCoupon>
    fun findByExternalId(externalId: InAppSubscriptionCouponExternalId): InAppSubscriptionCoupon?
    fun update(copy: InAppSubscriptionCoupon): InAppSubscriptionCoupon
}

interface InAppSubscriptionCouponRedeemRepository {
    fun createCouponRedeem(accountId: AccountId, coupon: InAppSubscriptionCoupon, endsAt: ZonedDateTime?): InAppSubscriptionCouponRedeem

    fun findOrNull(redeemId: CouponRedeemId): InAppSubscriptionCouponRedeem?

    fun findAllBy(accountId: AccountId): List<InAppSubscriptionCouponRedeem>

    fun findAllBy(couponId: CouponId): List<InAppSubscriptionCouponRedeem>
}

data class CouponCode(
    val value: String,
)

data class CouponId(
    val value: String,
) {
    constructor() : this("COUPON-${UUID.randomUUID()}")
}

data class CouponRedeemId(
    val value: String,
) {
    constructor() : this("COUPON-REDEEM-${UUID.randomUUID()}")
}

data class InAppSubscriptionCouponRedeem(
    val id: CouponRedeemId,
    val accountId: AccountId,
    val couponId: CouponId,
    val offerGroup: String?,
    val subscriptionEndsAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
    val createdAt: ZonedDateTime,
)

data class CreateInAppSubscriptionCouponCommand(
    val code: CouponCode,
    val description: String,
    val free: Boolean,
    val offerGroup: String?,
    val quantity: Int,
    val externalId: InAppSubscriptionCouponExternalId?,
    val externalOfferId: String?,
    val expiresAt: ZonedDateTime,
    val subscriptionPeriod: Period,
    val extraParams: Map<String, String>? = null,
)

data class InAppSubscriptionCouponExternalId(val provider: String, val value: String)

data class InAppSubscriptionCoupon(
    val id: CouponId,
    val code: CouponCode,
    val description: String,
    val free: Boolean,
    val offerGroup: String?,
    val availableQuantity: Int,
    val originalQuantity: Int,
    val enabled: Boolean,
    val expiresAt: ZonedDateTime,
    val subscriptionPeriod: Period,
    val createdAt: ZonedDateTime,
    val externalId: InAppSubscriptionCouponExternalId?,
    val extraParams: Map<String, String>?,
) {
    val isExpired: Boolean = expiresAt < getZonedDateTime()
}

data class UpdateCouponCommand(
    val id: CouponId,
    val code: CouponCode,
    val description: String,
    val free: Boolean,
    val offerGroup: String?,
    val quantity: Int,
    val enabled: Boolean,
    val expiresAt: ZonedDateTime,
)

sealed class CouponError(val errorMessage: String) : PrintableSealedClassV2() {
    data object CouponNotFound : CouponError("Coupon not found")
    data object SubscriptionError : CouponError("Subscription error occurred")
    data object CouponCodeAlreadyExists : CouponError("Coupon code already exists")
    data object CouponExternalIdAlreadyExists : CouponError("Coupon external id already exists")
    data object ExpiredCoupon : CouponError("The coupon has expired")
    data object CouponWithoutQuantity : CouponError("Coupon has no remaining quantity")
    data class CouponAlreadyRedeemedByAccount(val coupon: InAppSubscriptionCoupon) : CouponError("Coupon has already been redeemed")
    data object InvalidCoupon : CouponError("Invalid coupon")
    data object AccountHasActiveSubscription : CouponError("Account already has an active subscription")
    data object AlreadyLockedException : CouponError("Coupon is already locked")
}