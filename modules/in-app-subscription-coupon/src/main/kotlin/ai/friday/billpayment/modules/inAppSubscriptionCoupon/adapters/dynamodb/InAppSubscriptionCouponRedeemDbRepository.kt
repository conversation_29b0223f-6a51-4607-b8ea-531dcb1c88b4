package ai.friday.billpayment.modules.inAppSubscriptionCoupon.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.InAppSubscriptionCouponModule
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponRedeemId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCoupon
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponRedeem
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponRedeemRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

private const val SCAN_KEY = "IN_APP_SUBSCRIPTION_COUPON_REDEEM"

@InAppSubscriptionCouponModule
class InAppSubscriptionCouponRedeemDbRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) : InAppSubscriptionCouponRedeemRepository {
    override fun createCouponRedeem(accountId: AccountId, coupon: InAppSubscriptionCoupon, endsAt: ZonedDateTime?): InAppSubscriptionCouponRedeem {
        val redeemId = CouponRedeemId()
        val createdAt = getZonedDateTime()
        val entity =
            CouponRedeemEntity().apply {
                primaryKey = redeemId.value
                scanKey = SCAN_KEY
                gSIndex1PrimaryKey = "$SCAN_KEY#${accountId.value}"
                gSIndex1ScanKey = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                gSIndex2PrimaryKey = "$SCAN_KEY#${coupon.id.value}"
                gSIndex2ScanKey = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                couponId = coupon.id.value
                this.accountId = accountId.value
                offerGroup = coupon.offerGroup
                subscriptionEndsAt = endsAt?.format(DateTimeFormatter.ISO_DATE_TIME)
                updatedAt = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                this.createdAt = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
            }
        dynamoDbDAO.save(entity)
        return entity.toInAppSubscriptionCouponRedeem()
    }

    override fun findAllBy(accountId: AccountId): List<InAppSubscriptionCouponRedeem> =
        dynamoDbDAO
            .queryIndexOnHashKeyValue(
                primaryKey = "$SCAN_KEY#${accountId.value}",
                type = CouponRedeemEntity::class.java,
            ).map { it.toInAppSubscriptionCouponRedeem() }

    override fun findAllBy(couponId: CouponId): List<InAppSubscriptionCouponRedeem> =
        dynamoDbDAO
            .queryIndex2OnHashKeyValue(
                primaryKey = "$SCAN_KEY#${couponId.value}",
                type = CouponRedeemEntity::class.java,
            ).map { it.toInAppSubscriptionCouponRedeem() }

    override fun findOrNull(redeemId: CouponRedeemId): InAppSubscriptionCouponRedeem? =
        dynamoDbDAO
            .queryTableOnHashKeyAndRangeKey(
                primaryKey = redeemId.value,
                scanKey = SCAN_KEY,
                type = CouponRedeemEntity::class.java,
            ).firstOrNull()
            ?.toInAppSubscriptionCouponRedeem()
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class CouponRedeemEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDBAttribute(attributeName = "AccountId")
    lateinit var accountId: String

    @get:DynamoDBAttribute(attributeName = "CouponId")
    lateinit var couponId: String

    @get:DynamoDBAttribute(attributeName = "SubscriptionEndsAt")
    var subscriptionEndsAt: String? = null

    @get:DynamoDBAttribute(attributeName = "OfferGroup")
    var offerGroup: String? = null

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex2PrimaryKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var gSIndex2PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex2ScanKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var gSIndex2ScanKey: String

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String
}

private fun CouponRedeemEntity.toInAppSubscriptionCouponRedeem(): InAppSubscriptionCouponRedeem =
    InAppSubscriptionCouponRedeem(
        id = CouponRedeemId(primaryKey),
        couponId = CouponId(this.couponId),
        accountId = AccountId(this.accountId),
        subscriptionEndsAt = ZonedDateTime.parse(this.subscriptionEndsAt, DateTimeFormatter.ISO_DATE_TIME),
        offerGroup = this.offerGroup,
        updatedAt = ZonedDateTime.parse(this.updatedAt, DateTimeFormatter.ISO_DATE_TIME),
        createdAt = ZonedDateTime.parse(this.createdAt, DateTimeFormatter.ISO_DATE_TIME),
    )