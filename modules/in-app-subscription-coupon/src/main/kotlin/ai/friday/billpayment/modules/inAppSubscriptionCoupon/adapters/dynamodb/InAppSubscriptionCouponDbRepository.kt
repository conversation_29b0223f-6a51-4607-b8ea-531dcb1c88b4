package ai.friday.billpayment.modules.inAppSubscriptionCoupon.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.InAppSubscriptionCouponModule
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponCode
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponError
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CouponId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.CreateInAppSubscriptionCouponCommand
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCoupon
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponExternalId
import ai.friday.billpayment.modules.inAppSubscriptionCoupon.app.InAppSubscriptionCouponRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import java.time.Period
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

private const val SCAN_KEY = "IN_APP_SUBSCRIPTION_COUPON"
private const val INDEX1_PARTITION_KEY = "IN_APP_SUBSCRIPTION_COUPON_CODE"

@InAppSubscriptionCouponModule
class InAppSubscriptionCouponDbRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) : InAppSubscriptionCouponRepository {
    override fun create(coupon: CreateInAppSubscriptionCouponCommand): InAppSubscriptionCoupon {
        val createdAt = getZonedDateTime()
        val entity =
            CouponEntity().apply {
                primaryKey = CouponId().value
                scanKey = SCAN_KEY
                gSIndex1PrimaryKey = "$INDEX1_PARTITION_KEY#${coupon.code.value}"
                gSIndex1ScanKey = true.toString()
                gSIndex2PrimaryKey = SCAN_KEY
                gSIndex2ScanKey = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                gSIndex3PrimaryKey = coupon.externalId?.let { externalId -> "$SCAN_KEY#${externalId.provider}#${externalId.value}" }
                gSIndex3ScanKey = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                externalIdProvider = coupon.externalId?.provider
                externalIdValue = coupon.externalId?.value
                externalOfferIdValue = coupon.externalOfferId
                code = coupon.code.value
                description = coupon.description
                enabled = true
                free = coupon.free
                quantity = coupon.quantity
                originalQuantity = coupon.quantity
                offerGroup = coupon.offerGroup
                expiresAt = coupon.expiresAt.format(DateTimeFormatter.ISO_DATE_TIME)
                subscriptionPeriod = coupon.subscriptionPeriod.toString()
                this.createdAt = createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                extraParams = coupon.extraParams
            }
        dynamoDbDAO.save(entity)
        return entity.toInAppSubscriptionCoupon()
    }

    override fun redeem(couponId: CouponId): Either<CouponError, InAppSubscriptionCoupon> {
        val entity = findEntity(couponId) ?: return CouponError.CouponNotFound.left()

        if (entity.quantity < 1 || !entity.enabled) {
            return CouponError.CouponWithoutQuantity.left()
        }

        val newQuantity = entity.quantity - 1
        val stillEnabled = newQuantity > 0

        val updatedCouponEntity = entity.apply {
            quantity = newQuantity
            gSIndex1ScanKey = stillEnabled.toString()
            enabled = stillEnabled
            updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
        }
        dynamoDbDAO.save(updatedCouponEntity)
        return updatedCouponEntity.toInAppSubscriptionCoupon().right()
    }

    override fun findEnabledOrNull(code: CouponCode): InAppSubscriptionCoupon? =
        dynamoDbDAO
            .queryIndexOnHashKeyValueAndRangeKey(
                primaryKey = "$INDEX1_PARTITION_KEY#${code.value}",
                scanKey = true.toString(),
                type = CouponEntity::class.java,
            ).singleOrNull()
            ?.toInAppSubscriptionCoupon()

    override fun find(code: CouponCode): List<InAppSubscriptionCoupon> =
        dynamoDbDAO
            .queryIndexOnHashKeyValue(
                primaryKey = "$INDEX1_PARTITION_KEY#${code.value}",
                type = CouponEntity::class.java,
            ).map { it.toInAppSubscriptionCoupon() }

    override fun findByExternalId(externalId: InAppSubscriptionCouponExternalId): InAppSubscriptionCoupon? =
        dynamoDbDAO
            .queryIndex3OnHashKeyValue(
                partitionKey = "$SCAN_KEY#${externalId.provider}#${externalId.value}",
                type = CouponEntity::class.java,
            ).singleOrNull()?.toInAppSubscriptionCoupon()

    override fun update(toBeUpdated: InAppSubscriptionCoupon): InAppSubscriptionCoupon {
        val enabled = toBeUpdated.availableQuantity > 0 && toBeUpdated.enabled
        findEntity(toBeUpdated.id)?.let {
            val updatedAt = getZonedDateTime()
            it.apply {
                gSIndex1PrimaryKey = "$INDEX1_PARTITION_KEY#${toBeUpdated.code.value}"
                gSIndex1ScanKey = enabled.toString()
                gSIndex3PrimaryKey = toBeUpdated.externalId?.let { externalId -> "$SCAN_KEY#${externalId.provider}#${externalId.value}" }
                code = toBeUpdated.code.value
                description = toBeUpdated.description
                this.enabled = enabled
                free = toBeUpdated.free
                quantity = toBeUpdated.availableQuantity
                offerGroup = toBeUpdated.offerGroup
                expiresAt = toBeUpdated.expiresAt.format(DateTimeFormatter.ISO_DATE_TIME)
                this.updatedAt = updatedAt.format(DateTimeFormatter.ISO_DATE_TIME)
                externalIdProvider = toBeUpdated.externalId?.provider
                externalIdValue = toBeUpdated.externalId?.value
                subscriptionPeriod = toBeUpdated.subscriptionPeriod.toString()
            }
            dynamoDbDAO.save(it)
            return it.toInAppSubscriptionCoupon()
        } ?: throw IllegalArgumentException("Coupon not found")
    }

    override fun findOrNull(id: CouponId): InAppSubscriptionCoupon? =
        findEntity(id)
            ?.toInAppSubscriptionCoupon()

    private fun findEntity(id: CouponId) = dynamoDbDAO
        .queryTableOnHashKeyAndRangeKey(
            primaryKey = id.value,
            scanKey = SCAN_KEY,
            type = CouponEntity::class.java,
        ).firstOrNull()

    override fun findAll(): List<InAppSubscriptionCoupon> =
        dynamoDbDAO
            .queryIndex2OnHashKeyValue(
                primaryKey = SCAN_KEY,
                type = CouponEntity::class.java,
            ).map { it.toInAppSubscriptionCoupon() }
}

// TODO: ajustar primary key e criar index para consultar todos os cupons
@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class CouponEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDBAttribute(attributeName = "Code")
    lateinit var code: String

    @get:DynamoDBAttribute(attributeName = "Description")
    var description: String? = null

    @get:DynamoDBAttribute(attributeName = "Quantity")
    var quantity: Int = 0

    @get:DynamoDBAttribute(attributeName = "OriginalQuantity")
    var originalQuantity: Int = 0

    @get:DynamoDBAttribute(attributeName = "Enabled")
    var enabled: Boolean = false

    @get:DynamoDBAttribute(attributeName = "Free")
    var free: Boolean = false

    @get:DynamoDBAttribute(attributeName = "ExpiresAt")
    lateinit var expiresAt: String

    @get:DynamoDBAttribute(attributeName = "SubscriptionPeriod")
    lateinit var subscriptionPeriod: String

    @get:DynamoDBAttribute(attributeName = "OfferGroup")
    var offerGroup: String? = null

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex2PrimaryKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var gSIndex2PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex2ScanKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var gSIndex2ScanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex3PrimaryKey", globalSecondaryIndexName = "GSIndex3")
    var gSIndex3PrimaryKey: String? = null

    @DynamoDBIndexRangeKey(attributeName = "GSIndex3ScanKey", globalSecondaryIndexName = "GSIndex3")
    var gSIndex3ScanKey: String? = null

    @get:DynamoDBAttribute(attributeName = "ExternalIdProvider")
    var externalIdProvider: String? = null

    @get:DynamoDBAttribute(attributeName = "ExternalIdValue")
    var externalIdValue: String? = null

    @get:DynamoDBAttribute(attributeName = "ExternalOfferIdValue")
    var externalOfferIdValue: String? = null

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDBAttribute(attributeName = "ExtraParams")
    var extraParams: Map<String, String>? = null
}

private fun CouponEntity.toInAppSubscriptionCoupon(): InAppSubscriptionCoupon =
    InAppSubscriptionCoupon(
        id = CouponId(primaryKey),
        code = CouponCode(this.code),
        description = this.description ?: "",
        expiresAt = ZonedDateTime.parse(this.expiresAt, DateTimeFormatter.ISO_DATE_TIME),
        subscriptionPeriod = Period.parse(this.subscriptionPeriod),
        free = this.free,
        availableQuantity = this.quantity,
        originalQuantity = this.originalQuantity,
        offerGroup = this.offerGroup,
        enabled = this.enabled,
        createdAt = ZonedDateTime.parse(this.createdAt, DateTimeFormatter.ISO_DATE_TIME),
        externalId = externalIdProvider?.let {
            InAppSubscriptionCouponExternalId(it, externalIdValue ?: "")
        },
        extraParams = this.extraParams,
    )