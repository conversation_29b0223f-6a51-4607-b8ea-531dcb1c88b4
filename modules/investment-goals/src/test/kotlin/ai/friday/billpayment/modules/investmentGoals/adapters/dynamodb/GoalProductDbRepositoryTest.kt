package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProduct
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductRisk
import ai.friday.billpayment.modules.investmentGoals.dailyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.disabledMonthlyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.enabledMonthlyCDIProduct
import ai.friday.billpayment.modules.investmentGoals.savingsProduct
import ai.friday.billpayment.modules.investmentGoals.shouldBeGoalProduct
import ai.friday.billpayment.modules.investmentGoals.shouldHaveGoalProduct
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class GoalProductDbRepositoryTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDAO = DynamoDbDAO(dynamoDB)

    private val goalProductRepository = GoalProductDbRepository(dynamoDAO)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @ParameterizedTest
    @MethodSource("products")
    fun `deve conseguir criar e buscar um produto`(product: GoalProduct) {
        goalProductRepository.save(product)

        goalProductRepository.findOrNull(product.id) shouldBeGoalProduct product
    }

    @ParameterizedTest
    @MethodSource("products")
    fun `deve atualizar um produto existente`(product: GoalProduct) {
        goalProductRepository.save(product)

        val updatedGoalProduct = product.copy(
            index = FixedIncomeIndex.entries.first { it != product.index },
            minimumTermDays = product.minimumTermDays?.plus(2),
            maximumTermDays = product.maximumTermDays?.minus(4),
            indexIncome = FixedIncomeIndexRate(product.indexIncome.rate + 17),
            enabled = !product.enabled,
            asset = "updated ${product.asset}",
            risk = GoalProductRisk.entries.first { it != product.risk },
            issuer = "updated ${product.issuer}",
            provider = "updated ${product.provider}",
        )

        goalProductRepository.save(updatedGoalProduct)

        goalProductRepository.findOrNull(product.id) shouldBeGoalProduct updatedGoalProduct
    }

    @Test
    fun `deve conseguir conseguir buscar todos os produtos`() {
        goalProductRepository.save(enabledMonthlyCDIProduct)
        goalProductRepository.save(disabledMonthlyCDIProduct)
        goalProductRepository.save(dailyCDIProduct)
        goalProductRepository.save(savingsProduct)

        val result = goalProductRepository.findAll()

        result.size shouldBe 4
        result shouldHaveGoalProduct enabledMonthlyCDIProduct
        result shouldHaveGoalProduct disabledMonthlyCDIProduct
        result shouldHaveGoalProduct dailyCDIProduct
        result shouldHaveGoalProduct savingsProduct
    }

    companion object {
        @JvmStatic
        fun products() = listOf(enabledMonthlyCDIProduct, disabledMonthlyCDIProduct, dailyCDIProduct, savingsProduct)
    }
}