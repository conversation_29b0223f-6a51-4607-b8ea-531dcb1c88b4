package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestment
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentId
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import java.time.LocalDate
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.MethodSource

class GoalInvestmentDbRepositoryTest {
    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDAO = DynamoDbDAO(dynamoDB)

    private val goalInvestmentRepository = GoalInvestmentDbRepository(dynamoDbDAO = dynamoDAO)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        investments().forEach {
            goalInvestmentRepository.save(it)
        }
    }

    @ParameterizedTest
    @MethodSource("investments")
    fun `deve encontrar pelo id da meta`(goalInvestment: GoalInvestment) {
        val investments = goalInvestmentRepository.findByGoalId(goalInvestment.goalId)

        investments.size shouldBe 1
        investments.single() shouldBeGoalInvestment goalInvestment
    }

    @ParameterizedTest
    @MethodSource("investments")
    fun `deve encontrar pelo id da bill`(goalInvestment: GoalInvestment) {
        goalInvestmentRepository.findByBillId(goalInvestment.billId) shouldBeGoalInvestment goalInvestment
    }

    private infix fun GoalInvestment?.shouldBeGoalInvestment(goalInvestment: GoalInvestment) {
        this.shouldNotBeNull()
        this.shouldBeEqualToIgnoringFields(
            goalInvestment,
            goalInvestment::createdAt,
            goalInvestment::updatedAt,
            goalInvestment::paidAt,
        )
        this.paidAt?.toInstant() shouldBe goalInvestment.paidAt?.toInstant()
    }

    companion object {
        private val defaultGoalInvestment = GoalInvestment(
            id = GoalInvestmentId(value = "INVESTMENT-1"),
            goalId = GoalId(value = "GOAL-1"),
            billId = BillId(value = "BILL-1"),
            dueDate = LocalDate.now(),
            paidAt = getZonedDateTime().plusDays(1),
            amount = 100_00,
            extraInstallment = false,
            status = GoalInvestmentStatus.DONE,
        )

        private val extraGoalInvestment = GoalInvestment(
            id = GoalInvestmentId(value = "INVESTMENT-2"),
            goalId = GoalId(value = "GOAL-2"),
            billId = BillId(value = "BILL-2"),
            dueDate = LocalDate.now().plusDays(2),
            paidAt = null,
            amount = 80_00,
            extraInstallment = true,
            status = GoalInvestmentStatus.CREATED,
        )

        @JvmStatic
        fun investments() = listOf(defaultGoalInvestment, extraGoalInvestment)
    }
}