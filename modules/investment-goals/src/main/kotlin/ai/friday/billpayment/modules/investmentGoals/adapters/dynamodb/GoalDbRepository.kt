package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.category.GoalCategoryId
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalNotFoundException
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalStatus
import ai.friday.billpayment.modules.investmentGoals.app.goal.InstallmentFrequency
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductId
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import java.time.LocalDate
import java.time.ZonedDateTime

private const val ENTITY_ID = "GOAL"

@InvestmentGoalsModule
class GoalDbRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) : GoalRepository {
    override fun save(goal: Goal): Goal {
        val entity =
            GoalEntity().apply {
                primaryKey = ENTITY_ID
                scanKey = goal.id.value
                gSIndex1PrimaryKey = goal.accountId.value
                gSIndex1ScanKey = ENTITY_ID
                gSIndex2PrimaryKey = goal.walletId.value
                gSIndex2ScanKey = ENTITY_ID
                gSIndex3PrimaryKey = goal.productId.value
                gSIndex3ScanKey = ENTITY_ID
                gSIndex4PrimaryKey = goal.id.value
                gSIndex4ScanKey = ENTITY_ID
                accountId = goal.accountId.value
                walletId = goal.walletId.value
                categoryId = goal.categoryId.value
                name = goal.name
                imageUrl = goal.imageUrl
                endDate = goal.endDate.format(dateFormat)
                amount = goal.amount
                installments = goal.totalInstallments
                installmentFrequency = goal.installments.frequency
                installmentInitialAmount = goal.installmentInitialAmount
                liquidity = goal.liquidity
                installmentExpirationDay = goal.installments.expirationDay
                productId = goal.productId.value
                createdAt = goal.createdAt.format(dateTimeFormat)
                updatedAt = getZonedDateTime().format(dateTimeFormat)
                lastInstallmentAmountOptimizedAt = goal.lastValueOptimizationAt?.format(dateTimeFormat)
                installmentOptimizedAmount = goal.installmentOptimizedAmount
                status = goal.status
                lastKnownNetBalanceAmount = goal.lastKnownNetBalanceAmount
                lastKnownNetBalanceAt = goal.lastKnownNetBalanceAt.format(dateTimeFormat)
                lastKnownNetBalancePerc = goal.lastKnownNetBalancePerc.toDouble()
            }
        dynamoDbDAO.save(entity)

        return entity.toGoal()
    }

    override fun findByAccountId(accountId: AccountId): List<Goal> =
        dynamoDbDAO
            .queryIndexOnHashKeyValueAndRangeKey(
                primaryKey = accountId.value,
                scanKey = ENTITY_ID,
                type = GoalEntity::class.java,
            ).map { it.toGoal() }

    override fun findByWalletId(walletId: WalletId): List<Goal> =
        dynamoDbDAO
            .queryIndex2OnHashKeyValueAndRangeKey(
                primaryKey = walletId.value,
                scanKey = ENTITY_ID,
                type = GoalEntity::class.java,
            ).map { it.toGoal() }

    override fun findByProductId(productId: GoalProductId): List<Goal> =
        dynamoDbDAO
            .queryIndex3OnHashKeyAndRangeKey(
                primaryKey = productId.value,
                scanKey = ENTITY_ID,
                type = GoalEntity::class.java,
            ).map { it.toGoal() }

    override fun findOrNull(goalId: GoalId): Goal? =
        dynamoDbDAO
            .queryIndex4OnHashKeyAndRangeKey(
                primaryKey = goalId.value,
                scanKey = ENTITY_ID,
                type = GoalEntity::class.java,
            ).firstOrNull()
            ?.toGoal()

    override fun find(goalId: GoalId): Either<GoalNotFoundException, Goal> {
        return findOrNull(goalId)?.right() ?: GoalNotFoundException(goalId).left()
    }

    override fun findGoalsToOptimize(optimizedSince: LocalDate): List<Goal> =
        dynamoDbDAO.queryTableOnHashKey(primaryKey = ENTITY_ID, type = GoalEntity::class.java)
            .filter { it.status == GoalStatus.ACTIVE }
            .filter { LocalDate.parse(it.endDate, dateFormat) > getLocalDate() && (it.lastInstallmentAmountOptimizedAt == null || ZonedDateTime.parse(it.lastInstallmentAmountOptimizedAt, dateTimeFormat).toLocalDate() <= optimizedSince) }
            .map { it.toGoal() }

    override fun updateLastKnowNetBalance(goalId: GoalId, netAmount: Long): Either<GoalNotFoundException, Goal> {
        val goal = find(goalId).getOrElse { return it.left() }

        return save(
            goal.copy(
                lastKnownNetBalanceAmount = netAmount,
                lastKnownNetBalanceAt = getZonedDateTime(),
            ),
        ).right()
    }

    private fun LocalDate.between(start: LocalDate, end: LocalDate): Boolean = this in start..end

    fun findAllGoals(): List<Goal> {
        return dynamoDbDAO.queryTableOnHashKey(primaryKey = ENTITY_ID, type = GoalEntity::class.java).map { it.toGoal() }
    }

    override fun findGoalsCloseToCompletion(): List<Goal> =
        dynamoDbDAO.queryTableOnHashKey(primaryKey = ENTITY_ID, type = GoalEntity::class.java)
            .filter {
                when (it.status) {
                    GoalStatus.ACTIVE,
                    GoalStatus.PAUSED,
                    -> true

                    GoalStatus.REMOVED,
                    GoalStatus.COMPLETED,
                    -> false
                }
            }
            .filter { it.lastKnownNetBalancePerc >= 99.0 || LocalDate.parse(it.endDate, dateFormat).between(getLocalDate().minusMonths(1), getLocalDate()) }
            .map { it.toGoal() }
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class GoalEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex2PrimaryKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var gSIndex2PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex2ScanKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var gSIndex2ScanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex3PrimaryKey", globalSecondaryIndexName = "GSIndex3")
    lateinit var gSIndex3PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex3ScanKey", globalSecondaryIndexName = "GSIndex3")
    lateinit var gSIndex3ScanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex4PrimaryKey", globalSecondaryIndexName = "GSIndex4")
    lateinit var gSIndex4PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex4ScanKey", globalSecondaryIndexName = "GSIndex4")
    lateinit var gSIndex4ScanKey: String

    @get:DynamoDBAttribute(attributeName = "AccountId")
    lateinit var accountId: String

    @get:DynamoDBAttribute(attributeName = "WalletId")
    lateinit var walletId: String

    @get:DynamoDBAttribute(attributeName = "CategoryId")
    lateinit var categoryId: String

    @get:DynamoDBAttribute(attributeName = "Name")
    lateinit var name: String

    @get:DynamoDBAttribute(attributeName = "ImageURL")
    lateinit var imageUrl: String

    @get:DynamoDBAttribute(attributeName = "EndDate")
    lateinit var endDate: String

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDBAttribute(attributeName = "Amount")
    var amount: Long = 0

    @get:DynamoDBAttribute(attributeName = "Installments")
    var installments: Int = 0

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @get:DynamoDBAttribute(attributeName = "Liquidity")
    lateinit var liquidity: GoalProductLiquidity

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @get:DynamoDBAttribute(attributeName = "InstallmentFrequency")
    lateinit var installmentFrequency: InstallmentFrequency

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @get:DynamoDBAttribute(attributeName = "Status")
    lateinit var status: GoalStatus

    @get:DynamoDBAttribute(attributeName = "InstallmentAmount")
    var installmentInitialAmount: Long = 0

    @get:DynamoDBAttribute(attributeName = "InstallmentExpirationDay")
    lateinit var installmentExpirationDay: String

    @get:DynamoDBAttribute(attributeName = "ProductId")
    lateinit var productId: String

    @get:DynamoDBAttribute(attributeName = "LastInstallmentAmountOptimizedAt")
    var lastInstallmentAmountOptimizedAt: String? = null

    @get:DynamoDBAttribute(attributeName = "InstallmentOptimizedAmount")
    var installmentOptimizedAmount: Long? = null

    @get:DynamoDBAttribute(attributeName = "LastKnownNetBalanceAmount")
    var lastKnownNetBalanceAmount: Long? = null

    @get:DynamoDBAttribute(attributeName = "LastKnownNetBalanceAt")
    var lastKnownNetBalanceAt: String? = null

    @get:DynamoDBAttribute(attributeName = "LastKnownNetBalancePerc")
    var lastKnownNetBalancePerc: Double = 0.0
}

private fun GoalEntity.toGoal(): Goal =
    Goal(
        id = GoalId(scanKey),
        name = this.name,
        imageUrl = this.imageUrl,
        accountId = AccountId(this.accountId),
        categoryId = GoalCategoryId(this.categoryId),
        endDate = LocalDate.parse(this.endDate, dateFormat),
        updatedAt = ZonedDateTime.parse(this.updatedAt, dateTimeFormat),
        createdAt = ZonedDateTime.parse(this.createdAt, dateTimeFormat),
        walletId = WalletId(this.walletId),
        amount = this.amount,
        liquidity = this.liquidity,
        installments = GoalInstallments.buildGoalInstallment(
            frequency = installmentFrequency,
            amount = installmentOptimizedAmount ?: installmentInitialAmount,
            expirationDay = installmentExpirationDay,
        ),
        productId = GoalProductId(productId),
        installmentInitialAmount = installmentInitialAmount,
        installmentOptimizedAmount = installmentOptimizedAmount,
        lastValueOptimizationAt = lastInstallmentAmountOptimizedAt?.let { ZonedDateTime.parse(it, dateTimeFormat) },
        status = status,

        lastKnownNetBalanceAmount = lastKnownNetBalanceAmount ?: 0,
        lastKnownNetBalanceAt = lastKnownNetBalanceAt?.let { ZonedDateTime.parse(it, dateTimeFormat) } ?: ZonedDateTime.parse(this.createdAt, dateTimeFormat),
    )