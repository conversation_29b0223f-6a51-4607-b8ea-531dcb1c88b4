package ai.friday.billpayment.modules.investmentGoals.app.redemption

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.payment.SettlementTarget
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import java.time.ZonedDateTime
import java.util.UUID

data class GoalRedemptionId(
    val value: String = "GOAL-REDEMPTION-${UUID.randomUUID()}",
)

data class GoalRedemption(
    val id: GoalRedemptionId = GoalRedemptionId(),
    val goalId: GoalId,
    val walletId: WalletId,
    val netAmount: Long,
    val penaltyRate: FixedIncomeIndexRate?,
    val status: GoalRedemptionStatus = GoalRedemptionStatus.CREATED,
    val actionSource: ActionSource.WalletActionSource,
    val createdAt: ZonedDateTime = BrazilZonedDateTimeSupplier.getZonedDateTime(),
    val updatedAt: ZonedDateTime = BrazilZonedDateTimeSupplier.getZonedDateTime(),
) : SettlementTarget

enum class GoalRedemptionStatus {
    CREATED, PROCESSING, DONE, FAILED
}

interface GoalRedemptionRepository {
    fun save(goalRedemption: GoalRedemption)
    fun findOrNull(goalRedemptionId: GoalRedemptionId): GoalRedemption?
    fun findByGoalId(goalId: GoalId): List<GoalRedemption>
    fun findByWalletId(walletId: WalletId): List<GoalRedemption>
}