package ai.friday.billpayment.modules.investmentGoals.adapters.investmentManager

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.getCPF
import ai.friday.billpayment.app.account.getName
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.InstallmentFrequency
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRedemptionAmountsResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRedemptionResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRequestResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeManagerProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePosition
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePositionId
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProduct
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionQuotation
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionQuotations
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionRequest
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeRedemptionType
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalRequestOperationId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerException
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerWithInvestmentResultException
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerWithRedemptionResultException
import ai.friday.billpayment.modules.investmentGoals.app.investment.UserGoalAvailableToRedeemPositions
import ai.friday.billpayment.modules.investmentGoals.app.investment.UserGoalPositionAvailableToRedeemAmount
import ai.friday.billpayment.modules.investmentGoals.app.investment.UserGoalPositions
import ai.friday.billpayment.modules.investmentGoals.app.product.IndexInterestRate
import ai.friday.morning.date.dateFormat
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.uri.UriBuilder
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlin.jvm.optionals.getOrElse
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@ConfigurationProperties("integrations.investment-manager")
@Requires(property = "modules.investment-goals.enabled", value = "true")
data class InvestmentManagerConfigurations @ConfigurationInject constructor(
    val host: String,
    val clientId: String,
    val clientSecret: String,
    val createAccountPath: String,
    val createFixedIncomeAccountPath: String,
    val investByIndexPath: String,
    val investStatusPath: String,
    val redemptionByAmountPath: String,
    val redemptionStatusPath: String,
    val userPositionPath: String,
    val userAllPositionPath: String,
    val userPositionAvailableToRedeemPath: String,
    val quotationPath: String,

    val indexRatesPath: String,
    val simulationOptimizeContributionPath: String,
    val simulationSimulateFutureValuePath: String,
    val compareAllRatesPath: String,
)

@InvestmentGoalsModule
open class InvestmentManagerFixedIncomeManagerAdapter(
    @param:Client(id = "investment-manager") private val httpClient: RxHttpClient,
    private val configuration: InvestmentManagerConfigurations,
) : FixedIncomeManagerProvider {
    private val logger = LoggerFactory.getLogger(this::class.java)
    private val className = "InvestmentManagerAdapter"

    @Cacheable(cacheNames = ["index-interest-rates"])
    override fun getIndexInterestRates(): Either<Exception, List<IndexInterestRate>> {
        val logName = "$className#getIndexInterestRates"
        val markers = Markers.empty()

        val uri = UriBuilder.of(configuration.indexRatesPath).build()
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.listOf(IndexInterestRateResponseTO::class.java),
                Argument.of(String::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.map { it.toDomain() }.right()
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NOT_FOUND) {
                return emptyList<IndexInterestRate>().right()
            }
            return InvestmentManagerException(e).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
    }

    override fun enableFixedIncomeAccount(accountRegisterData: AccountRegisterData, bankAccount: InternalBankAccount): Either<InvestmentManagerException, Unit> {
        createAccount(accountRegisterData).getOrElse { return it.left() }
        createAccountOnProvider(accountRegisterData.accountId, bankAccount).getOrElse { return it.left() }
        return Unit.right()
    }

    override fun invest(operationId: GoalRequestOperationId, accountId: AccountId, goalId: GoalId, amount: Long, index: FixedIncomeIndex, indexRate: FixedIncomeIndexRate, forceRedemptionDate: LocalDate?): Either<InvestmentManagerWithInvestmentResultException, FixedIncomeInvestmentRequestResult> {
        val logName = "$className#invest"
        val payload = InvestmentManagagerInvestmentRequestRequestTO(
            externalId = operationId.value,
            accountId = accountId.value,
            investmentGroupId = goalId.value,
            amount = amount,
            index = index,
            indexPercentage = indexRate.rate,
            forceRedemptionDate = forceRedemptionDate?.format(DateTimeFormatter.ISO_DATE),
        )

        val markers = append("request", payload)

        val httpRequest = HttpRequest.POST(configuration.investByIndexPath, payload)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(FixedIncomeInvestmentRequestResponseTO::class.java),
                Argument.of(String::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.toDomain().right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerWithInvestmentResultException(
                FixedIncomeInvestmentRequestResult(
                    externalId = null,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    fixedIncomeProduct = null,
                    errorMessage = e.message,
                    operationId = operationId,
                    positionId = null,
                    maturityDate = null,
                ),
                e,
            )
                .left()
        }
    }

    override fun checkInvestmentRequestStatus(externalId: InvestmentManagerExternalId): Either<InvestmentManagerWithInvestmentResultException, FixedIncomeInvestmentRequestResult> {
        val logName = "$className#checkInvestmentRequestStatus"
        val markers = append("externalId", externalId)

        val uri = UriBuilder.of(configuration.investStatusPath).path(externalId.value).build()
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(FixedIncomeInvestmentRequestResponseTO::class.java),
                Argument.of(String::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.toDomain().right()
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NOT_FOUND) {
                return InvestmentManagerWithInvestmentResultException(
                    FixedIncomeInvestmentRequestResult(
                        operationId = null,
                        externalId = externalId,
                        provider = FixedIncomeProvider.ARBI,
                        status = InvestmentManagerRequestStatus.NOT_FOUND,
                        fixedIncomeProduct = null,
                        errorMessage = e.message,
                        positionId = null,
                        maturityDate = null,
                    ),
                    null,
                ).left()
            }
            return InvestmentManagerWithInvestmentResultException(
                FixedIncomeInvestmentRequestResult(
                    operationId = null,
                    externalId = externalId,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    fixedIncomeProduct = null,
                    errorMessage = e.message,
                    positionId = null,
                    maturityDate = null,
                ),
                e,
            ).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerWithInvestmentResultException(
                FixedIncomeInvestmentRequestResult(
                    operationId = null,
                    externalId = externalId,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    fixedIncomeProduct = null,
                    errorMessage = e.message,
                    positionId = null,
                    maturityDate = null,
                ),
                e,
            ).left()
        }
    }

    override fun checkInvestmentRequestStatus(accountId: AccountId, operationId: GoalRequestOperationId): Either<InvestmentManagerWithInvestmentResultException, FixedIncomeInvestmentRequestResult> {
        val logName = "$className#checkInvestmentRequestStatus"
        val markers = append("operationId", operationId.value)

        val uri = UriBuilder.of(configuration.investStatusPath).path("accountId").path(accountId.value).path("externalId").path(operationId.value).build()
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.listOf(FixedIncomeInvestmentRequestResponseTO::class.java),
                Argument.of(String::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.firstOrNull()?.toDomain()?.right() ?: InvestmentManagerWithInvestmentResultException(
                FixedIncomeInvestmentRequestResult(
                    operationId = operationId,
                    externalId = null,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.NOT_FOUND,
                    fixedIncomeProduct = null,
                    errorMessage = null,
                    positionId = null,
                    maturityDate = null,
                ),
                null,
            ).left()
        } catch (e: HttpClientResponseException) {
            return InvestmentManagerWithInvestmentResultException(
                FixedIncomeInvestmentRequestResult(
                    operationId = operationId,
                    externalId = null,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    fixedIncomeProduct = null,
                    errorMessage = e.message,
                    positionId = null,
                    maturityDate = null,
                ),
                e,
            ).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerWithInvestmentResultException(
                FixedIncomeInvestmentRequestResult(
                    operationId = operationId,
                    externalId = null,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    fixedIncomeProduct = null,
                    errorMessage = e.message,
                    positionId = null,
                    maturityDate = null,
                ),
                e,
            ).left()
        }
    }

    override fun quoteRedemption(accountId: AccountId, goalId: GoalId, amount: Long, penaltyRate: FixedIncomeIndexRate?): Either<InvestmentManagerException, FixedIncomeRedemptionQuotations> {
        val logName = "$className#quoteRedemption"
        val payload = InvestmentManagerRedemptionRequestTO(
            externalId = GoalRequestOperationId().value,
            accountId = accountId.value,
            investmentGroupId = goalId.value,
            amount = amount,
            indexPercentage = penaltyRate?.rate,
        )

        val markers = append("request", payload)

        val httpRequest = HttpRequest.POST(configuration.quotationPath, payload)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(InvestmentManagerRedemptionQuotationByAmountResponseTO::class.java),
                Argument.of(Unit::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.toDomain(accountId, goalId).right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
    }

    override fun redemption(operationId: GoalRequestOperationId, accountId: AccountId, goalId: GoalId, amount: Long, penaltyRate: FixedIncomeIndexRate?): Either<InvestmentManagerWithRedemptionResultException, FixedIncomeInvestmentRedemptionResult> {
        val logName = "$className#redemption"
        val payload = InvestmentManagerRedemptionRequestTO(
            externalId = operationId.value,
            accountId = accountId.value,
            investmentGroupId = goalId.value,
            amount = amount,
            indexPercentage = penaltyRate?.rate,
        )

        val markers = append("request", payload)

        val httpRequest = HttpRequest.POST(configuration.redemptionByAmountPath, payload)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(InvestmentManagerRedemptionRedemptionRequestByAmountResponseTO::class.java),
                Argument.of(Unit::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.toDomain(operationId, amount).right()
        } catch (e: HttpClientResponseException) {
            val body = e.response.getBody(String::class.java).getOrElse { "EMPTY_BODY" }
            if (e.status == HttpStatus.CONFLICT) {
                return InvestmentManagerWithRedemptionResultException(
                    result = FixedIncomeInvestmentRedemptionResult(
                        operationId = operationId,
                        provider = FixedIncomeProvider.ARBI,
                        status = InvestmentManagerRequestStatus.FAILED,
                        redemptionGroupExternalId = null,
                        redemptionExternalIds = emptyList(),
                        errorMessage = body,
                        amounts = FixedIncomeInvestmentRedemptionAmountsResult(
                            amount = amount,
                            completedAmount = 0,
                            failedAmount = amount,
                        ),
                    ),
                    throwable = e,
                ).left()
            }
            logger.error(markers, logName, e)
            return InvestmentManagerWithRedemptionResultException(
                result = FixedIncomeInvestmentRedemptionResult(
                    operationId = operationId,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    redemptionGroupExternalId = null,
                    redemptionExternalIds = emptyList(),
                    errorMessage = e.message,
                    amounts = null,
                ),
                throwable = e,
            ).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerWithRedemptionResultException(
                result = FixedIncomeInvestmentRedemptionResult(
                    operationId = operationId,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    redemptionGroupExternalId = null,
                    redemptionExternalIds = emptyList(),
                    errorMessage = e.message,
                    amounts = null,
                ),
                throwable = e,
            ).left()
        }
    }

    override fun checkInvestmentRedemptionStatus(singleExternalId: InvestmentManagerExternalId?, groupExternalId: InvestmentManagerExternalId?): Either<InvestmentManagerWithRedemptionResultException, FixedIncomeInvestmentRedemptionResult> {
        val logName = "$className#checkInvestmentRedemptionStatus"
        val requests = if (groupExternalId != null) {
            findRedemptionGroupRequests(groupExternalId).getOrElse {
                return InvestmentManagerWithRedemptionResultException(
                    FixedIncomeInvestmentRedemptionResult(
                        operationId = null,
                        provider = FixedIncomeProvider.ARBI,
                        status = InvestmentManagerRequestStatus.UNKNOWN,
                        redemptionGroupExternalId = groupExternalId,
                        redemptionExternalIds = emptyList(),
                        errorMessage = it.message,
                        amounts = null,
                    ),
                    it,
                ).left()
            }
        } else if (singleExternalId != null) {
            findRedemptionRequest(singleExternalId).map { listOfNotNull(it) } getOrElse {
                return InvestmentManagerWithRedemptionResultException(
                    FixedIncomeInvestmentRedemptionResult(
                        operationId = null,
                        provider = FixedIncomeProvider.ARBI,
                        status = InvestmentManagerRequestStatus.UNKNOWN,
                        redemptionGroupExternalId = null,
                        redemptionExternalIds = listOf(singleExternalId),
                        errorMessage = it.message,
                        amounts = null,
                    ),
                    it,
                ).left()
            }
        } else {
            return InvestmentManagerWithRedemptionResultException(
                FixedIncomeInvestmentRedemptionResult(
                    operationId = null,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    redemptionGroupExternalId = null,
                    redemptionExternalIds = emptyList(),
                    errorMessage = "No externalId or groupId provided",
                    amounts = null,
                ),
                IllegalStateException(
                    "No externalId or groupId provided",
                ),
            ).left()
        }

        if (requests.isEmpty()) {
            return FixedIncomeInvestmentRedemptionResult(
                operationId = null,
                provider = FixedIncomeProvider.ARBI,
                status = InvestmentManagerRequestStatus.NOT_FOUND,
                redemptionGroupExternalId = groupExternalId,
                redemptionExternalIds = listOfNotNull(singleExternalId),
                errorMessage = "NOT FOUND",
                amounts = null,
            ).right()
        }

        val status = when {
            requests.any { it.status == InvestmentManagerRequestStatus.FAILED } -> InvestmentManagerRequestStatus.FAILED
            requests.all { it.status == InvestmentManagerRequestStatus.COMPLETED } -> InvestmentManagerRequestStatus.COMPLETED
            requests.all { it.status == InvestmentManagerRequestStatus.CREATED } -> InvestmentManagerRequestStatus.CREATED
            requests.any { it.status == InvestmentManagerRequestStatus.REQUESTED } -> InvestmentManagerRequestStatus.REQUESTED
            else -> {
                logger.error(append("context", "estado de algum resgate desconhecido").andAppend("requests", requests), logName)
                InvestmentManagerRequestStatus.UNKNOWN
            }
        }

        return FixedIncomeInvestmentRedemptionResult(
            operationId = null,
            provider = FixedIncomeProvider.ARBI,
            status = status,
            redemptionGroupExternalId = groupExternalId,
            redemptionExternalIds = listOfNotNull(singleExternalId),
            errorMessage = "NOT FOUND",
            amounts = FixedIncomeInvestmentRedemptionAmountsResult(
                amount = requests.sumOf { it.amount },
                completedAmount = requests.filter { it.status == InvestmentManagerRequestStatus.COMPLETED }.sumOf { it.amount },
                failedAmount = requests.filter { it.status == InvestmentManagerRequestStatus.FAILED }.sumOf { it.amount },
            ),
        ).right()
    }

    override fun checkInvestmentRedemptionStatus(accountId: AccountId, operationId: GoalRequestOperationId): Either<InvestmentManagerWithRedemptionResultException, FixedIncomeInvestmentRedemptionResult> {
        val redemptionRequests = findRedemptionRequest(accountId = accountId, operationId = operationId).getOrElse {
            return InvestmentManagerWithRedemptionResultException(
                FixedIncomeInvestmentRedemptionResult(
                    operationId = operationId,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.UNKNOWN,
                    redemptionGroupExternalId = null,
                    redemptionExternalIds = emptyList(),
                    errorMessage = it.message,
                    amounts = null,
                ),
                it,
            ).left()
        }

        return when {
            redemptionRequests.isEmpty() -> {
                FixedIncomeInvestmentRedemptionResult(
                    operationId = null,
                    provider = FixedIncomeProvider.ARBI,
                    status = InvestmentManagerRequestStatus.NOT_FOUND,
                    redemptionGroupExternalId = null,
                    redemptionExternalIds = emptyList(),
                    errorMessage = "NOT FOUND",
                    amounts = null,
                ).right()
            }

            redemptionRequests.size == 1 -> checkInvestmentRedemptionStatus(singleExternalId = redemptionRequests.first().externalId, groupExternalId = null)
            else -> checkInvestmentRedemptionStatus(singleExternalId = null, groupExternalId = redemptionRequests.first().redemptionGroupExternalId)
        }
    }

    override fun findRedemptionRequest(externalId: InvestmentManagerExternalId): Either<InvestmentManagerException, FixedIncomeRedemptionRequest?> {
        val logName = "$className#findRedemptionRequest"
        val markers = append("externalId", externalId)

        val uri = UriBuilder.of(configuration.redemptionStatusPath).path(externalId.value).build()
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(FixedIncomeRedemptionRequestResponseTO::class.java),
                Argument.of(String::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.toDomain().right()
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NOT_FOUND) {
                return null.right()
            }
            return InvestmentManagerException(e).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
    }

    override fun findRedemptionRequest(accountId: AccountId, operationId: GoalRequestOperationId): Either<InvestmentManagerException, List<FixedIncomeRedemptionRequest>> {
        val logName = "$className#findRedemptionRequest"
        val markers = append("accountId", accountId.value).andAppend("operationId", operationId.value)

        val uri = UriBuilder.of(configuration.redemptionStatusPath).path("accountId").path(accountId.value).path("externalId").path(operationId.value).build()
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        markers.andAppend("uri", uri)
        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.listOf(FixedIncomeRedemptionRequestResponseTO::class.java),
                Argument.of(String::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.map { it.toDomain() }.right()
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NOT_FOUND) {
                return emptyList<FixedIncomeRedemptionRequest>().right()
            }
            return InvestmentManagerException(e).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
    }

    override fun findRedemptionGroupRequests(externalId: InvestmentManagerExternalId): Either<InvestmentManagerException, List<FixedIncomeRedemptionRequest>> {
        val logName = "$className#findRedemptionGroupRequests"
        val markers = append("externalId", externalId)

        val uri = UriBuilder.of(configuration.redemptionStatusPath).path("group").path(externalId.value).build()
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(FixedIncomeRedemptionGroupResponseTO::class.java),
                Argument.of(String::class.java),
            )

            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.requests.map { it.toDomain() }.right()
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NOT_FOUND) {
                return emptyList<FixedIncomeRedemptionRequest>().right()
            }
            return InvestmentManagerException(e).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
    }

    override fun userGoalPositions(accountId: AccountId, goalId: GoalId): Either<InvestmentManagerException, UserGoalPositions> {
        val logName = "$className#userGoalPositions"

        val markers = append("accountId", accountId.value).andAppend("goalId", goalId.value)

        val uri = UriBuilder.of(configuration.userPositionPath).path(accountId.value).queryParam("investmentGroupId", goalId.value).build()
        markers.andAppend("uri", uri)
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(InvestmentManagerUserPositionResponseTO::class.java),
                Argument.of(Unit::class.java),
            )

            val response = call.firstOrError().blockingGet()
            logger.info(markers, logName)
            return response.toDomain().right()
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NOT_FOUND) {
                return UserGoalPositions.ofEmpty(accountId, goalId).right()
            }
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
    }

    override fun userPositions(accountId: AccountId): Either<InvestmentManagerException, List<UserGoalPositions>> {
        val logName = "$className#userPositions"

        val markers = append("accountId", accountId.value)

        val uri = UriBuilder.of(configuration.userAllPositionPath).path(accountId.value).build()
        markers.andAppend("uri", uri)
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.listOf(InvestmentManagerUserPositionResponseTO::class.java),
                Argument.of(Unit::class.java),
            )

            val response = call.firstOrError().blockingGet()
            logger.info(markers, logName)
            return response.map { it.toDomain() }.right()
        } catch (e: HttpClientResponseException) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
    }

    override fun userGoalPositionAvailableToRedeem(accountId: AccountId, goalId: GoalId, penalty: FixedIncomeIndexRate?): Either<InvestmentManagerException, UserGoalAvailableToRedeemPositions> {
        val logName = "$className#userGoalPositionAvailableToRedeem"

        val markers = append("accountId", accountId.value).andAppend("goalId", goalId.value)

        val requestMap = mutableMapOf<String, Any>("accountId" to accountId.value)
        val uri = UriBuilder.of(configuration.userPositionAvailableToRedeemPath)
            .queryParam("investmentGroupId", goalId.value)
            .queryParam("indexPercentage", penalty?.rate)
            .expand(requestMap)

        markers.andAppend("uri", uri)
        val httpRequest = HttpRequest.GET<Unit>(uri)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(InvestmentManagerAvailableToRedeemResponseTO::class.java),
                Argument.of(Unit::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers, logName)
            return response.toDomain().right()
        } catch (e: HttpClientResponseException) {
            if (e.status == HttpStatus.NOT_FOUND) {
                return createEmptyUserGoalAvailableToRedeemPositions(accountId, goalId).right()
            }
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
    }

    override fun optimizeMonthlyContribution(currentGrossAmount: Long, nextPaymentDate: LocalDate, dateToComplete: LocalDate, goalNetAmount: Long, installmentFrequency: InstallmentFrequency, index: FixedIncomeIndex, indexPercentage: FixedIncomeIndexRate): Either<InvestmentManagerException, Long> {
        val logName = "$className#optimizeMonthlyContribution"
        val payload = OptimizeMonthlyContributionRequestTO(
            index = index,
            frequency = installmentFrequency,
            currentGrossAmount = currentGrossAmount,
            nextPaymentDate = nextPaymentDate.format(DateTimeFormatter.ISO_DATE),
            dateToComplete = dateToComplete.format(DateTimeFormatter.ISO_DATE),
            targetFutureNetValue = goalNetAmount,
            indexPercentage = indexPercentage.rate,
        )

        val markers = append("request", payload)

        val httpRequest = HttpRequest.PUT(configuration.simulationOptimizeContributionPath, payload)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        return try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(OptimizeMonthlyContributionResponseTO::class.java),
                Argument.of(Unit::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            response.installmentContribution.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            InvestmentManagerException(e).left()
        }
    }

    override fun simulateFutureNetValue(currentGrossAmount: Long, nextPaymentDate: LocalDate, dateToComplete: LocalDate, installmentAmount: Long, installmentFrequency: InstallmentFrequency, index: FixedIncomeIndex, indexPercentage: FixedIncomeIndexRate): Either<InvestmentManagerException, Long> {
        val logName = "$className#simulateFutureNetValue"
        val payload = SimulateFutureValueRequestTO(
            index = index,
            frequency = installmentFrequency,
            currentGrossAmount = currentGrossAmount,
            nextPaymentDate = nextPaymentDate.format(DateTimeFormatter.ISO_DATE),
            dateToComplete = dateToComplete.format(DateTimeFormatter.ISO_DATE),
            installmentContribution = installmentAmount,
            indexPercentage = indexPercentage.rate,
        )

        val markers = append("request", payload)

        val httpRequest = HttpRequest.PUT(configuration.simulationSimulateFutureValuePath, payload)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        return try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.of(SimulateFutureValueResponseTO::class.java),
                Argument.of(Unit::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            response.netAmount.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            InvestmentManagerException(e).left()
        }
    }

    @Cacheable(cacheNames = ["compare-index-interest-rates"])
    override fun compareAllRates(startDate: LocalDate, endDate: LocalDate, indexRelativeRate: FixedIncomeIndexRate): Either<InvestmentManagerException, Map<FixedIncomeIndex, Double>> {
        val logName = "$className#compareAllRates"
        val payload = CompareAllRatesRequestTO(
            startDate = startDate.format(dateFormat),
            endDate = endDate.format(dateFormat),
            indexRelativeRate = indexRelativeRate.rate,
        )

        val markers = append("request", payload)

        val httpRequest = HttpRequest.PUT(configuration.compareAllRatesPath, payload)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        return try {
            val call = httpClient.retrieve(
                httpRequest,
                Argument.mapOf(FixedIncomeIndex::class.java, Double::class.java),
                Argument.of(Unit::class.java),
            )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            response.right()
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            InvestmentManagerException(e).left()
        }
    }

    private fun createAccount(accountRegisterData: AccountRegisterData): Either<InvestmentManagerException, Unit> {
        val logName = "$className#enableFixedIncomeAccount"
        val payload = InvestmentManagerCreateAccountRequestTO(
            personType = "PF",
            cpfOrCnpj = accountRegisterData.getCPF()!!,
            fullName = accountRegisterData.getName(),
            birthDate = accountRegisterData.birthDate!!.format(dateFormat),
            address = InvestmentManagerAddressRequestTO(
                street = accountRegisterData.address!!.streetName,
                number = accountRegisterData.address!!.number!!,
                neighborhood = accountRegisterData.address!!.neighborhood,
                postalCode = accountRegisterData.address!!.zipCode,
                complement = accountRegisterData.address!!.complement,
                city = accountRegisterData.address!!.city,
                stateAcronym = accountRegisterData.address!!.state,
                country = "Brasil",
            ),
            documentDetails = InvestmentManagerDocumentDetailsRequestTO(
                documentType = when (val type = accountRegisterData.documentInfo!!.type()) {
                    InvestmentManagerDocumentTypeRequestTO.CPF -> InvestmentManagerDocumentTypeRequestTO.RG
                    else -> type
                },
                documentNumber = accountRegisterData.documentInfo!!.number(),
                documentDateEmission = accountRegisterData.documentInfo!!.expeditionDate!!.format(dateFormat),
            ),
            gender = accountRegisterData.calculatedGender?.toInvestmentManagerTO() ?: InvestmentManagerGenderRequestTO.OTHER,
            maritalStatus = InvestmentManagerMaritalStatusRequestTO.OTHER,
            accountId = accountRegisterData.accountId.value,
            email = accountRegisterData.emailAddress.value,
        )

        val markers = append("request", payload)

        val httpRequest = HttpRequest.POST(configuration.createAccountPath, payload)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        try {
            val call = httpClient.exchange(
                httpRequest,
                Argument.of(Unit::class.java),
            )
            call.firstOrError().blockingGet().body()
            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
        return Unit.right()
    }

    private fun createAccountOnProvider(accountId: AccountId, bankAccount: InternalBankAccount): Either<InvestmentManagerException, Unit> {
        val logName = "$className#createAccountOnProvider"
        val payload = InvestmentManagerCreateAccountOnProviderRequestTO(
            accountId = accountId.value,
            providerAccountId = bankAccount.buildFullAccountNumber().padStart(10, '0'),
        )

        val markers = append("request", payload)

        val httpRequest = HttpRequest.POST(configuration.createFixedIncomeAccountPath, payload)
            .basicAuth(configuration.clientId, configuration.clientSecret)
            .contentType(MediaType.APPLICATION_JSON)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.exchange(
            httpRequest,
            Argument.of(Unit::class.java),
        )

        try {
            call.firstOrError().blockingGet().body()
            logger.info(markers, logName)
        } catch (e: Exception) {
            logger.error(markers, logName, e)
            return InvestmentManagerException(e).left()
        }
        return Unit.right()
    }
}

private fun InvestmentManagerRedemptionQuotationByAmountResponseTO.toDomain(accountId: AccountId, goalId: GoalId): FixedIncomeRedemptionQuotations {
    return FixedIncomeRedemptionQuotations(
        accountId = accountId,
        goalId = goalId,
        quotations = quotations.map { it.toDomain() },
        principalValue = principalValue,
        grossValue = grossValue,
        netValue = netValue,
        taxValue = taxValue,
        penaltyValue = penaltyValue,
    )
}

private fun InvestmentManagerRedemptionQuotationPositionResponseTO.toDomain(): FixedIncomeRedemptionQuotation {
    return FixedIncomeRedemptionQuotation(
        quotationId = quotationId?.let { InvestmentManagerExternalId(it) },
        provider = provider,
        positionId = FixedIncomePositionId(positionId),
        principalValue = principalValue,
        grossValue = grossValue,
        netValue = netValue,
        quantity = quantity,
        pu = pu,
        penaltyRate = FixedIncomeIndexRate(indexPercentage),
        taxValue = taxValue,
        redemptionType = FixedIncomeRedemptionType.valueOf(redemptionType),
        penaltyValue = penaltyValue,
    )
}

private fun createEmptyUserGoalAvailableToRedeemPositions(accountId: AccountId, goalId: GoalId): UserGoalAvailableToRedeemPositions {
    return UserGoalAvailableToRedeemPositions(
        accountId = accountId,
        goalId = goalId,
        positions = emptyList(),
        netValue = 0,
    )
}

private fun InvestmentManagerAvailableToRedeemResponseTO.toDomain(): UserGoalAvailableToRedeemPositions {
    return UserGoalAvailableToRedeemPositions(
        accountId = AccountId(investmentGroupId),
        goalId = GoalId(investmentGroupId),
        positions = positions.map { it.toDomain() },
        netValue = netValue,
    )
}

private fun InvestmentManagerPositionAvailableToRedeemResponseTO.toDomain(): UserGoalPositionAvailableToRedeemAmount {
    return UserGoalPositionAvailableToRedeemAmount(
        provider = provider,
        positionId = FixedIncomePositionId(positionId),
        availableToRedeem = availableToRedeem,
    )
}

private fun InvestmentManagerUserPositionResponseTO.toDomain(): UserGoalPositions {
    return UserGoalPositions(
        accountId = AccountId(investmentGroupId),
        goalId = GoalId(investmentGroupId),
        positions = positions.map { it.toDomain() },
        netValue = netValue,
        grossValue = grossValue,
        investedValue = investedValue,
        irValue = irValue,
        iofValue = iofValue,
        earningValue = earningValue,
    )
}

private fun InvestmentManagerFixedIncomePositionResponseTO.toDomain(): FixedIncomePosition {
    return FixedIncomePosition(
        provider = provider,
        product = product?.toDomain(),
        positionId = positionId,
        operationDate = LocalDate.parse(operationDate, dateFormat),
        liquidityDueDate = LocalDate.parse(liquidityDueDate, dateFormat),
        maturityDate = LocalDate.parse(maturityDate, dateFormat),
        unitPrice = unitPrice,
        quantity = quantity,
        netValue = netValue,
        grossValue = grossValue,
        name = name,
        investedValue = investedValue,
        irValue = irValue,
        iofValue = iofValue,
        earningValue = earningValue,
    )
}

private fun FixedIncomeRedemptionRequestResponseTO.toDomain(): FixedIncomeRedemptionRequest {
    return FixedIncomeRedemptionRequest(
        externalId = InvestmentManagerExternalId(fixedIncomeRedemptionRequestId),
        goalId = GoalId(investmentGroupId),
        redemptionGroupExternalId = redemptionGroupId?.let { InvestmentManagerExternalId(it) },
        accountId = AccountId(accountId),
        provider = provider,
        amount = amount,
        status = InvestmentManagerRequestStatus.valueOf(status),
        positionId = FixedIncomePositionId(positionId),
        createdAt = createdAt,
    )
}

private fun FixedIncomeInvestmentRequestResponseTO.toDomain(): FixedIncomeInvestmentRequestResult {
    return FixedIncomeInvestmentRequestResult(
        operationId = externalId?.let { GoalRequestOperationId(externalId) },
        externalId = InvestmentManagerExternalId(fixedIncomeInvestmentRequestId),
        provider = provider,
        status = InvestmentManagerRequestStatus.valueOf(status),
        fixedIncomeProduct = fixedIncomeProduct.toDomain(),
        errorMessage = null,
        positionId = positionId?.let { FixedIncomePositionId(it) },
        maturityDate = LocalDate.parse(maturityDate, dateFormat),
    )
}

private fun FixedIncomeProductResponseTO.toDomain(): FixedIncomeProduct {
    return FixedIncomeProduct(
        provider = provider,
        productId = productId,
        name = name,
        index = index,
        indexPercentage = FixedIncomeIndexRate(indexPercentage),
        minTransactionValue = minTransactionValue,
        maxTransactionValue = maxTransactionValue,
        tradeTimeLimit = tradeTimeLimit,
    )
}

private fun Gender.toInvestmentManagerTO(): InvestmentManagerGenderRequestTO {
    return when (this) {
        Gender.M -> InvestmentManagerGenderRequestTO.MALE
        Gender.F -> InvestmentManagerGenderRequestTO.FEMALE
    }
}

private fun DocumentInfo.number() = when (type()) {
    InvestmentManagerDocumentTypeRequestTO.RG -> rg
    InvestmentManagerDocumentTypeRequestTO.CNH -> cnhNumber ?: rg
    InvestmentManagerDocumentTypeRequestTO.PASSPORT -> throw IllegalArgumentException("PASSPORT is not supported")
    InvestmentManagerDocumentTypeRequestTO.CPF -> cpf
}

private fun DocumentInfo.type(): InvestmentManagerDocumentTypeRequestTO {
    return when (docType) {
        DocumentType.CNH, DocumentType.CNHV2 -> InvestmentManagerDocumentTypeRequestTO.CNH
        DocumentType.RG, DocumentType.NEWRG -> InvestmentManagerDocumentTypeRequestTO.RG
        DocumentType.CNPJ -> throw IllegalArgumentException("CNPJ is not supported")
        DocumentType.CPF -> InvestmentManagerDocumentTypeRequestTO.CPF
        is DocumentType.OTHER -> throw IllegalArgumentException("documento deve ser CNH ou RG")
    }
}

private fun InvestmentManagerRedemptionRedemptionRequestByAmountResponseTO.toDomain(operationId: GoalRequestOperationId?, amount: Long): FixedIncomeInvestmentRedemptionResult {
    return FixedIncomeInvestmentRedemptionResult(
        operationId = operationId,
        provider = FixedIncomeProvider.ARBI,
        status = InvestmentManagerRequestStatus.CREATED,
        redemptionGroupExternalId = redemptionGroupId?.let { InvestmentManagerExternalId(it) },
        redemptionExternalIds = fixedIncomeRedemptionRequestIds.map { InvestmentManagerExternalId(it) },
        errorMessage = null,
        amounts = FixedIncomeInvestmentRedemptionAmountsResult(
            amount = amount,
            completedAmount = 0,
            failedAmount = 0,
        ),
    )
}

data class OptimizeMonthlyContributionResponseTO(
    val installmentContribution: Long,
)

data class OptimizeMonthlyContributionRequestTO(
    val index: FixedIncomeIndex,
    val frequency: InstallmentFrequency,
    val currentGrossAmount: Long,
    val nextPaymentDate: String,
    val dateToComplete: String,
    val targetFutureNetValue: Long,
    val indexPercentage: Int?,
)

data class SimulateFutureValueRequestTO(
    val index: FixedIncomeIndex,
    val frequency: InstallmentFrequency,
    val currentGrossAmount: Long,
    val nextPaymentDate: String,
    val dateToComplete: String,
    val installmentContribution: Long,
    val indexPercentage: Int?,
)

data class SimulateFutureValueResponseTO(
    val initialGrossAmount: Long,
    val investedAmount: Long,
    val grossAmount: Long,
    val taxAmount: Long,
    val netAmount: Long,
    val profitAmount: Long,
)

data class InvestmentManagerRedemptionQuotationByAmountResponseTO(
    val quotations: List<InvestmentManagerRedemptionQuotationPositionResponseTO>,
    val principalValue: Long,
    val grossValue: Long,
    val netValue: Long,
    val taxValue: Long,
    val penaltyValue: Long,
)

data class InvestmentManagerRedemptionQuotationPositionResponseTO(
    val quotationId: String?,
    val provider: FixedIncomeProvider,
    val positionId: String,
    val principalValue: Long,
    val grossValue: Long,
    val netValue: Long,
    val quantity: Int,
    val pu: Double,
    val indexPercentage: Int,
    val taxValue: Long,
    val redemptionType: String,
    val penaltyValue: Long,
)

data class InvestmentManagerAvailableToRedeemResponseTO(
    val investmentGroupId: String,
    val positions: List<InvestmentManagerPositionAvailableToRedeemResponseTO>,
    val netValue: Long,
)

data class InvestmentManagerPositionAvailableToRedeemResponseTO(
    val provider: FixedIncomeProvider,
    val positionId: String,
    val availableToRedeem: Long,
)

data class InvestmentManagerUserPositionResponseTO(
    val investmentGroupId: String,
    val positions: List<InvestmentManagerFixedIncomePositionResponseTO>,
    val netValue: Long,
    val grossValue: Long,
    val investedValue: Long,
    val irValue: Long,
    val iofValue: Long,
    val earningValue: Long,
)

data class InvestmentManagerFixedIncomePositionResponseTO(
    val provider: FixedIncomeProvider,
    val product: FixedIncomeProductResponseTO?,
    val positionId: String,

    val operationDate: String,
    val liquidityDueDate: String,
    val maturityDate: String,
    val unitPrice: Long,
    val quantity: Long,
    val netValue: Long,
    val grossValue: Long,
    val name: String,

    val investedValue: Long,
    val irValue: Long,
    val iofValue: Long,
    val earningValue: Long,
)

data class FixedIncomeRedemptionGroupResponseTO(
    val fixedIncomeRedemptionGroupId: String,
    val accountId: String,
    val investmentGroupId: String,
    val amount: Long,
    val status: String,
    val requests: List<FixedIncomeRedemptionRequestResponseTO>,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FixedIncomeRedemptionRequestResponseTO(
    val fixedIncomeRedemptionRequestId: String,
    val investmentGroupId: String,
    val redemptionGroupId: String?,
    val accountId: String,
    val provider: FixedIncomeProvider,
    val amount: Long,
    val status: String,
    val positionId: String,
    val createdAt: ZonedDateTime,
    val externalId: String?,
)

data class InvestmentManagerRedemptionRedemptionRequestByAmountResponseTO(
    val redemptionGroupId: String?,
    val fixedIncomeRedemptionRequestIds: List<String>,
)

data class InvestmentManagerRedemptionRequestTO(
    val externalId: String,
    val accountId: String,
    val investmentGroupId: String,
    val amount: Long,
    val indexPercentage: Int?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FixedIncomeInvestmentRequestResponseTO(
    val fixedIncomeInvestmentRequestId: String,
    val externalId: String?,
    val investmentGroupId: String,
    val accountId: String,
    val provider: FixedIncomeProvider,
    val amount: Long,
    val status: String,
    val positionId: String?,
    val fixedIncomeProduct: FixedIncomeProductResponseTO,
    val forceRedemptionDate: String?,
    val createdAt: ZonedDateTime,
    val maturityDate: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class FixedIncomeProductResponseTO(
    val provider: FixedIncomeProvider,
    val productId: String,
    val name: String,
    val index: FixedIncomeIndex,
    val indexPercentage: Int,
    val minTransactionValue: Long,
    val maxTransactionValue: Long,
    val tradeTimeLimit: LocalTime,
)

data class InvestmentManagagerInvestmentRequestRequestTO(
    val accountId: String,
    val investmentGroupId: String,
    val amount: Long,
    val index: FixedIncomeIndex,
    val indexPercentage: Int,
    val forceRedemptionDate: String?,
    val externalId: String,
) {
    val provider: FixedIncomeProvider = FixedIncomeProvider.ARBI
}

data class InvestmentManagerCreateAccountRequestTO(
    val personType: String = "PF",
    val cpfOrCnpj: String,
    val fullName: String,
    val birthDate: String,
    val address: InvestmentManagerAddressRequestTO,
    val documentDetails: InvestmentManagerDocumentDetailsRequestTO,
    val gender: InvestmentManagerGenderRequestTO,
    val maritalStatus: InvestmentManagerMaritalStatusRequestTO,
    val accountId: String,
    val email: String,
)

data class InvestmentManagerAddressRequestTO(
    val street: String,
    val number: String,
    val neighborhood: String,
    val postalCode: String,
    val complement: String?,
    val city: String,
    val stateAcronym: String,
    val country: String,
)

data class InvestmentManagerDocumentDetailsRequestTO(
    val documentType: InvestmentManagerDocumentTypeRequestTO,
    val documentNumber: String,
    val documentDateEmission: String,
)

enum class InvestmentManagerGenderRequestTO {
    MALE, FEMALE, OTHER
}

enum class InvestmentManagerMaritalStatusRequestTO {
    MARRIED, DIVORCED, LEGALLY_SEPARATED, SEPARATED, SINGLE, WIDOWED, CIVIL_UNION, OTHER,
}

enum class InvestmentManagerDocumentTypeRequestTO {
    RG, CNH, PASSPORT, CPF
}

data class InvestmentManagerCreateAccountOnProviderRequestTO(
    val accountId: String,
    val providerAccountId: String,
) {
    val provider: FixedIncomeProvider = FixedIncomeProvider.ARBI
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class IndexInterestRateResponseTO(
    val index: FixedIncomeIndex,
    val date: String,
    val defaultScale: String,
    val interestRate: Map<String, Double>,
) {
    fun toDomain() = IndexInterestRate(
        index = index,
        defaultScale = defaultScale,
        yearlyInterest = interestRate[FixedIncomeIndexScale.YEARLY.name]!!,
        monthlyInterest = interestRate[FixedIncomeIndexScale.MONTHLY.name]!!,
        weeklyInterest = interestRate[FixedIncomeIndexScale.WEEKLY.name]!!,
        dailyInterest = interestRate[FixedIncomeIndexScale.DAILY.name]!!,
    )
}

private enum class FixedIncomeIndexScale {
    DAILY, MONTHLY, WEEKLY, YEARLY
}

data class CompareAllRatesRequestTO(
    val startDate: String,
    val endDate: String,
    val indexRelativeRate: Int,
)