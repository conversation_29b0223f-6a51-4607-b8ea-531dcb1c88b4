package ai.friday.billpayment.modules.investmentGoals.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndexRate
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionId
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionRepository
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemptionStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import java.time.ZonedDateTime

private const val ENTITY_ID = "GOAL_REDEMPTION"

@InvestmentGoalsModule
class GoalRedemptionDbRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) : GoalRedemptionRepository {
    private val mapper = jacksonObjectMapper()

    override fun save(goalRedemption: GoalRedemption) {
        val entity =
            GoalRedemptionEntity().apply {
                primaryKey = ENTITY_ID
                scanKey = goalRedemption.id.value
                gSIndex1PrimaryKey = goalRedemption.goalId.value
                gSIndex1ScanKey = "$ENTITY_ID#${goalRedemption.status}"
                gSIndex2PrimaryKey = goalRedemption.walletId.value
                gSIndex2ScanKey = "$ENTITY_ID#${goalRedemption.status}"
                gSIndex3PrimaryKey = goalRedemption.id.value
                gSIndex3ScanKey = ENTITY_ID
                goalId = goalRedemption.goalId.value
                walletId = goalRedemption.walletId.value
                netAmount = goalRedemption.netAmount
                penaltyRate = goalRedemption.penaltyRate?.rate
                status = goalRedemption.status
                source = mapper.writeValueAsString(goalRedemption.actionSource)
                createdAt = goalRedemption.createdAt.format(dateTimeFormat)
                updatedAt = getZonedDateTime().format(dateTimeFormat)
            }
        dynamoDbDAO.save(entity)
    }

    override fun findByGoalId(goalId: GoalId): List<GoalRedemption> =
        dynamoDbDAO
            .queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
                primaryKey = goalId.value,
                scanKey = ENTITY_ID,
                type = GoalRedemptionEntity::class.java,
            ).map { it.toGoalRedemption() }

    override fun findByWalletId(walletId: WalletId): List<GoalRedemption> =
        dynamoDbDAO
            .queryIndex2OnHashKeyValueAndRangeKeyBeginsWith(
                primaryKey = walletId.value,
                scanKey = ENTITY_ID,
                type = GoalRedemptionEntity::class.java,
            ).map { it.toGoalRedemption() }

    override fun findOrNull(goalRedemptionId: GoalRedemptionId): GoalRedemption? =
        dynamoDbDAO
            .queryIndex3OnHashKeyAndRangeKey(
                primaryKey = goalRedemptionId.value,
                scanKey = ENTITY_ID,
                type = GoalRedemptionEntity::class.java,
            ).firstOrNull()
            ?.toGoalRedemption()

    private fun GoalRedemptionEntity.toGoalRedemption() = GoalRedemption(
        id = GoalRedemptionId(value = scanKey),
        goalId = GoalId(value = goalId),
        walletId = WalletId(value = walletId),
        netAmount = netAmount,
        penaltyRate = penaltyRate?.let { FixedIncomeIndexRate(it) },
        status = status,
        actionSource = mapper.readValue(source, ActionSource.WalletActionSource::class.java),
        createdAt = ZonedDateTime.parse(createdAt, dateTimeFormat),
        updatedAt = ZonedDateTime.parse(updatedAt, dateTimeFormat),
    )
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class GoalRedemptionEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex2PrimaryKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var gSIndex2PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex2ScanKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var gSIndex2ScanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex3PrimaryKey", globalSecondaryIndexName = "GSIndex3")
    lateinit var gSIndex3PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex3ScanKey", globalSecondaryIndexName = "GSIndex3")
    lateinit var gSIndex3ScanKey: String

    @get:DynamoDBAttribute(attributeName = "GoalId")
    lateinit var goalId: String

    @get:DynamoDBAttribute(attributeName = "WalletId")
    lateinit var walletId: String

    @get:DynamoDBAttribute(attributeName = "NetAmount")
    var netAmount: Long = 0

    @get:DynamoDBAttribute(attributeName = "PenaltyRate")
    var penaltyRate: Int? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @get:DynamoDBAttribute(attributeName = "Status")
    lateinit var status: GoalRedemptionStatus

    @DynamoDBAttribute(attributeName = "Source")
    lateinit var source: String

    @get:DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String
}