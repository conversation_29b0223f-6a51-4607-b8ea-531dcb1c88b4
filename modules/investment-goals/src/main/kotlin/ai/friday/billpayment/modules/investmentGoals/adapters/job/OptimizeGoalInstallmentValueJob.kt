package ai.friday.billpayment.modules.investmentGoals.adapters.job

import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModuleNoTest
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.goal.InstallmentFrequency
import ai.friday.billpayment.modules.investmentGoals.app.messaging.GoalMessagePublisher
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModuleNoTest
open class OptimizeGoalInstallmentMonthlyValueJob(
    goalService: GoalService,
    goalMessagePublisher: GoalMessagePublisher,
    @Property(name = "schedules.optimizeGoalInstallmentMonthlyValueJob.cron") cron: String,
) : OptimizeGoalInstallmentValueJob(goalService, goalMessagePublisher, cron = cron) {
    override fun execute() {
        doExecute(InstallmentFrequency.MONTHLY)
    }
}

@InvestmentGoalsModuleNoTest
open class OptimizeGoalInstallmentWeeklyValueJob(
    goalService: GoalService,
    goalMessagePublisher: GoalMessagePublisher,
    @Property(name = "schedules.optimizeGoalInstallmentWeeklyValueJob.cron") cron: String,
) : OptimizeGoalInstallmentValueJob(goalService, goalMessagePublisher, cron = cron) {
    override fun execute() {
        doExecute(InstallmentFrequency.WEEKLY)
    }
}

abstract class OptimizeGoalInstallmentValueJob(
    private val goalService: GoalService,
    private val goalMessagePublisher: GoalMessagePublisher,
    cron: String,
) : AbstractJob(cron = cron) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    fun doExecute(installmentFrequency: InstallmentFrequency) {
        val logName = "OptimizeGoalInstallmentValueJob#execute"

        goalService.findGoalsEligibleToOptimization(installmentFrequency = installmentFrequency).forEach { goal ->
            goalMessagePublisher.publishOptimizeGoalInstallmentAmount(goal.id)
            logger.info(append("goalId", goal.id).andAppend("installmentFrequency", installmentFrequency.name), logName)
        }
    }
}