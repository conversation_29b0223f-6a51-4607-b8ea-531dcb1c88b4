package ai.friday.billpayment.modules.investmentGoals.app.payment

import ai.friday.billpayment.adapters.notification.BillPaymentEmailSenderService
import ai.friday.billpayment.adapters.s3.ReceiptConfiguration
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.bill.toEpochMillis
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.NotificationService
import ai.friday.billpayment.app.integrations.ReceiptTemplateCompiler
import ai.friday.billpayment.app.notification.ButtonWhatsAppDeeplinkParameter
import ai.friday.billpayment.app.notification.EmailTemplatesConfiguration
import ai.friday.billpayment.app.notification.HandlebarsTempate2Html2Image
import ai.friday.billpayment.app.notification.HandlebarsTempate2Html2ImageCommand
import ai.friday.billpayment.app.notification.NotificationMedia
import ai.friday.billpayment.app.notification.NotificationTemplate
import ai.friday.billpayment.app.notification.NotificationType
import ai.friday.billpayment.app.notification.TemplatesConfiguration
import ai.friday.billpayment.app.notification.TemplatesHtmlToImageConfiguration
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.notification.buildReceiptBillButtonPath
import ai.friday.billpayment.app.payment.InvestmentReceiptData
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.buildFormattedAmount
import ai.friday.billpayment.app.payment.buildFormattedHeaderDateTime
import ai.friday.billpayment.app.payment.formatAccountNumber
import ai.friday.billpayment.app.payment.formatBankData
import ai.friday.billpayment.app.payment.formatRoutingNumber
import ai.friday.billpayment.app.payment.getFormattedDocument
import ai.friday.billpayment.app.payment.receipt.builder.ReceiptDataSender
import ai.friday.billpayment.app.utils.TemplateHelper
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.mediaTypeFrom
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.Goal
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalInstallments
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRedemptionResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.redemption.GoalRedemption
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormatBR
import arrow.core.getOrElse
import java.io.Serializable
import java.time.LocalDate
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
class GoalsInvestmentNotificationService(
    private val notificationService: NotificationService,
    private val walletService: WalletService,
    private val goalRepository: GoalRepository,
    private val templatesConfiguration: TemplatesConfiguration,
    private val billPaymentEmailSenderService: BillPaymentEmailSenderService,
    private val investmentReceiptImageBuilder: InvestmentReceiptImageBuilder,
    private val goalInvestmentRepository: GoalInvestmentRepository,
) : ReceiptDataSender {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun canSendReceiptFor(receiptData: ReceiptData): Boolean {
        return when (receiptData) {
            is InvestmentReceiptData -> true
            else -> false
        }
    }

    override fun notifyReceipt(bill: Bill, receiptData: ReceiptData, membersToNotify: List<Member>, wallet: Wallet) {
        when (receiptData) {
            is InvestmentReceiptData -> {
                notifyInvestmentReceipt(bill, receiptData, membersToNotify, wallet)
            }

            else -> {
                logger.error(append("receiptData", receiptData).andAppend("context", "Unexpected receipt data"), "GoalsInvestmentNotificationService#notifyReceipt")
            }
        }
    }

    private fun notifyInvestmentReceipt(
        bill: Bill,
        receiptData: InvestmentReceiptData,
        membersToNotify: List<Member>,
        wallet: Wallet,
    ) {
        val goal = bill.goalId?.let { goalRepository.find(it) }?.getOrElse { throw IllegalStateException() } ?: throw IllegalStateException()
        val paidInvestment = goalInvestmentRepository.findByBillId(bill.billId) ?: throw IllegalStateException()

        notificationService.notifyMembersViaChatbot(membersToNotify, NotificationType.INVESTMENT_RECEIPT) { account ->
            billPaymentEmailSenderService.sendReceiptEmail(
                receiptData = receiptData,
                recipient = account.emailAddress,
                wallet = wallet,
            )

            val notificationMedia = if (paidInvestment.extraInstallment) {
                investmentReceiptImageBuilder.buildInstagrammableExtraInvestmentReceiptHtml(goal)
            } else {
                investmentReceiptImageBuilder.buildInstagrammableInvestmentReceiptHtml(goal, bill.dueDate)
            }

            val template = templatesConfiguration.whatsappTemplates.walletInvestmentBillReceiptWithBadge

            WhatsappNotification(
                accountId = account.accountId,
                receiver = MobilePhone(account.mobilePhone),
                template = NotificationTemplate(template),
                parameters = buildList {
                    add(buildFormattedAmount(receiptData.totalAmount))
                    add(receiptData.assignor)
                },
                buttonWhatsAppParameter = buildReceiptBillButtonPath(receiptData.walletId, receiptData.billId),
                media = notificationMedia,
            )
        }
    }

    fun notifyInvestmentRedemptionCreated(goalRedemption: GoalRedemption) {
        val logName = "InvestmentNotificationService#notifyInvestmentRedemptionCreated"
        val wallet = walletService.findWallet(goalRedemption.walletId)
        val members = chooseMembers(wallet)

        val goal = goalRepository.findOrNull(goalRedemption.goalId) ?: run {
            logger.error(append("goalRedemption", goalRedemption).andAppend("context", "Goal not found"), logName)
            return
        }

        notificationService.notifyMembers(
            members = members,
            type = NotificationType.INVESTMENT_REDEMPTION_CREATED,
        ) { account ->

            WhatsappNotification(
                account = account,
                template = NotificationTemplate(templatesConfiguration.whatsappTemplates.investmentRedemptionCreated),
                parameters = buildList {
                    add(goal.name)
                    add(buildFormattedAmount(goalRedemption.netAmount))
                    add(wallet.name)
                },
                buttonWhatsAppParameter = ButtonWhatsAppDeeplinkParameter(buildGoalDetailsButtonPath(goal.id)),
            )
        }
    }

    fun notifyInvestmentRedemptionFinished(goalRedemption: GoalRedemption, redemptionResult: FixedIncomeInvestmentRedemptionResult) {
        val logName = "InvestmentNotificationService#notifyInvestmentRedemptionFinished"

        val wallet = walletService.findWallet(goalRedemption.walletId)
        val members = chooseMembers(wallet)

        val goal = goalRepository.findOrNull(goalRedemption.goalId) ?: run {
            logger.error(append("goalRedemption", goalRedemption).andAppend("redemptionResult", redemptionResult).andAppend("context", "Goal not found"), logName)
            return
        }

        val amounts = redemptionResult.amounts

        when (redemptionResult.status) {
            InvestmentManagerRequestStatus.COMPLETED -> {
                notificationService.notifyMembers(
                    members = members,
                    type = NotificationType.INVESTMENT_REDEMPTION_COMPLETED,
                ) { account ->

                    if (amounts == null || amounts.completedAmount == 0L) {
                        logger.error(append("redemptionResult", redemptionResult).andAppend("context", "completed redemption deveria ter amounts"), logName)
                    }

                    billPaymentEmailSenderService.sendTemplatedReceiptEmail(
                        templateName = templatesConfiguration.email.local.investmentRedemptionFinished,
                        templateParams = mapOf(
                            "totalAmount" to buildFormattedAmount(amounts?.completedAmount ?: goalRedemption.netAmount),
                            "date" to buildFormattedHeaderDateTime(goalRedemption.createdAt),
                            "recipientName" to goal.name,
                            "success" to "true",
                            "walletName" to wallet.name,
                        ),
                        subject = "Resgate realizado",
                        recipient = account.emailAddress,
                    )

                    WhatsappNotification(
                        account = account,
                        template = NotificationTemplate(templatesConfiguration.whatsappTemplates.investmentRedemptionCompleted),
                        parameters = buildList {
                            add(goal.name)
                            add(buildFormattedAmount(goalRedemption.netAmount))
                            add(wallet.name)
                        },
                        buttonWhatsAppParameter = ButtonWhatsAppDeeplinkParameter(buildGoalDetailsButtonPath(goal.id)),
                    )
                }
            }

            InvestmentManagerRequestStatus.FAILED, InvestmentManagerRequestStatus.NOT_FOUND -> {
                if (amounts == null || amounts.completedAmount == 0L) {
                    notificationService.notifyMembers(
                        members = members,
                        type = NotificationType.INVESTMENT_REDEMPTION_FAILED,
                    ) { account ->
                        billPaymentEmailSenderService.sendTemplatedReceiptEmail(
                            templateName = templatesConfiguration.email.local.investmentRedemptionFinished,
                            templateParams = mapOf(
                                "totalAmount" to buildFormattedAmount(goalRedemption.netAmount),
                                "date" to buildFormattedHeaderDateTime(goalRedemption.createdAt),
                                "recipientName" to goal.name,
                                "failed" to "true",
                                "walletName" to wallet.name,
                            ),
                            subject = "Resgate não realizado",
                            recipient = account.emailAddress,
                        )

                        WhatsappNotification(
                            account = account,
                            template = NotificationTemplate(templatesConfiguration.whatsappTemplates.investmentRedemptionFailed),
                            parameters = buildList {
                                add(goal.name)
                                add(buildFormattedAmount(goalRedemption.netAmount))
                            },
                            buttonWhatsAppParameter = ButtonWhatsAppDeeplinkParameter(buildGoalDetailsButtonPath(goal.id)),
                        )
                    }
                } else {
                    notificationService.notifyMembers(
                        members = members,
                        type = NotificationType.INVESTMENT_REDEMPTION_PARTIAL_FAILED,
                    ) { account ->
                        billPaymentEmailSenderService.sendTemplatedReceiptEmail(
                            templateName = templatesConfiguration.email.local.investmentRedemptionFinished,
                            templateParams = mapOf(
                                "totalAmount" to buildFormattedAmount(goalRedemption.netAmount),
                                "date" to buildFormattedHeaderDateTime(goalRedemption.createdAt),
                                "recipientName" to goal.name,
                                "partialFailed" to "true",
                                "walletName" to wallet.name,
                                "partialSuccessAmount" to buildFormattedAmount(amounts.completedAmount),

                            ),
                            subject = "Resgate parcialmente realizado",
                            recipient = account.emailAddress,
                        )

                        WhatsappNotification(
                            account = account,
                            template = NotificationTemplate(templatesConfiguration.whatsappTemplates.investmentRedemptionPartialFailed),
                            parameters = buildList {
                                add(goal.name)
                                add(buildFormattedAmount(goalRedemption.netAmount))
                                add(buildFormattedAmount(amounts.completedAmount))
                                add(wallet.name)
                            },
                            buttonWhatsAppParameter = ButtonWhatsAppDeeplinkParameter(buildGoalDetailsButtonPath(goal.id)),
                        )
                    }
                }
            }

            InvestmentManagerRequestStatus.CREATED,
            InvestmentManagerRequestStatus.REQUESTED,
            InvestmentManagerRequestStatus.UNKNOWN,
            -> {
                logger.error(append("redemptionResult", redemptionResult).andAppend("context", "Unexpected redemption status"), logName)
                return
            }
        }
    }

    fun notifyInvestmentValueOptmized(goal: Goal, oldValue: Long, optimizedValue: Long) {
        val wallet = walletService.findWallet(goal.walletId)
        val members = chooseMembers(wallet)

        notificationService.notifyMembers(
            members = members,
            type = NotificationType.INVESTMENT_VALUE_OPTIMIZED,
        ) { account ->

            WhatsappNotification(
                account = account,
                template = if (optimizedValue > oldValue) {
                    NotificationTemplate(templatesConfiguration.whatsappTemplates.investmentValueOptimizedIncreased)
                } else {
                    NotificationTemplate(templatesConfiguration.whatsappTemplates.investmentValueOptimizedDecreased)
                },
                parameters = buildList {
                    add(goal.name)
                    add(buildFormattedAmount(oldValue))
                    add(buildFormattedAmount(optimizedValue))
                },
                buttonWhatsAppParameter = ButtonWhatsAppDeeplinkParameter(buildGoalDetailsButtonPath(goal.id)),
            )
        }
    }

    fun notifyGoalCompleted(goal: Goal, currentNetAmount: Long) {
        if (currentNetAmount <= 0) {
            return
        }

        val wallet = walletService.findWallet(goal.walletId)
        val members = chooseMembers(wallet)

        val hasCompletedByDate = goal.endDate <= getLocalDate()
        val hasCompletedByAmount = currentNetAmount >= goal.amount

        if (!hasCompletedByDate && !hasCompletedByAmount) {
            logger.error(append("goalId", goal.id).andAppend("contextMessage", "Goal has not been completed"), "GoalsInvestmentNotificationService#notifyGoalCompleted")
            return
        }

        fun buildParameters(totalParams: Int): List<String> {
            return when (totalParams) {
                2 -> {
                    return buildList {
                        add(goal.name)
                        add(buildFormattedAmount(currentNetAmount))
                    }
                }

                3 -> {
                    return buildList {
                        add(goal.name)
                        add(buildFormattedAmount(currentNetAmount))
                        add(goal.endDate.format(dateFormatBR))
                    }
                }

                else -> {
                    throw IllegalStateException("Unexpected totalParams")
                }
            }
        }

        notificationService.notifyMembers(
            members = members,
            type = NotificationType.GOAL_COMPLETED,
        ) { account ->

            val defaultButton = ButtonWhatsAppDeeplinkParameter(buildGoalDetailsButtonPath(goal.id))

            val (template, parameters, buttonWhatsAppParameter) =
                when (goal.liquidity) {
                    GoalProductLiquidity.DAILY -> {
                        if (hasCompletedByDate) {
                            Triple(templatesConfiguration.whatsappTemplates.goalWithDailyLiquidityCompletedByDateWithBadge, buildParameters(3), defaultButton)
                        } else {
                            Triple(templatesConfiguration.whatsappTemplates.goalWithDailyLiquidityCompletedByAmountWithBadge, buildParameters(2), defaultButton)
                        }
                    }

                    GoalProductLiquidity.MATURITY -> {
                        if (hasCompletedByDate) {
                            Triple(templatesConfiguration.whatsappTemplates.goalWithMaturityLiquidityCompletedByDateWithBadge, buildParameters(3), null)
                        } else {
                            Triple(templatesConfiguration.whatsappTemplates.goalWithMaturityLiquidityCompletedByAmountWithBadge, buildParameters(3), defaultButton)
                        }
                    }
                }

            val notificationMedia =
                if (hasCompletedByDate) {
                    investmentReceiptImageBuilder.buildInstagrammableGoalEndDateReachedReceiptHtml(goal)
                } else {
                    investmentReceiptImageBuilder.buildInstagrammableGoalCompletedReceiptHtml(goal)
                }

            WhatsappNotification(
                account = account,
                template = NotificationTemplate(template),
                parameters = parameters,
                buttonWhatsAppParameter = buttonWhatsAppParameter,
                media = notificationMedia,
            )
        }
    }

    fun notifyGoalPausedViaCompletion(goal: Goal, currentNetAmount: Long) {
        if (currentNetAmount <= 0) {
            return
        }

        val wallet = walletService.findWallet(goal.walletId)
        val members = chooseMembers(wallet)

        val hasCompletedByDate = goal.endDate <= getLocalDate()
        val hasCompletedByAmount = currentNetAmount >= goal.amount

        if (!hasCompletedByDate && !hasCompletedByAmount) {
            logger.error(append("goalId", goal.id).andAppend("contextMessage", "Goal has not been completed"), "GoalsInvestmentNotificationService#notifyGoalPausedViaCompletion")
            return
        }

        if (goal.liquidity != GoalProductLiquidity.MATURITY) {
            logger.error(append("goalId", goal.id).andAppend("contextMessage", "Goal liquidity should be DAILY"), "GoalsInvestmentNotificationService#notifyGoalPausedViaCompletion")
            return
        }

        notificationService.notifyMembers(
            members = members,
            type = NotificationType.GOAL_COMPLETED,
        ) { account ->
            val template = if (hasCompletedByDate) {
                templatesConfiguration.whatsappTemplates.goalWithDailyLiquidityPausedByDate
            } else {
                templatesConfiguration.whatsappTemplates.goalWithDailyLiquidityPausedByAmount
            }

            val parameters = buildList {
                add(goal.name)
            }

            WhatsappNotification(
                account = account,
                template = NotificationTemplate(template),
                parameters = parameters,
                buttonWhatsAppParameter = ButtonWhatsAppDeeplinkParameter(buildGoalDetailsButtonPath(goal.id)),
            )
        }
    }

    private fun chooseMembers(wallet: Wallet): List<Member> {
        return wallet.getMembersCanViewBalance()
    }

    private fun buildGoalDetailsButtonPath(goalId: GoalId): String {
        return "metas/detalhes/${goalId.value}"
    }
}

@InvestmentGoalsModule
class InvestmentReceiptHandlebarCompiler(
    private val emailTemplatesConfiguration: EmailTemplatesConfiguration,
) : ReceiptTemplateCompiler {
    override fun canBuildFor(receiptData: ReceiptData): Boolean {
        return when (receiptData) {
            is InvestmentReceiptData -> true
            else -> false
        }
    }

    override fun buildReceiptHtml(receiptData: ReceiptData, wallet: Wallet): CompiledHtml {
        return when (receiptData) {
            is InvestmentReceiptData -> buildInvestmentReceiptHtml(receiptData)
            else -> throw IllegalStateException("Unexpected receipt data")
        }
    }

    override fun buildReceiptMailHtml(receiptData: ReceiptData): CompiledHtml {
        return when (receiptData) {
            is InvestmentReceiptData -> buildInvestmentReceiptMailHtml(receiptData)
            else -> throw IllegalStateException("Unexpected receipt data")
        }
    }

    private fun buildInvestmentReceiptHtml(receiptData: InvestmentReceiptData): CompiledHtml {
        val pageHeight = 26
        val map = mapOf(
            "totalAmount" to buildFormattedAmount(receiptData.totalAmount),
            "groupName" to receiptData.investmentGroupLabel,
            "dateTime" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "date" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "recipientName" to receiptData.assignor,
            "productName" to receiptData.productName,
            "productProvider" to receiptData.productProvider,
            "productInterestRateLabel" to receiptData.productInterestRateLabel,
            "maturityDate" to receiptData.maturityDate.format(dateFormatBR),
            "goalEndDate" to receiptData.goalEndDate?.format(dateFormatBR),
            "payerName" to receiptData.payer.name,
            "payerDocument" to getFormattedDocument(receiptData.payer.document),
            "payerBankNumber" to formatBankData(receiptData.payerFinancialInstitution),
            "payerRoutingNumber" to formatRoutingNumber(receiptData.payerBankAccount.routingNo.toString()),
            "payerAccountNumber" to formatAccountNumber(
                receiptData.payerBankAccount.accountNo.toBigInteger(),
                receiptData.payerBankAccount.accountDv,
            ),
            "positionId" to receiptData.positionId,
            "pageHeight" to pageHeight,
        )
        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.investmentReceipt, map))
    }

    private fun buildInvestmentReceiptMailHtml(receiptData: InvestmentReceiptData): CompiledHtml {
        val map = mapOf(
            "isInvestment" to "true",
            "totalAmount" to buildFormattedAmount(receiptData.totalAmount),
            "date" to buildFormattedHeaderDateTime(receiptData.dateTime),
            "recipientName" to receiptData.assignor,
            "positionId" to receiptData.positionId,
            "productName" to receiptData.productName,
        )
        return CompiledHtml(TemplateHelper.applyTemplate(emailTemplatesConfiguration.local.mailReceipt, map))
    }
}

@InvestmentGoalsModule
class InvestmentReceiptImageBuilder(
    private val templatesHtmlToImageConfiguration: TemplatesHtmlToImageConfiguration,
    private val receiptConfiguration: ReceiptConfiguration,
    private val handlebarsTempate2Html2Image: HandlebarsTempate2Html2Image,
) {
    private val logger = LoggerFactory.getLogger(this::class.java)

    private val imageFormat = "png"

    private fun generateNotificationMediaImageForHtml(command: HandlebarsTempate2Html2ImageCommand): NotificationMedia.Image {
        val publicLink = handlebarsTempate2Html2Image.renderTemplateImageAndCreatePublicLink(command) ?: run {
            logger.error(append("context", "Could not retrieve public link for image").andAppend("placeToStore", command.placeToStore), "GoalsInvestmentNotificationService#generateNotificationMediaImageForHtml")
            throw IllegalStateException("Could not retrieve public link for image")
        }
        return NotificationMedia.Image(publicLink, command.mediaType.toString(), null, "Investimento realizado com sucesso")
    }

    fun buildInstagrammableInvestmentReceiptHtml(goal: Goal, dueDate: LocalDate): NotificationMedia.Image {
        val map = buildInstagrammableTemplateMap(goal, dueDate)
        val command = buildHandlebarsTempate2Html2ImageCommand(
            goal,
            templatePath = templatesHtmlToImageConfiguration.instagrammableInvestmentReceipt,
            templateData = map,
        )

        return generateNotificationMediaImageForHtml(command)
    }

    fun buildInstagrammableExtraInvestmentReceiptHtml(goal: Goal): NotificationMedia.Image {
        val map = buildInstagrammableTemplateMap(goal)
        val command = buildHandlebarsTempate2Html2ImageCommand(
            goal,
            templatePath = templatesHtmlToImageConfiguration.instagrammableExtraInvestmentReceipt,
            templateData = map,
        )
        return generateNotificationMediaImageForHtml(command)
    }

    fun buildInstagrammableGoalCompletedReceiptHtml(goal: Goal): NotificationMedia.Image {
        val map = buildInstagrammableTemplateMap(goal)
        val command = buildHandlebarsTempate2Html2ImageCommand(
            goal,
            templatePath = templatesHtmlToImageConfiguration.instagrammableGoalCompletedReceipt,
            templateData = map,
        )
        return generateNotificationMediaImageForHtml(command)
    }

    fun buildInstagrammableGoalEndDateReachedReceiptHtml(goal: Goal): NotificationMedia.Image {
        val map = buildInstagrammableTemplateMap(goal)
        val command = buildHandlebarsTempate2Html2ImageCommand(
            goal,
            templatePath = templatesHtmlToImageConfiguration.instagrammableGoalEndDateReachedReceipt,
            templateData = map,
        )
        return generateNotificationMediaImageForHtml(command)
    }

    private fun buildHandlebarsTempate2Html2ImageCommand(goal: Goal, templatePath: String, templateData: Map<String, Serializable>): HandlebarsTempate2Html2ImageCommand {
        return HandlebarsTempate2Html2ImageCommand(
            templatePath = templatePath,
            templateData = templateData,
            placeToStore = StoredObject(receiptConfiguration.bucketRegion, receiptConfiguration.bucketName, "${goal.walletId.value}/badge/${goal.id.value}/${LocalDate.now().toEpochMillis()}.$imageFormat"),
            imageFormat = imageFormat,
            mediaType = mediaTypeFrom(imageFormat),
        )
    }

    private fun buildInstagrammableTemplateMap(goal: Goal, dueDate: LocalDate? = null): Map<String, Serializable> {
        val goalBasicMap = mapOf(
            "imageUrl" to goal.imageUrl,
            "goalName" to goal.name,
        )

        val dueDateMap = dueDate?.let {
            val currentInstallmentNumber = GoalInstallments.calculateInstallmentNumber(goal, dueDate)
            val progress = currentInstallmentNumber * 100.0 / goal.totalInstallments
            mapOf(
                "installmentNumber" to currentInstallmentNumber,
                "installmentTotal" to goal.totalInstallments,
                "progress" to if (progress < 1.0) {
                    1
                } else {
                    progress.toInt()
                },
            )
        } ?: emptyMap()

        return goalBasicMap + dueDateMap
    }
}