package ai.friday.billpayment.modules.investmentGoals.app.payment

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.payment.ErrorSource
import ai.friday.billpayment.app.payment.InvestmentCheckout
import ai.friday.billpayment.app.payment.PaymentOperation
import ai.friday.billpayment.app.payment.PaymentStatus
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.SettlementStatus
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.modules.investmentGoals.InvestmentGoalsModule
import ai.friday.billpayment.modules.investmentGoals.app.goal.GoalService
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeIndex
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeInvestmentRequestResult
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomePositionId
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProduct
import ai.friday.billpayment.modules.investmentGoals.app.investment.FixedIncomeProvider
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentRepository
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalInvestmentStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.GoalRequestOperationId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerExternalId
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerRequestStatus
import ai.friday.billpayment.modules.investmentGoals.app.investment.InvestmentManagerService
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductLiquidity
import ai.friday.billpayment.modules.investmentGoals.app.product.GoalProductService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getEpochMilli
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import java.time.LocalDate
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@InvestmentGoalsModule
class GoalInvestmentCheckout(
    @Property(name = "integrations.investment-manager.investment-wait-time") private val waitTime: Long,
    @Property(name = "integrations.investment-manager.investment-pooling-interval") private val poolingInterval: Long,
    @Property(name = "investments.maxRedemptionDays") private val maxRedemptionDays: Long,
    private val goalService: GoalService,
    private val investmentManagerService: InvestmentManagerService,
    private val goalProductService: GoalProductService,
    private val transactionService: TransactionService,
    private val goalInvestmentRepository: GoalInvestmentRepository,
) : InvestmentCheckout {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun execute(transaction: Transaction): Transaction {
        val logName = "GoalInvestmentCheckout#execute"
        val bill = transaction.settlementData.getTarget<Bill>()
        val amount = bill.amountTotal
        val markers = append("billId", bill.billId.value).andAppend("transactionId", transaction.id.value).andAppend("amount", amount).andAppend("accountId", transaction.payer.accountId.value)

        val goalId = bill.goalId ?: run {
            val errorDescription = "bill sem goalId"
            logger.error(markers.andAppend("context", errorDescription), logName)
            return transactionFailedWithoutInvestment(transaction, amount, errorDescription)
        }

        markers.andAppend("goalId", goalId.value)

        val goal = goalService.findGoal(goalId).getOrElse {
            val errorDescription = "goal não encontrado"
            logger.error(markers.andAppend("context", errorDescription), logName)
            return transactionFailedWithoutInvestment(transaction, amount, errorDescription)
        }

        markers.andAppend("goalProductId", goal.productId.value)

        val goalProduct = goalProductService.findProduct(goal.productId).getOrElse {
            val errorDescription = "produto não encontrado"
            logger.error(markers.andAppend("context", errorDescription), logName)
            return transactionFailedWithoutInvestment(transaction, amount, errorDescription)
        }

        if (goalProduct.index == FixedIncomeIndex.SAVINGS) {
            val errorDescription = "investimento em poupança não é permitido"
            logger.error(markers.andAppend("context", errorDescription), logName)
            return transactionFailedWithoutInvestment(transaction, amount, errorDescription)
        }

        val forceRedemptionDate = when (goalProduct.liquidity) {
            GoalProductLiquidity.DAILY -> null
            GoalProductLiquidity.MATURITY -> {
                val maxRedemptionDate = LocalDate.now().plusDays(maxRedemptionDays)
                if (goal.endDate.isAfter(maxRedemptionDate)) {
                    maxRedemptionDate
                } else {
                    goal.endDate
                }
            }
        }

        val existingSettlementOperation = transaction.settlementData.settlementOperation
        if (existingSettlementOperation != null) {
            logger.error(markers.andAppend("context", "Execute chamado com settlement preexistente. Risco de liquidar 2x").andAppend("ACTION", "VERIFY").andAppend("transaction", transaction), logName)
            val externalId = (existingSettlementOperation as GoalInvestmentSettlementOperation).investmentRequestId
            val operationId = existingSettlementOperation.operationId
            val investmentRequestUpdated = investmentManagerService.checkInvestmentRequestStatus(externalId = externalId).getOrElse {
                logger.error(markers.andAppend("context", "erro na resposta no InvestmentManager").andAppend("result", it.result), logName, it.throwable)
                return transaction
            }
            return updateTransactionByStatus(operationId, transaction, investmentRequestUpdated, transaction.settlementData.getTarget()).first
        } else {
            val operationId = GoalRequestOperationId()

            val investmentResponse = investmentManagerService.invest(
                operationId = operationId,
                accountId = transaction.payer.accountId,
                goalId = goal.id,
                amount = amount,
                index = goalProduct.index,
                indexRate = goalProduct.indexIncome,
                forceRedemptionDate = forceRedemptionDate,
            ).getOrElse {
                logger.error(markers.andAppend("context", "erro na resposta no InvestmentManager.").andAppend("result", it.result), logName, it.throwable)
                it.result
            }

            val (updatedTransaction, shouldReturn) = updateTransactionByStatus(operationId, transaction, investmentResponse, bill)
            if (shouldReturn) {
                return updatedTransaction
            }
            val updatedResponse = checkInvestmentStatusManyTimes(
                accountId = goal.accountId,
                operationId = operationId,
                externalId = investmentResponse.externalId,
                poolingInterval = poolingInterval,
                maxWaitTime = waitTime,
                waitForStatuses = listOf(InvestmentManagerRequestStatus.FAILED, InvestmentManagerRequestStatus.COMPLETED),
            )

            return updateTransactionByStatus(operationId, updatedTransaction, updatedResponse, bill).first
        }
    }

    private fun updateTransactionByStatus(
        operationId: GoalRequestOperationId,
        transaction: Transaction,
        investmentResponse: FixedIncomeInvestmentRequestResult,
        bill: Bill,
    ): Pair<Transaction, Boolean> {
        return when (investmentResponse.status) {
            InvestmentManagerRequestStatus.CREATED,
            InvestmentManagerRequestStatus.REQUESTED,
            InvestmentManagerRequestStatus.UNKNOWN,
            -> transactionProcessing(operationId, transaction, investmentResponse, bill.amountTotal) to false

            InvestmentManagerRequestStatus.COMPLETED -> transactionCompleted(operationId, transaction, investmentResponse, bill.amountTotal) to true
            InvestmentManagerRequestStatus.FAILED, InvestmentManagerRequestStatus.NOT_FOUND -> transactionFailed(operationId, transaction, investmentResponse, bill.amountTotal) to true
        }
    }

    private fun transactionProcessing(operationId: GoalRequestOperationId, transaction: Transaction, investmentResponse: FixedIncomeInvestmentRequestResult, amountTotal: Long): Transaction {
        updateGoalInvestmentStatus(transaction, GoalInvestmentStatus.PROCESSING)

        transaction.apply {
            status = TransactionStatus.PROCESSING
            paymentData.toSingle().payment = investmentResponse.toGoalInvestmentAuthorization(operationId, amountTotal)
            settlementData.settlementOperation = investmentResponse.toGoalInvestmentSettlementOperation(operationId, amountTotal, investmentResponse.positionId)
        }
        transactionService.save(transaction)
        return transaction
    }

    private fun transactionCompleted(operationId: GoalRequestOperationId, transaction: Transaction, investmentResponse: FixedIncomeInvestmentRequestResult, amountTotal: Long): Transaction {
        updateGoalInvestmentStatus(transaction, GoalInvestmentStatus.DONE)
        transaction.apply {
            status = TransactionStatus.COMPLETED
            paymentData.toSingle().payment = investmentResponse.toGoalInvestmentAuthorization(operationId, amountTotal)
            settlementData.settlementOperation = investmentResponse.toGoalInvestmentSettlementOperation(operationId, amountTotal, investmentResponse.positionId)
        }

        return transaction
    }

    private fun transactionFailed(operationId: GoalRequestOperationId, transaction: Transaction, investmentResponse: FixedIncomeInvestmentRequestResult, amountTotal: Long): Transaction {
        updateGoalInvestmentStatus(transaction, GoalInvestmentStatus.FAILED)

        transaction.apply {
            status = TransactionStatus.FAILED
            paymentData.toSingle().payment = investmentResponse.toGoalInvestmentAuthorization(operationId, amountTotal)
            settlementData.settlementOperation = investmentResponse.toGoalInvestmentSettlementOperation(operationId, amountTotal, investmentResponse.positionId)
        }
        return transaction
    }

    private fun updateGoalInvestmentStatus(transaction: Transaction, status: GoalInvestmentStatus) {
        val bill = transaction.settlementData.getTarget<Bill>()
        goalInvestmentRepository.findByBillId(bill.billId)?.let {
            val paidDate = if (status == GoalInvestmentStatus.DONE) getZonedDateTime() else null
            goalInvestmentRepository.save(it.copy(status = status, paidAt = paidDate, amount = bill.amountTotal))
        } ?: run {
            logger.error(
                append("context", "pedido de investimento não encontrado. usuário não terá histórico de investimento")
                    .andAppend("billId", bill.billId)
                    .andAppend("goalId", bill.goalId?.value)
                    .andAppend("walletId", bill.walletId.value),
                "GoalInvestmentCheckout#updateGoalInvestmentStatus",
            )
        }
    }

    private fun transactionFailedWithoutInvestment(transaction: Transaction, amountTotal: Long, errorMessage: String): Transaction {
        transaction.apply {
            status = TransactionStatus.FAILED
            paymentData.toSingle().payment = GoalInvestmentAuthorization(
                operationId = null,
                status = InvestmentManagerRequestStatus.FAILED,
                amount = amountTotal,
                errorDescription = errorMessage,
            )
        }
        return transaction
    }

    private fun checkInvestmentStatusManyTimes(
        accountId: AccountId,
        operationId: GoalRequestOperationId,
        externalId: InvestmentManagerExternalId?,
        poolingInterval: Long,
        maxWaitTime: Long,
        waitForStatuses: List<InvestmentManagerRequestStatus>,
    ): FixedIncomeInvestmentRequestResult {
        val logName = "InvestmentCheckout#checkInvestmentStatusManyTimes"
        val startTime = getEpochMilli()
        val markers = append("investmentManagerExternalId", externalId?.value).andAppend("poolingInterval", poolingInterval)
            .andAppend("maxWaitTime", maxWaitTime)
        while (true) {
            Thread.sleep(poolingInterval)
            val investmentRequestUpdated =
                if (externalId != null) {
                    investmentManagerService.checkInvestmentRequestStatus(externalId = externalId)
                } else {
                    investmentManagerService.checkInvestmentRequestStatus(accountId = accountId, operationId = operationId)
                }.getOrElse {
                    logger.error(markers.andAppend("context", "erro na resposta no InvestmentManager").andAppend("result", it.result), logName, it.throwable)
                    return it.result
                }

            val elapsedTime = getEpochMilli() - startTime

            val statusMatch = waitForStatuses.contains(investmentRequestUpdated.status)

            markers.andAppend("status", investmentRequestUpdated.status).andAppend("elapsedTime", elapsedTime).andAppend("statusMatch", statusMatch)
            logger.info(markers, "InvestmentCheckout#checkInvestmentStatusManyTimes")

            if (statusMatch || maxWaitTime < elapsedTime) {
                return investmentRequestUpdated
            }
        }
    }

    override fun rollbackTransaction(transaction: Transaction): Transaction {
        transaction.rollback()
        return transaction
    }

    override fun checkSettlementStatus(transaction: Transaction): SettlementStatus {
        val logName = "GoalInvestmentCheckout#checkSettlementStatus"

        val externalId = (transaction.settlementData.settlementOperation as GoalInvestmentSettlementOperation).investmentRequestId
        val operationId = (transaction.settlementData.settlementOperation as GoalInvestmentSettlementOperation).operationId
        val markers = append("transactionId", transaction.id.value).andAppend("investmentRequestId", externalId.value)

        val investmentRequestUpdated = investmentManagerService.checkInvestmentRequestStatus(externalId = externalId).getOrElse {
            logger.error(markers.andAppend("context", "erro na resposta no InvestmentManager").andAppend("result", it.result), logName, it.throwable)
            return SettlementStatus.Processing
        }

        val (updatedTransaction, _) = updateTransactionByStatus(operationId, transaction, investmentRequestUpdated, transaction.settlementData.getTarget())

        return when (updatedTransaction.status) {
            TransactionStatus.COMPLETED -> {
                SettlementStatus.Success()
            }

            TransactionStatus.FAILED -> {
                SettlementStatus.Failure(investmentRequestUpdated.errorMessage ?: "")
            }

            TransactionStatus.PROCESSING -> SettlementStatus.Processing
            TransactionStatus.UNDONE -> SettlementStatus.Failure("INVALID UNDONE STATE")
        }
    }
}

data class GoalInvestmentAuthorization(
    val operationId: GoalRequestOperationId?,
    val status: InvestmentManagerRequestStatus,
    val amount: Long,
    val errorDescription: String = "",
) : PaymentOperation {
    override fun hasError(): Boolean {
        return status.isError()
    }

    override fun getErrorMessage(): String {
        return errorDescription
    }

    override fun status(): PaymentStatus {
        return when (status) {
            InvestmentManagerRequestStatus.COMPLETED -> PaymentStatus.SUCCESS
            InvestmentManagerRequestStatus.FAILED, InvestmentManagerRequestStatus.NOT_FOUND -> PaymentStatus.ERROR
            InvestmentManagerRequestStatus.CREATED,
            InvestmentManagerRequestStatus.REQUESTED,
            InvestmentManagerRequestStatus.UNKNOWN,
            -> PaymentStatus.UNKNOWN
        }
    }

    override fun toErrorSource(): ErrorSource {
        return ErrorSource.INVESTMENT_MANAGER
    }

    override fun isRetryable(): Boolean {
        return status() == PaymentStatus.ERROR
    }
}

data class GoalInvestmentSettlementOperation(
    val operationId: GoalRequestOperationId,
    val investmentRequestId: InvestmentManagerExternalId,
    var status: InvestmentManagerRequestStatus,
    val amount: Long,
    var errorDescription: String = "",
    val provider: FixedIncomeProvider,
    val product: FixedIncomeProduct?,
    val positionId: FixedIncomePositionId?,
    val maturityDate: LocalDate?,
) : SettlementOperation {
    override val authentication: String? = null

    override val gateway: FinancialServiceGateway = when (provider) {
        FixedIncomeProvider.ARBI -> FinancialServiceGateway.ARBI
    }

    override fun isRetryable() = true

    override fun getErrorMessage() = errorDescription

    override fun confirm() {
        this.status = InvestmentManagerRequestStatus.COMPLETED
        this.errorDescription = ""
    }

    override fun shouldCheckSettlementStatus(): Boolean {
        return when (this.status) {
            InvestmentManagerRequestStatus.CREATED,
            InvestmentManagerRequestStatus.REQUESTED,
            InvestmentManagerRequestStatus.UNKNOWN,
            -> true

            InvestmentManagerRequestStatus.COMPLETED,
            InvestmentManagerRequestStatus.FAILED,
            InvestmentManagerRequestStatus.NOT_FOUND,
            -> false
        }
    }

    override fun settlementId(): String {
        return this.investmentRequestId.value
    }
}

private fun FixedIncomeInvestmentRequestResult.toGoalInvestmentAuthorization(operationId: GoalRequestOperationId?, amountTotal: Long): GoalInvestmentAuthorization {
    return GoalInvestmentAuthorization(
        operationId = operationId,
        status = status,
        amount = amountTotal,
        errorDescription = errorMessage ?: "",
    )
}

private fun FixedIncomeInvestmentRequestResult.toGoalInvestmentSettlementOperation(operationId: GoalRequestOperationId, amountTotal: Long, positionId: FixedIncomePositionId?): GoalInvestmentSettlementOperation? {
    return this.externalId?.let {
        GoalInvestmentSettlementOperation(
            operationId = operationId,
            investmentRequestId = it,
            status = status,
            amount = amountTotal,
            errorDescription = errorMessage ?: "",
            provider = provider,
            product = fixedIncomeProduct,
            positionId = positionId,
            maturityDate = maturityDate,
        )
    }
}