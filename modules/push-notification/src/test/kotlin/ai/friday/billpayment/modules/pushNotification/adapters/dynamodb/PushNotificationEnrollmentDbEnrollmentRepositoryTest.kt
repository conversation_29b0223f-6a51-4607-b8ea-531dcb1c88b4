package ai.friday.billpayment.modules.pushNotification.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationEnrollment
import ai.friday.billpayment.modules.pushNotification.app.PushNotificationToken
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class PushNotificationEnrollmentDbEnrollmentRepositoryTest {
    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val pushNotificationEnrollmentDbEnrollmentRepository =
        ai.friday.billpayment.modules.pushNotification.adapters.dynamodb.PushNotificationEnrollmentDbEnrollmentRepository(
            dynamoDbDAO,
        )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    private val pushNotificationEnrollment = PushNotificationEnrollment(
        token = PushNotificationToken("*************"),
        enabled = true,
    )

    @Test
    fun `cria um push notification enrollment e lê do banco`() {
        pushNotificationEnrollmentDbEnrollmentRepository.save(AccountId(ACCOUNT_ID), pushNotificationEnrollment)
        val pushNotificationEnrollmentDb = pushNotificationEnrollmentDbEnrollmentRepository.findByToken(
            AccountId(ACCOUNT_ID),
            pushNotificationEnrollment.token,
        )

        pushNotificationEnrollmentDb shouldBe pushNotificationEnrollment
    }
}