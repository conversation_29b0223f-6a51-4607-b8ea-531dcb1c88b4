package ai.friday.billpayment.modules.openfinance.app

import DynamoDBUtils
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.fingerprint.DeviceFingerprint
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.DeviceScreenResolution
import ai.friday.billpayment.app.fingerprint.DeviceStatus
import ai.friday.billpayment.app.fingerprint.DeviceType
import ai.friday.billpayment.app.fingerprint.MobileDeviceDetails
import ai.friday.billpayment.app.fingerprint.RegisteredDevice
import ai.friday.billpayment.app.integrations.ConfirmSweepingCashInError
import ai.friday.billpayment.app.integrations.CrmRepository
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.ExternalTransactionId
import ai.friday.billpayment.app.integrations.LimitType
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.integrations.RequestSweepingCashInCommand
import ai.friday.billpayment.app.integrations.RequestSweepingCashInError
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.integrations.SweepingCashInSource
import ai.friday.billpayment.app.integrations.SweepingCashInStatus
import ai.friday.billpayment.app.integrations.SweepingParticipant
import ai.friday.billpayment.app.notification.BillPaymentNotification
import ai.friday.billpayment.app.notification.ChatBotMessagePublisher
import ai.friday.billpayment.app.notification.TemplatesConfiguration
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.balance
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createOpenFinanceDataTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.modules.chatbotai.adapters.ChatbotAiTransactionError
import ai.friday.billpayment.modules.chatbotai.adapters.TransactionResult
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransactionPaymentStatus
import ai.friday.billpayment.modules.chatbotai.app.ChatbotAiTransactionService
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingAccountDBRepository
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingAccountDynamoDAO
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingCashIn
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingCashInDBRepository
import ai.friday.billpayment.modules.openfinance.adapters.OpenFinanceSweepingCashInDynamoDAO
import ai.friday.billpayment.modules.openfinance.adapters.SweepingCashInStatusResponse
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import java.net.URI
import java.time.Duration
import java.time.ZonedDateTime
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient

class SweepingAccountServiceTest {

    val sweepingLimitsConfiguration = SweepingLimits(
        totalAllowedAmount = 1000_00,
        transactionLimit = 1000_00,
        periodicLimits = PeriodicLimits(
            day = PeriodicLimit(
                quantityLimit = 10,
                transactionLimit = 1_000_00,
            ),
            week = PeriodicLimit(
                quantityLimit = 50,
                transactionLimit = 5_000_00,
            ),
            month = PeriodicLimit(
                quantityLimit = 100,
                transactionLimit = 10_000_00,
            ),
            year = PeriodicLimit(
                quantityLimit = 300,
                transactionLimit = 300_000_00,
            ),
        ),
    )

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val dynamoDB: AmazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val client: DynamoDbEnhancedAsyncClient =
        DynamoDbEnhancedAsyncClient
            .builder()
            .dynamoDbClient(DynamoDBUtils.getDynamoDbClientAsync())
            .build()

    private val walletService: WalletService = mockk {
        every {
            findPrimaryWallet(wallet.founder.accountId)
        } returns wallet
        every {
            findWallet(wallet.id)
        } returns wallet
    }

    private val sweepingAccountRepository: OpenFinanceSweepingAccountRepository =
        spyk(OpenFinanceSweepingAccountDBRepository(OpenFinanceSweepingAccountDynamoDAO(client)))

    private val sweepingCashInRepository: OpenFinanceSweepingCashInRepository =
        spyk(OpenFinanceSweepingCashInDBRepository(OpenFinanceSweepingCashInDynamoDAO(client)))

    private val whatsappTemplateConnected = "connected"
    private val whatsappTemplateConnectionFailed = "failed"
    private val whatsappTemplateTransferError = "error"

    private val chatBotMessagePublisher: ChatBotMessagePublisher = mockk(relaxed = true)
    private val templatesConfiguration: TemplatesConfiguration = mockk {
        every { whatsappTemplates } returns mockk {
            every { sweepingAccountConnected } returns whatsappTemplateConnected
            every { sweepingAccountConnectionFailed } returns whatsappTemplateConnectionFailed
            every { sweepingCashInError } returns whatsappTemplateTransferError
            every { sweepingCashInInsufficientBalanceError } returns "balance"
            every { sweepingTransferChatbotRetryableError } returns "sweepingTransferChatbotRetryableError"
            every { sweepingTransferChatbotParticipantBalanceError } returns "sweepingTransferChatbotParticipantBalanceError"
            every { sweepingTransferChatbotParticipantLimitError } returns "sweepingTransferChatbotParticipantLimitError"
            every { sweepingTransferChatbotInvalidConsentError } returns "sweepingTransferChatbotInvalidConsentError"
            every { sweepingTransferChatbotConsentLimitError } returns "sweepingTransferChatbotConsentLimitError"
            every { sweepingTransferWebappRetryableError } returns "sweepingTransferWebappRetryableError"
            every { sweepingTransferWebappParticipantBalanceError } returns "sweepingTransferWebappParticipantBalanceError"
            every { sweepingTransferWebappParticipantLimitError } returns "sweepingTransferWebappParticipantLimitError"
            every { sweepingTransferWebappInvalidConsentError } returns "sweepingTransferWebappInvalidConsentError"
            every { sweepingTransferWebappConsentLimitError } returns "sweepingTransferWebappConsentLimitError"
        }
    }

    private val openFinanceAdapter: OpenFinanceAdapter = mockk()
    private val accountService: AccountService = mockk() {
        every { findAccountPaymentMethodByIdAndAccountId(any(), any()) } returns balance
        every { findAccountById(wallet.founder.accountId) } returns walletFixture.founderAccount
    }
    private val crmRepository: CrmRepository = mockk {
        every { publishEvent(any<AccountId>(), any(), any()) } just runs
    }
    private val deviceFingerprintService: DeviceFingerprintService = mockk {
        every { getOrNull(any()) } returns null
    }

    val sweepingParticipant = SweepingParticipant(
        id = "1",
        name = "Banco",
        compe = "compe",
        ispb = "ispbe",
        restrictedTo = emptyList(),
    )

    private val messagePublisher: MessagePublisher = mockk(relaxed = true)

    private val chatbotAiTransactionService: ChatbotAiTransactionService = mockk() {
        every {
            updatePaymentStatus(any(), any(), any())
        } returns TransactionResult.Success.right()
    }

    private val sweepingAccountService = SweepingAccountService(
        accountService = accountService,
        sweepingAccountRepository = sweepingAccountRepository,
        sweepingCashInRepository = sweepingCashInRepository,
        openFinanceAdapter = openFinanceAdapter,
        participants = listOf(sweepingParticipant),
        chatBotMessagePublisher = chatBotMessagePublisher,
        templatesConfiguration = templatesConfiguration,
        walletService = walletService,
        deviceFingerprintService = deviceFingerprintService,
        messagePublisher = messagePublisher,
        checkSweepingCashInStatusQueueName = "checkSweepingCashInQueueName",
        chatbotAiTransactionService = chatbotAiTransactionService,
    )

    @BeforeEach
    fun setUp() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, accountId = wallet.founder.accountId)
        createOpenFinanceDataTable(dynamoDB)
    }

    @Nested
    @DisplayName("ao solicitar um cash in")
    inner class RequestSweepingCashInTest {
        @Test
        fun `se o id da requisicao jah existir com dados iguais nao deve enviar uma nova`() {
            val request = RequestSweepingCashInCommand(
                sweepingCashInId = SweepingCashInId(),
                walletId = wallet.id,
                amount = 1000,
                description = "description",
                consentId = null,
            )

            val sweepingCashIn = OpenFinanceSweepingCashIn(
                id = request.sweepingCashInId,
                walletId = request.walletId,
                amount = request.amount,
                description = request.description,
                requestSource = request.requestSource,
                approvalSource = request.approvalSource,
                externalTransactionId = request.externalTransactionId,
                consentId = SweepingConsentId("consentId"),
                status = SweepingCashInStatus.FAILED,
                creditorId = CreditorId("creditorId"),
                error = null,
                errorDescription = null,
                endToEnd = EndToEnd(),
            )

            sweepingCashInRepository.save(sweepingCashIn)

            val result = sweepingAccountService.requestSweepingCashIn(request)

            result.isRight() shouldBe true
            result.map {
                it shouldBe sweepingCashIn.endToEnd
            }

            verify {
                openFinanceAdapter wasNot called
            }
        }

        private fun SweepingCashInSource.anotherSource() = SweepingCashInSource.entries.first { it != this }

        @ParameterizedTest
        @CsvSource(
            "false, false, false, false, false, false",
            "false, true , true , true , true , true",
            "true , false, true , true , true , true",
            "true , true , false, true , true , true",
            "true , true , true , false, true , true",
            "true , true , true , true , false, true",
            "true , true , true , true , true , false",
        )
        fun `se o id da requisicao jah existir com dados diferentes deve retornar erro`(sameWalletId: Boolean, sameAmount: Boolean, sameDescription: Boolean, sameRequestSource: Boolean, sameApprovalSource: Boolean, sameExternalTransctionId: Boolean) {
            val request = RequestSweepingCashInCommand(
                sweepingCashInId = SweepingCashInId(),
                walletId = wallet.id,
                amount = 1000,
                description = "description",
                consentId = null,
            )

            val sweepingCashIn = OpenFinanceSweepingCashIn(
                id = request.sweepingCashInId,
                walletId = if (sameWalletId) request.walletId else WalletId(),
                amount = if (sameAmount) request.amount else 1,
                description = if (sameDescription) request.description else "",
                requestSource = if (sameRequestSource) request.requestSource else request.requestSource.anotherSource(),
                approvalSource = if (sameApprovalSource) request.approvalSource else request.approvalSource.anotherSource(),
                externalTransactionId = if (sameExternalTransctionId) request.externalTransactionId else ExternalTransactionId(),
                consentId = SweepingConsentId("consentId"),
                status = SweepingCashInStatus.FAILED,
                creditorId = CreditorId("creditorId"),
                error = null,
                errorDescription = null,
                endToEnd = EndToEnd(),
            )

            sweepingCashInRepository.save(sweepingCashIn)

            val result = sweepingAccountService.requestSweepingCashIn(request)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<RequestSweepingCashInError.Conflict>()
            }

            verify {
                openFinanceAdapter wasNot called
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.modules.openfinance.app.SweepingAccountServiceTest#requestSweepingCashInErrors")
        fun `deve salvar a requisicao mesmo se o adapter falhar`(error: RequestSweepingCashInError, expectedStatus: SweepingCashInStatus) {
            val consent = setupConsent(status = ConsentStatus.AUTHORISED)

            val command = RequestSweepingCashInCommand(
                sweepingCashInId = SweepingCashInId(),
                walletId = consent.walletId,
                amount = 1000,
                description = "description",
                requestSource = SweepingCashInSource.CHATBOT,
                externalTransactionId = ExternalTransactionId(),
                consentId = null,
            )

            every {
                openFinanceAdapter.requestSweepingCashIn(
                    sweepingCashInId = command.sweepingCashInId,
                    consentId = consent.id,
                    amount = command.amount,
                    description = command.description,
                    creditorId = consent.creditorId,
                    riskSignals = any(),
                )
            } returns error.left()

            val result = sweepingAccountService.requestSweepingCashIn(command)
            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe error
            }

            val sweepingCashIn = sweepingCashInRepository.findOrNull(command.sweepingCashInId)
            sweepingCashIn.shouldNotBeNull()
            sweepingCashIn shouldMatchCommand command
            sweepingCashIn shouldMatchConsent consent
            sweepingCashIn.status shouldBe expectedStatus
            sweepingCashIn.endToEnd shouldBe null
        }

        @Test
        fun `deve tentar resolver o pagamento se o adapter falhar com erro generico`() {
            val consent = setupConsent(status = ConsentStatus.AUTHORISED)

            val command = RequestSweepingCashInCommand(
                sweepingCashInId = SweepingCashInId(),
                walletId = consent.walletId,
                amount = 1000,
                description = "description",
                consentId = null,
            )

            every {
                openFinanceAdapter.requestSweepingCashIn(
                    sweepingCashInId = command.sweepingCashInId,
                    consentId = consent.id,
                    amount = command.amount,
                    description = command.description,
                    creditorId = consent.creditorId,
                    riskSignals = any(),
                )
            } returns RequestSweepingCashInError.GenericError("mocked").left()

            val result = sweepingAccountService.requestSweepingCashIn(command)
            result.isLeft() shouldBe true

            val slot = slot<Any>()
            verify {
                messagePublisher.sendMessage("checkSweepingCashInQueueName", capture(slot))
            }
            with(slot.captured) {
                this.shouldBeTypeOf<CheckSweepingCashInStatusMessage>()
                this.sweepingCashInId shouldBe command.sweepingCashInId.value
            }
        }

        @Test
        fun `deve solicitar um cash in`() {
            val consent = setupConsent(status = ConsentStatus.AUTHORISED)

            val command = RequestSweepingCashInCommand(
                sweepingCashInId = SweepingCashInId(),
                walletId = consent.walletId,
                amount = 1000,
                description = "description",
                consentId = null,
            )

            val registeredDevice = RegisteredDevice(
                deviceIds = mapOf(),
                fingerprint = DeviceFingerprint(value = ""),
                status = DeviceStatus.ACTIVE,
                creationDate = getZonedDateTime(),
                details = MobileDeviceDetails(
                    uuid = UUID.randomUUID(),
                    alias = "alias",
                    fingerprint = "fingerprint",
                    screenResolution = DeviceScreenResolution(width = 1, height = 2),
                    type = DeviceType.IOS,
                    fresh = false,
                    dpi = 0.0,
                    manufacturer = "manufacturer",
                    model = "model",
                    rooted = false,
                    storageCapacity = 0,
                    osId = "osId",
                ),
                liveness = null,
            )

            every {
                deviceFingerprintService.getOrNull(wallet.founder.accountId)
            } returns registeredDevice

            val endToEnd = EndToEnd()
            val slot = slot<SweepingRiskSignals>()
            every {
                openFinanceAdapter.requestSweepingCashIn(
                    sweepingCashInId = command.sweepingCashInId,
                    consentId = consent.id,
                    amount = command.amount,
                    description = command.description,
                    creditorId = consent.creditorId,
                    riskSignals = capture(slot),
                )
            } returns endToEnd.right()

            val result = sweepingAccountService.requestSweepingCashIn(command)
            result.isRight() shouldBe true
            result.map {
                it shouldBe endToEnd
            }

            val sweepingCashIn = sweepingCashInRepository.findOrNull(command.sweepingCashInId)
            sweepingCashIn.shouldNotBeNull()
            sweepingCashIn shouldMatchCommand command
            sweepingCashIn shouldMatchConsent consent
            sweepingCashIn.status shouldBe SweepingCashInStatus.CREATED
            sweepingCashIn.endToEnd shouldBe endToEnd

            with(slot.captured) {
                shouldBeTypeOf<ManualSweepingRiskSignals>()
                deviceId shouldBe registeredDevice.details.uuid.toString()
                isRootedDevice shouldBe registeredDevice.details.rooted
                // screenBrightness shouldBe 0.75 // FIXME
                // elapsedTimeSinceBoot shouldBe Duration.ofDays(7) // FIXME
                osVersion shouldBe registeredDevice.details.type.name
                userTimeZoneOffset shouldBe "-03" // FIXME
                language shouldBe "pt" // FIXME
                screenDimensions?.width shouldBe registeredDevice.details.screenResolution.width
                screenDimensions?.height shouldBe registeredDevice.details.screenResolution.height
                accountTenure shouldBe consent.createdAt.toLocalDate()
            }
        }

        private infix fun OpenFinanceSweepingCashIn.shouldMatchCommand(command: RequestSweepingCashInCommand) {
            id shouldBe command.sweepingCashInId
            walletId shouldBe command.walletId
            amount shouldBe command.amount
            description shouldBe command.description
            requestSource shouldBe command.requestSource
            approvalSource shouldBe command.approvalSource
            externalTransactionId shouldBe command.externalTransactionId
        }

        private infix fun OpenFinanceSweepingCashIn.shouldMatchConsent(consent: SweepingConsent) {
            consentId shouldBe consent.id
            creditorId shouldBe consent.creditorId
        }
    }

    @Nested
    @DisplayName("ao atualizar um cash in")
    inner class UpdateSweepingPaymentTest {

        @ParameterizedTest
        @EnumSource(SweepingCashInStatus::class)
        fun `nao deve atualizar o cash in quando o estado eh igual`(status: SweepingCashInStatus) {
            val sweepingCashIn = setupSweepingCashIn(status)

            val result = sweepingAccountService.updateSweepingCashIn(
                sweepingCashInId = sweepingCashIn.id,
                status = status,
                error = null,
                errorDescription = null,
            )

            result.isRight() shouldBe true

            // jah chama para salvar no setupPayment, por isso exactly 1
            verify(exactly = 1) {
                sweepingCashInRepository.save(any())
            }
        }

        @ParameterizedTest
        @EnumSource(SweepingCashInStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["CREATED", "UNKNOWN"])
        fun `nao deve atualizar o cash in quando o estado atual eh final`(status: SweepingCashInStatus) {
            val sweepingCashIn = setupSweepingCashIn(status)

            val result = sweepingAccountService.updateSweepingCashIn(
                sweepingCashInId = sweepingCashIn.id,
                status = SweepingCashInStatus.UNKNOWN,
                error = null,
                errorDescription = null,
            )

            result.isRight() shouldBe true

            // jah chama para salvar no setupPayment, por isso exactly 1
            verify(exactly = 1) {
                sweepingCashInRepository.save(any())
            }

            val updatedCashIn = sweepingCashInRepository.findOrNull(sweepingCashIn.id)

            updatedCashIn.shouldNotBeNull()
            updatedCashIn.shouldBeEqualToIgnoringFields(sweepingCashIn, sweepingCashIn::createdAt, sweepingCashIn::updatedAt)
        }

        @ParameterizedTest
        @EnumSource(SweepingCashInStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["CREATED"])
        fun `deve atualizar o cash in quando o estado eh diferente`(status: SweepingCashInStatus) {
            val consent = setupConsent()
            val sweepingCashIn = setupSweepingCashIn(status = SweepingCashInStatus.CREATED, consentId = consent.id)

            val result = sweepingAccountService.updateSweepingCashIn(
                sweepingCashInId = sweepingCashIn.id,
                status = status,
                error = null,
                errorDescription = null,
            )

            result.isRight() shouldBe true

            val updatedCashIn = sweepingCashInRepository.findOrNull(sweepingCashIn.id)

            updatedCashIn.shouldNotBeNull()
            updatedCashIn.shouldBeEqualToIgnoringFields(sweepingCashIn, sweepingCashIn::status, sweepingCashIn::createdAt, sweepingCashIn::updatedAt)
            updatedCashIn.status shouldBe status

            if (status != SweepingCashInStatus.FAILED) {
                verify {
                    chatBotMessagePublisher wasNot called
                }
            }
        }

        @DisplayName("se for uma transação iniciada pelo chatbot")
        @Nested
        inner class ChatBotTransactionTest {

            @ParameterizedTest
            @CsvSource(
                "FAILED, FAILED",
                "SUCCESS, SUCCESS",
            )
            fun `deve notificar o chatbot quando terminou de procesar a transferencia inteligente`(status: SweepingCashInStatus, paymentStatus: ChatbotAiTransactionPaymentStatus) {
                val consent = setupConsent()
                val sweepingCashIn = setupSweepingCashIn(status = SweepingCashInStatus.CREATED, requestSource = SweepingCashInSource.CHATBOT, consentId = consent.id)

                val result = sweepingAccountService.updateSweepingCashIn(
                    sweepingCashInId = sweepingCashIn.id,
                    status = status,
                    error = null,
                    errorDescription = null,
                )

                result.isRight() shouldBe true

                verify {
                    chatbotAiTransactionService.updatePaymentStatus(
                        transactionId = sweepingCashIn.externalTransactionId!!.value,
                        mobilePhone = walletFixture.founderAccount.mobilePhone,
                        paymentStatus = paymentStatus,
                    )
                }
            }

            @ParameterizedTest
            @EnumSource(SweepingCashInStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["FAILED", "SUCCESS"])
            fun `nao deve atualizar quando nao consegue notificar o chatbot`(status: SweepingCashInStatus) {
                val sweepingCashIn = setupSweepingCashIn(status = SweepingCashInStatus.CREATED, requestSource = SweepingCashInSource.CHATBOT)

                every {
                    chatbotAiTransactionService.updatePaymentStatus(any(), any(), any())
                } returns ChatbotAiTransactionError.UnknownError("message").left()

                val result = sweepingAccountService.updateSweepingCashIn(
                    sweepingCashInId = sweepingCashIn.id,
                    status = status,
                    error = null,
                    errorDescription = null,
                )

                result.isLeft() shouldBe true
                result.mapLeft {
                    it.shouldBeTypeOf<UpdateSweepingCashInError.GenericError>()
                    it.message shouldBe "UnknownError"
                }

                val updatedCashIn = sweepingCashInRepository.findOrNull(sweepingCashIn.id)

                updatedCashIn.shouldNotBeNull()
                updatedCashIn.shouldBeEqualToIgnoringFields(sweepingCashIn, sweepingCashIn::createdAt, sweepingCashIn::updatedAt)
            }

            @ParameterizedTest
            @EnumSource(SweepingCashInStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["FAILED", "SUCCESS"])
            fun `nao deve notificar o chatbot enquanto nao terminou de procesar a transferencia inteligente`(status: SweepingCashInStatus) {
                val sweepingCashIn = setupSweepingCashIn(status = SweepingCashInStatus.CREATED, requestSource = SweepingCashInSource.CHATBOT)

                val result = sweepingAccountService.updateSweepingCashIn(
                    sweepingCashInId = sweepingCashIn.id,
                    status = status,
                    error = null,
                    errorDescription = null,
                )

                result.isRight() shouldBe true

                verify {
                    chatbotAiTransactionService wasNot called
                }
            }
        }

        @ParameterizedTest
        @CsvSource(
            "WEB_APP,4000,sweepingTransferWebappRetryableError,defaultErrorDescription",
            "WEB_APP,VALOR_ACIMA_LIMITE,sweepingTransferWebappParticipantLimitError,defaultErrorDescription",
            "WEB_APP,SALDO_INSUFICIENTE,sweepingTransferWebappParticipantBalanceError,defaultErrorDescription",
            "WEB_APP,LIMITE_PERIODO_VALOR_EXCEDIDO,sweepingTransferWebappRetryableError,defaultErrorDescription",
            "WEB_APP,LIMITE_PERIODO_VALOR_EXCEDIDO,sweepingTransferWebappInvalidConsentError,Consentimento invalido (status nao e \"authorised\" ou esta expirado).",
            "WEB_APP,LIMITE_PERIODO_QUANTIDADE_EXCEDIDO,sweepingTransferWebappRetryableError,defaultErrorDescription",
            "WEB_APP,LIMITE_PERIODO_QUANTIDADE_EXCEDIDO,sweepingTransferWebappInvalidConsentError,Consentimento invalido (status nao e \"authorised\" ou esta expirado).",
            "CHATBOT,4000,sweepingTransferChatbotRetryableError,defaultErrorDescription",
            "CHATBOT,VALOR_ACIMA_LIMITE,sweepingTransferChatbotParticipantLimitError,defaultErrorDescription",
            "CHATBOT,SALDO_INSUFICIENTE,sweepingTransferChatbotParticipantBalanceError,defaultErrorDescription",
            "CHATBOT,LIMITE_PERIODO_VALOR_EXCEDIDO,sweepingTransferChatbotRetryableError,defaultErrorDescription",
            "CHATBOT,LIMITE_PERIODO_VALOR_EXCEDIDO,sweepingTransferChatbotInvalidConsentError,Consentimento invalido (status nao e \"authorised\" ou esta expirado).",
            "CHATBOT,LIMITE_PERIODO_QUANTIDADE_EXCEDIDO,sweepingTransferChatbotRetryableError,defaultErrorDescription",
            "CHATBOT,LIMITE_PERIODO_QUANTIDADE_EXCEDIDO,sweepingTransferChatbotInvalidConsentError,Consentimento invalido (status nao e \"authorised\" ou esta expirado).",
        )
        fun `deve notificar o usuario via chatbot quando o cash in falhou`(requestSource: SweepingCashInSource, error: String, expectedTemplate: String, errorDescription: String) {
            val status = SweepingCashInStatus.FAILED
            val consent = setupConsent()
            val sweepingCashIn = setupSweepingCashIn(status = SweepingCashInStatus.CREATED, requestSource = requestSource, consentId = consent.id)

            val result = sweepingAccountService.updateSweepingCashIn(
                sweepingCashInId = sweepingCashIn.id,
                status = status,
                error = error,
                errorDescription = errorDescription,
            )

            result.isRight() shouldBe true

            val updatedCashIn = sweepingCashInRepository.findOrNull(sweepingCashIn.id)

            updatedCashIn.shouldNotBeNull()
            updatedCashIn.shouldBeEqualToIgnoringFields(sweepingCashIn, sweepingCashIn::status, sweepingCashIn::createdAt, sweepingCashIn::updatedAt, sweepingCashIn::error, sweepingCashIn::errorDescription)
            updatedCashIn.status shouldBe status
            updatedCashIn.error shouldBe error
            updatedCashIn.errorDescription shouldBe errorDescription

            val accountSlot = slot<Account>()
            val notificationSlot = slot<BillPaymentNotification>()
            verify {
                chatBotMessagePublisher.publishGenericNotification(capture(accountSlot), capture(notificationSlot))
            }
            accountSlot.captured.accountId shouldBe wallet.founder.accountId
            notificationSlot.captured.template.value shouldBe expectedTemplate
        }
    }

    @Nested
    @DisplayName("ao confirmar um cash in")
    inner class ConfirmSweepingCashIn {

        @Test
        fun `deve retornar ItemNotFound quando nao encontrar o sweepingCashIn pelo endToEnd`() {
            val endToEnd = EndToEnd()

            val result = sweepingAccountService.confirmSweepingCashIn(endToEnd)

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeInstanceOf<ConfirmSweepingCashInError.ItemNotFound>()
                it.endToEnd shouldBe endToEnd
            }
        }

        @Test
        fun `deve retornar o id do sweepingCashIn quando encontrar o endToEnd`() {
            val sweepingCashIn = setupSweepingCashIn(SweepingCashInStatus.CREATED)

            val result = sweepingAccountService.confirmSweepingCashIn(sweepingCashIn.endToEnd!!)

            result.isRight() shouldBe true
            result.map {
                it shouldBe sweepingCashIn.id
            }

            sweepingCashInRepository.find(sweepingCashIn.id).status shouldBe SweepingCashInStatus.SUCCESS
        }
    }

    @Nested
    @DisplayName("ao consultar um cash in")
    inner class GetSweepingPaymentTest {
        @ParameterizedTest
        @EnumSource(value = SweepingCashInStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["CREATED", "UNKNOWN"])
        fun `deve retornar o estado salvo quando eh final`(status: SweepingCashInStatus) {
            val sweepingCashIn = setupSweepingCashIn(status)

            val result = sweepingAccountService.getSweepingCashIn(
                sweepingCashInId = sweepingCashIn.id,
            )

            result.isRight() shouldBe true
            result.map {
                it.status shouldBe status
            }

            verify {
                openFinanceAdapter wasNot called
            }
        }

        @ParameterizedTest
        @EnumSource(value = SweepingCashInStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["FAILED", "SUCCESS"])
        fun `deve consultar o open finance quando o estado salvo nao eh final`(status: SweepingCashInStatus) {
            val sweepingCashIn = setupSweepingCashIn(status)

            val response = SweepingCashInStatusResponse(
                status = SweepingCashInStatus.WAITING_SETTLEMENT,
                error = null,
                errorDescription = null,
            )

            every {
                openFinanceAdapter.getSweepingCashInStatus(sweepingCashIn.id)
            } returns response.right()

            val result = sweepingAccountService.getSweepingCashIn(
                sweepingCashInId = sweepingCashIn.id,
            )

            result.isRight() shouldBe true
            result.map {
                it.shouldBeEqualToIgnoringFields(sweepingCashIn, sweepingCashIn::createdAt, sweepingCashIn::updatedAt, sweepingCashIn::status)
                it.status shouldBe response.status
            }

            val updatedCashIn = sweepingCashInRepository.findOrNull(sweepingCashIn.id)

            updatedCashIn.shouldNotBeNull()
            updatedCashIn.shouldBeEqualToIgnoringFields(sweepingCashIn, sweepingCashIn::status, sweepingCashIn::createdAt, sweepingCashIn::updatedAt)
            updatedCashIn.status shouldBe response.status
        }

        @ParameterizedTest
        @CsvSource(
            "4000,sweepingTransferWebappRetryableError",
            "VALOR_ACIMA_LIMITE,sweepingTransferWebappParticipantLimitError",
            "SALDO_INSUFICIENTE,sweepingTransferWebappParticipantBalanceError",
        )
        fun `deve notificar o usuario via chatbot quando o cash in falha`(error: String, expectedTemplate: String) {
            val consent = setupConsent()
            val sweepingCashIn = setupSweepingCashIn(status = SweepingCashInStatus.CREATED, consentId = consent.id)

            every {
                openFinanceAdapter.getSweepingCashInStatus(sweepingCashIn.id)
            } returns SweepingCashInStatusResponse(
                status = SweepingCashInStatus.FAILED,
                error = error,
                errorDescription = "errorDescription",
            ).right()

            val result = sweepingAccountService.getSweepingCashIn(
                sweepingCashInId = sweepingCashIn.id,
            )

            result.isRight() shouldBe true
            result.map {
                it.shouldBeEqualToIgnoringFields(sweepingCashIn, sweepingCashIn::createdAt, sweepingCashIn::updatedAt, sweepingCashIn::status, sweepingCashIn::error, sweepingCashIn::errorDescription)
                it.status shouldBe SweepingCashInStatus.FAILED
                it.error shouldBe error
                it.errorDescription shouldBe "errorDescription"
            }

            val updatedCashIn = sweepingCashInRepository.findOrNull(sweepingCashIn.id)

            updatedCashIn.shouldNotBeNull()
            updatedCashIn.shouldBeEqualToIgnoringFields(sweepingCashIn, sweepingCashIn::createdAt, sweepingCashIn::updatedAt, sweepingCashIn::status, sweepingCashIn::error, sweepingCashIn::errorDescription)
            updatedCashIn.status shouldBe SweepingCashInStatus.FAILED

            val accountSlot = slot<Account>()
            val notificationSlot = slot<BillPaymentNotification>()
            verify {
                chatBotMessagePublisher.publishGenericNotification(capture(accountSlot), capture(notificationSlot))
            }
            accountSlot.captured.accountId shouldBe wallet.founder.accountId
            notificationSlot.captured.template.value shouldBe expectedTemplate
        }
    }

    @Nested
    @DisplayName("ao consultar as últimas transferências")
    inner class LastCahsInsTest {

        @Test
        fun `deve retornar uma lista vazia quando nao existe transferencia nas ultimas 24 horas`() {
            setupSweepingCashIn(createdAt = getZonedDateTime().minusDays(1).minusSeconds(1))

            sweepingAccountService.getLastActiveCashIns(wallet.id).shouldBeEmpty()
        }

        @Test
        fun `deve ignorar o cashIn se nao encontrar o participante`() {
            val consent = setupConsent(participantId = OpenFinanceParticipantId("anotherId"))

            setupSweepingCashIn(createdAt = getZonedDateTime(), consentId = consent.id)

            sweepingAccountService.getLastActiveCashIns(wallet.id).shouldBeEmpty()
        }

        @ParameterizedTest
        @EnumSource(value = SweepingCashInStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["CREATED", "UNKNOWN"])
        fun `quando nao existe transferencia pendente nas ultimas 24 horas deve retornar a ultima transferencia concluida nos ultimos 30 min`(status: SweepingCashInStatus) {
            val consent = setupConsent()

            val cashIn1 = setupSweepingCashIn(createdAt = getZonedDateTime(), consentId = consent.id, status = status)
            val cashIn2 = setupSweepingCashIn(createdAt = getZonedDateTime().minusMinutes(31), consentId = consent.id, status = status)

            sweepingAccountService.getLastActiveCashIns(wallet.id) shouldMatch listOf(cashIn1)
        }

        @ParameterizedTest
        @EnumSource(value = SweepingCashInStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["SUCCESS", "FAILED", "WAITING_SETTLEMENT"])
        fun `deve retornar as transferencias pendentes feitas nas ultimas 24 horas`(status: SweepingCashInStatus) {
            val consent = setupConsent()

            val processedCashIn1 = setupSweepingCashIn(createdAt = getZonedDateTime(), consentId = consent.id, status = SweepingCashInStatus.SUCCESS)
            val processedCashIn2 = setupSweepingCashIn(createdAt = getZonedDateTime(), consentId = consent.id, status = SweepingCashInStatus.FAILED)
            val processedCashIn3 = setupSweepingCashIn(createdAt = getZonedDateTime(), consentId = consent.id, status = SweepingCashInStatus.WAITING_SETTLEMENT)
            val pendingCashIn1 = setupSweepingCashIn(createdAt = getZonedDateTime(), consentId = consent.id, status = status)
            val pendingCashIn2 = setupSweepingCashIn(createdAt = getZonedDateTime(), consentId = consent.id, status = status)

            sweepingAccountService.getLastActiveCashIns(wallet.id) shouldMatch listOf(pendingCashIn1, pendingCashIn2)

            val allSweepingCasInIds = sweepingCashInRepository.findByWalletId(wallet.id, Duration.ofDays(1)).map {
                it.id
            }

            allSweepingCasInIds shouldContainExactlyInAnyOrder listOf(
                processedCashIn1.id,
                processedCashIn2.id,
                processedCashIn3.id,
                pendingCashIn1.id,
                pendingCashIn2.id,
            )
        }

        private infix fun List<ai.friday.billpayment.app.integrations.OFSweepingCashIn>.shouldMatch(cashIns: List<OpenFinanceSweepingCashIn>) {
            size shouldBe cashIns.size
            cashIns.forEach { cashIn ->
                firstOrNull { it.id == cashIn.id } shouldMatch cashIn
            }
        }

        private infix fun ai.friday.billpayment.app.integrations.OFSweepingCashIn?.shouldMatch(cashIn: OpenFinanceSweepingCashIn) {
            shouldNotBeNull()
            id shouldBe cashIn.id
            participant shouldBe sweepingParticipant
            amount shouldBe cashIn.amount
            status shouldBe cashIn.status
            updatedAt.format(dateTimeFormat) shouldBe cashIn.updatedAt.format(dateTimeFormat)
        }
    }

    private fun setupConsent(status: ConsentStatus = ConsentStatus.AWAITING_AUTHORISATION, participantId: OpenFinanceParticipantId = OpenFinanceParticipantId(sweepingParticipant.id)): SweepingConsent {
        val consentId = SweepingConsentId(UUID.randomUUID().toString())
        val consent = SweepingConsent(
            accountId = wallet.founder.accountId,
            id = consentId,
            creditorId = CreditorId("ID_2"),
            walletId = wallet.id,
            participantId = participantId,
            status = status,
            startDateTime = ZonedDateTime.now(),
            expirationDateTime = ZonedDateTime.now().plusYears(2),
            createdAt = ZonedDateTime.now(),
            updatedAt = ZonedDateTime.now(),
            url = URI.create("http://example.com").toURL(),
            configuration = sweepingLimitsConfiguration,
        )

        return sweepingAccountRepository.save(consent)
    }

    private fun setupSweepingCashIn(status: SweepingCashInStatus = SweepingCashInStatus.FAILED, createdAt: ZonedDateTime = ZonedDateTime.now(), consentId: SweepingConsentId = SweepingConsentId("quem"), requestSource: SweepingCashInSource = SweepingCashInSource.WEB_APP): OpenFinanceSweepingCashIn {
        val sweepingCashIn = OpenFinanceSweepingCashIn(
            id = SweepingCashInId(),
            walletId = wallet.id,
            description = "maiestatis",
            consentId = consentId,
            status = status,
            amount = 2386,
            creditorId = CreditorId("tortor"),
            endToEnd = EndToEnd(),
            createdAt = createdAt,
            requestSource = requestSource,
            approvalSource = requestSource,
            externalTransactionId = if (requestSource == SweepingCashInSource.WEB_APP) {
                null
            } else {
                ExternalTransactionId()
            },
        )

        sweepingCashInRepository.save(sweepingCashIn)

        return sweepingCashIn
    }

    companion object {
        @JvmStatic
        fun requestSweepingCashInErrors() = listOf(
            Arguments.of(
                RequestSweepingCashInError.GenericError("mocked"),
                SweepingCashInStatus.UNKNOWN,
            ),
            Arguments.of(
                RequestSweepingCashInError.PaymentNotCreated,
                SweepingCashInStatus.FAILED,
            ),
            Arguments.of(
                RequestSweepingCashInError.LimitError(LimitType.DAILY),
                SweepingCashInStatus.FAILED,
            ),
            Arguments.of(
                RequestSweepingCashInError.ItemNotFound("message"),
                SweepingCashInStatus.FAILED,
            ),
            Arguments.of(
                RequestSweepingCashInError.ActiveConsentNotFound(WalletId()),
                SweepingCashInStatus.FAILED,
            ),
            Arguments.of(
                RequestSweepingCashInError.Conflict,
                SweepingCashInStatus.FAILED,
            ),
        )
    }
}