package ai.friday.billpayment.modules.openfinance.adapters

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.LimitType
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.integrations.RequestSweepingCashInError
import ai.friday.billpayment.app.integrations.SweepingCashInId
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.AutomaticSweepingRiskSignals
import ai.friday.billpayment.modules.openfinance.app.BankAccountBalance
import ai.friday.billpayment.modules.openfinance.app.BankAccountResourceId
import ai.friday.billpayment.modules.openfinance.app.ConsentStatus
import ai.friday.billpayment.modules.openfinance.app.CreditorId
import ai.friday.billpayment.modules.openfinance.app.CustomErrors
import ai.friday.billpayment.modules.openfinance.app.DataConsent
import ai.friday.billpayment.modules.openfinance.app.DataConsentId
import ai.friday.billpayment.modules.openfinance.app.GetSweepingCashInStatusError
import ai.friday.billpayment.modules.openfinance.app.ManualSweepingRiskSignals
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceAdapter
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceAdapterException
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceError
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceSweepingAccountError
import ai.friday.billpayment.modules.openfinance.app.PeriodicLimit
import ai.friday.billpayment.modules.openfinance.app.PeriodicLimits
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentId
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentLink
import ai.friday.billpayment.modules.openfinance.app.SweepingLimits
import ai.friday.billpayment.modules.openfinance.app.SweepingRiskSignals
import ai.friday.billpayment.modules.openfinance.app.SweepingRiskSignalsType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonTypeInfo
import com.fasterxml.jackson.annotation.JsonTypeName
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.net.URI
import java.time.ZonedDateTime
import kotlin.jvm.optionals.getOrNull
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@OpenFinance
class DefaultOpenFinanceAdapter(
    @param:Client(id = "openfinance") private val httpClient: RxHttpClient,
    @Property(name = "integrations.openfinance.clientid") private val clientId: String,
    @Property(name = "integrations.openfinance.clientsecret") private val secretId: String,
) : OpenFinanceAdapter {
    override fun createDataConsent(
        accountId: AccountId,
        document: Document,
        participantId: OpenFinanceParticipantId,
    ): Either<CustomErrors, DataConsent> {
        val logName = "DefaultOpenFinanceAdapter#createDataConsent"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("document", document.value)
            .andAppend("participantId", participantId.value)

        try {
            val uri = "/openFinance/data/consent"
            markers.andAppend("uri", uri)

            val requestBody = CreateDataConsentRequestTO(accountId.value, document.value, participantId = participantId.value)

            val httpRequest =
                HttpRequest
                    .POST(uri, requestBody)
                    .basicAuth(clientId, secretId)
                    .accept(MediaType.APPLICATION_JSON_TYPE)
            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(CreateDataConsentResponseTO::class.java),
                )

            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return DataConsent(
                dataConsentId = DataConsentId(response.dataConsentId),
                participantId = participantId,
                accountId = accountId,
                document = document,
                url = URI.create(response.link).toURL(),
                status = ConsentStatus.AWAITING_AUTHORISATION,
            ).right()
        } catch (e: HttpClientResponseException) {
            logHttpClientResponseException(markers, logName, e)
            return OpenFinanceAdapterException(e).left()
        } catch (e: Exception) {
            logException(markers, e, logName)
            return OpenFinanceError(e).left()
        }
    }

    override fun getBankAccountBalance(dataConsentId: DataConsentId, bankAccountResourceId: BankAccountResourceId): Either<String, BankAccountBalance> {
        val logName = "DefaultOpenFinanceAdapter#getBankAccountBalance"
        val markers = Markers.append("dataConsentId", dataConsentId.value)
            .andAppend("bankAccountId", bankAccountResourceId.value)

        try {
            val uri = "/openFinance/data/consent/${dataConsentId.value}/bankAccount/${bankAccountResourceId.value}/balance"
            markers.andAppend("uri", uri)

            val httpRequest = HttpRequest.GET<String>(uri)
                .basicAuth(clientId, secretId)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.retrieve(httpRequest, Argument.of(BankAccountBalanceTO::class.java))

            val response = call.firstOrError().blockingGet()
            markers.andAppend("response", response)

            return BankAccountBalance(
                availableAmount = response.availableAmount,
                blockedAmount = response.blockedAmount,
                automaticallyInvestedAmount = response.automaticallyInvestedAmount,
                updateDateTime = ZonedDateTime.parse(response.updateDateTime, dateTimeFormat),
            ).right()
        } catch (e: Exception) {
            logException(markers, e, logName)
            return e.message.orEmpty().left()
        }
    }

    override fun createSweepingConsent(wallet: Wallet, paymentMethod: InternalBankAccount, participantId: OpenFinanceParticipantId, sweepingLimits: SweepingLimits): Either<OpenFinanceSweepingAccountError, SweepingConsentLink> {
        val logName = "DefaultOpenFinanceAdapter#createSweepingConsent"
        val uri = "/openFinance/sweeping/consent"
        val request = CreateSweepingConsentRequestTO(
            userAccountId = wallet.founder.accountId.value,
            user = UserConsent(
                taxId = wallet.founder.document,
                name = wallet.founder.name,
            ),
            participantId = participantId.value,
            creditorConsentTO = CreditorConsentTO(
                type = "BANK_ACCOUNT",
                name = "Banco Arbi S.A.",
                ispb = "********", // TODO pegar em integrations.arbi.codInstituicaoPagador
                number = paymentMethod.buildFullAccountNumber(),
                issuer = paymentMethod.routingNo.toString(),
                accountType = "CACC",
            ),
            sweepingLimits = sweepingLimits.toTO(),
        )
        val httpRequest = HttpRequest
            .POST(uri, request)
            .basicAuth(clientId, secretId)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val markers = Markers.append("WalletId", wallet.id.value)
            .andAppend("uri", uri)
            .andAppend("request", request)

        try {
            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(CreateSweepingConsentResponseTO::class.java),
                )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return SweepingConsentLink(
                sweepingConsentId = SweepingConsentId(value = response.sweepingAccountId),
                creditorId = CreditorId(value = response.creditorId),
                consentUrl = URI.create(response.consentUrl).toURL(),
                configuration = response.configuration?.toSweepingLimits() ?: sweepingLimits,
                startDateTime = response.startDateTime?.let { BrazilZonedDateTimeSupplier.parseToZonedDateTime(it) },
                expirationDateTime = response.expirationDateTime?.let { BrazilZonedDateTimeSupplier.parseToZonedDateTime(it) },
                participantId = participantId,
            ).right()
        } catch (e: HttpClientResponseException) {
            logHttpClientResponseException(markers, logName, e)
            return OpenFinanceSweepingAccountError.ConsentCreationError(e.message.orEmpty()).left()
        } catch (e: Exception) {
            logException(markers, e, logName)
            return OpenFinanceSweepingAccountError.ConsentCreationError(e.message.orEmpty()).left()
        }
    }

    override fun revokeSweepingConsent(consentId: SweepingConsentId): Either<Exception, Unit> {
        val markers = Markers.append("sweepingConsentId", consentId.value)
        val logName = "DefaultOpenFinanceAdapter#revokeSweepingConsent"
        val uri = "/openFinance/sweeping/consent/${consentId.value}"
        val httpRequest = HttpRequest
            .DELETE<Unit>(uri)
            .basicAuth(clientId, secretId)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        try {
            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(Unit::class.java),
                )
            call.firstOrError().blockingGet()
            logger.info(markers, logName)
            return Unit.right()
        } catch (e: HttpClientResponseException) {
            logHttpClientResponseException(markers, logName, e)
            return e.left()
        } catch (e: Exception) {
            logException(markers, e, logName)
            return e.left()
        }
    }

    override fun revokeDataConsent(dataConsentId: DataConsentId): Either<Exception, Unit> {
        val markers = Markers.append("dataConsentId", dataConsentId.value)
        val logName = "DefaultOpenFinanceAdapter#revokeDataConsent"
        val uri = "/openFinance/data/consent/${dataConsentId.value}"
        val httpRequest = HttpRequest
            .DELETE<Unit>(uri)
            .basicAuth(clientId, secretId)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        try {
            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(Unit::class.java),
                )
            call.firstOrError().blockingGet()
            logger.info(markers, logName)
            return Unit.right()
        } catch (e: HttpClientResponseException) {
            logHttpClientResponseException(markers, logName, e)
            return e.left()
        } catch (e: Exception) {
            logException(markers, e, logName)
            return e.left()
        }
    }

    override fun requestSweepingCashIn(sweepingCashInId: SweepingCashInId, consentId: SweepingConsentId, amount: Long, description: String, creditorId: CreditorId, riskSignals: SweepingRiskSignals?): Either<RequestSweepingCashInError, EndToEnd?> {
        val logName = "DefaultOpenFinanceAdapter#requestSweepingCashIn"
        val uri = "/openFinance/sweeping/payment"
        val body = SweepingPaymentRequestTO(
            requestId = sweepingCashInId.value,
            consentId = consentId.value,
            amount = amount,
            description = description,
            creditorId = creditorId.value,
            riskSignals = riskSignals?.toSweepingRiskSignalsTO(),
        )

        val markers = Markers.append("uri", uri)
            .andAppend("request", body)

        val httpRequest = HttpRequest
            .POST(uri, body)
            .basicAuth(clientId, secretId)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        try {
            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(SweepingPaymentResponseTO::class.java),
                    Argument.of(ResponseTO::class.java),
                )
            val response = call.firstOrError().blockingGet()
            logger.info(markers.andAppend("response", response), logName)
            return response.endToEnd?.let { EndToEnd(it) }.right()
        } catch (e: HttpClientResponseException) {
            val responseTO = e.response.getBody(ResponseTO::class.java).getOrNull()
            logHttpClientResponseException(markers, logName, e, responseTO)
            return when (responseTO?.code) {
                "4002" -> RequestSweepingCashInError.LimitError(LimitType.DAILY).left()
                "4003" -> RequestSweepingCashInError.LimitError(LimitType.WEEKLY).left()
                "4004" -> RequestSweepingCashInError.LimitError(LimitType.MONTHLY).left()
                "4005" -> RequestSweepingCashInError.LimitError(LimitType.YEARLY).left()
                "4006" -> RequestSweepingCashInError.LimitError(LimitType.GLOBAL).left()
                "4007" -> RequestSweepingCashInError.LimitError(LimitType.TRANSACTION).left()
                "4008" -> RequestSweepingCashInError.LimitError(LimitType.UNKNOWN).left()
                "4100" -> RequestSweepingCashInError.PaymentNotCreated.left()
                else -> RequestSweepingCashInError.GenericError("${e.javaClass.simpleName}: ${e.message}").left()
            }
        } catch (e: Exception) {
            logException(markers, e, logName)
            return RequestSweepingCashInError.GenericError("${e.javaClass.simpleName}: ${e.message}").left()
        }
    }

    override fun getSweepingCashInStatus(sweepingCashInId: SweepingCashInId): Either<GetSweepingCashInStatusError, SweepingCashInStatusResponse> {
        val logName = "DefaultOpenFinanceAdapter#getSweepingCashInStatus"
        val markers = Markers.append("sweepingCashInRequestId", sweepingCashInId.value)

        try {
            val uri = "/openFinance/sweeping/payment/${sweepingCashInId.value}/"
            markers.andAppend("uri", uri)

            val httpRequest = HttpRequest.GET<String>(uri)
                .basicAuth(clientId, secretId)
                .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.retrieve(httpRequest, Argument.of(SweepingPaymentStatusResponseTO::class.java))

            val response = call.firstOrError().blockingGet()
            markers.andAppend("response", response)

            return SweepingCashInStatusResponse(
                status = response.status.openFinanceStatus,
                error = response.error,
                errorDescription = response.errorDescription,
            ).right()
        } catch (e: Exception) {
            logException(markers, e, logName)
            return GetSweepingCashInStatusError.GenericError(e.message.orEmpty()).left()
        }
    }

    private fun logHttpClientResponseException(markers: LogstashMarker, logName: String, e: HttpClientResponseException, responseTO: ResponseTO? = null) {
        val responseBodyOrTO = responseTO ?: e.response.getBody(String::class.java).getOrNull()
        logger.error(
            markers.andAppend("httpStatus", e.status)
                .andAppend("responseBody", responseBodyOrTO),
            logName,
            e,
        )
    }

    private fun logException(markers: LogstashMarker, e: Exception, logName: String) {
        logger.error(
            markers.andAppend("error", e.localizedMessage),
            logName,
            e,
        )
    }

    companion object {
        private val logger = LoggerFactory.getLogger(DefaultOpenFinanceAdapter::class.java)
    }
}

private fun SweepingRiskSignals.toSweepingRiskSignalsTO() = when (this) {
    is AutomaticSweepingRiskSignals -> AutomaticPaymentRiskSignalsTO(
        lastLoginDateTime = lastLoginDateTime.format(dateTimeFormat),
        pixKeyRegistrationDateTime = pixKeyRegistrationDateTime?.format(dateTimeFormat),
    )
    is ManualSweepingRiskSignals -> ManualPaymentRiskSignalsTO(
        deviceId = deviceId,
        isRootedDevice = isRootedDevice,
        screenBrightness = screenBrightness,
        elapsedTimeSinceBoot = elapsedTimeSinceBoot?.toMillis(),
        osVersion = osVersion,
        userTimeZoneOffset = userTimeZoneOffset,
        language = language,
        screenDimensions = screenDimensions?.let {
            ScreenDimensionsTO(
                width = it.width,
                height = it.height,
            )
        },
    )
}

private fun SweepingLimits.toTO(): SweepingConsentLimitsTO {
    return SweepingConsentLimitsTO(
        transactionLimit = this.transactionLimit,
        totalAllowedAmount = this.totalAllowedAmount,
        periodicLimits = this.periodicLimits.toTO(),
    )
}

private fun PeriodicLimits.toTO(): PeriodicLimitsTO {
    return PeriodicLimitsTO(
        day = this.day.toTO(),
        week = this.week.toTO(),
        month = this.month.toTO(),
        year = this.year.toTO(),
    )
}

private fun PeriodicLimit.toTO(): PeriodicLimitTO {
    return PeriodicLimitTO(
        quantityLimit = this.quantityLimit,
        transactionLimit = this.transactionLimit,
    )
}

private fun SweepingConsentLimitsTO.toSweepingLimits() = SweepingLimits(
    totalAllowedAmount = this.totalAllowedAmount,
    transactionLimit = this.transactionLimit,
    periodicLimits = this.periodicLimits.toPeriodicLimits(),
)

private fun PeriodicLimitsTO.toPeriodicLimits(): PeriodicLimits {
    return PeriodicLimits(
        day = this.day.toPeriodicLimit(),
        week = this.week.toPeriodicLimit(),
        month = this.month.toPeriodicLimit(),
        year = this.year.toPeriodicLimit(),
    )
}

private fun PeriodicLimitTO.toPeriodicLimit(): PeriodicLimit {
    return PeriodicLimit(
        quantityLimit = this.quantityLimit,
        transactionLimit = this.transactionLimit,
    )
}

data class CreateDataConsentRequestTO(
    val userAccountId: String,
    val document: String,
    val participantId: String,
)

data class CreateDataConsentResponseTO(
    val dataConsentId: String,
    val link: String,
)

data class CreateSweepingConsentRequestTO(
    val userAccountId: String,
    val user: UserConsent,
    val businessEntity: UserConsent? = null,
    val participantId: String,
    val creditorConsentTO: CreditorConsentTO,
    val sweepingLimits: SweepingConsentLimitsTO,
)

data class CreditorConsentTO(
    val type: String,
    val name: String,
    val ispb: String,
    val issuer: String,
    val number: String,
    val accountType: String,
)

data class UserConsent(
    val taxId: String,
    val name: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class CreateSweepingConsentResponseTO(
    val sweepingAccountId: String,
    val creditorId: String,
    val consentUrl: String,
    val expirationDateTime: String?,
    val startDateTime: String?,
    val configuration: SweepingConsentLimitsTO? = null,
)

data class SweepingConsentLimitsTO(
    val totalAllowedAmount: Long,
    val transactionLimit: Long,
    val periodicLimits: PeriodicLimitsTO,
)

data class PeriodicLimitsTO(
    val day: PeriodicLimitTO,
    val week: PeriodicLimitTO,
    val month: PeriodicLimitTO,
    val year: PeriodicLimitTO,
)

data class PeriodicLimitTO(
    val quantityLimit: Long,
    val transactionLimit: Long,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class SweepingPaymentResponseTO(
    val requestId: String,
    val endToEnd: String?,
)

data class SweepingPaymentRequestTO(
    val requestId: String,
    val consentId: String,
    val creditorId: String,
    val amount: Long,
    val description: String,
    val riskSignals: SweepingPaymentRiskSignalsTO?,
)

@JsonTypeInfo(
    include = JsonTypeInfo.As.PROPERTY,
    use = JsonTypeInfo.Id.NAME,
    property = "type",
    visible = true,
)
sealed interface SweepingPaymentRiskSignalsTO {
    val type: SweepingRiskSignalsType
}

@JsonTypeName("AUTOMATIC")
data class AutomaticPaymentRiskSignalsTO(
    val lastLoginDateTime: String,
    val pixKeyRegistrationDateTime: String?,
) : SweepingPaymentRiskSignalsTO {
    override val type = SweepingRiskSignalsType.AUTOMATIC
}

@JsonTypeName("MANUAL")
data class ManualPaymentRiskSignalsTO(
    val deviceId: String,
    val isRootedDevice: Boolean?,
    val screenBrightness: Number?,
    val elapsedTimeSinceBoot: Long?,
    val osVersion: String?,
    val userTimeZoneOffset: String?,
    val language: String?,
    val screenDimensions: ScreenDimensionsTO?,
) : SweepingPaymentRiskSignalsTO {
    override val type = SweepingRiskSignalsType.MANUAL
}

data class ScreenDimensionsTO(
    val height: Int,
    val width: Int,
)

data class ResponseTO(
    val code: String,
    val message: String,
)

data class SweepingPaymentStatusResponseTO(
    val status: SweepingPaymentStatus,
    val error: String? = null,
    val errorDescription: String? = null,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class BankAccountBalanceTO(
    val availableAmount: Long,
    val blockedAmount: Long,
    val automaticallyInvestedAmount: Long,
    val updateDateTime: String,
)