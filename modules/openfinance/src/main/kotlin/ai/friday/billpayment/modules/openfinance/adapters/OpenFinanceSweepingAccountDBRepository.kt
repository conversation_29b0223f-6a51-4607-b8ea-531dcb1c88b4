package ai.friday.billpayment.modules.openfinance.adapters

import ai.friday.billpayment.adapters.dynamodb.AbstractAsyncDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ConditionalType
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_TABLE_NAME
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.OpenFinanceParticipantId
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.BankAccountNumberId
import ai.friday.billpayment.modules.openfinance.app.ConsentStatus
import ai.friday.billpayment.modules.openfinance.app.CreditorId
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceResourceType
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceSweepingAccountRepository
import ai.friday.billpayment.modules.openfinance.app.SweepingConsent
import ai.friday.billpayment.modules.openfinance.app.SweepingConsentId
import ai.friday.billpayment.modules.openfinance.app.SweepingDebtor
import ai.friday.billpayment.modules.openfinance.app.SweepingLimits
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import jakarta.inject.Singleton
import java.net.URI
import java.time.ZoneId
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import kotlinx.coroutines.flow.toList
import kotlinx.coroutines.reactive.asFlow
import kotlinx.coroutines.runBlocking
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Singleton
class OpenFinanceSweepingAccountDynamoDAO(
    cli: DynamoDbEnhancedAsyncClient,
) : AbstractAsyncDynamoDAO<OpenFinanceSweepingAccountEntity>(cli) {
    override fun args() = OPEN_FINANCE_DATA_TABLE_NAME to OpenFinanceSweepingAccountEntity::class.java
}

private const val INDEX2_KEY = "SWEEPING_ACCOUNT"

@OpenFinance
class OpenFinanceSweepingAccountDBRepository(
    private val client: OpenFinanceSweepingAccountDynamoDAO,
) : OpenFinanceSweepingAccountRepository {
    override fun save(account: SweepingConsent): SweepingConsent {
        val entity =
            OpenFinanceSweepingAccountEntity().apply {
                primaryKey = account.id.value
                scanKey = OpenFinanceResourceType.SWEEPING_ACCOUNT.name + "#${account.accountId.value}"
                gSIndex1PartitionKey = account.walletId.value
                gSIndex1RangeKey = OpenFinanceResourceType.SWEEPING_ACCOUNT.name
                gSIndex2PartitionKey = INDEX2_KEY
                gSIndex2RangeKey = account.accountId.value
                accountId = account.accountId.value
                resourceType = OpenFinanceResourceType.SWEEPING_ACCOUNT
                createdAt = account.createdAt.format(DateTimeFormatter.ISO_INSTANT)
                updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_INSTANT)
                creditorId = account.creditorId.value
                sweepingAccountId = account.id.value
                walletId = account.walletId.value
                participant = account.participantId.value
                status = account.status
                startDateTime = account.startDateTime?.format(DateTimeFormatter.ISO_INSTANT)
                expirationDateTime = account.expirationDateTime?.format(DateTimeFormatter.ISO_INSTANT)
                url = account.url.toExternalForm()
                configuration = getObjectMapper().writeValueAsString(account.configuration)
                debtor = account.debtor?.let { debtor -> getObjectMapper().writeValueAsString(debtor.toEntity()) }
            }
        client.save(entity).block()

        return entity.toDomain()
    }

    private fun SweepingDebtor.toEntity() = SweepingDebtorEntity(
        name = null,
        ispb = ispb,
        issuer = routingNumber,
        number = accountNumber,
        accountType = accountType,
        bankName = bankName,
        bankAccountNumberId = bankAccountNumberId?.value,
    )

    override fun findByWalletId(walletId: WalletId): List<SweepingConsent> {
        return runBlocking {
            client.findByPrimaryKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex1,
                partitionKey = walletId.value,
                sortKey = OpenFinanceResourceType.SWEEPING_ACCOUNT.name,
                conditionalType = ConditionalType.EQUALS,
            ).map {
                it.toDomain()
            }.asFlow().toList(mutableListOf<SweepingConsent>())
        }
    }

    override fun findBySweepingAccountId(sweepingConsentId: SweepingConsentId): SweepingConsent {
        return client.listByPartitionKey(
            sweepingConsentId.value,
        ).map {
            it.toDomain()
        }.blockFirst() ?: throw ItemNotFoundException("Sweeping account not found")
    }

    override fun findAll(): List<SweepingConsent> {
        return runBlocking {
            client.findByPartitionKeyOnIndex(GlobalSecondaryIndexNames.GSIndex2, INDEX2_KEY).map {
                it.toDomain()
            }.asFlow().toList(mutableListOf<SweepingConsent>())
        }
    }

    private fun OpenFinanceSweepingAccountEntity.toDomain() = SweepingConsent(
        accountId = AccountId(accountId),
        id = SweepingConsentId(sweepingAccountId),
        creditorId = CreditorId(creditorId),
        walletId = WalletId(walletId),
        participantId = OpenFinanceParticipantId(participant),
        status = status,
        startDateTime = startDateTime?.let { startDateTime -> ZonedDateTime.parse(startDateTime, DateTimeFormatter.ISO_INSTANT.withZone(ZoneId.of("UTC"))) },
        expirationDateTime = expirationDateTime?.let { expirationDateTime -> ZonedDateTime.parse(expirationDateTime, DateTimeFormatter.ISO_INSTANT.withZone(ZoneId.of("UTC"))) },
        createdAt = ZonedDateTime.parse(createdAt, DateTimeFormatter.ISO_INSTANT.withZone(ZoneId.of("UTC"))),
        updatedAt = ZonedDateTime.parse(updatedAt, DateTimeFormatter.ISO_INSTANT.withZone(ZoneId.of("UTC"))),
        url = URI.create(url).toURL(),
        configuration = parseObjectFrom<SweepingLimits>(configuration),
        debtor = debtor?.let { debtor -> parseObjectFrom<SweepingDebtorEntity>(debtor).toDomain() },
    )
}

data class SweepingDebtorEntity(
    val name: String?,
    val ispb: String?,
    val issuer: String?,
    val number: String?,
    val accountType: String?,
    val bankName: String?,
    val bankAccountNumberId: String?,
) {
    fun toDomain() = SweepingDebtor(
        ispb = ispb.orEmpty(),
        routingNumber = issuer.orEmpty(),
        accountNumber = number.orEmpty(),
        accountType = accountType.orEmpty(),
        bankName = bankName,
        bankAccountNumberId = bankAccountNumberId?.let { BankAccountNumberId(it) },
    )
}

@DynamoDbBean
class OpenFinanceSweepingAccountEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_PARTITION_KEY)
    lateinit var primaryKey: String // consentId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // accountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    lateinit var gSIndex1RangeKey: String // resourceType

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2PartitionKey")
    lateinit var gSIndex2PartitionKey: String // SWEEPING_ACCOUNT

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex2"])
    @get:DynamoDbAttribute(value = "GSIndex2RangeKey")
    lateinit var gSIndex2RangeKey: String // accountId

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "SweepingAccountId")
    lateinit var sweepingAccountId: String

    @get:DynamoDbAttribute(value = "CreditorId")
    lateinit var creditorId: String

    @get:DynamoDbAttribute(value = "WalletId")
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "ResourceType")
    lateinit var resourceType: OpenFinanceResourceType

    @get:DynamoDbAttribute(value = "Participant")
    lateinit var participant: String

    @get:DynamoDbAttribute(value = "Status")
    lateinit var status: ConsentStatus

    @get:DynamoDbAttribute(value = "StartDateTime")
    var startDateTime: String? = null

    @get:DynamoDbAttribute(value = "ExpirationDateTime")
    var expirationDateTime: String? = null

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String

    @get:DynamoDbAttribute(value = "Url")
    lateinit var url: String

    @get:DynamoDbAttribute(value = "Configuration")
    lateinit var configuration: String

    @get:DynamoDbAttribute(value = "Debtor")
    var debtor: String? = null
}