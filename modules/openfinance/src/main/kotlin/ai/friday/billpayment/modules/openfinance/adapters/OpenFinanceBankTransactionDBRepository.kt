package ai.friday.billpayment.modules.openfinance.adapters

import ai.friday.billpayment.adapters.dynamodb.AbstractAsyncDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ConditionalType
import ai.friday.billpayment.adapters.dynamodb.GlobalSecondaryIndexNames
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.OPEN_FINANCE_DATA_TABLE_NAME
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankTransaction
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceBankTransactionRepository
import ai.friday.billpayment.modules.openfinance.app.OpenFinanceResourceType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import java.time.LocalDate
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@OpenFinance
class OpenFinanceTransactionDynamoDAO(
    cli: DynamoDbEnhancedAsyncClient,
) : AbstractAsyncDynamoDAO<OpenFinanceTransactionEntity>(cli) {
    override fun args() = OPEN_FINANCE_DATA_TABLE_NAME to OpenFinanceTransactionEntity::class.java
}

@OpenFinance
class OpenFinanceBankTransactionDBRepository(
    private val client: OpenFinanceTransactionDynamoDAO,
) : OpenFinanceBankTransactionRepository {
    override fun save(transaction: OpenFinanceBankTransaction) {
        val entity =
            OpenFinanceTransactionEntity().apply {
                primaryKey = transaction.transactionId
                scanKey = OpenFinanceResourceType.BANK_TRANSACTION.name
                gSIndex1PartitionKey = transaction.userAccountId
                gSIndex1RangeKey = "${OpenFinanceResourceType.BANK_TRANSACTION.name}#${transaction.transactionDate}"
                accountId = transaction.userAccountId
                bankAccountId = transaction.bankAccountId
                transactionId = transaction.transactionId
                transactionDate = transaction.transactionDate
                data = transaction.data
                resourceType = OpenFinanceResourceType.BANK_TRANSACTION
                createdAt = transaction.createdAt ?: getZonedDateTime().format(dateFormat)
                updatedAt = transaction.updatedAt ?: getZonedDateTime().format(dateFormat)
            }
        client.save(entity).block()
    }

    override fun find(transactionId: String): Mono<OpenFinanceBankTransaction> =
        client.findByPrimaryKey(transactionId, OpenFinanceResourceType.BANK_TRANSACTION.name).map {
            OpenFinanceBankTransaction(
                userAccountId = it.accountId,
                bankAccountId = it.bankAccountId,
                transactionId = it.transactionId,
                transactionDate = it.transactionDate,
                createdAt = it.createdAt,
                updatedAt = it.updatedAt,
                data = it.data,
            )
        }

    override fun findByAccountId(accountId: AccountId): Flux<OpenFinanceBankTransaction> =
        client
            .findByPrimaryKeyOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex1,
                partitionKey = accountId.value,
                sortKey = OpenFinanceResourceType.BANK_TRANSACTION.name,
                conditionalType = ConditionalType.BEGINS_WITH,
            ).map { transaction ->
                OpenFinanceBankTransaction(
                    userAccountId = transaction.accountId,
                    bankAccountId = transaction.bankAccountId,
                    transactionId = transaction.transactionId,
                    transactionDate = transaction.transactionDate,
                    createdAt = transaction.createdAt,
                    updatedAt = transaction.updatedAt,
                    data = transaction.data,
                )
            }

    // from is inclusive and to is exclusive
    override fun findByAccountIdAndDate(
        accountId: AccountId,
        from: LocalDate,
        to: LocalDate,
    ): Flux<OpenFinanceBankTransaction> =
        client
            .findByPrimaryKeyAndBetweenSortKeysOnIndex(
                index = GlobalSecondaryIndexNames.GSIndex1,
                partitionKey = accountId.value,
                sortKeyFrom = "${OpenFinanceResourceType.BANK_TRANSACTION.name}#${from.format(dateFormat)}",
                sortKeyTo = "${OpenFinanceResourceType.BANK_TRANSACTION.name}#${to.format(dateFormat)}",
            ).map { transaction ->
                OpenFinanceBankTransaction(
                    userAccountId = transaction.accountId,
                    bankAccountId = transaction.bankAccountId,
                    transactionId = transaction.transactionId,
                    transactionDate = transaction.transactionDate,
                    createdAt = transaction.createdAt,
                    updatedAt = transaction.updatedAt,
                    data = transaction.data,
                )
            }
}

@DynamoDbBean
class OpenFinanceTransactionEntity {
    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_PARTITION_KEY)
    lateinit var primaryKey: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = OPEN_FINANCE_DATA_RANGE_KEY)
    lateinit var scanKey: String

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PartitionKey")
    lateinit var gSIndex1PartitionKey: String // accountId

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1RangeKey")
    lateinit var gSIndex1RangeKey: String // resourceType#transactionDate

    @get:DynamoDbAttribute(value = "AccountId")
    lateinit var accountId: String

    @get:DynamoDbAttribute(value = "ParentId")
    lateinit var bankAccountId: String

    @get:DynamoDbAttribute(value = "ResourceId")
    lateinit var transactionId: String

    @get:DynamoDbAttribute(value = "ResourceType")
    lateinit var resourceType: OpenFinanceResourceType

    @get:DynamoDbAttribute(value = "Date")
    lateinit var transactionDate: String

    @get:DynamoDbAttribute(value = "EncryptedData")
    lateinit var data: String

    @get:DynamoDbAttribute(value = "CreatedAt")
    lateinit var createdAt: String

    @get:DynamoDbAttribute(value = "UpdatedAt")
    lateinit var updatedAt: String
}