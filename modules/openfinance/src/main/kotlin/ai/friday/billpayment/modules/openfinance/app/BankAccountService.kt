package ai.friday.billpayment.modules.openfinance.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.modules.openfinance.OpenFinance
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import jakarta.inject.Named
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

const val bankAccountNumberLockProvider = "bank-account-number"

@OpenFinance
class BankAccountService(
    private val consentService: ConsentService,
    private val openFinanceAdapter: OpenFinanceAdapter,
    private val bankAccountNumberRepository: OpenFinanceBankAccountNumberRepository,
    @Named(bankAccountNumberLockProvider) private val lockProvider: InternalLock,
) {
    private val logger = LoggerFactory.getLogger(BankAccountService::class.java)

    fun registerBankAccountNumber(accountId: AccountId, data: BankAccountNumberData): Either<AlreadyLockedError, OpenFinanceBankAccountNumber> {
        val lock = lockProvider.acquireLock("${accountId.value}#${data.ispb}")
            ?: return AlreadyLockedError(accountId, data.ispb).left()

        return try {
            bankAccountNumberRepository.findByAccountIdAndIspbAndRoutingNumber(
                accountId = accountId,
                ispb = data.ispb,
                routingNumber = data.routingNumber,
            ).firstOrNull {
                data.matches(it.data)
            }?.let { existingNumber ->
                if (existingNumber.data.accountDv == null && data.accountDv != null) {
                    bankAccountNumberRepository.save(existingNumber.copy(data = data))
                } else {
                    existingNumber
                }
            } ?: bankAccountNumberRepository.save(
                OpenFinanceBankAccountNumber(
                    id = BankAccountNumberId(),
                    accountId = accountId,
                    data = data,
                    createdAt = getZonedDateTime(),
                    updatedAt = getZonedDateTime(),
                ),
            )
        } finally {
            lock.unlock()
        }.right()
    }

    fun getBalance(accountId: AccountId, bankAccountNumberId: BankAccountNumberId): Either<String, BankAccountBalance> {
        val logName = "BankAccountService#getBalance"
        val markers = Markers.append("accountId", accountId.value)
            .andAppend("bankAccountDataId", bankAccountNumberId.value)

        val activeConsents = consentService.getActiveBankAccountDataConsents(accountId)
        markers.andAppend("activeConsents", activeConsents)

        val consent = activeConsents.firstOrNull { it.bankAccountData.id == bankAccountNumberId }
        markers.andAppend("consent", consent)

        if (consent == null) {
            logger.warn(markers, logName)
            return "consent not found for ${bankAccountNumberId.value}".left()
        }

        if (consent.bankAccountData !is BankAccount2BankAccountDataAdapter) {
            logger.warn(markers, logName)
            return "invalid bankAccountData type ${consent.bankAccountData::class.java.simpleName}".left()
        }

        return openFinanceAdapter.getBankAccountBalance(
            dataConsentId = consent.id,
            bankAccountResourceId = consent.bankAccountData.bankAccount.bankAccountResourceId,
        )
    }
}

data class BankAccountBalance(
    val availableAmount: Long,
    val blockedAmount: Long,
    val automaticallyInvestedAmount: Long,
    val updateDateTime: ZonedDateTime,
)

data class OpenFinanceBankAccountNumber(
    val id: BankAccountNumberId,
    val accountId: AccountId,
    val data: BankAccountNumberData,
    val createdAt: ZonedDateTime,
    val updatedAt: ZonedDateTime,
)

data class BankAccountNumberData(
    val ispb: String,
    val bankName: String,
    val routingNumber: String,
    val accountNumber: String,
    val accountDv: String?,
) {
    private val sanitizedRoutingNumber = routingNumber.removeLeadingZeros()
    private val sanitizedAccountNumber = accountNumber.removeLeadingZeros()
    private val fullAccountNumber = sanitizedAccountNumber + accountDv.orEmpty()

    fun matches(other: BankAccountNumberData): Boolean {
        return ispb == other.ispb &&
            sanitizedRoutingNumber == other.sanitizedRoutingNumber &&
            when {
                accountDv != null && other.accountDv != null -> fullAccountNumber == other.fullAccountNumber
                accountDv == null && other.accountDv == null -> fullAccountNumber == other.fullAccountNumber || fullAccountNumber.dropLast(1) == other.fullAccountNumber || fullAccountNumber == other.fullAccountNumber.dropLast(1)
                accountDv != null -> fullAccountNumber == other.fullAccountNumber || sanitizedAccountNumber == other.fullAccountNumber
                else -> fullAccountNumber == other.fullAccountNumber || fullAccountNumber == other.sanitizedAccountNumber
            }
    }

    override fun toString(): String = "$ispb#$sanitizedRoutingNumber#$sanitizedAccountNumber#${accountDv.orEmpty()}"
}

fun String.removeLeadingZeros() = dropWhile { it == '0' }

data class AlreadyLockedError(
    val accountId: AccountId,
    val ispb: String,
)