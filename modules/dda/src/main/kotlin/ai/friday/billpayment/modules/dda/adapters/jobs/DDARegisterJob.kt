package ai.friday.billpayment.modules.dda.adapters.jobs

import ai.friday.billpayment.app.feature.FeaturesRepository
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.job.AbstractJob
import ai.friday.billpayment.modules.dda.app.DDAService
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requires(notEnv = ["staging"])
open class DDARegisterJob(
    private val ddaService: DDAService,
    private val featuresRepository: FeaturesRepository,
    private val featureConfiguration: FeatureConfiguration,
    @Property(name = "schedules.registerUsersDda.cron") cron: String,
) : AbstractJob(
    cron = cron,
    lockAtLeastFor = "1m",
    lockAtMostFor = "10m",
) {
    override fun execute() {
        if (featuresRepository.getAll().maintenanceMode) {
            LOG.info(Markers.append("jobExcecutionSkipped", "maintenanceMode"), "ExecuteRegisterDDAJob")
            return
        }

        try {
            when (featureConfiguration.ddaBatchAddAsync) {
                true -> ddaService.batchRegisterAsync()
                false -> ddaService.batchRegister()
            }
        } catch (e: Exception) {
            LOG.error("ExecuteRegisterDDAJob", e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DDARegisterJob::class.java)
    }
}
