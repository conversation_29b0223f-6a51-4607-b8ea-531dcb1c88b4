package ai.friday.billpayment.modules.dda.app

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime

@FridayMePoupe
class CreateDDARegister(
    private val ddaRepository: DDARepository,
    private val ddaConfig: DDAConfig,
) {

    fun register(accountId: AccountId, document: String, ddaStatus: DDAStatus? = null, ddaProvider: DDAProvider? = null): DDAStatus {
        val readRegister = ddaRepository.find(accountId)

        val currentRegister = if (readRegister == null) {
            val ddaRegister = DDARegister(
                accountId = accountId,
                document = document,
                created = getZonedDateTime(),
                status = ddaStatus ?: DDAStatus.PENDING,
                provider = ddaProvider ?: ddaConfig.provider,
            )
            ddaRepository.save(ddaRegister)
        } else {
            readRegister.copy(status = ddaStatus ?: DDAStatus.PENDING).also {
                ddaRepository.save(it)
            }
        }

        val oldAccountRegister = ddaRepository.findOlderRegister(Document(document), accountId)

        if (oldAccountRegister != null) {
            ddaRepository.save(oldAccountRegister.copy(status = DDAStatus.CLOSED))
        }

        return currentRegister.status
    }
}
