package ai.friday.billpayment.modules.dda.adapters.arbi

import ai.friday.billpayment.app.bill.BarCode

// Data classes for Arbi DDA integration
data class DDATO(val dda: DDA)

data class DDA(
    val inscricaoParceiro: String,
    val tokenUsuario: String,
    val contaTitular: String,
    val inscricao: String,
    val tipoPessoa: String,
    val tipoRetornoTitulos: String,
    val dataVctoTitulo: String,
)

data class DDAAgregadoTO(val ddaagregado: DDAAgregado)

data class DDAAgregado(
    val inscricaoParceiro: String,
    val tokenUsuario: String,
    val inscricaotitular: String,
    val inscricaoagregado: String,
    val tipomanutencao: String,
    val tipopessoaagregado: String? = null,
)

data class ArbiResponseTO(
    val idStatus: Int,
    val descricaoStatus: String,
    val resultado: String?,
    val idRequisicaoArbi: String? = null,
)

data class DdaResultTO(
    val cpfCnpj: String,
    val descricao: String,
    val status: String,
)

// Enums
private enum class MaintenanceType(val code: String) {
    Include("I"), Exclude("E")
}

// Exceptions
class ArbiDDAPayerNotFoundException : RuntimeException("DDA Payer not found")
class ArbiAdapterException : RuntimeException("Arbi adapter error")
