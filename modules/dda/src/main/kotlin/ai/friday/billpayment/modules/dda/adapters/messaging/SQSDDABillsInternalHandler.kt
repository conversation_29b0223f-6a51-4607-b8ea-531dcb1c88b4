package ai.friday.billpayment.modules.dda.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractParallelSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.addBaggage
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.DDACallbackService
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillRegisterStatus
import ai.friday.billpayment.app.payment.DefaultBillValidationResponse
import ai.friday.billpayment.modules.dda.app.DDAExecutionResult
import ai.friday.billpayment.modules.dda.app.DDAItem
import ai.friday.billpayment.modules.dda.app.DDAProvider
import ai.friday.billpayment.modules.dda.app.DDAService
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.LocalDate
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSDDABillsInternalHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val ddaService: DDAService,
    @Property(name = "sqs.ddaBillsInternalQueueName") private val queueName: String,
    private val ddaCallbackService: DDACallbackService? = null,
) : AbstractParallelSQSHandler(
    amazonSQS,
    configuration,
    queueName,
    minParallelism = 1,
    maxParallelism = 10,
    healthIndicatorTimeInMillis = 2_000,
    consumers = 3,
) {
    private val logger = LoggerFactory.getLogger(SQSDDABillsInternalHandler::class.java)

    @NewSpan
    @Trace
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val event = parseObjectFrom<DDABillMessageTO>(message.body())

        addBaggage("x_request_id" to event.requestId, "digitable" to event.bill.barCode?.digitable)

        val billRegisterData = event.bill

        if (billRegisterData.barCode == null ||
            billRegisterData.payerDocument == null ||
            billRegisterData.dueDate == null
        ) {
            throw IllegalArgumentException("invalid dda bill")
        }

        val ddaItem =
            DDAItem(
                amount = (billRegisterData.amount / 100.0),
                barcode = billRegisterData.barCode,
                document = billRegisterData.payerDocument,
                dueDate = billRegisterData.dueDate,
                ddaProvider = event.ddaProvider,
            )

        val markers = append("ddaItem", ddaItem)
        val oneMonthAgo = LocalDate.now().minusMonths(1)

        val billPreemptValidationResponse =
            DefaultBillValidationResponse(
                billRegisterStatus = event.status,
                billRegisterData = billRegisterData,
                gateway = FinancialServiceGateway.valueOf(event.ddaProvider.name),
            )

        if (ddaItem.dueDate.isBefore(oneMonthAgo)) {
            logger.warn(
                markers.andAppend("reason", "Vencimento anterior a $oneMonthAgo"),
                "SQSDDABillsHandlerV2",
            )
            billRegisterData.notifyEventIgnoredCallback()
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }

        val result =
            runCatching { ddaService.executeBill(ddaItem, billPreemptValidationResponse) }
                .getOrElse { DDAExecutionResult.Error(it) }

        return when (result) {
            is DDAExecutionResult.Error -> {
                logger.error(
                    markers.andAppend("result", result).andAppend("description", result.description),
                    "SQSDDABillsHandlerV2",
                    result.throwable,
                )
                billRegisterData.notifyErrorCallback()
                SQSHandlerResponse(shouldDeleteMessage = false)
            }

            DDAExecutionResult.Added -> {
                logger.info(markers.andAppend("result", result), "SQSDDABillsHandlerV2")
                billRegisterData.notifyBillAddedCallback()
                SQSHandlerResponse(shouldDeleteMessage = true)
            }

            is DDAExecutionResult.Updated -> {
                logger.info(markers.andAppend("result", result), "SQSDDABillsHandlerV2")
                billRegisterData.notifyBillUpdatedCallback()
                SQSHandlerResponse(shouldDeleteMessage = true)
            }

            DDAExecutionResult.AccountNotFound, is DDAExecutionResult.NotAdded, is DDAExecutionResult.NotUpdated -> {
                logger.warn(markers.andAppend("result", result), "SQSDDABillsHandlerV2")
                billRegisterData.notifyEventIgnoredCallback()
                SQSHandlerResponse(shouldDeleteMessage = true)
            }
        }
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("event", message), "SQSDDABillsHandlerV2", e)
        val deleteMessage = (e is IllegalStateException || e is java.lang.IllegalArgumentException)
        return SQSHandlerResponse(shouldDeleteMessage = deleteMessage)
    }

    // some helper methods just to improve readability of main code
    private fun BillRegisterData.notifyErrorCallback() = idNumber?.let { ddaCallbackService?.notifyError(it) }

    private fun BillRegisterData.notifyBillAddedCallback() = idNumber?.let { ddaCallbackService?.notifyBillAdded(it) }

    private fun BillRegisterData.notifyBillUpdatedCallback() = idNumber?.let { ddaCallbackService?.notifyBillUpdated(it) }

    private fun BillRegisterData.notifyEventIgnoredCallback() = idNumber?.let { ddaCallbackService?.notifyEventIgnored(it) }
}

@JsonIgnoreProperties(ignoreUnknown = true)
data class DDABillMessageTO(
    val ddaProvider: DDAProvider,
    val status: BillRegisterStatus,
    val bill: BillRegisterData,
    val requestId: String? = null,
)
