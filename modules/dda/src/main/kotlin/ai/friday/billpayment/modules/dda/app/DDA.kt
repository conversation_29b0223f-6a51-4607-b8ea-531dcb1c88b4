package ai.friday.billpayment.modules.dda.app

import ai.friday.billpayment.app.account.AccountId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import java.time.ZonedDateTime

enum class DDAStatus {
    CREATED, // Quando o usuário é criado, mas ainda não foi ativado
    PENDING, // Aguardando solicitação ao provedor de DDA para iniciar ativação
    REQUESTING, // Quando o processo é assíncrono, significa que o pedido foi feito porém ainda não há retorno
    REQUESTED, // Significa que o pedido foi confirmado pelo provedor de DDA e o agregado será ativado
    ACTIVE, // Importação/Migração foi finalizada e o usuário está ativo
    PENDING_CLOSE, // Aguardando solicitação ao provedor de DDA para iniciar o descadastro
    CLOSING, // Pedido de opt out assíncrono foi realizado, porém a resposta não é conhecida
    CLOSED, // Pedido de opt out confirmado
    DENIED, // Retorno de optin foi recusado

    // Migration process
    PENDING_MIGRATION_OPTIN, // Em escopo de migração - Aguarda solicitação ao novo provedor de DDA para iniciar ativação
    MIGRATING_OPTIN, // Em escopo de migração - quando o processo é assíncrono - significa que o pedido foi feito ao novo provedor de DDA porém ainda não há retorno
    PENDING_MIGRATION_OPTOUT, // Em escopo de migração - Aguarda solicitação ao antigo provedor de DDA para iniciar opt-out
    MIGRATING_OPTOUT, // Em escopo de migração - quando o processo é assíncrono - significa que o pedido foi feito ao antigo provedor de DDA porém ainda não há retorno
}

fun DDAStatus.optedOut() = this == DDAStatus.CLOSED || this == DDAStatus.CLOSING

data class DDARegister(
    val accountId: AccountId,
    val document: String,
    val created: ZonedDateTime,
    val status: DDAStatus,
    val lastUpdated: ZonedDateTime = getZonedDateTime(),
    val lastSuccessfullExecution: ZonedDateTime? = null,
    val provider: DDAProvider = DDAProvider.ARBI,
    val migrated: ZonedDateTime? = null,
)

enum class DDAProvider {
    ARBI,
}

data class DDARegisterMessage(
    val accountId: AccountId,
    val document: String,
)

fun DDARegister.toDDARegisterMessage() = DDARegisterMessage(
    accountId = accountId,
    document = document,
)

interface FullDDAPostProcessor {
    fun process(ddaRegister: DDARegister): Either<FullDDAPostProcessorError, Unit>
}

sealed interface FullDDAPostProcessorError {
    val exception: Exception
    data class FindAccountError(override val exception: Exception) : FullDDAPostProcessorError
    data class ActivateAccountError(override val exception: Exception) : FullDDAPostProcessorError
}
