package ai.friday.billpayment.modules.dda.adapters.jobs

import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.messaging.SQSMessagePublisher
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.modules.dda.app.DDAService
import ai.friday.billpayment.modules.dda.app.toDDARegisterMessage
import io.micronaut.tracing.annotation.NewSpan
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

// @Singleton
// @Requires(notEnv = ["test", "staging"])
open class StartDDABillJob(
    private val ddaService: DDAService,
    private val configuration: SQSMessageHandlerConfiguration,
    private val messagePublisher: SQSMessagePublisher,
) {

    @NewSpan
    // @Scheduled(cron = "\${schedules.importPartialDda.cron}")
    // @SchedulerLock(name = "importPartialDda", lockAtLeastFor = "30m")
    open fun execute() {
        try {
            LOG.info(append("step", "begin"), "StartDDABillJob")
            val ddaRegisterList = ddaService.findActive()
            ddaRegisterList
                .forEach {
                    val message = QueueMessage(
                        configuration.ddaQueueName,
                        getObjectMapper().writeValueAsString(it.toDDARegisterMessage()),
                    )
                    messagePublisher.sendMessage(message)
                }
            LOG.info(append("step", "end").andAppend("accounts", ddaRegisterList.size), "StartDDABillJob")
        } catch (e: Exception) {
            LOG.error("StartDDABillJob", e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(StartDDABillJob::class.java)
    }
}
