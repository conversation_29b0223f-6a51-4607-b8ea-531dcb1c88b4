package ai.friday.billpayment.modules.dda.app

import ai.friday.billpayment.PrintableSealedClass
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.integrations.DocumentType
import java.time.LocalDate

interface DDAProviderService {
    fun getBills(
        dueDate: LocalDate,
        document: String,
    ): List<DDAItem>

    // TODO: temporary implementation
    fun getBillsWithoutMicronautParser(
        dueDate: LocalDate,
        document: String,
    ): Boolean

    fun add(
        documents: List<Document>,
        documentType: DocumentType,
    ): List<Document>

    fun batchAdd(documents: List<Document>): DDABatchResult

    fun queryBatchAdd(idRequisicao: String): DDAQueryBatchAddResult

    fun queryBatchRemove(idRequisicao: String): DDAQueryBatchRemoveResult

    fun remove(document: Document)

    fun batchRemove(documents: List<Document>): DDABatchResult
}

sealed class DDABatchResult : PrintableSealedClass() {
    data class Accepted(
        val requestId: String,
    ) : DDABatchResult()

    data object UnknownResponse : DDABatchResult()
}

sealed class DDAQueryBatchRemoveResult : PrintableSealedClass() {
    data class PartialSuccess(
        val failed: List<Document>,
        val success: List<Document>,
    ) : DDAQueryBatchRemoveResult()

    data class Failed(
        val failed: List<Document>,
    ) : DDAQueryBatchRemoveResult()

    data class PayerNotFound(
        val document: String,
    ) : DDAQueryBatchRemoveResult()

    data object PendingRequestError : DDAQueryBatchRemoveResult()

    data object ProcessingRequest : DDAQueryBatchRemoveResult()

    data object Success : DDAQueryBatchRemoveResult()

    data object UnknownResponse : DDAQueryBatchRemoveResult()
}

sealed class DDAQueryBatchAddResult : PrintableSealedClass() {
    data class PartialSuccess(
        val failed: List<Document>,
        val success: List<Document>,
    ) : DDAQueryBatchAddResult()

    data class Failed(
        val failed: List<Document>,
    ) : DDAQueryBatchAddResult()

    data object PendingRequestError : DDAQueryBatchAddResult()

    data object ProcessingRequest : DDAQueryBatchAddResult()

    data object Success : DDAQueryBatchAddResult()

    data object UnknownResponse : DDAQueryBatchAddResult()
}

interface DDARepository {
    fun save(ddaRegister: DDARegister): DDARegister

    fun find(accountId: AccountId): DDARegister?

    fun find(document: Document): DDARegister?

    fun findOlderRegister(
        document: Document,
        newerAccountId: AccountId,
    ): DDARegister?

    fun findByStatus(status: DDAStatus): List<DDARegister>

    fun find(
        barCode: BarCode,
        document: String,
        dueDate: LocalDate,
    ): DDABill?

    fun save(ddaBill: DDABill)
}

interface DDACallbackService {
    fun notifyError(idNumber: String)
    fun notifyBillAdded(idNumber: String)
    fun notifyBillUpdated(idNumber: String)
    fun notifyEventIgnored(idNumber: String)
}
