package ai.friday.billpayment.modules.dda.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.dda.app.DDABill
import ai.friday.billpayment.modules.dda.app.DDAProvider
import ai.friday.billpayment.modules.dda.app.DDARegister
import ai.friday.billpayment.modules.dda.app.DDAStatus
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

private const val DDAIndexPartitionKey = "DDA_REGISTER"

@FridayMePoupe
class DDADbRepository(private val dynamoDbDAO: DynamoDbDAO) : DDARepository {

    override fun save(ddaRegister: DDARegister): DDARegister {
        val entity = DDAEntity().apply {
            primaryKey = ddaRegister.accountId.value
            scanKey = DDAIndexPartitionKey
            accountId = ddaRegister.accountId.value
            document = ddaRegister.document
            created = ddaRegister.created.format(DateTimeFormatter.ISO_DATE_TIME)
            lastUpdated = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
            status = ddaRegister.status
            index1HashKey = DDAIndexPartitionKey
            index1RangeKey = ddaRegister.status.name
            lastSuccessfullExecution =
                ddaRegister.lastSuccessfullExecution?.format(DateTimeFormatter.ISO_DATE_TIME)
            provider = ddaRegister.provider
            index2HashKey = DDAIndexPartitionKey
            index2RangeKey = ddaRegister.document
            migrated = ddaRegister.migrated?.format(DateTimeFormatter.ISO_DATE_TIME)
        }
        dynamoDbDAO.save(entity)
        return ddaRegister
    }

    override fun find(accountId: AccountId): DDARegister? {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKey(
            primaryKey = accountId.value,
            scanKey = DDAIndexPartitionKey,
            DDAEntity::class.java,
        ).firstOrNull()?.toDDARegister()
    }

    override fun find(document: Document): DDARegister? {
        return dynamoDbDAO.queryIndex2OnHashKeyValueAndRangeKey(
            primaryKey = DDAIndexPartitionKey,
            scanKey = document.value,
            type = DDAEntity::class.java,
        ).map {
            it.toDDARegister()
        }.singleOrNull()
    }

    override fun findOlderRegister(document: Document, newerAccountId: AccountId): DDARegister? {
        return dynamoDbDAO.queryIndex2OnHashKeyValueAndRangeKey(
            primaryKey = DDAIndexPartitionKey,
            scanKey = document.value,
            type = DDAEntity::class.java,
        ).filter {
            it.accountId != newerAccountId.value
        }.map {
            it.toDDARegister()
        }.singleOrNull()
    }

    override fun findByStatus(status: DDAStatus): List<DDARegister> {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
            primaryKey = DDAIndexPartitionKey,
            scanKey = status.name,
            type = DDAEntity::class.java,
        ).map {
            it.toDDARegister()
        }
    }

    override fun find(barCode: BarCode, document: String, dueDate: LocalDate): DDABill? {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKey(
            barCode.digitable,
            "DDA#$document#${dueDate.format(dateFormat)}",
            DDABillEntity::class.java,
        )
            .map {
                DDABill(
                    barcode = barCode,
                    document = it.document,
                    dueDate = dueDate,
                    walletId = WalletId(value = it.walletId),
                    billId = BillId(value = it.billId),
                    lastStatus = BillStatus.fromString(it.lastStatus),
                )
            }.firstOrNull()
    }

    override fun save(ddaBill: DDABill) {
        val entity = DDABillEntity().apply {
            barcode = ddaBill.barcode.digitable
            document = ddaBill.document
            dueDate = ddaBill.dueDate.format(dateFormat)
            scanKey = "DDA#${ddaBill.document}#$dueDate"
            walletId = ddaBill.walletId.value
            billId = ddaBill.billId.value
            lastStatus = ddaBill.lastStatus.name
        }
        dynamoDbDAO.save(entity)
    }
}

private fun DDAEntity.toDDARegister() = DDARegister(
    accountId = AccountId(this.accountId),
    document = this.document,
    created = ZonedDateTime.parse(this.created, DateTimeFormatter.ISO_DATE_TIME),
    lastUpdated = ZonedDateTime.parse(this.lastUpdated, DateTimeFormatter.ISO_DATE_TIME),
    lastSuccessfullExecution = this.lastSuccessfullExecution?.let {
        ZonedDateTime.parse(
            it,
            DateTimeFormatter.ISO_DATE_TIME,
        )
    },
    status = this.status,
    provider = this.provider,
    migrated = this.migrated?.let {
        ZonedDateTime.parse(
            it,
            DateTimeFormatter.ISO_DATE_TIME,
        )
    },
)

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class DDABillEntity {

    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var barcode: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBAttribute(attributeName = "Document")
    lateinit var document: String

    @DynamoDBAttribute(attributeName = "Name")
    lateinit var walletId: String

    @DynamoDBAttribute(attributeName = "BillId")
    lateinit var billId: String

    @DynamoDBAttribute(attributeName = "Duedate")
    lateinit var dueDate: String

    @DynamoDBAttribute(attributeName = "LastStatus")
    lateinit var lastStatus: String
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class DDAEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // ACCOUNT-ID

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // DDA

    @DynamoDBAttribute(attributeName = "AccountId")
    lateinit var accountId: String

    @DynamoDBAttribute(attributeName = "Document")
    lateinit var document: String

    @DynamoDBAttribute(attributeName = "Created")
    lateinit var created: String

    @DynamoDBAttribute(attributeName = "LastUpdated")
    lateinit var lastUpdated: String

    @DynamoDBAttribute(attributeName = "LastSuccessfullExecution")
    var lastSuccessfullExecution: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Status")
    lateinit var status: DDAStatus

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var index1HashKey: String // DDA

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var index1RangeKey: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Provider")
    var provider: DDAProvider = DDAProvider.ARBI

    @DynamoDBIndexHashKey(attributeName = "GSIndex2PrimaryKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var index2HashKey: String // DDA

    @DynamoDBIndexRangeKey(attributeName = "GSIndex2ScanKey", globalSecondaryIndexName = "GSIndex2")
    lateinit var index2RangeKey: String

    @DynamoDBAttribute(attributeName = "MigratedAt")
    var migrated: String? = null
}
