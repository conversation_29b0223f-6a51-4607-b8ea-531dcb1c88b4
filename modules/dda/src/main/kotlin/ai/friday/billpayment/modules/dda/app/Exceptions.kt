package ai.friday.billpayment.modules.dda.app

import ai.friday.billpayment.app.account.AccountId

class CallDDAAgregadoException(e: Exception) : RuntimeException(e)

class DDARegisterNotFoundException(accountId: AccountId) :
    RuntimeException(String.format("Account %s does not have a DDA register", accountId.value))

class DDANotActiveException(accountId: AccountId) :
    RuntimeException(String.format("DDA is not active yet for account %s", accountId.value))

class DDARegisterFailsException(message: String) : RuntimeException(message)
