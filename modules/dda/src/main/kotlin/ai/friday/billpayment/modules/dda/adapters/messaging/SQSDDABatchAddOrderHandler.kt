package ai.friday.billpayment.modules.dda.adapters.messaging

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.DDAProviderService
import ai.friday.billpayment.app.integrations.DDAQueryBatchAddResult
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.modules.dda.app.BatchAddOrder
import ai.friday.billpayment.modules.dda.app.DDAService
import ai.friday.billpayment.modules.dda.app.DDAStatus
import ai.friday.billpayment.modules.dda.app.toDDARegisterMessage
import io.micronaut.context.annotation.Requires
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.runBlocking
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import parallelMap
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(notEnv = ["staging"])
open class SQSDDABatchAddOrderHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val ddaService: DDAService,
    private val ddaProviderService: DDAProviderService,
    private val ddaRepository: DDARepository,
    private val messagePublisher: MessagePublisher,
) : AbstractSQSHandler(amazonSQS, configuration, configuration.ddaBatchAddOrdersQueueName) {
    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val order = parseObjectFrom<BatchAddOrder>(m.body())

        val result =
            runCatching { ddaProviderService.queryBatchAdd(idRequisicao = order.requestId) }
                .getOrElse {
                    val marker = append("idRequisicao", order.requestId).andAppend("result", result)
                    LOG.error(marker, "SQSDDABatchAddOrderHandler", it)
                    return SQSHandlerResponse(false)
                }

        when (result) {
            is DDAQueryBatchAddResult.ProcessingRequest -> return SQSHandlerResponse(false)
            is DDAQueryBatchAddResult.Failed,
            is DDAQueryBatchAddResult.PendingRequestError,
            is DDAQueryBatchAddResult.UnknownResponse,
            -> handleFailures(order.documents.map { Document(it) })

            is DDAQueryBatchAddResult.Success -> handleSuccess(order.documents.map { Document(it) })
            is DDAQueryBatchAddResult.PartialSuccess -> {
                handleFailures(result.failed)
                handleSuccess(result.success)
            }
        }
        LOG.info(append("requestId", order.requestId).andAppend("result", result), "SQSDDABatchAddOrderHandler")
        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    private fun handleSuccess(documents: List<Document>) {
        runBlocking(Dispatchers.IO) {
            val result =
                documents.parallelMap { document ->
                    val ddaRegister = ddaService.findDDARegister(document)
                    ddaRegister?.let { dda -> ddaRepository.save(dda.copy(status = DDAStatus.REQUESTED)) }
                    ddaRegister
                }

            val messages = result.mapNotNull { it?.toDDARegisterMessage() }

            if (messages.isNotEmpty()) {
                messagePublisher.sendBatchDDARequestedMessage(messages)
            }
        }

        LOG.info(
            append("log_message", "success").andAppend("documents", documents),
            "SQSDDABatchAddOrderHandler",
        )
    }

    private fun handleFailures(documents: List<Document>) {
        runBlocking(Dispatchers.IO) {
            documents.parallelMap { document ->
                runCatching {
                    val ddaRegister = ddaService.findDDARegister(document)
                    ddaRegister?.let { dda -> ddaRepository.save(dda.copy(status = DDAStatus.PENDING)) }
                }.onFailure {
                    LOG.error(
                        append("error_message", "error reseting dda register to pending")
                            .andAppend("document", document),
                        "SQSDDABatchAddOrderHandler",
                        it,
                    )
                }
            }
        }

        LOG.error(
            append("error_message", "reseting dda register to pending")
                .andAppend("documents", documents),
            "SQSDDABatchAddOrderHandler",
        )
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        LOG.error(append("event", m), "SQSDDABatchAddOrderHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSDDABatchAddOrderHandler::class.java)
    }
}
