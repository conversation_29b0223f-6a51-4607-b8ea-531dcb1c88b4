package ai.friday.billpayment.modules.dda.adapters.messaging

import ai.friday.billpayment.ServerError
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.arbi.convertToLongOrException
import ai.friday.billpayment.adapters.arbi.convertToLongOrZero
import ai.friday.billpayment.adapters.arbi.formatDocument
import ai.friday.billpayment.adapters.arbi.formatDocumentSacadorAvalista
import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FinancialIdentifier
import ai.friday.billpayment.app.FinancialInstitutionGlobalData
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BillAmountCalculator
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.bill.deltaInSeconds
import ai.friday.billpayment.app.firstNotNullAndNotEmpty
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.BillRegisterData
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.DiscountData
import ai.friday.billpayment.app.payment.DiscountType
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.FineData
import ai.friday.billpayment.app.payment.FineType
import ai.friday.billpayment.app.payment.InterestData
import ai.friday.billpayment.app.payment.InterestType
import ai.friday.billpayment.app.payment.PartialPaymentAmountType
import ai.friday.billpayment.app.payment.RecipientChain
import ai.friday.billpayment.app.payment.getRecipientByPayer
import ai.friday.billpayment.modules.dda.app.ArbiDdaRtt
import ai.friday.billpayment.modules.dda.app.DDAExecutionResult
import ai.friday.billpayment.modules.dda.app.DDAItem
import ai.friday.billpayment.modules.dda.app.DDAProvider
import ai.friday.billpayment.modules.dda.app.DDARegister
import ai.friday.billpayment.modules.dda.app.DDAService
import ai.friday.billpayment.modules.dda.app.DDAStatus
import ai.friday.billpayment.app.metrics.metricRegister
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import arrow.core.getOrElse
import com.fasterxml.jackson.annotation.JsonAlias
import com.fasterxml.jackson.annotation.JsonIgnore
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSDDABillsHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val ddaService: DDAService,
    private val calculator: BillAmountCalculator,
    private val ddaRepository: DDARepository,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    configuration.ddaBills,
    consumers = 3,
) {
    @field:Property(name = "integrations.arbi.paymentTimeLimit")
    lateinit var paymentTimeLimit: String

    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val ddaBillTO = parseObjectFrom<DDABillTO>(message.body())

        val ddaItem =
            DDAItem(
                amount = ddaBillTO.vlrtitulo,
                barcode = BarCode.of(ddaBillTO.numcodbarras),
                document = ddaBillTO.cnpj_cpf_sacado,
                dueDate = LocalDate.parse(ddaBillTO.datavencimento, dateTimeFormat),
                ddaProvider = DDAProvider.ARBI,
            )

        val markers = append("ddaItem", ddaItem)

        val oneMonthAgo = getLocalDate().minusMonths(1)

        if (ddaItem.dueDate.isBefore(oneMonthAgo)) {
            logger.info(
                markers.andAppend("reason", "Vencimento anterior a $oneMonthAgo"),
                "SQSDDABillsHandler",
            )
            return SQSHandlerResponse(shouldDeleteMessage = true)
        }

        val billPreemptValidationResponse = buildBillValidationResponse(ddaBillTO)

        if (billPreemptValidationResponse?.billRegisterData?.amountCalculationModel in
            listOf(
                    AmountCalculationModel.UNKNOWN,
                    AmountCalculationModel.ON_DEMAND,
                )
        ) {
            logger.warn(
                markers.andAppend(
                    "TpModlCalc",
                    billPreemptValidationResponse?.billRegisterData?.amountCalculationModel,
                ),
                "SQSDDABillsHandler",
            )
        }

        return when (val result = ddaService.executeBill(ddaItem, billPreemptValidationResponse)) {
            is DDAExecutionResult.Error -> {
                logger.warn(
                    markers.andAppend("result", result).andAppend("description", result.description),
                    "SQSDDABillsHandler",
                    result.throwable,
                )
                SQSHandlerResponse(shouldDeleteMessage = !result.isRetryable)
            }

            DDAExecutionResult.AccountNotFound -> {
                logger.warn(markers.andAppend("result", result), "SQSDDABillsHandler")
                SQSHandlerResponse(shouldDeleteMessage = true)
            }

            else -> {
                val ddaRegister = ddaRepository.find(document = Document(ddaItem.document))

                if (ddaBillTO.dthrsittitulo != null && isDddRegisterActivatedAtLast3Days(ddaRegister)) {
                    val lastUpdate = getDDAItemLastUpdate(ddaBillTO)

                    if (lastUpdate != null) {
                        val rtt = calcRtt(lastUpdate)

                        markers.andAppend("rtt", rtt)
                        metricRegister(ArbiDdaRtt(rtt))
                    }
                }

                logger.info(markers.andAppend("result", result), "SQSDDABillsHandler")

                SQSHandlerResponse(shouldDeleteMessage = true)
            }
        }
    }

    private fun isDddRegisterActivatedAtLast3Days(ddaRegister: DDARegister?): Boolean {
        if (ddaRegister == null || ddaRegister.status != DDAStatus.ACTIVE) {
            return false
        }

        return ddaRegister.lastUpdated.isBefore(getZonedDateTime().minusDays(3))
    }

    private fun getDDAItemLastUpdate(ddaBillTO: DDABillTO): ZonedDateTime? {
        val updatedDate =
            ddaBillTO.dthrsittitulo?.let {
                LocalDateTime.parse(ddaBillTO.dthrsittitulo, dateTimeFormat).atZone(brazilTimeZone)
            }

        val operationalDate =
            ddaBillTO.baixasOperacionais
                .maxOfOrNull { LocalDateTime.parse(it.dataprocbaixa, dateTimeFormat).atZone(brazilTimeZone) }

        val effectiveDate =
            ddaBillTO.baixasOperacionais
                .maxOfOrNull { LocalDateTime.parse(it.dataprocbaixa, dateTimeFormat).atZone(brazilTimeZone) }

        return listOfNotNull(updatedDate, operationalDate, effectiveDate).maxOrNull()
    }

    private fun calcRtt(date: ZonedDateTime): Long {
        return getZonedDateTime().deltaInSeconds(date)
    }

    private enum class BillAmountCalculationMode {
        CALCULATOR,
        EXTERNAL_VALIDATE,
        NOTHING,
    }

    private fun getBillAmountCalculationMode(ddaBillTO: DDABillTO): BillAmountCalculationMode {
        val amountCalculationModel = AmountCalculationModel.getByCode(ddaBillTO.tipocalculo)
        val discountList =
            listOf(
                Pair(ddaBillTO.coddesconto01, ddaBillTO.datadesconto01.toLocalDate()),
                Pair(ddaBillTO.coddesconto02, ddaBillTO.datadesconto02.toLocalDate()),
                Pair(ddaBillTO.coddesconto03, ddaBillTO.datadesconto03.toLocalDate()),
            )
        val dueDate = LocalDate.parse(ddaBillTO.datavencimento, dateTimeFormat)
        val hasActiveDiscount = hasActiveDiscount(discountList, dueDate)
        val hasPenalty =
            FineType.getByCode(ddaBillTO.codmulta) != FineType.FREE || InterestType.getByCode(ddaBillTO.codmora) != InterestType.FREE
        val hasComplexCalculationAfterDueDate =
            amountCalculationModel in
                listOf(
                    AmountCalculationModel.BENEFICIARY_AFTER_DUE_DATE,
                    AmountCalculationModel.BENEFICIARY_ONLY,
                )
        val hasComplexCalculation = amountCalculationModel == AmountCalculationModel.BENEFICIARY_ONLY

        return when {
            amountCalculationModel == AmountCalculationModel.UNKNOWN -> BillAmountCalculationMode.EXTERNAL_VALIDATE
            hasComplexCalculation -> BillAmountCalculationMode.EXTERNAL_VALIDATE
            ddaBillTO.isOverdue() && hasComplexCalculationAfterDueDate -> BillAmountCalculationMode.EXTERNAL_VALIDATE
            !ddaBillTO.isOverdue() && !hasActiveDiscount -> BillAmountCalculationMode.NOTHING
            ddaBillTO.isOverdue() && !hasPenalty -> BillAmountCalculationMode.NOTHING
            else -> BillAmountCalculationMode.CALCULATOR
        }.also {
            logger.info(
                append("amountCalculationMode", it.name)
                    .andAppend("ddaBill", ddaBillTO),
                "GetBillAmountCalculationMode",
            )
        }
    }

    fun buildBillValidationResponse(ddaBillTO: DDABillTO): BillValidationResponse? {
        val dueDate = LocalDate.parse(ddaBillTO.datavencimento, dateTimeFormat)
        when (getBillAmountCalculationMode(ddaBillTO)) {
            BillAmountCalculationMode.EXTERNAL_VALIDATE -> return null
            BillAmountCalculationMode.NOTHING -> {
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimit)
                if (validationResponse == null) {
                    logger.warn(
                        append(
                            "ddaBillTO",
                            ddaBillTO,
                        ).andAppend("importantFieldNull", true),
                        "buildBillValidationResponse",
                    )
                    return null
                }
                return validationResponse
            }
            BillAmountCalculationMode.CALCULATOR -> {
                val validationResponse = ddaBillTO.toValidationResponse(paymentTimeLimit)
                if (validationResponse == null) {
                    logger.warn(
                        append(
                            "ddaBillTO",
                            ddaBillTO,
                        ).andAppend("importantFieldNull", true),
                        "buildBillValidationResponse",
                    )
                    return null
                }
                return calculate(validationResponse, dueDate)
            }
        }
    }

    private fun calculate(
        validationResponse: ArbiValidationResponse,
        dueDate: LocalDate,
    ): BillValidationResponse? {
        if (!calculator.isAbleToCalculateInternally(
                amountCalculationModel = validationResponse.billRegisterData!!.amountCalculationModel,
                dueDate = dueDate,
            )
        ) {
            return null
        }

        with(validationResponse.billRegisterData) {
            val markers = append("digitableNumber", validationResponse.billRegisterData.barCode?.digitable)

            val calculatorResponse =
                calculator.calculate(
                    amount = amount,
                    dueDate = dueDate,
                    fineData = fineData,
                    interestData = interestData,
                    discountData = discountData,
                    abatement = abatement,
                ).getOrElse {
                    when (it) {
                        is ServerError ->
                            logger.warn(
                                markers,
                                "SQSDDABillsHandler#calculator",
                                it.ex,
                            )

                        else ->
                            logger.warn(
                                markers.andAppend("errorMessage", it),
                                "SQSDDABillsHandler#calculator",
                            )
                    }

                    return null
                }

            return validationResponse.copy(
                billRegisterData =
                validationResponse.billRegisterData.copy(
                    discount = calculatorResponse.discount,
                    interest = calculatorResponse.interest,
                    fine = calculatorResponse.fine,
                    amountTotal = calculatorResponse.totalAmount,
                ),
            )
        }
    }

    private fun hasActiveDiscount(
        discountList: List<Pair<String?, LocalDate?>>,
        dueDate: LocalDate,
    ): Boolean {
        val list =
            discountList.mapNotNull { (codDesconto, dataDesconto) ->
                if (codDesconto != null && codDesconto != "0") {
                    dataDesconto ?: dueDate
                } else {
                    null
                }
            }

        return if (list.isEmpty()) {
            false
        } else {
            !list.stream().max(LocalDate::compareTo).get().isBefore(getLocalDate())
        }
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("event", message.body()), "SQSDDABillsHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSDDABillsHandler::class.java)
    }
}

private fun String?.toLocalDate(): LocalDate? {
    return this?.takeIf { it.isNotBlank() }?.let { LocalDate.parse(this, dateTimeFormat) }
}

private val dateTimeFormat: DateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss")

internal fun DDABillTO.toValidationResponse(paymentTimeLimit: String): ArbiValidationResponse? {
    val assignor: String =
        FinancialInstitutionGlobalData.getInstitutionByCode(FinancialIdentifier.COMPE(codifcedente))
            ?: codifcedente.toString()

    val recipientChain = getRecipientChain(this)
    val paymentStatus = this.sitpagamentocip.toIntOrNull() ?: return null

    val amountPaidInfo = calculateAmountPaid(this)

    val partialPaymentType =
        when (this.tipoautrecdivergente) {
            "1" -> PartialPaymentAmountType.ANY_VALUE
            "2" -> PartialPaymentAmountType.BETWEEN_MINIMUM_AND_MAXIMUM
            "3" -> PartialPaymentAmountType.NOT_ACCEPT_DIFFERENT_VALUE
            "4" -> PartialPaymentAmountType.ONLY_MINIMUM_VALUE
            else -> null
        }

    val divergentPayment =
        when (partialPaymentType) {
            PartialPaymentAmountType.ANY_VALUE, PartialPaymentAmountType.BETWEEN_MINIMUM_AND_MAXIMUM, PartialPaymentAmountType.ONLY_MINIMUM_VALUE -> {
                val partial =
                    if (this.indvalorperc_min == "V") {
                        DivergentPayment(
                            minimumAmount = convertToLongOrZero(this.vlrmintitulo.toString()),
                            minimumPercentage = null,
                            amountType = partialPaymentType,
                        )
                    } else {
                        DivergentPayment(
                            minimumAmount = null,
                            minimumPercentage = this.vlrmintitulo.toBigDecimal(),
                            amountType = partialPaymentType,
                        )
                    }

                if (this.indvalorperc_max == "V") {
                    partial.copy(
                        maximumAmount = convertToLongOrZero(this.vlrmaxtitulo.toString()),
                        maximumPercentage = null,
                    )
                } else {
                    partial.copy(
                        maximumAmount = null,
                        maximumPercentage = this.vlrmaxtitulo.toBigDecimal(),
                    )
                }
            }

            PartialPaymentAmountType.NOT_ACCEPT_DIFFERENT_VALUE -> DivergentPayment(amountType = PartialPaymentAmountType.NOT_ACCEPT_DIFFERENT_VALUE)
            else -> null
        }

    return ArbiValidationResponse(
        billRegisterData =
        BillRegisterData(
            billType = BillType.FICHA_COMPENSACAO,
            assignor = assignor,
            recipient = recipientChain.getRecipientByPayer(this.cnpj_cpf_sacado, assignor),
            recipientChain = recipientChain,
            payerDocument = formatDocument(this.cnpj_cpf_sacado, this.tipopessacado),
            payerName = this.nomesacado,
            amount = convertToLongOrException(this.vlrtitulo.toString()),
            discount = 0,
            interest = 0,
            fine = 0,
            amountTotal = convertToLongOrException(this.vlrtitulo.toString()),
            expirationDate = LocalDate.parse(this.datalimpagto, dateTimeFormat),
            settleDate = getLocalDate(),
            paymentLimitTime = paymentTimeLimit,
            dueDate = LocalDate.parse(datavencimento, dateTimeFormat),
            amountCalculationModel = AmountCalculationModel.getByCode(this.tipocalculo),
            fichaCompensacaoType = FichaCompensacaoType.findByCode((this.codespeciedoc.toInt())),
            amountPaid = amountPaidInfo?.amountPaid,
            paidDate = amountPaidInfo?.paidDate,
            idNumber = this.numidentcdda.toString(),
            interestData =
            InterestData(
                type = InterestType.getByCode(this.codmora),
                value = this.vlrpercmora.toBigDecimal(),
                date = this.datamora.toLocalDate(),
            ),
            fineData =
            FineData(
                type = FineType.getByCode(this.codmulta),
                value = this.vlrpercmulta.toBigDecimal(),
                date = this.datamulta.toLocalDate(),
            ),
            discountData =
            DiscountData(
                type = DiscountType.getByCode(this.coddesconto01),
                value1 = this.vlrpercdesconto01.toBigDecimal(),
                date1 = this.datadesconto01.toLocalDate(),
                value2 = this.vlrpercdesconto02.toBigDecimal(),
                date2 = this.datadesconto02.toLocalDate(),
                value3 = this.vlrpercdesconto03.toBigDecimal(),
                date3 = this.datadesconto03.toLocalDate(),
            ),
            abatement = this.vlrabatimento,
            divergentPayment = divergentPayment,
        ),
        paymentStatus = paymentStatus,
        resultado =
        paymentStatus?.let { paymentStatus ->
            ArbiValidationResponse.convertStatusToErrorDescription(
                paymentStatus,
            )
        },
    )
}

private data class AmountPaidInfo(
    val amountPaid: Long,
    val paidDate: LocalDate?,
)

private fun calculateAmountPaid(ddaBillTO: DDABillTO): AmountPaidInfo? {
    val maxDate = getLocalDate().minusDays(15)
    val baixasEfetivas =
        ddaBillTO.baixasEfetivas.filter { it.dataprocbaixa != null && maxDate.isBefore(it.dataprocbaixa.toLocalDate()) }
    val baixasOperacionais =
        ddaBillTO.baixasOperacionais.filter { it.dataprocbaixa != null && maxDate.isBefore(it.dataprocbaixa.toLocalDate()) }

    fun calculateAmountPaidInfo(list: List<BaixaTO>): AmountPaidInfo {
        val baixas =
            list
                .groupBy {
                    it.numidentbaixa
                }.map {
                    it.value.sortedByDescending { value -> value.vlrbaixa }[0]
                }.filter { it.vlrbaixa > 0 }

        val amountPaid = baixas.sumOf { it.vlrbaixa }
        val paidDate = baixas.mapNotNull { it.dataprocbaixa.toLocalDate() }.maxOfOrNull { it }

        return AmountPaidInfo(convertToLongOrException(amountPaid.toString()), paidDate)
    }

    return when {
        baixasEfetivas.isNotEmpty() -> calculateAmountPaidInfo(baixasEfetivas)
        baixasOperacionais.isNotEmpty() -> calculateAmountPaidInfo(baixasOperacionais)
        else -> null
    }
}

private fun getRecipientChain(ddaBillTO: DDABillTO) =
    RecipientChain(
        sacadorAvalista =
        ddaBillTO.nomesacador.takeIf { it.isNotEmpty() }?.let {
            Recipient(
                name = it,
                document = formatDocumentSacadorAvalista(ddaBillTO.cnpj_cpf_sacador, ddaBillTO.tipopessacador),
            )
        },
        originalBeneficiary =
        firstNotNullAndNotEmpty(
            ddaBillTO.nomefantasiacedenteori,
            ddaBillTO.nomecedenteori,
        )?.let {
            Recipient(
                name = it,
                document = formatDocument(ddaBillTO.cnpj_cpf_cedenteori, ddaBillTO.tipopescedenteori),
            )
        },
        finalBeneficiary =
        firstNotNullAndNotEmpty(
            ddaBillTO.nomefantasiacedente,
            ddaBillTO.nomecedente,
        )?.let {
            Recipient(
                name = it,
                document = formatDocument(ddaBillTO.cnpj_cpf_cedente, ddaBillTO.tipopescedente),
            )
        },
    )

data class DDABillTO(
    val numidentcdda: Long,
    val ultnumrefcadtit: Long,
    val ultnumseqcadtit: Long,
    val codifcedente: Long,
    val tipopescedenteori: String?,
    val cnpj_cpf_cedenteori: String,
    val nomecedenteori: String,
    val nomefantasiacedenteori: String?,
    val tipopescedente: String?,
    val cnpj_cpf_cedente: String?,
    val nomecedente: String?,
    val nomefantasiacedente: String?,
    val tipopessacado: String,
    val cnpj_cpf_sacado: String,
    val nomesacado: String,
    val nomefantasiasacado: String,
    val tipopessacador: String,
    val cnpj_cpf_sacador: String,
    val nomesacador: String,
    val identnossonumero: String,
    val numcodbarras: String,
    val linhadigitavel: String,
    val datavencimento: String,
    val vlrtitulo: Double,
    val codespeciedoc: String,
    val dataemissao: String,
    val qtdediasprotesto: Int?,
    val datalimpagto: String,
    val tipopagto: Int,
    val numparcela: Int?,
    val qtdparcela: Int?,
    val indtitulonegociado: String,
    val indbloqueio: String,
    val indparcial: String,
    val vlrabatimento: Double,
    val datamora: String?,
    val codmora: String,
    val vlrpercmora: Double,
    val datamulta: String?,
    val codmulta: String,
    val vlrpercmulta: Double,
    val datadesconto01: String?,
    val coddesconto01: String,
    val vlrpercdesconto01: Double,
    val datadesconto02: String?,
    val coddesconto02: String?,
    val vlrpercdesconto02: Double,
    val datadesconto03: String?,
    val coddesconto03: String?,
    val vlrpercdesconto03: Double,
    val indvalorperc_min: String,
    val vlrmintitulo: Double,
    val indvalorperc_max: String,
    val vlrmaxtitulo: Double,
    val tipocalculo: String,
    val tipoautrecdivergente: String,
    val aceite: String?,
    val sitpagamento: String?,
    val sitpagamentocip: String,
    val dthrsittitulo: String?,
    val datahoradda: String?,
    val baixasOperacionais: List<BaixaTO>,
    val baixasEfetivas: List<BaixaTO>,
) {
    @JsonIgnore
    fun isOverdue() = getLocalDate().isAfter(LocalDate.parse(datavencimento, dateTimeFormat))
}

data class BaixaTO(
    @JsonAlias("agebcobaixa_e")
    val agebcobaixa: String?,
    @JsonAlias("codbcobaixa_e")
    val codbcobaixa: String?,
    @JsonAlias("canal_e")
    val canal: String?,
    @JsonAlias("cnpj_cpf_port_e")
    val cnpj_cpf_port: String?,
    @JsonAlias("nome_port_e")
    val nome_port: String?,
    @JsonAlias("dataprocbaixa_e")
    val dataprocbaixa: String?,
    @JsonAlias("meio_e")
    val meio: String?,
    @JsonAlias("numidentbaixa_e")
    val numidentbaixa: Long,
    @JsonAlias("seqbaixa_e")
    val seqbaixa: Int,
    @JsonAlias("tpbaixa_e")
    val tpbaixa: Int,
    @JsonAlias("vlrbaixa_e")
    val vlrbaixa: Double,
)
