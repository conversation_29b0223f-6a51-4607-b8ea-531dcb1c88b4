package ai.friday.billpayment.modules.dda.app

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.arbi.ArbiDDAPayerNotFoundException
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillSynchronizationStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.SynchronizeBillResponse
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.chatbot.OpenFinanceIncentiveService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.DDABatchResult
import ai.friday.billpayment.app.integrations.DDAProviderService
import ai.friday.billpayment.app.integrations.DDARepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.FindBillService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.billRegisterData
import ai.friday.billpayment.createDDAConfig
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.BILL_ID_7
import ai.friday.billpayment.integration.CNPJ_1
import ai.friday.billpayment.integration.DIGITABLE_LINE
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.DOCUMENT_3
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.modules.dda.adapters.dynamodb.DDADbRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.right
import io.kotest.matchers.collections.shouldContainAll
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.HttpResponse
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.time.Instant
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZoneId
import java.time.ZonedDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.math.min
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

class DDAServiceTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(amazonDynamoDB = dynamoDB)

    private val ddaProviderService: DDAProviderService = mockk(relaxed = true)
    private val fichaCompensacaoService: FichaCompensacaoService = mockk()
    private val findBillService: FindBillService = mockk()
    private val featureConfiguration: FeatureConfiguration = mockk(relaxed = true)
    private val accountRepository: AccountRepository = mockk(relaxUnitFun = true)
    private val billValidationService: BillValidationService = mockk()
    private val updateBillService: UpdateBillService = mockk()
    private val ddaRepository: DDARepository = mockk(relaxUnitFun = true, relaxed = true) {
        every { save(any<DDARegister>()) } answers { firstArg() }
    }
    private val messagePublisher: MessagePublisher = mockk(relaxed = true)
    private val fullDDAPostProcessor: FullDDAPostProcessor = mockk(relaxed = true)

    val mockDdaConfig = mockk<DDAConfig>(relaxed = true) {
        every { maxAddUser } returns MAX_ADD_USER
        every { maxAddUserAsync } returns MAX_ADD_USER_ASYNC
        every { activateWaitMinutes } returns 90
    }

    val createDDARegister = CreateDDARegister(
        ddaRepository = ddaRepository,
        ddaConfig = mockDdaConfig,
    )

    val walletService = mockk<WalletService>() {
        every { findWallet(any()) } returns mockk(relaxed = true) {
            every { status } returns WalletStatus.ACTIVE
        }
    }

    private val openFinanceIncentiveService: OpenFinanceIncentiveService = mockk(relaxed = true)

    private val ddaService = DDAService(
        ddaProviderService = ddaProviderService,
        findBillService = findBillService,
        featureConfiguration = featureConfiguration,
        accountRepository = accountRepository,
        billValidationService = billValidationService,
        updateBillService = updateBillService,
        ddaRepository = ddaRepository,
        fullDDAPostProcessor = fullDDAPostProcessor,
        messagePublisher = messagePublisher,
        ddaConfig = mockDdaConfig,
        createDDARegister = createDDARegister,
        fichaCompensacaoService = fichaCompensacaoService,
        walletService = walletService,
        openFinanceIncentiveService = openFinanceIncentiveService,
    )

    @Nested
    @DisplayName("ao executar o registro de DDA em grandes lotes assincronamente ")
    inner class DDABatchRegisterAsyncScope {

        @Test
        fun `deve listar registros de DDA que estão com status PENDING`() {
            ddaService.batchRegisterAsync()
            verify(exactly = 1) { ddaRepository.findByStatus(DDAStatus.PENDING) }
        }

        @Test
        fun `deve adicionar a lista de documentos no DDA e atualizar registro de DDA para REQUESTING e retornar o identificador da requisição`() {
            every {
                ddaRepository.findByStatus(DDAStatus.PENDING)
            } returns ddaRegisterPendingList

            every {
                ddaProviderService.batchAdd(any())
            } returns DDABatchResult.Accepted(BATCH_REQUEST_ID)

            ddaService.batchRegisterAsync()

            val slots = mutableListOf<DDARegister>()

            val expectedSaves = min(ddaRegisterPendingList.size, MAX_ADD_USER_ASYNC)
            verify(exactly = expectedSaves) { ddaRepository.save(capture(slots)) }

            slots.forEach {
                it.status shouldBe DDAStatus.REQUESTING
            }

            ddaRegisterPendingList.map { it.document } shouldContainAll slots.map { it.document }
        }

        @Test
        fun `deve enfileirar o lote que foi solicitado para verificação posterior`() {
            every {
                ddaRepository.findByStatus(DDAStatus.PENDING)
            } returns listOf(ddaRegisterPending)

            every {
                ddaProviderService.batchAdd(any())
            } returns DDABatchResult.Accepted(BATCH_REQUEST_ID)

            ddaService.batchRegisterAsync()

            val slot = slot<BatchAddOrder>()

            verify(exactly = 1) { messagePublisher.sendBatchAddOrderMessage(capture(slot)) }

            slot.captured.requestId shouldBe BATCH_REQUEST_ID
            slot.captured.documents shouldBe listOf(ddaRegisterPending.document)
        }

        @Test
        fun `não deve chamar mais documentos que a configuração(maxAddUserAsync) permite`() {
            every {
                ddaRepository.findByStatus(any())
            } returns ddaRegisterPendingList

            val slotDDARegisterList = slot<List<Document>>()
            ddaService.batchRegisterAsync()
            verify(exactly = 1) { ddaProviderService.batchAdd(capture(slotDDARegisterList)) }

            slotDDARegisterList.captured.size shouldBe MAX_ADD_USER_ASYNC
        }

        @Test
        fun `ao receber erro da integração, não deve alterar o status do registro de DDA`() {
            every { ddaProviderService.batchAdd(any()) } throws HttpClientResponseException(
                "error",
                HttpResponse.badRequest("body"),
            )

            ddaService.batchRegisterAsync()
            verify(exactly = 0) { messagePublisher.sendMessage(any()) }
            verify(exactly = 0) { ddaRepository.save(any<DDARegister>()) }
        }

        @Test
        fun `deve remover cadastro de usuários a descadastrar se não houver usuários pendentes de cadastro`() {
            every {
                ddaRepository.findByStatus(DDAStatus.PENDING)
            } returns emptyList()

            every {
                ddaRepository.findByStatus(DDAStatus.PENDING_CLOSE)
            } returns ddaRegisterClosingList

            withGivenDateTime(ZonedDateTime.of(LocalDate.of(2022, 8, 10), LocalTime.NOON, brazilTimeZone)) {
                ddaService.batchRegisterAsync()
            }
            verify(exactly = 0) { ddaProviderService.batchAdd(any()) }

            verify(exactly = 1) { ddaProviderService.remove(any()) }
        }
    }

    @Nested
    @DisplayName("given wallet user")
    inner class WalletUser {

        private val defaultWalletId = WalletId("WALLET-ID ${UUID.randomUUID()}")
        val account = Account(
            accountId = AccountId(ACCOUNT_ID),
            name = "Name",
            emailAddress = EmailAddress("email"),
            document = DOCUMENT,
            documentType = "documentType",
            mobilePhone = "mobilePhone",
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            status = AccountStatus.APPROVED,
            configuration = LegacyAccountConfiguration(
                creditCardConfiguration = CreditCardConfiguration(
                    quota = 20_000_00,
                ),
                defaultWalletId = defaultWalletId,
                receiveDDANotification = false,
            ),
            imageUrlSmall = null,
            imageUrlLarge = null,
            subscriptionType = SubscriptionType.PIX,
        )

        private val billAdded = BillAdded(
            billId = BillId(BILL_ID_7),
            created = Instant.now().toEpochMilli(),
            walletId = defaultWalletId,
            description = "ACTIVE BILL",
            dueDate = getLocalDate().plusDays(3),
            amount = 100L,
            billType = BillType.FICHA_COMPENSACAO,
            barcode = BarCode.ofDigitable(DIGITABLE_LINE),
            assignor = "AMERICANAS",
            document = DOCUMENT,
            paymentLimitTime = LocalTime.parse("20:00", timeFormat),
            lastSettleDate = getLocalDate().plusDays(1).format(dateFormat),
            actionSource = ActionSource.DDA(accountId = account.accountId),
        )

        val bill = Bill.build(billAdded)

        private val ddaRegisterActive = DDARegister(
            accountId = account.accountId,
            document = account.document,
            created = getZonedDateTime(),
            lastUpdated = getZonedDateTime(),
            status = DDAStatus.ACTIVE,
        )

        @BeforeEach
        fun setUp() {
            every { accountRepository.findAccountByDocument(DOCUMENT) } returns account
            every { accountRepository.findById(account.accountId) } returns account

            every { billValidationService.validate(ofType(BarCode::class)) } answers {
                ArbiValidationResponse(
                    null,
                    12,
                    null,
                )
            }
            every { ddaRepository.find(accountId = any()) } answers { ddaRegisterActive }

            every { fichaCompensacaoService.createFichaDeCompensacao(any()) } answers {
                CreateBillResult.FAILURE.BillAlreadyExists(
                    bill,
                )
            }
        }

        @Test
        fun `should add bill on default wallet`() {
            val slot = slot<CreateFichaDeCompensacaoRequest>()

            every { ddaRepository.find(any(), any(), any()) } returns null
            every { fichaCompensacaoService.createFichaDeCompensacao(capture(slot)) } answers {
                CreateBillResult.SUCCESS(
                    bill,
                )
            }

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )
            val result = ddaService.executeBill(ddaItem = ddaItem)
            result shouldBe DDAExecutionResult.Added
            with(slot.captured) {
                walletId shouldBe account.configuration.defaultWalletId
            }

            val ddaBill = DDABill(
                barcode = ddaItem.barcode,
                document = ddaItem.document,
                dueDate = ddaItem.dueDate,
                walletId = account.configuration.defaultWalletId!!,
                billId = bill.billId,
                lastStatus = bill.status,
            )
            verify { ddaRepository.save(ddaBill) }
        }

        @ParameterizedTest
        @EnumSource(
            DDAStatus::class,
            mode = EnumSource.Mode.EXCLUDE,
            names = ["REQUESTED", "ACTIVE", "PENDING_MIGRATION_OPTOUT", "MIGRATING_OPTOUT"],
        )
        fun `should not add bill when user DDA is not ACTIVE or REQUESTED`(ddaStatus: DDAStatus) {
            every { ddaRepository.find(accountId = any()) } answers { ddaRegisterActive.copy(status = ddaStatus) }

            val ddaItem = DDAItem(
                ddaProvider = DDAProvider.ARBI,
                amount = 100.0,
                barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
                dueDate = getLocalDate(),
                document = DOCUMENT,
            )

            val result = ddaService.executeBill(ddaItem = ddaItem)

            result.shouldBeTypeOf<DDAExecutionResult.NotAdded>()

            verify {
                accountRepository.findById(any()) wasNot Called
            }
        }
    }

    companion object {
        private const val MAX_ADD_USER = 10
        private const val MAX_ADD_USER_ASYNC = 5
        private const val BATCH_REQUEST_ID = "batch-request-id"

        private val ddaRegisterPending = DDARegister(
            accountId = AccountId(ACCOUNT_ID),
            document = DOCUMENT,
            created = getZonedDateTime(),
            status = DDAStatus.PENDING,
        )

        private val ddaRegisterPendingList = (1..10).map {
            DDARegister(
                accountId = AccountId("account-$it"),
                document = "document-$it",
                created = getZonedDateTime(),
                status = DDAStatus.PENDING,
            )
        }

        private val ddaRegisterClosingList = listOf(
            DDARegister(
                accountId = AccountId(ACCOUNT_ID),
                document = DOCUMENT,
                created = getZonedDateTime(),
                status = DDAStatus.PENDING_CLOSE,
            )
        )
    }
}
