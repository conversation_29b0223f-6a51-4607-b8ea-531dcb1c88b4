package ai.friday.billpayment.modules.pfm.app

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.and
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.pfm.CreateDefaultCategoriesError
import ai.friday.billpayment.app.pfm.CreateWalletBillCategoryError
import ai.friday.billpayment.app.pfm.DefaultWalletBillCategoryRepository
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.UpdateWalletBillCategoryError
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pfm.WalletBillCategoryRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.modules.pfm.PFMModule
import ai.friday.billpayment.modules.pfm.adapters.messaging.PFMSQSMessageHandlerConfiguration
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import datadog.trace.api.Trace
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.CacheInvalidate
import io.micronaut.cache.annotation.Cacheable
import jakarta.inject.Named
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

const val categoriesLockProvider = "categories"

@PFMModule
@CacheConfig("categories")
open class WalletBillCategoryService(
    private val defaultWalletBillCategoryRepository: DefaultWalletBillCategoryRepository,
    private val walletBillCategoryRepository: WalletBillCategoryRepository,
    @Named(categoriesLockProvider) private val lockProvider: InternalLock,
    private val messagePublisher: MessagePublisher,
    private val configuration: PFMSQSMessageHandlerConfiguration,
) : PFMWalletCategoryService {
    private val logger = LoggerFactory.getLogger(WalletBillCategoryService::class.java)

    @CacheInvalidate(parameters = ["walletId"])
    override fun createDefaultWalletCategories(walletId: WalletId): Either<CreateDefaultCategoriesError, List<WalletBillCategory>> {
        val markers = Markers.append("walletId", walletId)

        val lock = lockProvider.acquireLock(walletId.value) ?: return CreateDefaultCategoriesError.CouldNotAcquireLock.left()

        if (walletBillCategoryRepository.findByWalletId(walletId).any { it.default }) {
            lock.unlock()
            return CreateDefaultCategoriesError.WalletAlreadyHasCategories.left()
        }

        val categories = defaultWalletBillCategoryRepository.findAll().map { defaultWalletBillCategory ->
            val walletBillCategory =
                WalletBillCategory(
                    walletId = walletId,
                    categoryId = PFMCategoryId(),
                    name = defaultWalletBillCategory.name,
                    icon = defaultWalletBillCategory.icon,
                    enabled = true,
                    default = true,
                )

            walletBillCategoryRepository.save(walletBillCategory = walletBillCategory)

            logger.info(markers, "WalletBillCategoryService#createDefaultWalletCategories")
            walletBillCategory
        }

        lock.unlock()

        return categories.right()
    }

    @Cacheable(parameters = ["walletId"])
    @Trace
    override fun findWalletCategories(walletId: WalletId, createAsync: Boolean): List<WalletBillCategory> {
        val categories = walletBillCategoryRepository.findByWalletId(walletId).sortedBy { it.name }

        val categoriesResult = categories.ifEmpty {
            if (createAsync) {
                requestCreateDefaultCategories(walletId)
                categories
            } else {
                createDefaultWalletCategories(walletId).getOrElse {
                    logger.error(Markers.append("walletId", walletId.value).andAppend("reason", "Could not create default categories"), "WalletBillCategoryService#findWalletCategories")
                    emptyList()
                }
            }
        }

        logger.info(Markers.append("categories", categories).andAppend("walletId", walletId.value), "WalletBillCategoryService#findWalletCategories")
        return categoriesResult
    }

    override fun findCategory(walletId: WalletId, billCategoryId: PFMCategoryId) =
        walletBillCategoryRepository.findByWalletIdAndBillCategoryId(walletId, billCategoryId)

    @CacheInvalidate(parameters = ["walletId"])
    override fun create(
        walletId: WalletId,
        name: String,
        icon: String,
    ): Either<CreateWalletBillCategoryError, WalletBillCategory> {
        val markers =
            Markers.append("walletId", walletId.value)
                .andAppend("name", name)
                .andAppend("icon", icon)

        val walletCategories = walletBillCategoryRepository.findByWalletId(walletId)

        val sanitizedName = name.trim()

        val existingCategory = walletCategories.firstOrNull { it.name == sanitizedName }
        markers.andAppend("existingCategory", existingCategory)

        if (existingCategory != null) {
            logger.warn(markers, "WalletBillCategoryService#create")
            return CreateWalletBillCategoryError.AlreadyExists.left()
        }

        val category = WalletBillCategory(
            walletId = walletId,
            categoryId = PFMCategoryId(),
            name = sanitizedName,
            icon = icon,
            enabled = true,
            default = false,
        )

        markers.andAppend("category", category)

        walletBillCategoryRepository.save(category)

        logger.info(markers, "WalletBillCategoryService#create")
        return category.right()
    }

    @CacheInvalidate(parameters = ["walletId"])
    override fun update(walletId: WalletId, categoryId: PFMCategoryId, billCategoryName: String, billCategoryIcon: String, enabled: Boolean): Either<UpdateWalletBillCategoryError, WalletBillCategory> {
        val markers = Markers.append("walletBillCategory", categoryId).and("walletId" to walletId, "billCategoryName" to billCategoryName, "billCategoryIcon" to billCategoryIcon)
        val sanitizedName = billCategoryName.trim()

        val category = walletBillCategoryRepository.findByWalletIdAndBillCategoryId(walletId, categoryId)

        if (category == null) {
            logger.error(markers, "WalletBillCategoryService#update")
            return UpdateWalletBillCategoryError.NotFound.left()
        }

        val allCategories = walletBillCategoryRepository.findByWalletId(walletId)

        if (allCategories.any { it.name.lowercase() == sanitizedName.lowercase() && it.categoryId != categoryId }) {
            logger.warn(markers, "WalletBillCategoryService#update")
            return UpdateWalletBillCategoryError.NameAlreadyExists.left()
        }

        logger.info(markers, "WalletBillCategoryService#update")
        val updatedCategory = category.copy(name = sanitizedName, icon = billCategoryIcon, enabled = enabled)
        walletBillCategoryRepository.save(updatedCategory)
        return updatedCategory.right()
    }

    private fun requestCreateDefaultCategories(walletId: WalletId) {
        messagePublisher.sendMessage(configuration.createDefaultCategories, mapOf("walletId" to walletId.value))
    }
}

sealed class DisableWalletBillCategoryError : PrintableSealedClassV2() {
    data object NotFound : DisableWalletBillCategoryError()
}