package ai.friday.billpayment.modules.pfm.adapters.messaging

import ai.friday.billpayment.adapters.messaging.ParallelMessageHandlerConfiguration
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Secondary

@ConfigurationProperties("sqs")
@Secondary
class PFMSQSMessageHandlerConfiguration
@ConfigurationInject constructor(
    override val sqsWaitTime: Int,
    override val sqsCoolDownTime: Int,
    override val dlqArn: String,
    override val visibilityTimeout: Int,
    override val maxNumberOfMessages: Int,
    override val autoScaleWorkersInParallel: Boolean,
    val createDefaultCategories: String,
) : ParallelMessageHandlerConfiguration