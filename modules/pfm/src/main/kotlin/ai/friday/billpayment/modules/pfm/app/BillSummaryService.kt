package ai.friday.billpayment.modules.pfm.app

import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.pfm.SummaryEntry
import ai.friday.billpayment.app.pfm.SummaryEntryType
import ai.friday.billpayment.app.pfm.SummaryService
import ai.friday.billpayment.app.pfm.WalletBillCategoryRepository
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.canView
import ai.friday.billpayment.modules.pfm.PFMModule
import java.time.Month
import java.time.Year

@PFMModule
class BillSummaryService(private val repository: BillRepository, private val walletBillCategoryDbRepository: WalletBillCategoryRepository) : SummaryService {
    override fun generateSummaryList(walletId: WalletId, walletMember: Member, year: Year, month: Month): List<SummaryEntry> {
        return repository.findByWalletAndEffectiveDueDateMonth(walletId, year, month)
            .asSequence()
            .filter { it.status !in listOf(BillStatus.IGNORED, BillStatus.MOVED) }
            .filter { walletMember.canView(it) }
            .map {
                val category = it.categoryId?.let { categoryId -> walletBillCategoryDbRepository.findByWalletIdAndBillCategoryId(walletId, categoryId) }
                SummaryEntry(
                    category = category?.toBillCategory(),
                    totalAmount = it.amountTotal,
                    title = it.payee,
                    description = it.billDescription,
                    date = it.effectiveDueDate,
                    type = SummaryEntryType.PAYMENT,
                    entryType = when (it.billType) {
                        BillType.CONCESSIONARIA, BillType.FICHA_COMPENSACAO -> "Boleto"
                        BillType.INVOICE -> "TED"
                        BillType.PIX -> "PIX"
                        BillType.OTHERS -> "Outros"
                        BillType.INVESTMENT -> "Investimento"
                    },
                )
            }.toList()
    }
}