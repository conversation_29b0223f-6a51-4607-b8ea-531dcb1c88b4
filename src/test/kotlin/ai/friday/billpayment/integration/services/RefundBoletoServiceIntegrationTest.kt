package ai.friday.billpayment.integration.services

import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.app.Acquirer
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.FundProvider
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.Payer
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentNotCanceled
import ai.friday.billpayment.app.payment.RefundBoletoResponse
import ai.friday.billpayment.app.payment.RefundBoletoService
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.captureFunds.FridayCaptureFundsLocator
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.bankAccount
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentFailed
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.boletoTransaction
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.invoiceBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import arrow.core.getOrElse
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class RefundBoletoServiceIntegrationTest {

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(amazonDynamoDB = dynamoDB)
    private val accountRepository = AccountDbRepository(dynamoDbDAO = dynamoDbDAO)
    private val walletRepository = WalletDbRepository(dynamoDbDAO = dynamoDbDAO, accountRepository = mockk())
    private val billEventRepository = BillEventDBRepository(
        dynamoDbDAO = dynamoDbDAO,
        featureConfiguration = mockk(relaxed = true),
    )
    private val transactionRepository = DynamoDbTransactionRepository(
        dynamoDbDAO = dynamoDbDAO,
        accountRepository = accountRepository,
        transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, walletRepository),
    )
    private val internalBankService: FundProvider = mockk()
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val billRepository = DynamoDbBillRepository(dynamoDbDAO = dynamoDbDAO)
    private val updateBillService = UpdateBillService(
        billEventRepository = billEventRepository,
        billRepository = billRepository,
        billTrackingRepository = mockk(relaxed = true),
        eventPublisher = mockk(relaxed = true),
        boletoSettlementService = mockk(relaxed = true),
        billValidationService = mockk(relaxed = true) {
            every {
                validate(any<Bill>())
            } returns ArbiValidationResponse(
                billRegisterData = null,
                paymentStatus = null,
                resultado = null,
            )
        },
        lockProvider = mockk(relaxed = true),
        walletLimitsService = mockk(),
        walletService = mockk(),
        approveBillService = mockk(),
        denyBillService = mockk(),
        resolveScheduledBill = mockk(),
        cancelSchedulePaymentService = mockk {
            every { cancelScheduledPayment(any(), any(), any()) } returns true
            every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
        },
        mailboxWalletDataRepository = mockk(),
    )
    val acquirerService = mockk<AcquirerService>()
    private val refundBoletoService =
        RefundBoletoService(
            walletRepository = walletRepository,
            billEventRepository = billEventRepository,
            updateBillService = updateBillService,
            transactionRepository = transactionRepository,
            captureFundsLocator = FridayCaptureFundsLocator(
                fundProvider = internalBankService,
                acquirerService = acquirerService,
                creditCardFraudPreventionService = mockk(),
            ),
        )

    private val boletoAdded = billAddedFicha.copy(walletId = wallet.id)
    private val boletoPaymentStarted =
        billPaymentStart.copy(walletId = wallet.id, created = boletoAdded.created + 1)
    private val boletoPaid = billPaid.copy(walletId = wallet.id, created = boletoAdded.created + 2)
    private val boletoFailed =
        billPaymentFailed.copy(walletId = wallet.id, created = boletoAdded.created + 3)

    private val operationId = BankOperationId(UUID.randomUUID().toString())

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        walletRepository.save(wallet)
        billEventRepository.save(boletoAdded)
        accountRepository.save(walletFixture.founderAccount)
    }

    @Test
    fun `should return wallet not found when wallet does not exists`() {
        val walletId = WalletId("THIS_WALLET_DOES_NOT_EXISTS")
        val barcode = boletoAdded.barcode
        val response = refundBoletoService.refund(walletId, barcode)
        response shouldBe RefundBoletoResponse.WalletNotFound
    }

    @Test
    fun `should return boleto not found when boleto does not exists in the wallet`() {
        val walletId = wallet.id
        val barcode = BarCode.ofDigitable(FICHA_DE_COMPENSACAO_DIGITABLE_LINE_2)
        val response = refundBoletoService.refund(walletId, barcode)
        response shouldBe RefundBoletoResponse.BoletoNotFound
    }

    @Test
    fun `should return invalid boleto status when boleto is active`() {
        val walletId = wallet.id
        val barcode = boletoAdded.barcode
        val response = refundBoletoService.refund(walletId, barcode)
        response shouldBe RefundBoletoResponse.InvalidBoletoStatus
    }

    @Test
    fun `should return invalid boleto status when boleto is processing`() {
        val walletId = wallet.id
        val barcode = boletoAdded.barcode
        billEventRepository.save(boletoPaymentStarted)
        val response = refundBoletoService.refund(walletId, barcode)
        response shouldBe RefundBoletoResponse.InvalidBoletoStatus
    }

    @Test
    fun `should return boleto already refunded when boleto was refunded before`() {
        val walletId = wallet.id
        val barcode = boletoAdded.barcode
        billEventRepository.save(boletoPaymentStarted)
        billEventRepository.save(boletoPaid)
        billEventRepository.save(boletoFailed)
        val response = refundBoletoService.refund(walletId, barcode)
        response shouldBe RefundBoletoResponse.BoletoAlreadyRefunded
    }

    @Nested
    @DisplayName("paid with balance")
    inner class PaidWithCreditBalance {

        @BeforeEach
        fun setup() {
            val paymentMethod = accountRepository.createAccountPaymentMethod(
                accountId = walletFixture.founderAccount.accountId,
                bankAccount = bankAccount,
                position = 0,
                paymentMethodId = wallet.paymentMethodId,
                status = AccountPaymentMethodStatus.ACTIVE,
                mode = BankAccountMode.PHYSICAL,
            )

            val transaction = boletoTransaction.copy(
                id = boletoPaid.transactionId!!,
                payer = Payer(walletFixture.founderAccount.accountId, "", ""),
                status = TransactionStatus.COMPLETED,
                paymentData = boletoTransaction.paymentData.toSingle().copy(
                    payment = BalanceAuthorization(
                        operationId = operationId,
                        status = BankOperationStatus.SUCCESS,
                        amount = invoiceBill.amountTotal,
                        paymentGateway = FinancialServiceGateway.ARBI,
                    ),
                    accountPaymentMethod = paymentMethod,
                ),
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            )
            transactionRepository.save(transaction)
        }

        @ParameterizedTest
        @EnumSource(value = BankOperationStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["SUCCESS"])
        fun `should return payment refund error on error transferring the boleto amount back to the wallet`(status: BankOperationStatus) {
            val walletId = wallet.id
            val barcode = boletoAdded.barcode
            billEventRepository.save(boletoPaymentStarted)
            billEventRepository.save(boletoPaid)
            every {
                internalBankService.undoCaptureFunds(any(), any(), any(), any(), any(), any())
            } returns BankTransfer(
                operationId = BankOperationId(""),
                status = status,
                amount = boletoAdded.amount,
                errorDescription = "",
                gateway = FinancialServiceGateway.ARBI,
            )
            val response = refundBoletoService.refund(walletId, barcode)
            response shouldBe RefundBoletoResponse.PaymentRefundError

            val transaction = transactionRepository.findById(boletoPaid.transactionId!!)
            transaction.status shouldBe TransactionStatus.COMPLETED
            transaction.paymentData.toSingle()
                .get<BalanceAuthorization>().status shouldBe BankOperationStatus.SUCCESS
        }

        @Test
        fun `should return success on successfully refunded boleto`() {
            val walletId = wallet.id
            val barcode = boletoAdded.barcode
            billEventRepository.save(boletoPaymentStarted)
            billEventRepository.save(boletoPaid)
            every {
                internalBankService.undoCaptureFunds(any(), any(), any(), any(), any(), any())
            } returns BankTransfer(
                operationId = BankOperationId(UUID.randomUUID().toString()),
                status = BankOperationStatus.SUCCESS,
                amount = boletoAdded.amount,
                errorDescription = "",
                gateway = FinancialServiceGateway.ARBI,
            )
            val response = refundBoletoService.refund(walletId, barcode)
            response shouldBe RefundBoletoResponse.Success
            val refundedBill = billEventRepository.getBillById(boletoAdded.billId).getOrElse { throw it }
            val transaction = transactionRepository.findById(boletoPaid.transactionId!!)
            verify(exactly = 1) {
                internalBankService.undoCaptureFunds(
                    any(),
                    wallet.paymentMethodId,
                    any(),
                    operationId,
                    boletoAdded.amount,
                    any(),
                )
            }
            val refundedBills = billRepository.getRefundedBills(
                walletId = walletId,
                startDate = getLocalDate().atStartOfDay(brazilTimeZone),
                endDate = getLocalDate().plusDays(1).atStartOfDay(brazilTimeZone),
            )

            refundedBills.shouldHaveSize(1)
            with(refundedBills.first()) {
                amount shouldBe boletoAdded.amountTotal
                billId shouldBe boletoAdded.billId
                type shouldBe BillType.FICHA_COMPENSACAO
                transactionId shouldBe boletoPaid.transactionId
            }

            refundedBill.status shouldBe BillStatus.ACTIVE
            refundedBill.isRefunded() shouldBe true
            transaction.status shouldBe TransactionStatus.UNDONE
            transaction.paymentData.toSingle().get<BalanceAuthorization>().status shouldBe BankOperationStatus.REFUNDED
        }
    }

    @Nested
    @DisplayName("paid with creditCard")
    inner class PaidWithCreditCard {

        @BeforeEach
        fun setup() {
            val paymentMethod = accountRepository.createAccountPaymentMethod(
                accountId = walletFixture.founderAccount.accountId,
                creditCard = creditCard.method as CreditCard,
                position = 0,
                paymentMethodId = wallet.paymentMethodId,
                status = AccountPaymentMethodStatus.ACTIVE,
            )

            val transaction = boletoTransaction.copy(
                id = boletoPaid.transactionId!!,
                payer = Payer(walletFixture.founderAccount.accountId, "", ""),
                status = TransactionStatus.COMPLETED,
                paymentData = SinglePaymentData(
                    accountPaymentMethod = paymentMethod,
                    details = PaymentMethodsDetailWithCreditCard(
                        paymentMethodId = wallet.paymentMethodId,
                        netAmount = boletoAdded.amountTotal,
                        feeAmount = 10,
                        installments = 1,
                        calculationId = null,
                        fee = 4.0,
                    ),
                    payment = CreditCardAuthorization(
                        acquirer = Acquirer.CIELO,
                        transactionId = boletoPaid.transactionId!!.value,
                        status = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
                        acquirerTid = "acquirerTid",
                        amount = boletoAdded.amountTotal + 10,
                        acquirerReturnCode = null,
                        acquirerReturnMessage = "",
                        tid = null,
                        authorizationCode = null,
                    ),
                ),
            )
            transactionRepository.save(transaction)
        }

        @Test
        fun `should return success on successfully refunded boleto paid with credit card`() {
            val walletId = wallet.id
            val barcode = boletoAdded.barcode
            billEventRepository.save(boletoPaymentStarted)
            billEventRepository.save(boletoPaid)

            every {
                acquirerService.cancel(any())
            } just Runs

            val response = refundBoletoService.refund(walletId, barcode)
            response shouldBe RefundBoletoResponse.Success
            val refundedBill = billEventRepository.getBillById(boletoAdded.billId).getOrElse { throw it }
            val transaction = transactionRepository.findById(boletoPaid.transactionId!!)
            verify(exactly = 1) {
                acquirerService.cancel(boletoPaid.transactionId!!.value)
            }
            val refundedBills = billRepository.getRefundedBills(
                walletId = walletId,
                startDate = getLocalDate().atStartOfDay(brazilTimeZone),
                endDate = getLocalDate().plusDays(1).atStartOfDay(brazilTimeZone),
            )

            refundedBills.shouldHaveSize(1)
            with(refundedBills.first()) {
                amount shouldBe boletoAdded.amountTotal
                billId shouldBe boletoAdded.billId
                type shouldBe BillType.FICHA_COMPENSACAO
                transactionId shouldBe boletoPaid.transactionId
            }

            refundedBill.status shouldBe BillStatus.ACTIVE
            refundedBill.isRefunded() shouldBe true
            transaction.status shouldBe TransactionStatus.UNDONE
            transaction.paymentData.toSingle()
                .get<CreditCardAuthorization>().status shouldBe CreditCardPaymentStatus.REFUNDED
        }

        @Test
        fun `deve falhar o refund se não conseguir estornar na adquirente`() {
            val walletId = wallet.id
            val barcode = boletoAdded.barcode
            billEventRepository.save(boletoPaymentStarted)
            billEventRepository.save(boletoPaid)
            every {
                acquirerService.cancel(any())
            } throws PaymentNotCanceled("123", "Fake")

            val response = refundBoletoService.refund(walletId, barcode)

            response shouldBe RefundBoletoResponse.PaymentRefundError

            val transaction = transactionRepository.findById(boletoPaid.transactionId!!)
            transaction.status shouldBe TransactionStatus.COMPLETED
            transaction.paymentData.toSingle()
                .get<CreditCardAuthorization>().status shouldBe CreditCardPaymentStatus.PAYMENT_CONFIRMED
        }
    }
}