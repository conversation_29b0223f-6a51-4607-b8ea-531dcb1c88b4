package ai.friday.billpayment.integration.services

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.CREATED_ON
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.bill.DefaultBillEventPublisher
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.lock.InternalLockProvider
import ai.friday.billpayment.adapters.micronaut.RecurrenceConfigurationMicronaut
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillIgnored
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillPaymentScheduledBalanceInfo
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityService
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyException
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import ai.friday.billpayment.app.recurrence.CancelSubscriptionResult
import ai.friday.billpayment.app.recurrence.IgnoreRecurrenceErrors
import ai.friday.billpayment.app.recurrence.RecurrenceCreationError
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.recurrence.generateDateSequence
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billCategory
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.fixture.AccountPaymentMethodFixture
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.CORRELATION_ID
import ai.friday.billpayment.integration.GOVERNMENT_BARCODE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.RECURRENCE_ID
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.addBillEventIntoDb
import ai.friday.billpayment.integration.addBillIntoDb
import ai.friday.billpayment.integration.configureDailyLimits
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixKeyDetails
import ai.friday.billpayment.transactionId
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import ai.friday.billpayment.weeklyWalletRecurrenceNoEndDate
import ai.friday.billpayment.withBlockingAsyncCalls
import ai.friday.billpayment.withPixParticipants
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import arrow.core.getOrElse
import arrow.core.getOrHandle
import arrow.core.right
import io.kotest.inspectors.forAll
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldHaveAtLeastSize
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalTime
import java.util.UUID
import java.util.stream.Stream
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.api.fail
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

class BillRecurrenceServiceIntegrationTest {
    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val walletFixture =
        WalletFixture(founderAccountId = AccountId(ACCOUNT_ID), defaultWalletId = WalletId(WALLET_ID))
    private val wallet = walletFixture.buildWallet()

    private val recurrenceRepository = BillRecurrenceDBRepository(dynamoDbDAO, "2021-12-31")

    private val billEventRepository =
        BillEventDBRepository(
            dynamoDbDAO,
            allFalseFeatureConfiguration,
        )

    private val billRepository = DynamoDbBillRepository(dynamoDbDAO)

    private val recipientDbRepository = ContactDbRepository(dynamoDbDAO)

    private val categoryDbRepository =
        WalletBillCategoryDbRepository(WalletBillCategoryDynamoDAO(dynamoDbEnhancedClient))

    private val eventPublisher: EventPublisher = mockk(relaxed = true)

    private val walletRepository = WalletDbRepository(dynamoDbDAO, mockk())

    private val simpleLock = mockk<SimpleLock>(relaxed = true)
    private val lockProvider: InternalLockProvider =
        mockk {
            every {
                acquireLock(any())
            } returns simpleLock
        }

    private val paymentSchedulingService: PaymentSchedulingService =
        mockk(relaxed = true) {
            every {
                getScheduledBillsTotalAmount(any(), any(), any())
            } returns 0L
        }

    private val updateBillService =
        UpdateBillService(
            billEventRepository = billEventRepository,
            billRepository = billRepository,
            billTrackingRepository = mockk(relaxed = true),
            eventPublisher = eventPublisher,
            boletoSettlementService = mockk(relaxed = true),
            billValidationService = mockk(relaxed = true),
            lockProvider = lockProvider,
            walletLimitsService = mockk(),
            walletService = mockk(),
            approveBillService = mockk(),
            denyBillService = mockk(),
            resolveScheduledBill = mockk(),
            cancelSchedulePaymentService = mockk {
                every { cancelScheduledPayment(any(), any(), any()) } returns true
                every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
            },
            mailboxWalletDataRepository = mockk(),
        )
    private val fakeLastLimitDate: LocalDate = LocalDate.of(2020, 12, 31)
    private val fakeLimitDate: LocalDate =
        LocalDate.now().plusYears(2)
            .withDayOfMonth(LocalDate.now().month.length(LocalDate.now().plusYears(2).isLeapYear))

    private val walletLimitsService =
        WalletLimitsService(
            billRepository = billRepository,
            accountService = mockk(),
            walletRepository = walletRepository,
            paymentSchedulingService = paymentSchedulingService,
            internalLock = lockProvider,
        )

    private val pixKeyManagementMock =
        mockk<PixKeyManagement> {
            every { findKeyDetailsCacheable(any(), any()) } returns
                PixKeyDetailsResult(
                    pixKeyDetails = pixKeyDetails,
                    e2e = "123",
                )
        }

    private val billInstrumentationService = mockk<BillInstrumentationService>(relaxUnitFun = true)

    private val billEventPublisher =
        DefaultBillEventPublisher(
            eventRepository = billEventRepository,
            billRepository = billRepository,
            eventPublisher = mockk(relaxed = true),
        )

    private val scheduleBillSecurityService =
        mockk<ScheduleBillSecurityService> {
            every { canSchedule(any()) } returns Unit.right()
        }

    private val scheduleBillService =
        ScheduleBillService(
            billEventRepository = billEventRepository,
            accountService =
            mockk {
                every {
                    findAccountPaymentMethodByIdAndAccountId(any(), any())
                } returns
                    AccountPaymentMethodFixture.create(
                        method =
                        InternalBankAccount(
                            accountType = AccountType.CHECKING,
                            bankNo = 111,
                            routingNo = 4,
                            accountNo = 123555,
                            accountDv = "1",
                            document = wallet.founder.document,
                            bankAccountMode = BankAccountMode.PHYSICAL,
                        ),
                    )
            },
            paymentSchedulingService = paymentSchedulingService,
            scheduledBillPaymentServiceProvider = mockk(),
            billInstrumentationService = billInstrumentationService,
            billEventPublisher = billEventPublisher,
            lockProvider = lockProvider,
            scheduleBillSecurityService = scheduleBillSecurityService,
            batchSchedulePublisher = mockk(relaxUnitFun = true),
            walletRepository = walletRepository,
            isBatchScheduleNotificationEnabled = false,
            pinCodeService = mockk(relaxed = true),
        )

    private val createBillService =
        CreateBillService(
            billRepository = mockk(),
            contactService = ContactService(recipientDbRepository, billEventRepository, mockk(), mockk()),
            accountService = mockk(relaxed = true),
            pixKeyManagement = pixKeyManagementMock,
            updateBillService = updateBillService,
            tedConfiguration =
            mockk(relaxed = true) {
                every {
                    limitTime
                } returns "17:00"
            },
            walletLimitsService = walletLimitsService,
            possibleDuplicateBillService = mockk(relaxed = true),
            pixQRCodeParserService = mockk(),
            categoryService = mockk(),
            concessionariaService = mockk(),
        )

    private val recurrenceConfiguration =
        RecurrenceConfigurationMicronaut(
            lastLimitDate = fakeLastLimitDate.format(dateFormat),
            limitDate = fakeLimitDate.format(dateFormat),
        )

    private val accountRepository: AccountRepository =
        mockk {
            every {
                findById(walletFixture.founderAccount.accountId)
            } returns walletFixture.founderAccount
        }

    private val recurrenceService =
        BillRecurrenceService(
            billRecurrenceRepository = recurrenceRepository,
            createBillService = createBillService,
            updateBillService = updateBillService,
            billInstrumentationService = billInstrumentationService,
            recurrenceConfiguration = recurrenceConfiguration,
            possibleDuplicateBillService = mockk(),
            accountRepository = accountRepository,
            walletRepository = walletRepository,
            scheduleBillService = scheduleBillService,
        )

    private val walletLimit: Long = 999

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)

        walletRepository.save(wallet)
        configureDailyLimits(
            walletLimitsService = walletLimitsService,
            accountId = walletFixture.founder.accountId,
            walletId = wallet.id,
            dailyLimit = walletLimit,
            nighttimeLimit = walletLimit,
        )
    }

    @Test
    fun `should return illegal argument exception when start date gt limit date`() {
        val recurrence = buildRecurrence(startDate = fakeLimitDate.plusDays(1))
        val createdRecurrence = recurrenceService.create(recurrence, false)

        createdRecurrence.isLeft() shouldBe true
        createdRecurrence.mapLeft {
            it shouldBe RecurrenceCreationError.DueDateAfterLimit
        }

        verify {
            billInstrumentationService wasNot Called
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3])
    fun `should create weekly recurrence with any number of bills`(expectedCreatedBillsSize: Int) {
        val weeksToSubstract: Long = expectedCreatedBillsSize - 1L
        val recurrence = buildRecurrence(startDate = fakeLimitDate.minusWeeks(weeksToSubstract))

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                .toCompletableFuture().get()

        completedRecurrence.bills.size shouldBe expectedCreatedBillsSize
        recurrenceRepository.find(recurrence.id, recurrence.walletId) shouldBe completedRecurrence
        val bills = billRepository.findByWallet(recurrence.walletId)
        bills.size shouldBe expectedCreatedBillsSize
        bills.forEachIndexed { index, it ->
            it.amount shouldBe recurrence.amount
            it.dueDate shouldBe recurrence.rule.startDate.plusWeeks(index.toLong())
            it.billDescription shouldBe recurrence.description
            it.walletId.value shouldBe recurrence.walletId.value
            it.recipient?.pixKeyDetails shouldBe null
            it.recipient?.name shouldBe recurrence.recipientName
            it.recipient?.alias shouldBe recurrence.recipientAlias
            it.recipient?.document shouldBe recurrence.recipientDocument
            it.recipient?.bankAccount shouldBe recurrence.recipientBankAccount
            it.source shouldBe
                ActionSource.WalletRecurrence(
                    recurrenceId = recurrence.id,
                    accountId = walletFixture.founder.accountId,
                )
        }
        val first = billEventRepository.getBillById(completedRecurrence.bills.first())
        first.getOrNull()!!.recurrenceRule shouldBe recurrence.rule

        with(recipientDbRepository.findByAccountId(recurrence.contactAccountId)) {
            this shouldHaveSize 1
            completedRecurrence.contactId shouldBe first().id
        }

        verify {
            billInstrumentationService.createdRecurring(recurrence)
        }

        billRepository.findByContactId(contactId = completedRecurrence.contactId!!).size shouldBe 0
        billRepository.findRecurrenceBillByContactId(contactId = completedRecurrence.contactId!!).size shouldBe expectedCreatedBillsSize
    }

    @Test
    fun `should create weekly recurrence with bills until end date`() {
        val start = 10L
        val end = 4L
        val total = start - end + 1

        val recurrence =
            buildRecurrence(startDate = fakeLimitDate.minusWeeks(start), endDate = fakeLimitDate.minusWeeks(end))

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                .toCompletableFuture().get()

        completedRecurrence.bills.size shouldBe total
        recurrenceRepository.find(recurrence.id, recurrence.walletId) shouldBe completedRecurrence
        val bills = billRepository.findByWallet(recurrence.walletId)
        bills.size shouldBe total

        verify {
            billInstrumentationService.createdRecurring(recurrence)
        }
    }

    @Test
    fun `should create biweekly recurrence with bills until end date`() {
        val start = 6L
        val end = 2L
        val total = 3

        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.BIWEEKLY,
                startDate = fakeLimitDate.minusWeeks(start),
                endDate = fakeLimitDate.minusWeeks(end),
            )

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                .toCompletableFuture().get()

        completedRecurrence.bills.size shouldBe total
        recurrenceRepository.find(recurrence.id, recurrence.walletId) shouldBe completedRecurrence
        val bills = billRepository.findByWallet(recurrence.walletId)
        bills.size shouldBe total

        verify {
            billInstrumentationService.createdRecurring(recurrence)
        }
    }

    @Test
    fun `deve criar recorrência em qualquer dia do mês`() {
        val monthsToSubtract: Long = 0
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.MONTHLY,
                startDate = fakeLimitDate.minusMonths(monthsToSubtract).withDayOfMonth(28),
            )

        val completedRecurrence =
            recurrenceService.create(recurrence, false).getOrElse {
                fail(it.message)
            }

        val expectedCreatedBillsSize = 1

        completedRecurrence.recurrence.bills.size shouldBe expectedCreatedBillsSize
        recurrenceRepository.find(recurrence.id, recurrence.walletId).rule shouldBe completedRecurrence.recurrence.rule

        val bills = billRepository.findByWallet(recurrence.walletId)
        bills.size shouldBe expectedCreatedBillsSize
        bills.forEachIndexed { index, it ->
            it.amount shouldBe recurrence.amount
            it.dueDate shouldBe recurrence.rule.startDate.plusMonths(index.toLong())
            it.billDescription shouldBe recurrence.description
            it.walletId.value shouldBe recurrence.walletId.value
            it.recipient?.pixKeyDetails shouldBe null
            it.recipient?.name shouldBe recurrence.recipientName
            it.recipient?.alias shouldBe recurrence.recipientAlias
            it.recipient?.document shouldBe recurrence.recipientDocument
            it.recipient?.bankAccount shouldBe recurrence.recipientBankAccount
            it.source shouldBe
                ActionSource.WalletRecurrence(
                    recurrenceId = recurrence.id,
                    accountId = walletFixture.founder.accountId,
                )
        }

        verify {
            billInstrumentationService.createdRecurring(completedRecurrence.recurrence)
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [1, 2, 3, 13])
    fun `should create monthly recurrence with any number of bills`(expectedCreatedBillsSize: Int) {
        val monthsToSubtract: Long = expectedCreatedBillsSize - 1L
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.MONTHLY,
                startDate = fakeLimitDate.minusMonths(monthsToSubtract).withDayOfMonth(28),
            )

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                .toCompletableFuture().get()

        completedRecurrence.bills.size shouldBe expectedCreatedBillsSize
        recurrenceRepository.find(recurrence.id, recurrence.walletId) shouldBe completedRecurrence

        val bills = billRepository.findByWallet(recurrence.walletId)
        bills.size shouldBe expectedCreatedBillsSize
        bills.forEachIndexed { index, it ->
            it.amount shouldBe recurrence.amount
            it.dueDate shouldBe recurrence.rule.startDate.plusMonths(index.toLong())
            it.billDescription shouldBe recurrence.description
            it.walletId.value shouldBe recurrence.walletId.value
            it.recipient?.pixKeyDetails shouldBe null
            it.recipient?.name shouldBe recurrence.recipientName
            it.recipient?.alias shouldBe recurrence.recipientAlias
            it.recipient?.document shouldBe recurrence.recipientDocument
            it.recipient?.bankAccount shouldBe recurrence.recipientBankAccount
            it.source shouldBe
                ActionSource.WalletRecurrence(
                    recurrenceId = recurrence.id,
                    accountId = walletFixture.founder.accountId,
                )
        }

        verify {
            billInstrumentationService.createdRecurring(recurrence)
        }
    }

    @Test
    fun `should create pix recurrence with pix key`() {
        val expectedCreatedBillsSize = 10
        val weeksToSubtract = expectedCreatedBillsSize - 1L
        val recurrence =
            weeklyRecurrenceNoEndDate.copy(
                recipientBankAccount = null,
                recipientPixKey = pixKeyDetails.key,
                rule =
                weeklyRecurrenceNoEndDate.rule.copy(
                    startDate = fakeLimitDate.minusWeeks(weeksToSubtract),
                ),
                billType = BillType.PIX,
            )

        val completedRecurrence =
            recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                .toCompletableFuture().get()

        val bills = billRepository.findByWallet(recurrence.walletId)

        bills.size shouldBe expectedCreatedBillsSize
        bills.forEachIndexed { index, it ->
            it.billType shouldBe BillType.PIX
            it.amount shouldBe recurrence.amount
            it.dueDate shouldBe recurrence.rule.startDate.plusWeeks(index.toLong())
            it.billDescription shouldBe recurrence.description
            it.walletId.value shouldBe recurrence.walletId.value
            it.recipient?.name shouldBe pixKeyDetails.owner.name
            it.source shouldBe
                ActionSource.WalletRecurrence(
                    recurrenceId = recurrence.id,
                    accountId = walletFixture.founder.accountId,
                )
        }

        val databaseRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)
        databaseRecurrence shouldBe completedRecurrence

        with(recipientDbRepository.findByAccountId(recurrence.actionSource.accountId!!)) {
            this shouldHaveSize 1
            completedRecurrence.contactId shouldBe first().id
        }

        verify {
            billInstrumentationService.createdRecurring(recurrence)
        }
    }

    @Test
    fun `should create pix recurrence with pix key account`() {
        val isbp = "123"
        withPixParticipants(FinancialInstitution(name = "payee mock institution", ispb = isbp, compe = null)) {
            val expectedCreatedBillsSize = 10
            val weeksToSubtract = expectedCreatedBillsSize - 1L
            val recurrence =
                weeklyRecurrenceNoEndDate.copy(
                    recipientBankAccount = weeklyRecurrenceNoEndDate.recipientBankAccount!!.copy(ispb = isbp),
                    rule =
                    weeklyRecurrenceNoEndDate.rule.copy(
                        startDate = fakeLimitDate.minusWeeks(weeksToSubtract),
                    ),
                    billType = BillType.PIX,
                )

            val completedRecurrence =
                recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                    .toCompletableFuture().get()

            val bills = billRepository.findByWallet(recurrence.walletId)

            bills.size shouldBe expectedCreatedBillsSize
            bills.forEachIndexed { index, it ->
                it.billType shouldBe BillType.PIX
                it.amount shouldBe recurrence.amount
                it.dueDate shouldBe recurrence.rule.startDate.plusWeeks(index.toLong())
                it.billDescription shouldBe recurrence.description
                it.walletId.value shouldBe recurrence.walletId.value
                it.recurrenceRule shouldBe recurrence.rule
                it.recipient?.bankAccount shouldBe recurrence.recipientBankAccount
                it.source shouldBe
                    ActionSource.WalletRecurrence(
                        recurrenceId = recurrence.id,
                        accountId = walletFixture.founder.accountId,
                    )
            }
            verify(exactly = expectedCreatedBillsSize) { eventPublisher.publish(ofType(BillAdded::class), any()) }
            val databaseRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)
            databaseRecurrence shouldBe completedRecurrence

            verify {
                billInstrumentationService.createdRecurring(recurrence)
            }
        }
    }

    @Nested
    @DisplayName("assinatura Friday")
    inner class Subscription {
        @Test
        fun `deve agendar toda as contas recorrentes de assinatura friday`() {
            val isbp = "123"
            withPixParticipants(FinancialInstitution(name = "payee mock institution", ispb = isbp, compe = null)) {
                val expectedCreatedBillsSize = 10
                val weeksToSubtract = expectedCreatedBillsSize - 1L
                val recurrence =
                    weeklyRecurrenceNoEndDate.copy(
                        recipientBankAccount = weeklyRecurrenceNoEndDate.recipientBankAccount!!.copy(ispb = isbp),
                        rule =
                        weeklyRecurrenceNoEndDate.rule.copy(
                            startDate = fakeLimitDate.minusWeeks(weeksToSubtract),
                        ),
                        billType = BillType.PIX,
                        actionSource =
                        ActionSource.Subscription(
                            accountId = walletFixture.founder.accountId,
                        ),
                    )

                recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
                    .toCompletableFuture().get()

                val bills = billRepository.findByWallet(recurrence.walletId)

                bills.size shouldBe expectedCreatedBillsSize
                bills.forEach {
                    it.schedule shouldNotBe null
                }

                val listScheduleBill = mutableListOf<ScheduledBill>()

                verify {
                    paymentSchedulingService.schedule(capture(listScheduleBill))
                }

                listScheduleBill.size shouldBe expectedCreatedBillsSize
                listScheduleBill.forEach {
                    it.expires shouldBe false
                }
            }
        }

        @Test
        fun `não deve criar contato para assinatura Friday`() {
            val isbp = "123"
            withPixParticipants(FinancialInstitution(name = "payee mock institution", ispb = isbp, compe = null)) {
                val expectedCreatedBillsSize = 10
                val weeksToSubtract = expectedCreatedBillsSize - 1L
                val recurrence =
                    weeklyRecurrenceNoEndDate.copy(
                        recipientBankAccount = weeklyRecurrenceNoEndDate.recipientBankAccount!!.copy(ispb = isbp),
                        rule =
                        weeklyRecurrenceNoEndDate.rule.copy(
                            startDate = fakeLimitDate.minusWeeks(weeksToSubtract).withDayOfMonth(28),
                        ),
                        billType = BillType.PIX,
                        actionSource =
                        ActionSource.Subscription(
                            accountId = walletFixture.founder.accountId,
                        ),
                    )

                val updatedRecurrence =
                    recurrenceService.doCreate(
                        recurrence,
                        recurrence.rule.generateDateSequence(limitDate = fakeLimitDate),
                    )
                        .toCompletableFuture().get()

                updatedRecurrence.bills.forEach {
                    val bill = billEventRepository.getBillById(it).getOrNull()!!
                    bill.contactId shouldBe null
                }
            }
        }

        @Test
        fun `não deve criar contato para assinatura Friday com chave pix`() {
            val isbp = "123"
            withPixParticipants(FinancialInstitution(name = "payee mock institution", ispb = isbp, compe = null)) {
                val expectedCreatedBillsSize = 10
                val weeksToSubtract = expectedCreatedBillsSize - 1L
                val recurrence =
                    weeklyRecurrenceNoEndDate.copy(
                        recipientBankAccount = null,
                        recipientPixKey = PixKey("***********", type = PixKeyType.CPF),
                        rule =
                        weeklyRecurrenceNoEndDate.rule.copy(
                            startDate = fakeLimitDate.minusWeeks(weeksToSubtract).withDayOfMonth(28),
                        ),
                        billType = BillType.PIX,
                        actionSource =
                        ActionSource.Subscription(
                            accountId = walletFixture.founder.accountId,
                        ),
                    )

                val updatedRecurrence =
                    recurrenceService.doCreate(
                        recurrence,
                        recurrence.rule.generateDateSequence(limitDate = fakeLimitDate),
                    )
                        .toCompletableFuture().get()

                updatedRecurrence.bills.forEach {
                    val bill = billEventRepository.getBillById(it).getOrNull()!!
                    bill.contactId shouldBe null
                }
            }
        }

        @Nested
        @DisplayName("abonar uma mensalidade de assinatura")
        inner class IgnorSubscriptionFee {
            @Test
            fun `deve retornar SubscriptionFeeNotFound quando nao encontra uma cobranca na data informada`() {
                recurrenceRepository.save(weeklyWalletRecurrenceNoEndDate)

                val result =
                    recurrenceService.ignoreSubscriptionFee(
                        weeklyWalletRecurrenceNoEndDate.id,
                        weeklyWalletRecurrenceNoEndDate.walletId,
                        LocalDate.now(),
                    )

                result shouldBe BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.BillNotFound
            }

            @Test
            fun `deve retornar SubscriptionFeeProcessing quando esta processando o pagamento da cobranca na data informada`() {
                val dueDate = LocalDate.now()

                val walletId = weeklyWalletRecurrenceNoEndDate.walletId
                val billId = billAdded.billId

                billEventRepository.save(billAdded.copy(dueDate = dueDate, walletId = walletId))
                billEventRepository.save(billPaymentStart.copy(walletId = walletId))
                recurrenceRepository.save(weeklyWalletRecurrenceNoEndDate.copy(bills = listOf(billId)))

                val result =
                    recurrenceService.ignoreSubscriptionFee(
                        weeklyWalletRecurrenceNoEndDate.id,
                        walletId,
                        LocalDate.now(),
                    )

                result shouldBe BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.BillProcessing
            }

            @Test
            fun `deve retornar SubscriptionFeeAlreadyPaid quando a cobranca da data informada ja esta paga`() {
                val dueDate = LocalDate.now()

                val billId = billAdded.billId
                val walletId = weeklyWalletRecurrenceNoEndDate.walletId

                billEventRepository.save(
                    billAdded.copy(
                        dueDate = dueDate,
                        walletId = walletId,
                    ),
                )
                billEventRepository.save(billPaid.copy(walletId = walletId))
                recurrenceRepository.save(weeklyWalletRecurrenceNoEndDate.copy(bills = listOf(billId)))

                val result =
                    recurrenceService.ignoreSubscriptionFee(
                        weeklyWalletRecurrenceNoEndDate.id,
                        walletId,
                        dueDate,
                    )

                result shouldBe BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.BillAlreadyPaid
            }

            @Test
            fun `deve retornar Success quando consegue ignorar a cobranca da data informada`() {
                val dueDate = LocalDate.now()
                val billId = billAdded.billId
                val walletId = weeklyWalletRecurrenceNoEndDate.walletId
                val billEvent = billAdded.copy(dueDate = dueDate, walletId = walletId)

                billEventRepository.save(billEvent)
                billEventRepository.save(billPaymentScheduled)
                billRepository.save(Bill.build(billEvent, billPaymentScheduled))
                recurrenceRepository.save(weeklyWalletRecurrenceNoEndDate.copy(bills = listOf(billId)))

                val result =
                    recurrenceService.ignoreSubscriptionFee(
                        weeklyWalletRecurrenceNoEndDate.id,
                        walletId,
                        dueDate,
                    )

                result shouldBe BillRecurrenceService.IgnoreSubscriptionRecurrenceResult.Success

                assertThrows<IllegalStateException> {
                    billRepository.findBill(billId = billId, walletId = walletId)
                }
            }
        }

        @Nested
        @DisplayName("cancelamento de recorrencia de assinatura")
        inner class CancelSubscription {
            @Test
            fun `deve retornar erro caso a recorrência não exista`() {
                val ignoreSubscription =
                    recurrenceService.cancelSubscription(RecurrenceId(RECURRENCE_ID), ACCOUNT.defaultWalletId())

                ignoreSubscription shouldBe CancelSubscriptionResult.RecurrenceNotFound
            }

            @Test
            fun `deve retornar erro quando a recorrência não for de uma assinatura Friday`() {
                recurrenceRepository.save(weeklyRecurrenceNoEndDate)

                val result =
                    recurrenceService.cancelSubscription(weeklyRecurrenceNoEndDate.id, ACCOUNT.defaultWalletId())

                result shouldBe CancelSubscriptionResult.RecurrenceInvalid
            }

            @Test
            fun `deve retornar erro caso alguma conta esteja processando`() {
                recurrenceRepository.save(
                    weeklyRecurrenceNoEndDate.copy(
                        bills = listOf(billAdded.billId),
                        actionSource = ActionSource.Subscription(ACCOUNT.accountId),
                        walletId = billAdded.walletId,
                    ),
                )

                billEventRepository.save(billAdded)
                billEventRepository.save(billPaymentStart)
                billRepository.save(Bill.build(billAdded, billPaymentStart))

                val result =
                    recurrenceService.cancelSubscription(
                        weeklyRecurrenceNoEndDate.id,
                        billAdded.walletId,
                    )

                result shouldBe CancelSubscriptionResult.BillProcessing
            }

            @Test
            fun `deve retornar sucesso para uma recorrencia que ja esta ignorada`() {
                recurrenceRepository.save(weeklyRecurrenceNoEndDate.copy(status = RecurrenceStatus.IGNORED))

                val result =
                    recurrenceService.cancelSubscription(weeklyRecurrenceNoEndDate.id, ACCOUNT.defaultWalletId())

                result shouldBe CancelSubscriptionResult.Success
            }

            @Test
            fun `deve deletar as contas e ignorar a recorrencia caso so tenha contas ativas e retornar sucesso`() {
                val createdBill =
                    billAdded.copy(
                        actionSource =
                        ActionSource.SubscriptionRecurrence(
                            ACCOUNT.accountId,
                            weeklyRecurrenceNoEndDate.id,
                        ),
                    )

                recurrenceRepository.save(
                    weeklyRecurrenceNoEndDate.copy(
                        bills = listOf(createdBill.billId),
                        actionSource = ActionSource.Subscription(ACCOUNT.accountId),
                        walletId = createdBill.walletId,
                    ),
                )

                billEventRepository.save(createdBill)
                billEventRepository.save(billPaymentScheduled)
                billRepository.save(Bill.build(createdBill))

                val result =
                    recurrenceService.cancelSubscription(
                        weeklyRecurrenceNoEndDate.id,
                        createdBill.walletId,
                    )

                result shouldBe CancelSubscriptionResult.Success

                val recurrence = recurrenceRepository.find(weeklyRecurrenceNoEndDate.id, createdBill.walletId)
                recurrence.status shouldBe RecurrenceStatus.IGNORED

                assertThrows<IllegalStateException> {
                    billRepository.findBill(createdBill.billId, createdBill.walletId)
                }

                val slot = mutableListOf<BillEvent>()

                verify {
                    eventPublisher.publish(capture(slot), any())
                }

                slot.forEach {
                    it.billId shouldBe createdBill.billId
                }

                val canceledEvent = slot.single { it.eventType == BillEventType.PAYMENT_SCHEDULE_CANCELED }

                canceledEvent.shouldBeTypeOf<BillPaymentScheduleCanceled>()
                canceledEvent.reason shouldBe ScheduleCanceledReason.SUBSCRIPTION_CANCELED
            }

            @Test
            fun `caso tenha contas pagas, deve ignorar a partir da primeira em aberto e retornar ok`() {
                val createdBill =
                    billAdded.copy(
                        actionSource =
                        ActionSource.SubscriptionRecurrence(
                            ACCOUNT.accountId,
                            weeklyRecurrenceNoEndDate.id,
                        ),
                        billId = BillId(BILL_ID),
                        barcode = BarCode.ofDigitable(GOVERNMENT_BARCODE_LINE),
                    )

                val createdBillPaid =
                    billAdded.copy(
                        actionSource =
                        ActionSource.SubscriptionRecurrence(
                            ACCOUNT.accountId,
                            weeklyRecurrenceNoEndDate.id,
                        ),
                    )

                recurrenceRepository.save(
                    weeklyRecurrenceNoEndDate.copy(
                        bills = listOf(createdBillPaid.billId, createdBill.billId),
                        actionSource = ActionSource.Subscription(ACCOUNT.accountId),
                        walletId = createdBill.walletId,
                    ),
                )

                billEventRepository.save(createdBill)
                billEventRepository.save(billPaymentScheduled.copy(billId = createdBill.billId))
                billRepository.save(Bill.build(createdBill))

                billEventRepository.save(createdBillPaid)
                billEventRepository.save(billPaid)
                billRepository.save(Bill.build(createdBillPaid, billPaid))

                val result =
                    recurrenceService.cancelSubscription(
                        weeklyRecurrenceNoEndDate.id,
                        createdBill.walletId,
                    )

                result shouldBe CancelSubscriptionResult.Success

                val recurrence = recurrenceRepository.find(weeklyRecurrenceNoEndDate.id, createdBill.walletId)
                recurrence.rule.endDate shouldNotBe null

                assertThrows<IllegalStateException> {
                    billRepository.findBill(createdBill.billId, createdBill.walletId)
                }

                val slot = mutableListOf<BillEvent>()

                verify {
                    eventPublisher.publish(capture(slot), any())
                }

                val canceledEvent = slot.single { it.eventType == BillEventType.PAYMENT_SCHEDULE_CANCELED }

                canceledEvent.shouldBeTypeOf<BillPaymentScheduleCanceled>()
                canceledEvent.reason shouldBe ScheduleCanceledReason.SUBSCRIPTION_CANCELED
            }

            @Test
            fun `caso tenha contas vencidas, deve manter a primera conta vencida e retornar ok`() {
                val walletId = pixAdded.walletId
                val paidBillAdded =
                    pixAdded.copy(
                        actionSource =
                        ActionSource.SubscriptionRecurrence(
                            ACCOUNT.accountId,
                            weeklyRecurrenceNoEndDate.id,
                        ),
                        dueDate = LocalDate.now().minusMonths(1),
                        effectiveDueDate = LocalDate.now().minusMonths(1),
                        billId = BillId(BILL_ID),
                    )

                val overdueBillAdded =
                    pixAdded.copy(
                        actionSource =
                        ActionSource.SubscriptionRecurrence(
                            ACCOUNT.accountId,
                            weeklyRecurrenceNoEndDate.id,
                        ),
                        dueDate = LocalDate.now().minusDays(8),
                        effectiveDueDate = LocalDate.now().minusDays(8),
                        billId = BillId(BILL_ID_2),
                    )

                val activeBillAdded =
                    pixAdded.copy(
                        actionSource =
                        ActionSource.SubscriptionRecurrence(
                            ACCOUNT.accountId,
                            weeklyRecurrenceNoEndDate.id,
                        ),
                        dueDate = LocalDate.now().plusMonths(1),
                        effectiveDueDate = LocalDate.now().plusMonths(1),
                        billId = BillId(BILL_ID_3),
                    )

                recurrenceRepository.save(
                    weeklyRecurrenceNoEndDate.copy(
                        bills = listOf(paidBillAdded.billId, overdueBillAdded.billId, activeBillAdded.billId),
                        actionSource = ActionSource.Subscription(ACCOUNT.accountId),
                        walletId = walletId,
                    ),
                )

                saveBill(
                    paidBillAdded,
                    billPaymentScheduled.copy(billId = paidBillAdded.billId, created = paidBillAdded.created + 1),
                    billPaid.copy(billId = paidBillAdded.billId, created = paidBillAdded.created + 2),
                )
                saveBill(
                    overdueBillAdded,
                    billPaymentScheduled.copy(billId = overdueBillAdded.billId, created = overdueBillAdded.created + 1),
                )
                saveBill(
                    activeBillAdded,
                    billPaymentScheduled.copy(billId = activeBillAdded.billId, created = activeBillAdded.created + 1),
                )

                val result =
                    recurrenceService.cancelSubscription(
                        weeklyRecurrenceNoEndDate.id,
                        walletId,
                    )

                result shouldBe CancelSubscriptionResult.Success

                val recurrence =
                    recurrenceRepository.find(weeklyRecurrenceNoEndDate.id, walletId)
                recurrence.rule.endDate shouldNotBe null

                assertThrows<IllegalStateException> {
                    billRepository.findBill(activeBillAdded.billId, walletId)
                }

                val slot = mutableListOf<BillEvent>()
                verify {
                    eventPublisher.publish(capture(slot), any())
                }

                val canceledEvent = slot.single { it.eventType == BillEventType.PAYMENT_SCHEDULE_CANCELED }

                canceledEvent.shouldBeTypeOf<BillPaymentScheduleCanceled>()
                canceledEvent.billId shouldBe activeBillAdded.billId
                canceledEvent.reason shouldBe ScheduleCanceledReason.SUBSCRIPTION_CANCELED

                val overdueBill: BillView = billRepository.findBill(overdueBillAdded.billId, walletId)

                overdueBill.status shouldBe BillStatus.ACTIVE
            }
        }

        private fun saveBill(vararg billEvents: BillEvent) {
            billEvents.forEach {
                billEventRepository.save(it)
            }
            billRepository.save(Bill.build(*billEvents))
        }
    }

    @Test
    fun `should return error when ignoring a non recurrent bill`() {
        val activeBill = getActiveBill(wallet.id)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(dynamoDB, billAdded.copy(walletId = wallet.id))

        val result =
            recurrenceService.ignore(
                walletId = wallet.id,
                member = wallet.founder,
                billId = activeBill.billId,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            )

        result.isLeft() shouldBe true
        result.mapLeft {
            it shouldBe IgnoreRecurrenceErrors.BillIsNotRecurring
        }
    }

    @Test
    fun `should return error when ignoring a not found bill`() {
        val result =
            recurrenceService.ignore(
                walletId = wallet.id,
                member = wallet.founder,
                billId = BillId(UUID.randomUUID().toString()),
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            )
        result.isLeft() shouldBe true
        result.mapLeft {
            it shouldBe IgnoreRecurrenceErrors.BillNotFound
        }
    }

    @ParameterizedTest
    @MethodSource("notActiveStatusAndEventArguments")
    fun `should return error when bill is not ignorable`(
        billStatus: BillStatus,
        billEvent: BillEvent,
    ) {
        val (_, currentBill) =
            addBillOnRecurrence(
                buildRecurrence(startDate = fakeLimitDate),
                fakeLimitDate,
                billStatus,
                billEvent,
            )

        val result =
            recurrenceService.ignore(
                walletId = wallet.id,
                member = wallet.founder,
                billId = currentBill.billId,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            )
        result.isLeft() shouldBe true
        result.mapLeft {
            it shouldBe IgnoreRecurrenceErrors.BillIsNotIgnorable
        }
    }

    @Test
    fun `should ignore bill from recurrence`() {
        val (recurrence, currentBill) = addBillOnRecurrence(buildRecurrence(startDate = fakeLimitDate), fakeLimitDate)

        val result = withBlockingAsyncCalls {
            recurrenceService.ignore(
                walletId = wallet.id,
                member = wallet.founder,
                billId = currentBill.billId,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            )
        }

        result.isRight() shouldBe true
        with(recurrenceRepository.find(recurrence.id, recurrence.walletId)) {
            bills.size shouldBe 0
            rule.endDate shouldBe null
            status shouldBe RecurrenceStatus.IGNORED
        }
        currentBill.billId shouldHaveStatus BillStatus.IGNORED
        assertThrows<IllegalStateException> {
            billRepository.findBill(
                currentBill.billId,
                recurrence.walletId,
            )
        }
    }

    @Test
    fun `should ignore all bills from recurrence from a starting bill`() {
        val startingDate = fakeLimitDate.minusWeeks(3)
        val (recurrence0, firstBill) = addBillOnRecurrence(buildRecurrence(startDate = startingDate), startingDate)
        val (recurrence1, secondBill) = addBillOnRecurrence(recurrence0, fakeLimitDate.minusWeeks(2))
        val (recurrence2, thirdBill) = addBillOnRecurrence(recurrence1, fakeLimitDate.minusWeeks(1))
        val (recurrence, fourthBill) = addBillOnRecurrence(recurrence2, fakeLimitDate)

        val billtoIgnore = billEventRepository.getBillById(thirdBill.billId).getOrNull()!!

        val result =
            recurrenceService.doIgnore(
                billtoIgnore,
                ActionSource.Webapp(role = Role.OWNER),
            ).toCompletableFuture().get()

        with(result) {
            status shouldBe RecurrenceStatus.ACTIVE
            bills.size shouldBe 2
            rule.endDate shouldBe secondBill.dueDate
            this shouldBe recurrenceRepository.find(recurrence.id, wallet.id)
            allBillsShouldBeActive()
        }

        listOf(firstBill.billId, secondBill.billId).forEach {
            with(getBillFromDB(it)) {
                this.status shouldBe BillStatus.ACTIVE
                this.recurrenceRule!!.endDate shouldBe secondBill.dueDate
            }
        }

        listOf(thirdBill.billId, fourthBill.billId).forEach {
            with(it) { this shouldHaveStatus BillStatus.IGNORED }
            assertThrows<IllegalStateException> {
                billRepository.findBill(
                    it,
                    recurrence.walletId,
                )
            }
        }
    }

    @ParameterizedTest
    @MethodSource("notActiveStatusAndEventArguments")
    fun `should not remove not active bill on recurrence ignore`(
        billStatus: BillStatus,
        billEvent: BillEvent,
    ) {
        val startingDate = fakeLimitDate.minusWeeks(2)
        val (recurrence0, firstBill) = addBillOnRecurrence(buildRecurrence(startDate = startingDate), startingDate)
        val (recurrence1, secondBill) =
            addBillOnRecurrence(
                recurrence0,
                fakeLimitDate.minusWeeks(1),
                billStatus,
                billEvent,
            )
        val (recurrence, thirdBill) = addBillOnRecurrence(recurrence1, fakeLimitDate)

        val billtoIgnore = billEventRepository.getBillById(firstBill.billId).getOrNull()!!

        val result =
            recurrenceService.doIgnore(
                billtoIgnore,
                ActionSource.Webapp(role = Role.OWNER),
            ).toCompletableFuture().get()

        with(result) {
            bills.size shouldBe 0
            rule.endDate shouldBe null
            status shouldBe RecurrenceStatus.IGNORED
            this shouldBe recurrenceRepository.find(recurrence.id, wallet.id)
        }
        firstBill.billId shouldHaveStatus BillStatus.IGNORED
        with(billRepository.findBill(secondBill.billId, recurrence.walletId)) {
            recurrenceRule shouldBe null
            status shouldBe billStatus
        }
        thirdBill.billId shouldHaveStatus BillStatus.IGNORED
    }

    @Test
    fun `should not create any bill when there is only ignored recurrence`() {
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLimitDate,
            ).copy(status = RecurrenceStatus.IGNORED)

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.bills.shouldBeEmpty()
    }

    @Test
    fun `should not create any bill when there is only active recurrence with endDate before lastLimitDate`() {
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLastLimitDate.minusDays(1),
                endDate = fakeLastLimitDate,
            )

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.bills.shouldBeEmpty()
    }

    @Test
    fun `should create bill when there is active recurrence with endDate after lastBillDueDate`() {
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLastLimitDate.minusDays(1),
                endDate = fakeLastLimitDate.plusWeeks(1),
            )
                .setLastBillDueDate(fakeLastLimitDate.minusDays(1))

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.bills.shouldNotBeEmpty()
        val bills = billRepository.findByWallet(recurrence.walletId)
        bills.size shouldBe 1
        bills.first().dueDate shouldBe recurrence.rule.startDate.plusWeeks(1)
        savedRecurrence.lastDueDate shouldBe bills.last().dueDate
    }

    @Test
    fun `deve salvar categoria nos pagamentos quando há uma categoria na recorrencia`() {
        val recurrence = buildRecurrence(
            frequency = RecurrenceFrequency.WEEKLY,
            startDate = fakeLastLimitDate.minusDays(1),
            endDate = fakeLastLimitDate.plusWeeks(1),
            billCategoryId = billCategory.categoryId,
        )
            .setLastBillDueDate(fakeLastLimitDate.minusDays(1))

        categoryDbRepository.save(
            WalletBillCategory(
                walletId = WalletId(WALLET_ID),
                categoryId = billCategory.categoryId,
                name = billCategory.name,
                icon = billCategory.icon,
                enabled = true,
                default = billCategory.default,
            ),
        )
        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.bills.forAll {
            val bill = billEventRepository.getBillById(it).getOrHandle { fail(it.message) }
            bill.categoryId.shouldNotBeNull()
        }
    }

    @Test
    fun `deve usar os dados da consulta de pix em uma recorrencia quando os dados são consultados com sucesso`() {
        every { pixKeyManagementMock.findKeyDetailsCacheable(any(), any()) } returns
            PixKeyDetailsResult(
                pixKeyDetails,
                "",
            )
        val billPixkeyDetails =
            PixKeyDetails(
                key =
                PixKey(
                    value = "<EMAIL>",
                    type = PixKeyType.EMAIL,
                ),
                holder =
                PixKeyHolder(
                    accountNo = BigInteger("0"),
                    accountDv = "1",
                    ispb = "123",
                    institutionName = "123",
                    accountType = AccountType.CHECKING,
                    routingNo = 12,
                ),
                owner = PixKeyOwner(name = "teste", document = "123123"),
            )
        billEventRepository.save(
            pixKeyAdded.copy(
                recipient =
                Recipient(
                    name = weeklyWalletRecurrenceNoEndDate.recipientName,
                    document = weeklyWalletRecurrenceNoEndDate.recipientDocument,
                    alias = weeklyWalletRecurrenceNoEndDate.recipientAlias,
                    pixKeyDetails = billPixkeyDetails,
                ),
            ),
        )

        val recurrence =
            weeklyWalletRecurrenceNoEndDate.copy(
                walletId = wallet.id,
                rule =
                RecurrenceRule(
                    frequency = RecurrenceFrequency.WEEKLY,
                    startDate = fakeLastLimitDate.minusDays(1),
                    endDate = fakeLastLimitDate.plusWeeks(1),
                ),
                recipientBankAccount = null,
                bills = listOf(pixKeyAdded.billId),
                recipientPixKey = billPixkeyDetails.key,
            ).setLastBillDueDate(fakeLastLimitDate.minusDays(1))

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.bills.shouldNotBeEmpty()

        val billId = savedRecurrence.bills.last()
        savedRecurrence.bills.shouldHaveAtLeastSize(2)
        val bill = billEventRepository.getBillById(billId).getOrElse { fail(it.message) }

        bill.recipient?.alias shouldBe recurrence.recipientAlias
        bill.recipient?.bankAccount.shouldBeNull()
        bill.recipient?.document shouldBe pixKeyDetails.owner.document
        bill.recipient?.name shouldBe pixKeyDetails.owner.name
        bill.recipient?.pixKeyDetails?.key shouldBe pixKeyDetails.key
    }

    @Test
    fun `deve usar os dados do último pagamento em uma recorrencia quando os dados não são consultados com sucesso`() {
        val lastCreatedBill =
            pixKeyAdded.copy(
                recipient =
                Recipient(
                    name = weeklyWalletRecurrenceNoEndDate.recipientName,
                    document = weeklyWalletRecurrenceNoEndDate.recipientDocument,
                    alias = weeklyWalletRecurrenceNoEndDate.recipientAlias,
                    pixKeyDetails =
                    pixKeyDetails.copy(
                        key =
                        PixKey(
                            value = "<EMAIL>",
                            type = PixKeyType.EMAIL,
                        ),
                    ),
                ),
            )
        billEventRepository.save(lastCreatedBill)
        every {
            pixKeyManagementMock.findKeyDetailsCacheable(
                any(),
                any(),
            )
        } throws PixKeyException(PixKeyError.UnknownError)

        val recurrence =
            weeklyWalletRecurrenceNoEndDate.copy(
                walletId = wallet.id,
                rule =
                RecurrenceRule(
                    frequency = RecurrenceFrequency.WEEKLY,
                    startDate = fakeLastLimitDate.minusDays(1),
                    endDate = fakeLastLimitDate.plusWeeks(1),
                ),
                recipientBankAccount = null,
                bills = listOf(pixKeyAdded.billId),
                recipientPixKey = pixKeyDetails.key,
            ).setLastBillDueDate(fakeLastLimitDate.minusDays(1))

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.bills.shouldNotBeEmpty()
        savedRecurrence.bills.shouldHaveAtLeastSize(2)

        val billId = savedRecurrence.bills.last()
        val bill = billEventRepository.getBillById(billId).getOrElse { fail(it.message) }

        bill.recipient?.alias shouldBe recurrence.recipientAlias
        bill.recipient?.bankAccount.shouldBeNull()
        bill.recipient?.document shouldBe lastCreatedBill.recipient?.pixKeyDetails?.owner?.document
        bill.recipient?.name shouldBe lastCreatedBill.recipient?.pixKeyDetails?.owner?.name
        bill.recipient?.pixKeyDetails?.key shouldBe recurrence.recipientPixKey
    }

    @Test
    fun `should not create bill when there is active recurrence with endDate after lastLimitDate but bill was already created`() {
        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLastLimitDate.minusDays(1),
                endDate = fakeLastLimitDate.plusWeeks(1),
            )
        recurrenceRepository.save(recurrence)
        val (_, firstBill) = addBillOnRecurrence(recurrence, recurrence.rule.startDate.plusWeeks(1))

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.bills.shouldNotBeEmpty()
        val bills = billRepository.findByWallet(recurrence.walletId)
        bills.size shouldBe 1
        with(bills.first()) {
            dueDate shouldBe firstBill.dueDate
            billId shouldBe firstBill.billId
        }
    }

    @ParameterizedTest
    @EnumSource(AccountStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE"])
    fun `não deve considerar uma recorrência ativa de uma conta que não está ativa`(accountStatus: AccountStatus) {
        every {
            accountRepository.findById(walletFixture.founderAccount.accountId)
        } returns walletFixture.founderAccount.copy(status = accountStatus)

        val recurrence =
            buildRecurrence(
                frequency = RecurrenceFrequency.WEEKLY,
                startDate = fakeLastLimitDate.minusDays(1),
                endDate = fakeLastLimitDate.plusWeeks(1),
            )
                .setLastBillDueDate(fakeLastLimitDate.minusDays(1))

        recurrenceRepository.save(recurrence)

        recurrenceService.continueRecurrences()

        val savedRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)

        savedRecurrence.bills.shouldBeEmpty()
        val bills = billRepository.findByWallet(recurrence.walletId)
        bills.size shouldBe 0
    }

    private fun getBillFromDB(billId: BillId) =
        billEventRepository.getBillById(billId).getOrElse { throw NoStackTraceException("teste") }

    private infix fun BillId.shouldHaveStatus(billStatus: BillStatus) {
        getBillFromDB(this).status shouldBe billStatus
    }

    private fun BillRecurrence.allBillsShouldBeActive() {
        this.bills.forEach {
            val billOnDatabase =
                billRepository.findBill(
                    it,
                    this.walletId,
                )
            billOnDatabase.status shouldBe BillStatus.ACTIVE
        }
    }

    private fun addBillOnRecurrence(
        recurrence: BillRecurrence,
        recurrentDate: LocalDate,
        billStatus: BillStatus = BillStatus.ACTIVE,
        billEvent: BillEvent? = null,
    ): Pair<BillRecurrence, BillView> {
        val actionSource = ActionSource.Recurrence(recurrenceId = recurrence.id, role = Role.OWNER)
        val currentBill =
            BillView(
                walletId = recurrence.walletId,
                billType = BillType.INVOICE,
                billDescription = recurrence.description,
                amount = recurrence.amount,
                billId = billEvent?.let { it.billId } ?: BillId(),
                recipient = Recipient(recurrence.recipientName),
                paymentLimitTime = LocalTime.MIDNIGHT,
                createdOn = CREATED_ON,
                status = billStatus,
                dueDate = recurrentDate,
                effectiveDueDate = recurrentDate,
                source = actionSource,
                recurrenceRule = recurrence.rule,
                subscriptionFee = false,
                tags = emptySet(),
                categoryId = null,
                categorySuggestions = listOf(),
            )

        val billAdded =
            BillAdded(
                billId = currentBill.billId,
                created = getZonedDateTime().minusDays(1).toInstant().toEpochMilli(),
                walletId = recurrence.walletId,
                description = recurrence.description,
                dueDate = recurrentDate,
                effectiveDueDate = recurrentDate,
                amount = recurrence.amount,
                billType = BillType.INVOICE,
                recipient =
                Recipient(
                    name = recurrence.recipientName,
                    document = recurrence.recipientDocument,
                    alias = recurrence.recipientAlias,
                    bankAccount = recurrence.recipientBankAccount,
                ),
                document = "***********",
                paymentLimitTime = LocalTime.parse("17:00", timeFormat),
                actionSource = actionSource,
                recurrenceRule = recurrence.rule,
            )

        billEventRepository.save(billAdded)
        addBillIntoDb(dynamoDbDAO, currentBill)

        if (billEvent != null) {
            billEventRepository.save(billEvent)
        }

        val recurrenceNew = recurrence.plusBillId(billAdded.billId)
        recurrenceRepository.save(recurrenceNew)

        return Pair(recurrenceNew, currentBill)
    }

    private fun buildRecurrence(
        frequency: RecurrenceFrequency = RecurrenceFrequency.WEEKLY,
        startDate: LocalDate,
        endDate: LocalDate? = null,
        billCategoryId: PFMCategoryId? = null,
    ): BillRecurrence {
        return weeklyRecurrenceNoEndDate.copy(
            billCategoryId = billCategoryId,
            rule = RecurrenceRule(frequency = frequency, startDate = startDate, endDate = endDate),
            walletId = wallet.id,
        )
    }

    companion object {
        @JvmStatic
        fun notActiveStatusAndEventArguments(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    BillStatus.PROCESSING,
                    PaymentStarted(
                        walletId = WalletId(WALLET_ID),
                        created = getZonedDateTime().toInstant().toEpochMilli(),
                        billId = BillId(UUID.randomUUID().toString()),
                        transactionId = transactionId,
                        actionSource = ActionSource.System,
                        correlationId = CORRELATION_ID,
                    ),
                ),
                Arguments.arguments(
                    BillStatus.PAID,
                    BillPaid(
                        walletId = WalletId(WALLET_ID),
                        created = getZonedDateTime().toInstant().toEpochMilli(),
                        billId = BillId(UUID.randomUUID().toString()),
                        transactionId = transactionId,
                        actionSource = ActionSource.System,
                    ),
                ),
                Arguments.arguments(
                    BillStatus.IGNORED,
                    BillIgnored(
                        walletId = WalletId(WALLET_ID),
                        created = getZonedDateTime().toInstant().toEpochMilli(),
                        billId = BillId(UUID.randomUUID().toString()),
                        actionSource = ActionSource.System,
                    ),
                ),
                Arguments.arguments(
                    BillStatus.ACTIVE,
                    BillPaymentScheduled(
                        walletId = WalletId(WALLET_ID),
                        created = getZonedDateTime().toInstant().toEpochMilli(),
                        billId = BillId(UUID.randomUUID().toString()),
                        scheduledDate = LocalDate.parse("2020-12-31", dateFormat),
                        actionSource = ActionSource.System,
                        amount = 0,
                        paymentLimitTime = null,
                        infoData =
                        BillPaymentScheduledBalanceInfo(
                            amount = 0,
                            paymentMethodId = UUID.randomUUID().toString(),
                            calculationId = null,
                        ),
                        batchSchedulingId = BatchSchedulingId(),
                    ),
                ),
            )
        }
    }
}