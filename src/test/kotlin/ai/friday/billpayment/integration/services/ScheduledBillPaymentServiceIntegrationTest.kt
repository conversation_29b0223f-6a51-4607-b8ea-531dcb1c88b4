package ai.friday.billpayment.integration.services

import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InternalBankDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDBRepository
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.balance.DefaultBalanceService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduleStarted
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillPaymentScheduledBalanceInfo
import ai.friday.billpayment.app.bill.BillSchedulePostponed
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.schedule.CancelSchedulePaymentService
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.payment.BillNotPayableException
import ai.friday.billpayment.app.payment.BillPaymentCommand
import ai.friday.billpayment.app.payment.BillPaymentService
import ai.friday.billpayment.app.payment.DefaultScheduledBillPaymentService
import ai.friday.billpayment.app.payment.FridayScheduledBillPaymentServicePreProcessor
import ai.friday.billpayment.app.payment.MultiplePaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithExternalPayment
import ai.friday.billpayment.app.payment.PaymentNotScheduledException
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.DailyPaymentLimit
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.WalletPaymentLimits
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaymentFailedRetryable
import ai.friday.billpayment.billPaymentScheduleStarted
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.integration.BILL_ID_2
import ai.friday.billpayment.integration.BILL_ID_3
import ai.friday.billpayment.integration.CONCESSIONARIA_DIGITABLE_LINE
import ai.friday.billpayment.integration.ELECTRICITY_AND_GAS_BARCODE_LINE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_3
import ai.friday.billpayment.integration.PREFECTURE_BARCODE_LINE
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.omnibusBankAccountConfiguration
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.paymentMethodId2
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixPaymentScheduled
import ai.friday.billpayment.scheduledBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import arrow.core.right
import io.kotest.assertions.fail
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContainInOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.mockk.verifyOrder
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.UUID
import java.util.stream.Stream
import kotlin.math.max
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class ScheduledBillPaymentServiceIntegrationTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val scheduledBillRepository = spyk(ScheduledBillDBRepository(dynamoDbDAO))
    private val billPaymentServiceMock: BillPaymentService = mockk()
    private val bankAccountServiceMock: BankAccountService = mockk()

    private val scheduledWalletDBRepository = mockk<ScheduledWalletDBRepository>()

    private val paymentSchedulingService =
        spyk(PaymentSchedulingService(scheduledBillRepository, scheduledWalletDBRepository)) {
            every {
                cancelSchedule(any(), any(), any())
            } just Runs
        }

    private val billEventRepository = BillEventDBRepository(
        dynamoDbDAO,
        allFalseFeatureConfiguration,
    )
    private val billRepository = DynamoDbBillRepository(dynamoDbDAO)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val lockProvider: InternalLock = mockk(relaxed = true)

    private val cancelSchedulePaymentService = mockk<CancelSchedulePaymentService> {
        every { cancelScheduledPayment(any(), any(), any()) } returns true
        every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
    }

    val eventPublisher = mockk<EventPublisher>(relaxed = true)
    private val updateBillService = UpdateBillService(
        billEventRepository = billEventRepository,
        billRepository = billRepository,
        billTrackingRepository = mockk(relaxed = true),
        eventPublisher = eventPublisher,
        boletoSettlementService = mockk(),
        billValidationService = mockk(relaxed = true),
        lockProvider = mockk(relaxed = true),
        walletLimitsService = mockk(),
        walletService = mockk(),
        approveBillService = mockk(),
        denyBillService = mockk(),
        resolveScheduledBill = mockk(),
        cancelSchedulePaymentService = cancelSchedulePaymentService,
        mailboxWalletDataRepository = mockk(),
    ).apply {
        this.tedLimitTime = "17:00"
    }
    private val internalBankRepository = InternalBankDBRepository(dynamoDbDAO)
    val accountService = AccountService(
        accountConfigurationService = mockk(),
        accountRepository = AccountDbRepository(dynamoDbDAO = dynamoDbDAO),
        crmService = mockk(),
        chatBotMessagePublisher = mockk(),
        notificationAdapter = mockk(),
        walletRepository = mockk(),
    )

    val balanceService = DefaultBalanceService(
        accountService,
        internalBankRepository,
        bankAccountServiceMock,
        omnibusBankAccountConfiguration,
        walletService = mockk(),
    )

    private val walletRepository: WalletRepository = mockk()
    private val walletService: WalletService = mockk()

    private val walletLimitsService =
        spyk(
            WalletLimitsService(
                walletRepository = walletRepository,
                paymentSchedulingService = mockk(),
                billRepository = billRepository,
                accountService = mockk(),
                internalLock = lockProvider,
            ),
        )

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet(accountPaymentMethodId = paymentMethodId2)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadBalancePaymentMethod(
            accountRepository,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
        )
        billEventRepository.save(billAdded)
        billEventRepository.save(billPaymentScheduled)
        billEventRepository.save(
            billAdded.copy(
                billId = BillId(BILL_ID_2),
                barcode = BarCode.of(CONCESSIONARIA_DIGITABLE_LINE),
            ),
        )
        billEventRepository.save(billPaymentScheduled.copy(billId = BillId(BILL_ID_2)))
        billEventRepository.save(
            billAdded.copy(
                billId = BillId(BILL_ID_3),
                barcode = BarCode.of(FICHA_DE_COMPENSACAO_DIGITABLE_LINE),
            ),
        )
        billEventRepository.save(billPaymentScheduled.copy(billId = BillId(BILL_ID_3)))

        every { bankAccountServiceMock.getBalance(any()) } returns 15591L

        every { walletService.findWallet(any()) } returns wallet
        every { walletRepository.findWallet(any()) } returns wallet

        every { walletLimitsService.findWalletPaymentLimits(any()) } returns WalletPaymentLimits(
            daily = DailyPaymentLimit(amount = 100_000_00L, lastAmount = null, updatedAt = getZonedDateTime(), type = DailyPaymentLimitType.DAILY),
            nighttime = DailyPaymentLimit(amount = 1_000_00L, lastAmount = null, updatedAt = getZonedDateTime(), type = DailyPaymentLimitType.NIGHTTIME),
            monthly = null,
            whatsAppPayment = DailyPaymentLimit(amount = 100L, lastAmount = null, updatedAt = getZonedDateTime(), type = DailyPaymentLimitType.WHATSAPP_PAYMENT),
        ).right()
    }

    private val onePixPayService = mockk<OnePixPayService>(relaxed = true)

    private val scheduledBillPaymentService = DefaultScheduledBillPaymentService(
        billPaymentService = billPaymentServiceMock,
        balanceService = balanceService,
        paymentSchedulingService = paymentSchedulingService,
        updateBillService = updateBillService,
        walletService = walletService,
        walletLimitsService = walletLimitsService,
        lockProvider = lockProvider,
        walletLockProvider = mockk(relaxed = true),
        scheduledBillPaymentServicePreProcessor = FridayScheduledBillPaymentServicePreProcessor(onePixPayService = onePixPayService),
    )

    @Test
    fun `should not process scheduled bills when scheduled date bill is after today`() {
        scheduledBillRepository.save(scheduledBill)
        scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        verify(exactly = 0) { billPaymentServiceMock wasNot called }
    }

    @Test
    fun `should not process boleto nor ted before 8am`() {
        val pixScheduledId = BillId(UUID.randomUUID().toString())
        val concessionariaScheduledId = BillId(UUID.randomUUID().toString())
        val tedScheduledId = BillId(UUID.randomUUID().toString())

        val now = getZonedDateTime()

        every {
            billPaymentServiceMock.execute(any())
        } returns TransactionId("111")

        withGivenDateTime(now.withHour(2)) {
            createPixScheduledBill(pixScheduledId.value, 5_00, now.toLocalDate(), ScheduleTo.ASAP)
            createConcessionariaScheduledBill(concessionariaScheduledId.value, 1_00, now.toLocalDate(), ScheduleTo.ASAP)
            createTEDScheduledBill(tedScheduledId.value, 2_00, now.toLocalDate())
        }

        withGivenDateTime(now.withHour(3)) {
            scheduledBillPaymentService.process(walletId = scheduledBill.walletId)
        }

        val result = billEventRepository.getBillById(pixScheduledId)
        result.isRight() shouldBe true
        result.map {
            it.history.filterIsInstance<BillPaymentScheduleStarted>() shouldHaveSize 1
            it.history.last() shouldBe io.kotest.matchers.types.beOfType(BillPaymentScheduleStarted::class)
        }

        val result2 = billEventRepository.getBillById(concessionariaScheduledId)
        result2.isRight()
        result2.map {
            it.history.filterIsInstance<BillPaymentScheduleStarted>() shouldHaveSize 0
            it.history.last() shouldBe io.kotest.matchers.types.beOfType(BillPaymentScheduled::class)
        }

        val result3 = billEventRepository.getBillById(tedScheduledId)
        result3.isRight()
        result3.map {
            it.history.filterIsInstance<BillPaymentScheduleStarted>() shouldHaveSize 0
            it.history.last() shouldBe io.kotest.matchers.types.beOfType(BillPaymentScheduled::class)
        }

        val slot = slot<BillPaymentCommand>()

        verify(exactly = 1) {
            billPaymentServiceMock.execute(capture(slot))
        }

        slot.captured.billId shouldBe pixScheduledId
    }

    @Test
    fun `deve processar o OnePixPay antes de pegar as contas agendadas`() {
        every {
            billPaymentServiceMock.execute(
                BillPaymentCommand(
                    scheduledBill.walletId,
                    scheduledBill.billId,
                    ActionSource.Scheduled,
                    scheduledBill.paymentMethodsDetail,
                ),
            )
        } returns TransactionId("111")
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
            ),
        )

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        verifyOrder {
            onePixPayService.process(scheduledBill.walletId, null, null)
            scheduledBillRepository.findScheduledBillsByWalletIdAndUntilScheduledDate(any(), any())
        }
    }

    @Test
    fun `should pay bill when bill is scheduled to pay today`() {
        every {
            billPaymentServiceMock.execute(
                BillPaymentCommand(
                    scheduledBill.walletId,
                    scheduledBill.billId,
                    ActionSource.Scheduled,
                    scheduledBill.paymentMethodsDetail,
                ),
            )
        } returns TransactionId("111")
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
            ),
        )

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        val result = billEventRepository.getBillById(scheduledBill.billId)
        result.isRight() shouldBe true
        result.map {
            it.history.filterIsInstance<BillPaymentScheduleStarted>() shouldHaveSize 1
            it.history.last() shouldBe io.kotest.matchers.types.beOfType(BillPaymentScheduleStarted::class)
            it.history.filterIsInstance<BillSchedulePostponed>() shouldHaveSize 0
        }

        verify {
            billPaymentServiceMock.execute(
                BillPaymentCommand(
                    scheduledBill.walletId,
                    scheduledBill.billId,
                    ActionSource.Scheduled,
                    scheduledBill.paymentMethodsDetail,
                ),
            )
        }
    }

    @Test
    fun `should process bill but not publish BillPaymentScheduleStarted when it is already ongoing`() {
        every {
            billPaymentServiceMock.execute(
                BillPaymentCommand(
                    scheduledBill.walletId,
                    scheduledBill.billId,
                    ActionSource.Scheduled,
                    scheduledBill.paymentMethodsDetail,
                ),

            )
        } returns TransactionId("111")
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
            ),
        )
        billEventRepository.save(billPaymentScheduleStarted)

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        val result = billEventRepository.getBillById(scheduledBill.billId)
        result.isRight() shouldBe true
        result.map {
            it.history.filterIsInstance<BillPaymentScheduleStarted>() shouldHaveSize 1
            it.history.last() shouldBe io.kotest.matchers.types.beOfType(BillPaymentScheduleStarted::class)
        }

        verify {
            billPaymentServiceMock.execute(
                BillPaymentCommand(
                    scheduledBill.walletId,
                    scheduledBill.billId,
                    ActionSource.Scheduled,
                    scheduledBill.paymentMethodsDetail,
                ),

            )
        }
    }

    @Test
    fun `should do nothing when cannot find a valid payment method`() {
        every { walletRepository.findWallet(any()) } returns wallet

        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(
                    paymentMethodId = AccountPaymentMethodId(
                        PAYMENT_METHOD_ID_3,
                    ),
                    amount = scheduledBill.amount,
                ),
            ),
        )

        assertThrows<ItemNotFoundException> {
            val now = getZonedDateTime()
            withGivenDateTime(now.withHour(max(now.hour, 8))) {
                scheduledBillPaymentService.process(
                    scheduledBill.walletId,
                    getLocalDate(),
                )
            }
        }
        verify(exactly = 0) {
            billPaymentServiceMock.execute(
                any(),

            )
        }
    }

    @Test
    fun `should not pay any bill if has not enough balance`() {
        every {
            billPaymentServiceMock.execute(
                BillPaymentCommand(
                    scheduledBill.walletId,
                    scheduledBill.billId,
                    ActionSource.Scheduled,
                    scheduledBill.paymentMethodsDetail,
                ),

            )
        } returns TransactionId("111")
        every { bankAccountServiceMock.getBalance(any()) } returns 15500L
        val newScheduledBill = scheduledBill.copy(
            scheduledDate = getLocalDate(),
        )
        scheduledBillRepository.save(newScheduledBill)

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        val result = billEventRepository.getBillById(scheduledBill.billId)
        result.isRight() shouldBe true
        result.map {
            it.history.filterIsInstance<BillPaymentScheduleStarted>().shouldBeEmpty()
        }

        verify(exactly = 0) {
            billPaymentServiceMock.execute(any())
        }

        val billView = billRepository.findBill(scheduledBill.billId, scheduledBill.walletId)
        billView.schedule?.waitingFunds shouldBe true

        val response = billEventRepository.getBillById(scheduledBill.billId)
        response.isRight()
        response.map {
            it.history.filterIsInstance<BillSchedulePostponed>() shouldHaveSize 1
            it.history.last() shouldBe io.kotest.matchers.types.beOfType(BillSchedulePostponed::class)
        }
    }

    @Test
    fun `should pay other bill in case has balance for the second bill but not for the first one`() {
        withGivenDateTime(ZonedDateTime.of(LocalDate.of(2023, 7, 28), LocalTime.of(10, 0, 0), brazilTimeZone)) {
            every { billPaymentServiceMock.execute(any()) } returns TransactionId("111")
            every { bankAccountServiceMock.getBalance(any()) } returns 15500L

            scheduledBillRepository.save(
                scheduledBill.copy(
                    scheduledDate = getLocalDate(),
                ),
            )

            scheduledBillRepository.save(
                scheduledBill.copy(
                    billId = BillId(BILL_ID_2),
                    scheduledDate = getLocalDate(),
                    amount = 1L,
                    billType = BillType.PIX,
                    paymentMethodsDetail = createPaymentMethodsDetailWithBalance(paymentMethodId2, 1L),
                ),
            )
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())

            val slot = slot<BillPaymentCommand>()
            verify(exactly = 1) {
                billPaymentServiceMock.execute(capture(slot))
            }

            slot.captured.billId shouldBe BillId(BILL_ID_2)
        }
    }

    @Test
    fun `should pay first bill but not the second when not enough balance for both`() {
        every { billPaymentServiceMock.execute(any()) } returns TransactionId("111")
        every { bankAccountServiceMock.getBalance(any()) } returns billAdded.amountTotal
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
            ),
        )

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(BILL_ID_2),
                scheduledDate = getLocalDate(),
                amount = scheduledBill.amount + 1,
            ),
        )

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }
        val slot = slot<BillPaymentCommand>()
        verify(exactly = 1) {
            billPaymentServiceMock.execute(capture(slot))
        }
        slot.captured.billId shouldBe scheduledBill.billId
    }

    @Test
    fun `should pay only first bill but not the others when not enough balance for the second one`() {
        every { billPaymentServiceMock.execute(any()) } returns TransactionId("111")
        every { bankAccountServiceMock.getBalance(any()) } returns billAdded.amountTotal + 1L
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
            ),
        )
        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(BILL_ID_2),
                scheduledDate = getLocalDate(),
                amount = scheduledBill.amount + 1,
            ),
        )

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(BILL_ID_3),
                scheduledDate = getLocalDate(),
                amount = scheduledBill.amount + 2,
            ),
        )

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        val slot = slot<BillPaymentCommand>()
        verify(exactly = 1) {
            billPaymentServiceMock.execute(capture(slot))
        }
        slot.captured.billId shouldBe scheduledBill.billId
    }

    @Test
    fun `nao deve consultar saldo se os metodos utilizados no pagamento nao utilizarem saldo`() {
        every { billPaymentServiceMock.execute(any()) } returns TransactionId("111")

        val paymentMethodsDetailCreditCard = PaymentMethodsDetailWithCreditCard(
            netAmount = scheduledBill.amount,
            paymentMethodId = paymentMethodId2,
            feeAmount = 100,
            installments = 1,
            calculationId = null,
            fee = 4.0,
        )
        val paymentMethodsDetailExternalPayment = PaymentMethodsDetailWithExternalPayment(
            providerName = AccountProviderName.ME_POUPE,
        )

        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
                paymentMethodsDetail = paymentMethodsDetailCreditCard,
            ),
        )
        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(BILL_ID_2),
                scheduledDate = getLocalDate(),
                amount = scheduledBill.amount + 1,
                paymentMethodsDetail = paymentMethodsDetailExternalPayment,
            ),
        )
        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(BILL_ID_3),
                scheduledDate = getLocalDate(),
                amount = scheduledBill.amount + 2,
                paymentMethodsDetail = MultiplePaymentMethodsDetail(
                    methods = listOf(
                        paymentMethodsDetailCreditCard.copy(netAmount = scheduledBill.amount + 2),
                        paymentMethodsDetailExternalPayment,
                    ),
                ),
            ),
        )

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        verify(exactly = 0) { bankAccountServiceMock.getBalance(any()) }

        val slot = mutableListOf<BillPaymentCommand>()
        verify(exactly = 3) {
            billPaymentServiceMock.execute(capture(slot))
        }

        slot.size shouldBe 3

        slot.map { it.billId.value } shouldContainInOrder listOf(
            scheduledBill.billId.value,
            BILL_ID_2,
            BILL_ID_3,
        )
    }

    @Test
    fun `deve verificar o valor do saldo com o valor que sera pago com o metodo que exige a consulta de saldo`() {
        every { billPaymentServiceMock.execute(any()) } returns TransactionId("111")
        val balance = billAdded.amountTotal - 50L
        every { bankAccountServiceMock.getBalance(any()) } returns balance

        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
                paymentMethodsDetail = MultiplePaymentMethodsDetail(
                    methods = listOf(
                        PaymentMethodsDetailWithBalance(
                            amount = balance,
                            paymentMethodId = paymentMethodId2,
                            calculationId = null,
                        ),
                        PaymentMethodsDetailWithCreditCard(
                            netAmount = 50L,
                            paymentMethodId = paymentMethodId,
                            feeAmount = 100,
                            installments = 1,
                            calculationId = null,
                            fee = 4.0,
                        ),
                    ),
                ),
            ),
        )

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        val slot = slot<BillPaymentCommand>()
        verify(exactly = 1) {
            bankAccountServiceMock.getBalance(any())
            billPaymentServiceMock.execute(capture(slot))
        }

        slot.captured.billId shouldBe scheduledBill.billId
    }

    @Test
    fun `should execute all scheduled bill even some throw exception`() {
        every {
            billPaymentServiceMock.execute(
                any(),

            )
        } throws PaymentNotScheduledException() andThen TransactionId("X")
        every { bankAccountServiceMock.getBalance(any()) } returns 100000L
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
            ),
        )
        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(BILL_ID_2),
                scheduledDate = getLocalDate(),
            ),
        )
        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(BILL_ID_3),
                scheduledDate = getLocalDate(),
                amount = 1L,
            ),
        )

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        verify(exactly = 3) { billPaymentServiceMock.execute(any()) }
    }

    @Test
    fun `should not execute scheduled bill when locked`() {
        every { billPaymentServiceMock.execute(any()) } returns TransactionId("X")
        every { bankAccountServiceMock.getBalance(any()) } returns 100000L
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
            ),
        )
        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(BILL_ID_2),
                scheduledDate = getLocalDate(),
            ),
        )

        every { lockProvider.acquireLock(scheduledBill.billId.value) } returns null
        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }
        verify(exactly = 1) { billPaymentServiceMock.execute(any()) }
    }

    @Test
    fun `should call cancel schedule when processing not payable bill`() {
        every { billPaymentServiceMock.execute(any()) } throws BillNotPayableException(BillStatus.ALREADY_PAID)
        every { bankAccountServiceMock.getBalance(any()) } returns 100000L
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
            ),
        )

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        verify(exactly = 1) {
            paymentSchedulingService.cancelSchedule(
                scheduledBill.walletId,
                scheduledBill.billId,
                scheduledBill.batchSchedulingId,
            )
        }
    }

    @Test
    fun `deve cancelar agendamento se não encontrar a forma de pagamento`() {
        every { billPaymentServiceMock.execute(any()) } throws PaymentMethodNotFound("not found")
        every { bankAccountServiceMock.getBalance(any()) } returns 100000L
        scheduledBillRepository.save(
            scheduledBill.copy(
                scheduledDate = getLocalDate(),
            ),
        )

        val now = getZonedDateTime()
        withGivenDateTime(now.withHour(max(now.hour, 8))) {
            scheduledBillPaymentService.process(scheduledBill.walletId, getLocalDate())
        }

        verify(exactly = 1) {
            cancelSchedulePaymentService.cancelScheduledPayment(
                scheduledBill.walletId,
                scheduledBill.billId,
                ScheduleCanceledReason.CANT_PAY_WITH_CURRENT_CREDIT_CARD,
            )
        }
    }

    @Test
    fun `should wait retry after retryable payment failure`() {
        val bill = Bill.build(
            billAdded,
            billPaymentScheduled,
            billPaymentScheduleStarted,
            billPaymentStart,
            billPaymentFailedRetryable,
        )
        billRepository.save(bill)

        with(billRepository.findBill(billAdded.billId, billAdded.walletId)) {
            this.schedule?.waitingRetry shouldBe true
        }
    }

    @ParameterizedTest
    @MethodSource("shouldCancelBills")
    fun `should cancel bill schedule when time is after hours`(
        billAdded: BillEvent,
        now: ZonedDateTime,
        scheduleDate: LocalDate,
    ) {
        billEventRepository.save(billAdded)
        val (amountTotal, effectiveDueDate, billType) = when (billAdded) {
            is BillAdded -> Triple(billAdded.amountTotal, billAdded.effectiveDueDate!!, billAdded.billType)
            is FichaCompensacaoAdded -> Triple(
                billAdded.amountTotal,
                billAdded.effectiveDueDate,
                if (billAdded.barcode.checkIsConcessionaria()) BillType.CONCESSIONARIA else BillType.FICHA_COMPENSACAO,
            )

            else -> fail("bill event deve ser do tipo BillAdded ou FichaCompensacaoAdded")
        }

        val billPaymentScheduled = BillPaymentScheduled(
            billId = billAdded.billId,
            created = now.toInstant().toEpochMilli(),
            walletId = billAdded.walletId,
            actionSource = ActionSource.Webapp(role = Role.OWNER),
            scheduledDate = effectiveDueDate,
            amount = amountTotal,
            paymentLimitTime = if (billAdded is FichaCompensacaoAdded) billAdded.paymentLimitTime else null,
            infoData = BillPaymentScheduledBalanceInfo(
                amount = amountTotal,
                paymentMethodId = paymentMethodId2.value,
                calculationId = null,
            ),
            batchSchedulingId = BatchSchedulingId(),
        )

        billEventRepository.save(billPaymentScheduled)

        every { billPaymentServiceMock.execute(any()) } returns TransactionId("111")
        every { bankAccountServiceMock.getBalance(any()) } returns billPaymentScheduled.amount

        val scheduledBill = Bill.build(billAdded, billPaymentScheduled)

        scheduledBillRepository.save(
            ScheduledBill(
                walletId = scheduledBill.walletId,
                billId = scheduledBill.billId,
                scheduledDate = scheduleDate,
                billType = billType,
                amount = scheduledBill.amountTotal,
                scheduleTo = ScheduleTo.DUE_DATE,
                paymentLimitTime = if (billAdded is FichaCompensacaoAdded) billAdded.paymentLimitTime else null,
                paymentMethodsDetail = createPaymentMethodsDetailWithBalance(
                    paymentMethodId2,
                    scheduledBill.amountTotal,
                ),
                batchSchedulingId = BatchSchedulingId(),
            ),
        )

        withGivenDateTime(now) {
            scheduledBillPaymentService.process(scheduledBill.walletId, now.toLocalDate())
        }

        val result = billEventRepository.getBillById(billAdded.billId)

        result.isRight() shouldBe true
        result.map {
            it.history.last().eventType shouldBe BillEventType.PAYMENT_SCHEDULE_CANCELED
            (it.history.last() as BillPaymentScheduleCanceled).reason shouldBe ScheduleCanceledReason.EXPIRATION
        }
    }

    @ParameterizedTest
    @MethodSource("shouldStartPaymentScheduleBills")
    fun `should start bill payment schedule when time is not after hours`(
        billAdded: BillEvent,
        now: ZonedDateTime,
        scheduleDate: LocalDate,
    ) {
        billEventRepository.save(billAdded)
        val (amountTotal, effectiveDueDate, billType) = when (billAdded) {
            is BillAdded -> Triple(billAdded.amountTotal, billAdded.effectiveDueDate!!, billAdded.billType)
            is FichaCompensacaoAdded -> Triple(
                billAdded.amountTotal,
                billAdded.effectiveDueDate,
                BillType.FICHA_COMPENSACAO,
            )

            else -> fail("bill event deve ser do tipo BillAdded ou FichaCompensacaoAdded")
        }

        val billPaymentScheduled = BillPaymentScheduled(
            billId = billAdded.billId,
            created = now.toInstant().toEpochMilli(),
            walletId = billAdded.walletId,
            actionSource = ActionSource.Webapp(role = Role.OWNER),
            scheduledDate = effectiveDueDate,
            amount = amountTotal,
            paymentLimitTime = if (billAdded is FichaCompensacaoAdded) billAdded.paymentLimitTime else null,
            infoData = BillPaymentScheduledBalanceInfo(
                amount = amountTotal,
                paymentMethodId = paymentMethodId2.value,
                calculationId = null,
            ),
            batchSchedulingId = BatchSchedulingId(),
        )

        billEventRepository.save(billPaymentScheduled)

        every { billPaymentServiceMock.execute(any()) } returns TransactionId("111")
        every { bankAccountServiceMock.getBalance(any()) } returns billPaymentScheduled.amount

        val bill = Bill.build(billAdded, billPaymentScheduled)

        val scheduledBill = ScheduledBill(
            walletId = bill.walletId,
            billId = bill.billId,
            scheduledDate = scheduleDate,
            billType = billType,
            amount = bill.amountTotal,
            scheduleTo = ScheduleTo.DUE_DATE,
            paymentLimitTime = if (billAdded is FichaCompensacaoAdded) billAdded.paymentLimitTime else null,
            paymentMethodsDetail = createPaymentMethodsDetailWithBalance(
                paymentMethodId2,
                bill.amountTotal,
            ),
            batchSchedulingId = BatchSchedulingId(),
        )
        scheduledBillRepository.save(scheduledBill)

        withGivenDateTime(now) {
            scheduledBillPaymentService.process(scheduledBill.walletId, now.toLocalDate())
        }

        val result = billEventRepository.getBillById(billAdded.billId)

        result.isRight() shouldBe true
        result.map {
            it.history.last().eventType shouldBe BillEventType.PAYMENT_SCHEDULE_STARTED
        }
    }

    @Test
    fun `não deve desagendar uma conta expirada que esteja em PROCESSING()`() {
        val tedScheduledId = BillId(UUID.randomUUID().toString())

        val now = getZonedDateTime()

        every {
            billPaymentServiceMock.execute(any())
        } returns TransactionId("111")

        withGivenDateTime(now.withHour(10)) {
            createTEDScheduledBill(tedScheduledId.value, 2_00, now.toLocalDate())
            billEventRepository.save(
                billPaymentScheduleStarted.copy(
                    billId = tedScheduledId,
                    created = getZonedDateTime().toInstant().toEpochMilli() - 3,
                ),
            )

            billEventRepository.save(
                billPaymentStart.copy(
                    billId = tedScheduledId,
                    created = getZonedDateTime().toInstant().toEpochMilli() - 2,
                ),
            )
        }

        val result = billEventRepository.getBillById(tedScheduledId)
        result.isRight()

        withGivenDateTime(now.withHour(17).withMinute(0).withSecond(1)) {
            scheduledBillPaymentService.process(walletId = scheduledBill.walletId)
        }

        val result2 = billEventRepository.getBillById(tedScheduledId)
        result2.isRight()
        result2.map {
            it.history.filterIsInstance<BillPaymentScheduleCanceled>() shouldHaveSize 0
            it.history.last() shouldBe io.kotest.matchers.types.beOfType(PaymentStarted::class)
            it.status shouldBe BillStatus.PROCESSING
        }
    }

    private fun createPixScheduledBill(
        billId: String,
        amount: Long,
        scheduleDate: LocalDate = getZonedDateTime().toLocalDate(),
        scheduleTo: ScheduleTo = ScheduleTo.ASAP,
    ) {
        val billPixAdded = pixAdded.copy(
            billId = BillId(billId),
            amount = amount,
            amountTotal = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 10,
        )
        val billPixPaymentScheduled = pixPaymentScheduled.copy(
            billId = BillId(billId),
            amount = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 5,
        )

        billEventRepository.save(billPixAdded)
        billEventRepository.save(billPixPaymentScheduled)

        billRepository.save(Bill.build(billPixAdded, billPixPaymentScheduled))

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(billId),
                scheduledDate = scheduleDate,
                amount = amount,
                billType = BillType.PIX,
                scheduleTo = scheduleTo,
            ),
        )
    }

    private fun createConcessionariaScheduledBill(
        billId: String,
        amount: Long,
        scheduleDate: LocalDate = getZonedDateTime().toLocalDate(),
        scheduleTo: ScheduleTo = ScheduleTo.ASAP,
    ) {
        val boleto = billAdded.copy(
            billId = BillId(billId),
            amount = amount,
            amountTotal = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 10,
            barcode = BarCode.ofDigitable(ELECTRICITY_AND_GAS_BARCODE_LINE),
        )
        val boletoBillPaymentScheduled = billPaymentScheduled.copy(
            billId = BillId(billId),
            amount = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 5,
        )

        billEventRepository.save(boleto)
        billEventRepository.save(boletoBillPaymentScheduled)

        billRepository.save(Bill.build(boleto, boletoBillPaymentScheduled))

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(billId),
                scheduledDate = scheduleDate,
                amount = amount,
                billType = BillType.CONCESSIONARIA,
                scheduleTo = scheduleTo,
            ),
        )
    }

    private fun createTEDScheduledBill(
        billId: String,
        amount: Long,
        scheduleDate: LocalDate = getZonedDateTime().toLocalDate(),
        scheduleTo: ScheduleTo = ScheduleTo.ASAP,
    ) {
        val ted = invoiceAdded.copy(
            billId = BillId(billId),
            amount = amount,
            amountTotal = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 10,
        )
        val tedBillPaymentScheduled = billPaymentScheduled.copy(
            billId = BillId(billId),
            amount = amount,
            created = getZonedDateTime().toInstant().toEpochMilli() - 5,
        )

        billEventRepository.save(ted)
        billEventRepository.save(tedBillPaymentScheduled)

        billRepository.save(Bill.build(ted, tedBillPaymentScheduled))

        scheduledBillRepository.save(
            scheduledBill.copy(
                billId = BillId(billId),
                scheduledDate = scheduleDate,
                amount = amount,
                billType = BillType.INVOICE,
                scheduleTo = scheduleTo,
            ),
        )
    }

    companion object {
        @JvmStatic
        fun shouldCancelBills(): Stream<Arguments> { // TODO rever nome
            val afterHoursInvoice = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 17, 1), brazilTimeZone)
            val onTimeInvoice = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 16, 0), brazilTimeZone)
            val billAddedTime = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 0, 1), brazilTimeZone)
            val afterHoursFicha = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 23, 1), brazilTimeZone)

            val billId = UUID.randomUUID().toString()
            return Stream.of(
                Arguments.of(
                    invoiceAdded.copy(
                        billId = BillId(billId),
                        created = afterHoursInvoice.minusSeconds(10).toInstant().toEpochMilli(),
                    ),
                    afterHoursInvoice,
                    LocalDate.of(2021, 9, 9),
                ),
                Arguments.of(
                    invoiceAdded.copy(
                        billId = BillId(billId),
                        created = onTimeInvoice.minusSeconds(10).toInstant().toEpochMilli(),
                    ),
                    onTimeInvoice,
                    LocalDate.of(2021, 9, 8),
                ),
                Arguments.of(
                    billAddedFicha.copy(
                        billId = BillId(billId),
                        created = billAddedTime.minusSeconds(10).toInstant().toEpochMilli(),
                    ),
                    billAddedTime,
                    LocalDate.of(2021, 9, 8),
                ),
                Arguments.of(
                    billAddedFicha.copy(
                        billId = BillId(billId),
                        created = afterHoursFicha.minusSeconds(10).toInstant().toEpochMilli(),
                        paymentLimitTime = LocalTime.parse("23:00", timeFormat),
                    ),
                    afterHoursFicha,
                    LocalDate.of(2021, 9, 9),
                ),
                Arguments.of(
                    billAddedFicha.copy(
                        billId = BillId(billId),
                        created = afterHoursFicha.minusSeconds(10).toInstant().toEpochMilli(),
                        paymentLimitTime = LocalTime.parse("23:00", timeFormat),
                        barcode = BarCode.ofDigitable(ELECTRICITY_AND_GAS_BARCODE_LINE),
                    ),
                    afterHoursFicha,
                    LocalDate.of(2021, 9, 9),
                ),
                Arguments.of(
                    billAdded.copy(
                        billId = BillId(billId),
                        created = billAddedTime.minusSeconds(10).toInstant().toEpochMilli(),
                        barcode = BarCode.of(PREFECTURE_BARCODE_LINE),
                    ),
                    billAddedTime,
                    LocalDate.of(2021, 9, 8),
                ),
                Arguments.of(
                    pixAdded.copy(
                        billId = BillId(billId),
                        created = billAddedTime.minusSeconds(10).toInstant().toEpochMilli(),
                    ),
                    billAddedTime,
                    LocalDate.of(2021, 9, 8),
                ),
            )
        }

        @JvmStatic
        fun shouldStartPaymentScheduleBills(): Stream<Arguments> { // TODO rever nome
            val onTimeInvoice = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 16, 59), brazilTimeZone)
            val billAddedTime = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 0, 1), brazilTimeZone)
            val billId = UUID.randomUUID().toString()
            return Stream.of(
                Arguments.of(
                    invoiceAdded.copy(
                        billId = BillId(billId),
                        created = billAddedTime.minusSeconds(10).toInstant().toEpochMilli(),
                    ),
                    onTimeInvoice,
                    LocalDate.of(2021, 9, 9),
                ),
                Arguments.of(
                    invoiceAdded.copy(
                        billId = BillId(billId),
                        created = onTimeInvoice.minusSeconds(10).toInstant().toEpochMilli(),
                    ),
                    onTimeInvoice,
                    LocalDate.of(2021, 9, 9),
                ),
                Arguments.of(
                    billAddedFicha.copy(
                        billId = BillId(billId),
                        created = billAddedTime.minusSeconds(10).toInstant().toEpochMilli(),
                    ),
                    billAddedTime.withHour(8),
                    LocalDate.of(2021, 9, 9),
                ),
                Arguments.of(
                    billAdded.copy(
                        billId = BillId(billId),
                        created = billAddedTime.minusSeconds(10).toInstant().toEpochMilli(),
                        barcode = BarCode.of(PREFECTURE_BARCODE_LINE),
                    ),
                    billAddedTime.withHour(8),
                    LocalDate.of(2021, 9, 9),
                ),
                Arguments.of(
                    pixAdded.copy(
                        billId = BillId(billId),
                        created = billAddedTime.minusSeconds(10).toInstant().toEpochMilli(),
                    ),
                    billAddedTime,
                    LocalDate.of(2021, 9, 9),
                ),
            )
        }
    }
}