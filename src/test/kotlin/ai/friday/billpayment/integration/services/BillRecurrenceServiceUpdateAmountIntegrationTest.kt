package ai.friday.billpayment.integration.services

import DynamoDBUtils
import ai.friday.billpayment.adapters.bill.DefaultBillEventPublisher
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.lock.InternalLockProvider
import ai.friday.billpayment.adapters.micronaut.RecurrenceConfigurationMicronaut
import ai.friday.billpayment.adapters.micronaut.WalletConfigurationMicronaut
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreatePixRequest
import ai.friday.billpayment.app.bill.RecipientRequest
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityService
import ai.friday.billpayment.app.contact.ContactService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.payment.DefaultScheduledBillPaymentService
import ai.friday.billpayment.app.payment.FridayScheduledBillPaymentServicePreProcessor
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import ai.friday.billpayment.app.recurrence.generateDateSequence
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.configureDailyLimits
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.pixKeyDetails
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import ai.friday.billpayment.withBlockingAsyncCalls
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import arrow.core.right
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import java.time.Duration
import java.time.LocalDate
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class BillRecurrenceServiceUpdateAmountIntegrationTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val dynamoDbEnhancedAsyncClient = DynamoDBUtils.getDynamoDBAsync()

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.assistant))

    private val fakeLastLimitDate: LocalDate = getLocalDate().minusMonths(6)
    private val fakeLimitDate: LocalDate = getLocalDate().plusMonths(6)

    private val recurrenceRepository =
        BillRecurrenceDBRepository(dynamoDbDAO, fakeLimitDate.format(dateFormat))

    private val billEventRepository = BillEventDBRepository(
        dynamoDbDAO,
        allFalseFeatureConfiguration,
    )

    private val billRepository = DynamoDbBillRepository(dynamoDbDAO)

    private val recipientDbRepository = ContactDbRepository(dynamoDbDAO)

    private val eventPublisher: EventPublisher = mockk(relaxed = true)

    private val walletRepository = WalletDbRepository(dynamoDbDAO, mockk())

    private val notificationAdapter: NotificationAdapter = mockk(relaxUnitFun = true)

    private val simpleLock = mockk<SimpleLock>(relaxed = true)
    private val lockProvider: InternalLockProvider = mockk() {
        every {
            acquireLock(any())
        } returns simpleLock
    }

    private val scheduledBillRepository = ScheduledBillDBRepository(dynamoDbDAO)

    private val scheduledWalletDynamoDAO = ScheduledWalletDynamoDAO(dynamoDbEnhancedAsyncClient)
    private val scheduledWalletRepository = ScheduledWalletDBRepository(scheduledWalletDynamoDAO)

    private val paymentSchedulingService =
        PaymentSchedulingService(
            scheduledBillRepository = scheduledBillRepository,
            scheduledWalletRepository = scheduledWalletRepository,
        )

    private val accountService = mockk<AccountService>() {
        every {
            findAccountPaymentMethodByIdAndAccountId(wallet.paymentMethodId, wallet.founder.accountId)
        } returns walletFixture.buildBankAccount(wallet)
        every {
            findAccountById(wallet.founder.accountId)
        } returns walletFixture.founderAccount
    }

    private val walletService = WalletService(
        accountService = accountService,
        walletRepository = walletRepository,
        configuration = WalletConfigurationMicronaut(
            maxOpenInvites = 1,
            inviteExpiration = Duration.ZERO,
        ),
        notificationAdapter = notificationAdapter,
        eventPublisher = mockk(relaxed = true),
        crmService = mockk(),
        pixKeyRepository = mockk(),
    )

    private val walletLimitsService = WalletLimitsService(
        accountService = accountService,
        walletRepository = walletRepository,
        paymentSchedulingService = paymentSchedulingService,
        billRepository = billRepository,
        internalLock = lockProvider,
    )

    private val scheduledBillPaymentService = DefaultScheduledBillPaymentService(
        billPaymentService = mockk(),
        balanceService = mockk(),
        paymentSchedulingService = paymentSchedulingService,
        updateBillService = mockk(),
        walletService = mockk(),
        walletLimitsService = walletLimitsService,
        lockProvider = mockk(),
        walletLockProvider = mockk(),
        scheduledBillPaymentServicePreProcessor = FridayScheduledBillPaymentServicePreProcessor(onePixPayService = mockk()),
    )

    private val updateBillService = UpdateBillService(
        billEventRepository = billEventRepository,
        billRepository = billRepository,
        billTrackingRepository = mockk(relaxed = true),
        eventPublisher = eventPublisher,
        boletoSettlementService = mockk(relaxed = true),
        billValidationService = mockk(relaxed = true),
        lockProvider = lockProvider,
        walletLimitsService = walletLimitsService,
        walletService = walletService,
        approveBillService = mockk(),
        denyBillService = mockk(),
        resolveScheduledBill = mockk(),
        cancelSchedulePaymentService = mockk {
            every { cancelScheduledPayment(any(), any(), any()) } returns true
            every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
        },
        mailboxWalletDataRepository = mockk(),
    )

    private val billEventPublisher = DefaultBillEventPublisher(
        eventRepository = billEventRepository,
        billRepository = billRepository,
        eventPublisher = eventPublisher,
    )

    private val scheduleBillSecurityService = mockk<ScheduleBillSecurityService>(relaxed = true) {
        every { canSchedule(any()) } returns Unit.right()
    }

    private val scheduleBillService = ScheduleBillService(
        billEventRepository = billEventRepository,
        accountService = accountService,
        paymentSchedulingService = paymentSchedulingService,
        scheduledBillPaymentServiceProvider = mockk {
            every { get() } returns scheduledBillPaymentService
        },
        lockProvider = lockProvider,
        billInstrumentationService = mockk(relaxUnitFun = true),
        billEventPublisher = billEventPublisher,
        scheduleBillSecurityService = scheduleBillSecurityService,
        batchSchedulePublisher = mockk(relaxUnitFun = true),
        walletRepository = walletRepository,
        isBatchScheduleNotificationEnabled = false,
        pinCodeService = mockk(relaxed = true),
    )

    private val pixKeyManagementMock = mockk<PixKeyManagement>() {
        every { findKeyDetailsCacheable(any(), any()) } returns PixKeyDetailsResult(
            pixKeyDetails = pixKeyDetails,
            e2e = "123",
        )
    }

    private val createBillService = CreateBillService(
        billRepository = mockk(),
        contactService = ContactService(recipientDbRepository, billEventRepository, mockk(), mockk()),
        accountService = accountService,
        pixKeyManagement = pixKeyManagementMock,
        updateBillService = updateBillService,
        tedConfiguration = mockk(relaxed = true),
        walletLimitsService = walletLimitsService,
        possibleDuplicateBillService = mockk(relaxed = true),
        pixQRCodeParserService = mockk(),
        categoryService = mockk(relaxed = true),
        concessionariaService = mockk(),
    )

    private val billInstrumentationService = mockk<BillInstrumentationService>(relaxUnitFun = true)

    private val recurrenceConfiguration = RecurrenceConfigurationMicronaut(
        lastLimitDate = fakeLastLimitDate.format(dateFormat),
        limitDate = fakeLimitDate.format(dateFormat),
    )

    private val accountRepository: AccountRepository = mockk() {
        every {
            findById(walletFixture.founderAccount.accountId)
        } returns walletFixture.founderAccount
    }

    private val recurrenceService =
        BillRecurrenceService(
            billRecurrenceRepository = recurrenceRepository,
            createBillService = createBillService,
            updateBillService = updateBillService,
            billInstrumentationService = billInstrumentationService,
            recurrenceConfiguration = recurrenceConfiguration,
            possibleDuplicateBillService = mockk(),
            accountRepository = accountRepository,
            walletRepository = walletRepository,
            scheduleBillService = scheduleBillService,
        )

    private val walletLimit: Long = 999

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)

        walletRepository.save(wallet)
        configureDailyLimits(
            walletLimitsService = walletLimitsService,
            accountId = walletFixture.founder.accountId,
            walletId = wallet.id,
            dailyLimit = walletLimit,
            nighttimeLimit = walletLimit,
        )
    }

    @Test
    fun `se for uma conta recorrente deve atualizar o valor de todas as contas para a frente e da recorrencia tambem`() {
        val originalAmount = 100L
        val updatedAmount = 130L
        val (recurrence, bills, pivotBillId) = createRecurrenceAndScheduleFutureBills(
            originalAmount,
        )

        withBlockingAsyncCalls {
            recurrenceService.updateAmount(
                billId = pivotBillId,
                amount = updatedAmount,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            )
        }

        val databaseRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)
        databaseRecurrence.amount shouldBe updatedAmount

        bills.dropWhile { it.billId != pivotBillId }.drop(1).forEach { billview ->
            val updatedBill = billRepository.findBill(walletId = wallet.id, billId = billview.billId)
            updatedBill.amountTotal shouldBe updatedAmount
        }

        bills.takeWhile { it.billId != pivotBillId }.forEach { billview ->
            val updatedBill = billRepository.findBill(walletId = wallet.id, billId = billview.billId)
            updatedBill.amountTotal shouldBe originalAmount
        }
    }

    @Test
    fun `se for uma conta recorrente as recorrencias futuras agendadas não devem ser desagendadas quando o usuáiro tiver permissão para agendar`() {
        val originalAmount = 100L
        val updatedAmount = 130L
        val (recurrence, bills, pivotBillId) = createRecurrenceAndScheduleFutureBills(originalAmount)

        withBlockingAsyncCalls {
            recurrenceService.updateAmount(
                billId = pivotBillId,
                amount = updatedAmount,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            )
        }

        val databaseRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)
        databaseRecurrence.amount shouldBe updatedAmount

        bills.dropWhile { it.billId != pivotBillId }.drop(1).forEach { billview ->
            val updatedBill = billRepository.findBill(walletId = wallet.id, billId = billview.billId)
            updatedBill.schedule.shouldNotBeNull()
            updatedBill.amountTotal shouldBe updatedAmount
        }
    }

    @Test
    fun `se for uma conta recorrente as recorrencias futuras agendadas deverão ser desagendadas se o usuáiro não tiver permissão para agendar`() {
        val originalAmount = 100L
        val updatedAmount = 130L
        val (recurrence, bills, pivotBillId) = createRecurrenceAndScheduleFutureBills(originalAmount)

        withBlockingAsyncCalls {
            recurrenceService.updateAmount(
                billId = pivotBillId,
                amount = updatedAmount,
                actionSource = ActionSource.Api(accountId = walletFixture.assistant.accountId),
            )
        }

        val databaseRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)
        databaseRecurrence.amount shouldBe updatedAmount

        bills.dropWhile { it.billId != pivotBillId }.drop(1).forEach { billview ->
            val updatedBill = billRepository.findBill(walletId = wallet.id, billId = billview.billId)
            updatedBill.schedule.shouldBeNull()
            updatedBill.amountTotal shouldBe updatedAmount
        }
    }

    @Test
    fun ` se for uma conta recorrente as recorrencias futuras não devem validar o limite disponível para o dia`() {
        val originalAmount = 100L
        val updatedAmount = 900L
        val (recurrence, bills, pivotBillId) = createRecurrenceAndScheduleFutureBills(originalAmount)

        bills.dropWhile { it.billId != pivotBillId }.drop(1).forEach {
            val result = createBillService.createPix(
                CreatePixRequest(
                    description = "outro na mesma data",
                    dueDate = it.dueDate,
                    amount = walletLimit - originalAmount,
                    recipient = RecipientRequest(
                        id = null,
                        wallet.founder.accountId,
                        name = "nome",
                        document = null,
                        alias = "alias",
                        bankAccount = null,
                        pixKey = PixKey(
                            value = "<EMAIL>",
                            type = PixKeyType.EMAIL,
                        ),
                        qrCode = null,
                    ),
                    source = ActionSource.Api(accountId = wallet.founder.accountId),
                    recurrenceRule = null,
                    walletId = wallet.id,
                ),
                dryRun = false,
            )
            result.shouldBeTypeOf<CreateBillResult.SUCCESS>()

            scheduleBillService.schedulePayment(
                paymentWallet = wallet,
                billId = result.bill.billId,
                member = wallet.founder,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
                paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                    amount = result.bill.amountTotal,
                    paymentMethodId = wallet.paymentMethodId,
                    calculationId = null,
                ),
            )
        }

        withBlockingAsyncCalls {
            recurrenceService.updateAmount(
                billId = pivotBillId,
                amount = updatedAmount,
                actionSource = ActionSource.Api(accountId = walletFixture.founder.accountId),
            )
        }

        val databaseRecurrence = recurrenceRepository.find(recurrence.id, recurrence.walletId)
        databaseRecurrence.amount shouldBe updatedAmount

        bills.dropWhile { it.billId != pivotBillId }.drop(1).forEach { billview ->
            val updatedBill = billRepository.findBill(walletId = wallet.id, billId = billview.billId)
            updatedBill.schedule.shouldNotBeNull()
            updatedBill.amountTotal shouldBe updatedAmount
        }
    }

    private fun createRecurrenceAndScheduleFutureBills(originalAmount: Long): Triple<BillRecurrence, List<BillView>, BillId> {
        val recurrence = weeklyRecurrenceNoEndDate.copy(
            walletId = wallet.id,
            recipientBankAccount = null,
            recipientPixKey = pixKeyDetails.key,
            rule = weeklyRecurrenceNoEndDate.rule.copy(
                startDate = getLocalDate().withDayOfMonth(27),
            ),
            amount = originalAmount,
            billType = BillType.PIX,
            actionSource = ActionSource.Api(
                accountId = wallet.founder.accountId,
                originalBillId = BillId("13"),
            ),
        )

        recurrenceService.doCreate(recurrence, recurrence.rule.generateDateSequence(limitDate = fakeLimitDate))
            .toCompletableFuture().get()

        val bills = billRepository.findByWallet(recurrence.walletId)
        val pivotBillId = bills[2].billId
        scheduleBillsToDueDate(bills.dropWhile { it.billId != pivotBillId }.drop(1))
        return Triple(recurrence, bills, pivotBillId)
    }

    private fun scheduleBillsToDueDate(bills: List<BillView>) {
        bills.forEach { bill ->
            scheduleBillService.schedulePayment(
                paymentWallet = wallet,
                billId = bill.billId,
                member = wallet.founder,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
                paymentMethodsDetail = PaymentMethodsDetailWithBalance(
                    amount = bill.amountTotal,
                    paymentMethodId = wallet.paymentMethodId,
                    calculationId = null,
                ),
            )
        }
    }
}