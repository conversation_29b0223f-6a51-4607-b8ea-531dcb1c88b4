package ai.friday.billpayment.integration.services

import ai.friday.billpayment.CouldNotAcquireLockException
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.payment.BillPaymentCommand
import ai.friday.billpayment.app.payment.BillPaymentService
import ai.friday.billpayment.app.payment.Checkout
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.transaction.CompleteTransaction
import ai.friday.billpayment.app.payment.transaction.FailTransaction
import ai.friday.billpayment.app.payment.transaction.PrepareTransaction
import ai.friday.billpayment.app.payment.transaction.StartTransaction
import ai.friday.billpayment.app.payment.transaction.TransactionInProcess
import ai.friday.billpayment.boletoTransaction
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class BillPaymentServiceTest {

    private val checkout: Checkout = mockk(relaxed = true)

    private val checkoutLocator: CheckoutLocator = mockk()

    private val transactionService: TransactionService = mockk(relaxed = true)

    private val completeTransaction: CompleteTransaction = mockk(relaxed = true)

    private val prepareTransaction: PrepareTransaction = mockk(relaxed = true)

    private val startTransaction: StartTransaction = mockk()

    private val failTransaction: FailTransaction = mockk(relaxed = true)

    private val transactionInProcess: TransactionInProcess = mockk(relaxed = true)

    private val simpleLock: SimpleLock = mockk(relaxed = true)

    private val lockProvider: InternalLock = mockk {
        every { acquireLock(any()) } returns simpleLock
    }

    val service = BillPaymentService(
        transactionService = transactionService,
        checkoutLocator = checkoutLocator,
        startTransaction = startTransaction,
        prepareTransaction = prepareTransaction,
        completeTransaction = completeTransaction,
        failTransaction = failTransaction,
        transactionInProcess = transactionInProcess,
        lockProvider = lockProvider,
    )

    private lateinit var transaction: Transaction

    @BeforeEach
    fun setup() {
        transaction = boletoTransaction.copy(status = TransactionStatus.PROCESSING)

        every { checkoutLocator.getCheckout(transaction) } returns checkout
    }

    @Test
    fun `deve iniciar o pagamento de uma conta`() {
        val command = mockk<BillPaymentCommand>()
        every {
            startTransaction.execute(command)
        } returns boletoTransaction
        service.execute(command) shouldBe boletoTransaction.id
    }

    @Test
    fun `deve falhar a transacao quando ocorrer um erro ao preparar a transacao para processamento`() {
        every {
            prepareTransaction.execute(transaction)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.FAILED }
        }

        service.process(transaction)

        verify {
            transactionService.save(transaction)
            failTransaction.execute(transaction)
        }

        verify(exactly = 0) {
            checkout.execute(any())
            completeTransaction.execute(any())
            transactionInProcess.execute(any())
        }
    }

    @Test
    fun `deve falhar a transacao quando ocorrer um erro no checkout`() {
        every {
            checkout.execute(transaction)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.FAILED }
        }

        service.process(transaction)

        verify {
            prepareTransaction.execute(transaction)
            transactionService.save(transaction)
            failTransaction.execute(transaction)
            checkout.execute(any())
        }

        verify(exactly = 0) {
            completeTransaction.execute(any())
            transactionInProcess.execute(any())
        }
    }

    @Test
    fun `deve completar a transacao quando o checkout responder que a transacao esta finalizada`() {
        every {
            checkout.execute(transaction)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.COMPLETED }
        }

        service.process(transaction)

        verify {
            prepareTransaction.execute(transaction)
            transactionService.save(transaction)
            completeTransaction.execute(transaction)
            checkout.execute(transaction)
        }

        verify(exactly = 0) {
            failTransaction.execute(any())
            transactionInProcess.execute(any())
        }
    }

    @Test
    fun `deve manter a transacao em processamento quando o checkout responder que a transacao esta em processamento`() {
        every {
            checkout.execute(transaction)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.PROCESSING }
        }

        service.process(transaction)

        verify {
            prepareTransaction.execute(transaction)
            transactionService.save(transaction)
            transactionInProcess.execute(transaction)
            checkout.execute(transaction)
        }

        verify(exactly = 0) {
            failTransaction.execute(any())
            completeTransaction.execute(any())
        }
    }

    @ParameterizedTest
    @EnumSource(
        value = TransactionStatus::class,
        mode = EnumSource.Mode.EXCLUDE,
        names = ["PROCESSING", "COMPLETED", "FAILED"],
    )
    fun `deve lancar excecao quando o checkout responder que a transacao esta em um estado invalido`(transactionStatus: TransactionStatus) {
        every {
            checkout.execute(transaction)
        } answers {
            arg<Transaction>(0).apply { status = transactionStatus }
        }

        assertThrows<IllegalStateException> {
            service.process(transaction)
        }

        verify {
            prepareTransaction.execute(transaction)
            transactionService.save(transaction)
            checkout.execute(transaction)
        }

        verify(exactly = 0) {
            transactionInProcess.execute(any())
            failTransaction.execute(any())
            completeTransaction.execute(any())
        }
    }

    @Test
    fun `should not process transaction when lock cannot be acquired`() {
        every { lockProvider.acquireLock(any()) } returns null

        val exception = assertThrows<CouldNotAcquireLockException> {
            service.process(transaction)
        }

        verify(exactly = 1) { lockProvider.acquireLock(any()) }
        verify(exactly = 0) {
            prepareTransaction.execute(any())
            checkout.execute(any())
            completeTransaction.execute(any())
            failTransaction.execute(any())
            transactionInProcess.execute(any())
            simpleLock.unlock()
        }
    }

    @Test
    fun `should acquire and release lock during successful transaction processing`() {
        every {
            checkout.execute(transaction)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.COMPLETED }
        }

        service.process(transaction)

        verify(exactly = 1) { lockProvider.acquireLock(any()) }
        verify(exactly = 1) { simpleLock.unlock() }
    }

    @Test
    fun `should acquire and release lock during failed transaction processing`() {
        every {
            checkout.execute(transaction)
        } answers {
            arg<Transaction>(0).apply { status = TransactionStatus.FAILED }
        }

        service.process(transaction)

        verify(exactly = 1) { lockProvider.acquireLock(any()) }
        verify(exactly = 1) { simpleLock.unlock() }
    }
}