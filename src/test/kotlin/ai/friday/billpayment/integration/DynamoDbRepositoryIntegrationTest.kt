package ai.friday.billpayment.integration

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.getBillWithDueDate
import ai.friday.billpayment.paymentMethodId
import ai.friday.billpayment.paymentMethodId2
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import kotlin.test.assertEquals
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class DynamoDbRepositoryIntegrationTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val dynamoDbBillRepository = DynamoDbBillRepository(dynamoDbDAO)
    private val accountDbRepository = AccountDbRepository(dynamoDbDAO)

    @Test
    fun `should find overdue bills`() {
        createBillPaymentTable(dynamoDB)
        loadPaidBillsIntoDb(dynamoDB)
        assertEquals(0, dynamoDbBillRepository.findOverdueBills(walletId = WalletId(ACCOUNT_ID)).size)
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(
                walletId = WalletId(ACCOUNT_ID),
                daysToSubtract = 0,
                daysToAdd = 2,
                effectiveDaysToSubtract = 2,
                effectiveDaysToAdd = 0,
            ),
        )
        assertEquals(1, dynamoDbBillRepository.findOverdueBills(walletId = WalletId(ACCOUNT_ID)).size)
    }

    @Test
    fun `deve encontrar uma conta paga com a informação de como ela foi paga se o desagendamento ocorreu depois do pagamento`() {
        createBillPaymentTable(dynamoDB)
        val bill = Bill.build(
            billAdded.copy(billId = BillId(BILL_ID_4)),
            billPaymentScheduled.copy(billId = BillId(BILL_ID_4)),
            billPaymentStart.copy(billId = BillId(BILL_ID_4), correlationId = CORRELATION_ID),
            billPaid.copy(billId = BillId(BILL_ID_4)),
            billScheduleCanceled.copy(billId = BillId(BILL_ID_4)),
        )
        dynamoDbBillRepository.save(bill)

        val foundBill = dynamoDbBillRepository.findBill(BillId(BILL_ID_4), billAdded.walletId)
        foundBill.paymentDetails.shouldNotBeNull()
        foundBill.paymentDetails shouldBe billPaymentScheduled.infoData.toSchedulePaymentDetails()
        foundBill.transactionCorrelationId shouldBe CORRELATION_ID
    }

    @ParameterizedTest
    @EnumSource(ScheduleCanceledReason::class)
    fun `informação de como ela foi paga deve ser nula se o agendamento foi cancelado sem o pagamento`(reason: ScheduleCanceledReason) {
        createBillPaymentTable(dynamoDB)
        val bill = Bill.build(
            billAdded.copy(billId = BillId(BILL_ID_4)),
            billPaymentScheduled.copy(billId = BillId(BILL_ID_4)),
            billScheduleCanceled.copy(billId = BillId(BILL_ID_4), reason = reason),
        )
        dynamoDbBillRepository.save(bill)

        val foundBill = dynamoDbBillRepository.findBill(BillId(BILL_ID_4), billAdded.walletId)
        foundBill.paymentDetails.shouldBeNull()
    }

    @Test
    fun `should find bill coming due`() {
        createBillPaymentTable(dynamoDB)
        loadPaidBillsIntoDb(dynamoDB)
        assertEquals(0, dynamoDbBillRepository.findBillsComingDueWithin(WalletId(ACCOUNT_ID), 7).size)
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(
                daysToSubtract = 0,
                daysToAdd = 0,
                effectiveDaysToSubtract = 0,
                effectiveDaysToAdd = 0,
            ),
        )
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(
                daysToSubtract = 0,
                daysToAdd = 0,
                effectiveDaysToSubtract = 0,
                effectiveDaysToAdd = 1,
                billid = BillId(),
            ),
        )
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(
                daysToSubtract = 0,
                daysToAdd = 0,
                effectiveDaysToSubtract = 0,
                effectiveDaysToAdd = 2,
                billid = BillId(),
            ),
        )
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(
                daysToSubtract = 0,
                daysToAdd = 0,
                effectiveDaysToSubtract = 0,
                effectiveDaysToAdd = 3,
                billid = BillId(),
            ),
        )
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(
                daysToSubtract = 0,
                daysToAdd = 0,
                effectiveDaysToSubtract = 0,
                effectiveDaysToAdd = 4,
                billid = BillId(),
            ),
        )
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(
                daysToSubtract = 0,
                daysToAdd = 0,
                effectiveDaysToSubtract = 0,
                effectiveDaysToAdd = 5,
                billid = BillId(),
            ),
        )
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(
                daysToSubtract = 0,
                daysToAdd = 0,
                effectiveDaysToSubtract = 0,
                effectiveDaysToAdd = 6,
                billid = BillId(),
            ),
        )
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(
                daysToSubtract = 0,
                daysToAdd = 0,
                effectiveDaysToSubtract = 0,
                effectiveDaysToAdd = 8,
                billid = BillId(),
            ),
        )
        assertEquals(7, dynamoDbBillRepository.findBillsComingDueWithin(WalletId(WALLET_ID), 7).size)
    }

    @Test
    fun `should find bills coming due when effective due date is today`() {
        createBillPaymentTable(dynamoDB)
        loadPaidBillsIntoDb(dynamoDB)
        assertEquals(0, dynamoDbBillRepository.findBillsComingDue(WalletId(ACCOUNT_ID)).size)
        addBillIntoDb(
            dynamoDbDAO,
            getBillWithDueDate(walletId = WalletId(ACCOUNT_ID), daysToSubtract = 1, effectiveDaysToAdd = 0),
        )
        assertEquals(1, dynamoDbBillRepository.findBillsComingDue(WalletId(ACCOUNT_ID)).size)
    }

    @Test
    fun `should find payment method by id`() {
        createBillPaymentTable(dynamoDB)
        loadAccountIntoDb(dynamoDB)
        accountDbRepository.findAccountPaymentMethodByIdAndAccountId(
            paymentMethodId,
            AccountId(ACCOUNT_ID),
        ).id shouldBe paymentMethodId
        assertThrows<PaymentMethodNotFound> {
            accountDbRepository.findAccountPaymentMethodByIdAndAccountId(paymentMethodId2, AccountId(ACCOUNT_ID))
        }
        loadBlockedPaymentMethods(dynamoDB)
        assertEquals(
            paymentMethodId2,
            accountDbRepository.findAccountPaymentMethodByIdAndAccountId(paymentMethodId2, AccountId(ACCOUNT_ID)).id,
        )
    }

    @ParameterizedTest
    @EnumSource(AccountStatus::class, mode = EnumSource.Mode.INCLUDE, names = ["APPROVED", "ACTIVE"])
    fun `should return account when it is in status`(accountStatus: AccountStatus) {
        createBillPaymentTable(dynamoDB)

        val account = ACCOUNT.copy(status = accountStatus)
        accountDbRepository.save(account)

        val result = accountDbRepository.findAccountByDocument(account.document)
        result.accountId shouldBe account.accountId
        result.status shouldBe account.status
    }
}