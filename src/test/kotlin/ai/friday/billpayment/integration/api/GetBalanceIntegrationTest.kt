package ai.friday.billpayment.integration.api

import ai.friday.billpayment.adapters.api.AmountBalanceTO
import ai.friday.billpayment.adapters.api.CheckoutBalanceTO
import ai.friday.billpayment.adapters.api.WalletBalanceForecastTO
import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InternalBankDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.addBillIntoDb
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.scheduledBill
import ai.friday.billpayment.scheduledBill2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class GetBalanceIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
    val balanceService: BalanceService,
    val accountService: AccountService,
) {

    private val securityFixture = SecurityFixture()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val scheduledBillRepository = ScheduledBillDBRepository(dynamoDbDAO)
    private val internalBankRepository = InternalBankDBRepository(dynamoDbDAO)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)

    @MockBean(ArbiAdapter::class)
    fun bankAccountService(): BankAccountService = mockBankAccountService
    private val mockBankAccountService: BankAccountService = mockk()

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    private val cookie = securityFixture.cookieAuthOwner

    private val todayScheduled1 = scheduledBill.copy(scheduledDate = getLocalDate())
    private val todayScheduled2 = scheduledBill2.copy(scheduledDate = getLocalDate())
    private val tomorrowScheduled =
        scheduledBill2.copy(scheduledDate = getLocalDate().plusDays(1))

    private val wallet =
        walletFixture.buildWallet(otherMembers = listOf(walletFixture.assistant, walletFixture.participant))
    private val walletRepository: WalletRepository = WalletDbRepository(dynamoDbDAO, mockk())

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private fun createBill(
        effectiveDueDate: LocalDate,
        scheduledDate: LocalDate? = null,
        status: BillStatus = BillStatus.ACTIVE,
        paymentLimitTime: String = "22:00",
    ) = getActiveBill(walletId = wallet.id).copy(
        billId = BillId("BILL-${UUID.randomUUID()}"),
        amount = 25000L,
        amountTotal = 25000L,
        effectiveDueDate = effectiveDueDate,
        dueDate = effectiveDueDate.plusMonths(1),
        schedule = scheduledDate?.let { BillViewSchedule(date = scheduledDate) },
        status = status,
        paymentLimitTime = LocalTime.parse(paymentLimitTime, timeFormat),
    ).also {
        addBillIntoDb(dynamoDbDAO, it)
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadAccountIntoDb(dynamoDB)
        balanceService.invalidate(AccountPaymentMethodId(wallet.paymentMethodId.value))
    }

    /*
        @Disabled("fazer teste com carteira")
        @Test
        fun `should return empty list on owner without bank account`() {
            val balance = fetchBalances()
            assertTrue(balance.isEmpty())
        }
    */

    @Test
    fun `should return forbidden for member with viewBalance false on cashin amount`() {
        val cookie = buildCookie(walletFixture.assistantAccount)
        walletRepository.save(wallet)
        val thrown = assertThrows<HttpClientResponseException> {
            fetchAmountBalance(cookie)
        }
        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return forbidden for member with viewBalance false on cashin balance`() {
        val cookie = buildCookie(walletFixture.assistantAccount)
        walletRepository.save(wallet)
        val thrown = assertThrows<HttpClientResponseException> {
            fetchCashinBalance(cookie)
        }
        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return forbidden for member with viewBalance false on checkout balance`() {
        val cookie = buildCookie(walletFixture.assistantAccount)
        walletRepository.save(wallet)
        val thrown = assertThrows<HttpClientResponseException> {
            fetchCheckoutBalance(cookie)
        }
        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return total amount for active bills when today is first day of month on balance cashin request`(
        memberAccount: Account,
    ) {
        val cookie = buildCookie(memberAccount)
        walletRepository.save(wallet)

        val startZonedDate = ZonedDateTime.of(2021, 1, 1, 14, 0, 0, 0, brazilTimeZone)
        val today = startZonedDate.toLocalDate()

        val yesterday = today.minusDays(1)
        val nextWeek = today.plusDays(6)
        val nextFifteenDay = today.plusDays(14)
        val nextThirtyDay = today.plusDays(29)
        val lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth())
        val nextMonth = today.with(TemporalAdjusters.firstDayOfNextMonth())
        val farAwayDate = today.plusMonths(3)

        val yesterdayBill = createBill(effectiveDueDate = yesterday)
        val todayNotScheduledBill = createBill(effectiveDueDate = today)
        val todayOverduebill = createBill(effectiveDueDate = today, paymentLimitTime = "12:00")
        val yesterdayScheduledBill =
            createBill(effectiveDueDate = yesterday, scheduledDate = yesterday)

        createBill(effectiveDueDate = today, status = BillStatus.PROCESSING)
        val todayScheduledBill =
            createBill(effectiveDueDate = today.plusMonths(1), scheduledDate = today)

        val nextWeekBill = createBill(effectiveDueDate = nextWeek)
        val nextWeekScheduledBill =
            createBill(effectiveDueDate = nextWeek, scheduledDate = nextWeek)

        val fifteenDaysBill = createBill(effectiveDueDate = nextFifteenDay)
        val fifteenDaysScheduledBill = createBill(effectiveDueDate = nextFifteenDay, scheduledDate = nextFifteenDay)

        val nextThirtyDayBill = createBill(effectiveDueDate = nextThirtyDay)
        val nextThirtyScheduledBill = createBill(effectiveDueDate = nextThirtyDay, scheduledDate = nextThirtyDay)

        val monthBill = createBill(effectiveDueDate = lastDayOfMonth)
        val monthScheduledBill =
            createBill(effectiveDueDate = lastDayOfMonth, scheduledDate = lastDayOfMonth)

        val nextMonthBill = createBill(effectiveDueDate = nextMonth)
        val nextMonthScheduledBill =
            createBill(effectiveDueDate = nextMonth, scheduledDate = nextMonth)

        createBill(effectiveDueDate = farAwayDate)
        createBill(effectiveDueDate = farAwayDate, farAwayDate)

        val todayAmount = todayNotScheduledBill.amountTotal
        val weekAmount = todayAmount + nextWeekBill.amountTotal
        val fifteenDaysAmount = weekAmount + fifteenDaysBill.amountTotal
        val thirtyDaysAmount =
            fifteenDaysAmount + nextThirtyDayBill.amountTotal
        val monthAmount = thirtyDaysAmount + monthBill.amountTotal
        val nextMonthAmount = monthAmount + nextMonthBill.amountTotal

        val todayAmountScheduled =
            yesterdayScheduledBill.amountTotal + todayScheduledBill.amountTotal
        val weekAmountScheduled = todayAmountScheduled + nextWeekScheduledBill.amountTotal
        val fifteenDaysScheduled = weekAmountScheduled + fifteenDaysScheduledBill.amountTotal
        val thirtyDaysScheduled = fifteenDaysScheduled + nextThirtyScheduledBill.amountTotal
        val monthAmountScheduled =
            thirtyDaysScheduled + monthScheduledBill.amountTotal
        val nextMonthAmountScheduled = monthAmountScheduled + nextMonthScheduledBill.amountTotal
        val amountOverdue = yesterdayBill.amountTotal + todayOverduebill.amountTotal

        every { mockBankAccountService.getBalance("11") } returns 100L
        loadBalancePaymentMethod(
            accountRepository,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
        )

        withGivenDateTime(startZonedDate) {
            val cashInBalance = fetchCashinBalance(cookie)
            with(cashInBalance) {
                overdueAmount shouldBe amountOverdue
                open.amountToday shouldBe todayAmount
                open.amountWeek shouldBe weekAmount
                open.amountFifteenDays shouldBe fifteenDaysAmount
                open.amountThirtyDays shouldBe thirtyDaysAmount
                open.amountMonth shouldBe monthAmount
                open.amountNextMonth shouldBe nextMonthAmount

                scheduled.amountToday shouldBe todayAmountScheduled
                scheduled.amountWeek shouldBe weekAmountScheduled
                scheduled.amountFifteenDays shouldBe fifteenDaysScheduled
                scheduled.amountThirtyDays shouldBe thirtyDaysScheduled
                scheduled.amountMonth shouldBe monthAmountScheduled
                scheduled.amountNextMonth shouldBe nextMonthAmountScheduled

                dates.today shouldBe today.format(dateFormat)
                dates.week shouldBe nextWeek.format(dateFormat)
                dates.fifteenDays shouldBe nextFifteenDay.format(dateFormat)
                dates.thirtyDays shouldBe nextThirtyDay.format(dateFormat)
                dates.nextMonth shouldBe nextMonth.with(TemporalAdjusters.lastDayOfMonth())
                    .format(dateFormat)
            }
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return amount on balance amount request`(memberAccount: Account) {
        val cookie = buildCookie(memberAccount)
        walletRepository.save(wallet)

        val expectedAmount = 100L
        every { mockBankAccountService.getBalance("11") } returns expectedAmount
        loadBalancePaymentMethod(
            accountRepository,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
        )

        with(fetchAmountBalance(cookie)) {
            amount shouldBe expectedAmount
        }

        verify {
            mockBankAccountService.getBalance("11")
        }
    }

    @Test
    fun `deve permitir retornar um saldo negativo`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        walletRepository.save(wallet)

        val negativeAmount = -100L

        every { mockBankAccountService.getBalance("11") } returns negativeAmount

        loadBalancePaymentMethod(
            accountRepository,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
        )

        with(fetchAmountBalance(cookie)) {
            amount shouldBe negativeAmount
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return cached value on consecutive balance amount request`(memberAccount: Account) {
        val cookie = buildCookie(memberAccount)
        walletRepository.save(wallet)

        val expectedAmount = 100L
        every { mockBankAccountService.getBalance("11") } returns expectedAmount
        loadBalancePaymentMethod(
            accountRepository,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
        )

        with(fetchAmountBalance(cookie)) {
            amount shouldBe expectedAmount
        }

        with(fetchAmountBalance(cookie)) {
            amount shouldBe expectedAmount
        }

        verify(exactly = 1) {
            mockBankAccountService.getBalance("11")
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should refresh cached value on balance amount request after cache invalidate`(memberAccount: Account) {
        val cookie = buildCookie(memberAccount)
        walletRepository.save(wallet)

        val expectedAmount = 100L
        val refreshedAmount = 0L
        every { mockBankAccountService.getBalance("11") } returns expectedAmount andThen refreshedAmount
        loadBalancePaymentMethod(
            accountRepository,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
        )

        with(fetchAmountBalance(cookie)) {
            amount shouldBe expectedAmount
        }

        balanceService.invalidate(AccountPaymentMethodId(wallet.paymentMethodId.value))

        with(fetchAmountBalance(cookie)) {
            amount shouldBe refreshedAmount
        }

        verify(exactly = 2) {
            mockBankAccountService.getBalance("11")
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return total amount for active and scheduled bills when today is 1 day to end of month on balance cashin request`(
        memberAccount: Account,
    ) {
        val cookie = buildCookie(memberAccount)
        walletRepository.save(wallet)
        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)
        val today = startZonedDate.toLocalDate()

        val yesterday = today.minusDays(1)
        val nextWeek = today.plusDays(6)
        val nextFifteenDay = today.plusDays(14)
        val nextThirtyDay = today.plusDays(29)
        val lastDayOfMonth = today.with(TemporalAdjusters.lastDayOfMonth())
        val nextMonth = today.with(TemporalAdjusters.firstDayOfNextMonth())
        val farAwayDate = today.plusMonths(3)

        val yesterdayScheduledBill =
            createBill(effectiveDueDate = yesterday, scheduledDate = yesterday)

        val yesterdayBill = createBill(effectiveDueDate = yesterday)

        val todayBill = createBill(effectiveDueDate = today)

        val nextWeekBill = createBill(effectiveDueDate = nextWeek)

        val nextFifteenDayBill = createBill(effectiveDueDate = nextFifteenDay)

        val nextThirtyDayBill = createBill(effectiveDueDate = nextThirtyDay)

        val monthBill =
            createBill(effectiveDueDate = lastDayOfMonth)

        val nextMonthBill =
            createBill(effectiveDueDate = nextMonth)

        val farAwayBillScheduledForNextMonth =
            createBill(farAwayDate, scheduledDate = nextMonth)

        createBill(farAwayDate)

        val todayAmount = todayBill.amountTotal
        val weekAmount =
            todayAmount + nextWeekBill.amountTotal + monthBill.amountTotal + nextMonthBill.amountTotal
        val monthAmount = todayAmount + monthBill.amountTotal
        val nextFifteenDayAmount = weekAmount + nextFifteenDayBill.amountTotal
        val nextThirtyDayAmount = nextFifteenDayAmount + nextThirtyDayBill.amountTotal
        val nextMonthAmount = nextThirtyDayAmount

        val todayAmountScheduled = yesterdayScheduledBill.amountTotal
        val weekAmountScheduled =
            yesterdayScheduledBill.amountTotal + farAwayBillScheduledForNextMonth.amountTotal
        val monthAmountScheduled = yesterdayScheduledBill.amountTotal
        val nextFifteenDayScheduled = monthAmountScheduled + farAwayBillScheduledForNextMonth.amountTotal
        val nextThirtyDayScheduled = nextFifteenDayScheduled
        val nextMonthAmountScheduled =
            yesterdayScheduledBill.amountTotal + farAwayBillScheduledForNextMonth.amountTotal

        val amountOverdue = yesterdayBill.amountTotal
        every { mockBankAccountService.getBalance("11") } returns 100L
        loadBalancePaymentMethod(
            accountRepository,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
        )

        withGivenDateTime(startZonedDate) {
            val cashinBalance = fetchCashinBalance(cookie)
            with(cashinBalance) {
                overdueAmount shouldBe amountOverdue
                open.amountToday shouldBe todayAmount
                open.amountWeek shouldBe weekAmount
                open.amountFifteenDays shouldBe nextFifteenDayAmount
                open.amountThirtyDays shouldBe nextThirtyDayAmount
                open.amountMonth shouldBe monthAmount
                open.amountNextMonth shouldBe nextMonthAmount

                scheduled.amountToday shouldBe todayAmountScheduled
                scheduled.amountWeek shouldBe weekAmountScheduled
                scheduled.amountFifteenDays shouldBe nextFifteenDayScheduled
                scheduled.amountThirtyDays shouldBe nextThirtyDayScheduled
                scheduled.amountMonth shouldBe monthAmountScheduled
                scheduled.amountNextMonth shouldBe nextMonthAmountScheduled

                dates.today shouldBe today.format(dateFormat)
                dates.week shouldBe nextWeek.format(dateFormat)
                dates.fifteenDays shouldBe nextFifteenDay.format(dateFormat)
                dates.thirtyDays shouldBe nextThirtyDay.format(dateFormat)
                dates.nextMonth shouldBe nextMonth.with(TemporalAdjusters.lastDayOfMonth())
                    .format(dateFormat)
            }
        }
    }

    @Disabled("fazer teste com carteira")
    @Test
    fun `should return total amount for bills scheduled for today`() {
        val totalAmount = todayScheduled1.amount + todayScheduled2.amount

        every { mockBankAccountService.getBalance("11") } returns 100L
        loadBalancePaymentMethod(accountRepository)
        scheduledBillRepository.save(todayScheduled1)
        scheduledBillRepository.save(todayScheduled2)

        val balances = fetchBalances()
        balances[0].scheduled.amountToday shouldBe totalAmount
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return total amount for bills scheduled for today on checkout balance request`(memberAccount: Account) {
        val cookie = buildCookie(memberAccount)
        walletRepository.save(wallet)

        every { mockBankAccountService.getBalance("11") } returns 150L
        loadBalancePaymentMethod(
            accountRepository,
            accountId = wallet.founder.accountId.value,
            paymentMethodId = wallet.paymentMethodId.value,
        )

        val bill1 = createBill(effectiveDueDate = getLocalDate(), scheduledDate = getLocalDate())
        val bill2 = createBill(effectiveDueDate = getLocalDate(), scheduledDate = getLocalDate())

        val balance = fetchCheckoutBalance(cookie)
        balance.amount shouldBe 150
        balance.scheduledToDateAmount shouldBe bill1.amountTotal + bill2.amountTotal
    }

    /*
        @Disabled("fazer teste com carteira")
        @Test
        fun `should return total amount for all scheduled bills`() {
            scheduledBillRepository.save(todayScheduled1)
            scheduledBillRepository.save(todayScheduled2)
            scheduledBillRepository.save(tomorrowScheduled)
            val userBalance = 100L
            every { mockBankAccountService.getBalance("11") } returns userBalance
            loadBalancePaymentMethod(accountRepository)
            val balances = fetchBalances()
            balances[0].scheduled.amountToday shouldBe todayScheduled1.amount + todayScheduled2.amount + tomorrowScheduled.amount
        }
    */

    /*
        @Disabled("fazer teste com carteira")
        @Test
        fun `virtual bank account should return balance 0 on empty bank statement`() {
            loadVirtualBalance(amazonDynamoDB = dynamoDB, paymentMethodId = PAYMENT_METHOD_ID_4)

            val accountPaymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_4)
            balanceService.invalidate(accountPaymentMethodId)

            val balances = fetchBalances()

            balances.first().amount.shouldBeZero()
            verify { mockBankAccountService wasNot called }
        }
    */

    /*
        @Disabled("fazer teste com carteira")
        @Test
        fun `virtual bank account should return balance greater than zero when has only credit operation`() {
            loadVirtualBalance(amazonDynamoDB = dynamoDB, paymentMethodId = PAYMENT_METHOD_ID_4)

            val accountPaymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_4)
            balanceService.invalidate(accountPaymentMethodId)

            internalBankRepository.create(
                InternalBankStatementItem(
                    bankStatementItem = bankStatementItem,
                    accountPaymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_4),
                ),
            )

            val balances = fetchBalances()

            balances.first().amount shouldBe bankStatementItem.amount
            verify { mockBankAccountService wasNot called }
        }
    */

    /*
        @Disabled("fazer teste com carteira")
        @ParameterizedTest
        @CsvSource(
            value = [
                "123,123,0",
                "124,100,24",
                "*********,4568420,*********",
            ],
        )
        fun `virtual bank account should return expected balance`(
            creditValue: Long,
            debitValue: Long,
            expectedValue: Long,
        ) {
            loadVirtualBalance(amazonDynamoDB = dynamoDB, paymentMethodId = PAYMENT_METHOD_ID_4)

            val accountPaymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_4)
            balanceService.invalidate(accountPaymentMethodId)

            internalBankRepository.create(
                InternalBankStatementItem(
                    bankStatementItem = bankStatementItem.copy(flow = BankStatementItemFlow.CREDIT, amount = creditValue),
                    accountPaymentMethodId = accountPaymentMethodId,
                ),
            )
            internalBankRepository.create(
                InternalBankStatementItem(
                    bankStatementItem = bankStatementItem.copy(
                        flow = BankStatementItemFlow.DEBIT,
                        amount = debitValue,
                        operationNumber = "0002",
                    ),
                    accountPaymentMethodId = accountPaymentMethodId,
                ),
            )

            val balances = fetchBalances()

            with(balances.first()) {
                amount shouldBe expectedValue
            }
            verify { mockBankAccountService wasNot called }
        }
    */

    private fun buildBalanceHttpRequest() =
        HttpRequest.GET<WalletBalanceForecastTO>("/balance")
            .cookie(cookie)
            .header("X-API-VERSION", "2")

    private fun fetchBalances(): List<WalletBalanceForecastTO> =
        client
            .toBlocking()
            .retrieve(buildBalanceHttpRequest(), Argument.listOf(WalletBalanceForecastTO::class.java))

    private fun buildCashinBalanceHttpRequest(cookie: Cookie) =
        HttpRequest.GET<WalletBalanceForecastTO>("/balance/cashin")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)

    private fun buildAmountBalanceHttpRequest(cookie: Cookie) =
        HttpRequest.GET<AmountBalanceTO>("/balance/amount")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)

    private fun fetchAmountBalance(cookie: Cookie): AmountBalanceTO =
        client
            .toBlocking()
            .retrieve(buildAmountBalanceHttpRequest(cookie), Argument.of(AmountBalanceTO::class.java))

    private fun fetchCashinBalance(cookie: Cookie): WalletBalanceForecastTO =
        client
            .toBlocking()
            .retrieve(buildCashinBalanceHttpRequest(cookie), Argument.of(WalletBalanceForecastTO::class.java))

    private fun buildCheckoutBalanceHttpRequest(cookie: Cookie) =
        HttpRequest.GET<CheckoutBalanceTO>("/balance/checkout")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)

    private fun fetchCheckoutBalance(cookie: Cookie): CheckoutBalanceTO =
        client
            .toBlocking()
            .retrieve(buildCheckoutBalanceHttpRequest(cookie), Argument.of(CheckoutBalanceTO::class.java))

    companion object {
        private val walletFixture = WalletFixture()

        @JvmStatic
        fun memberAccounts(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(walletFixture.founderAccount),
                Arguments.arguments(walletFixture.participantAccount),
            )
        }
    }
}