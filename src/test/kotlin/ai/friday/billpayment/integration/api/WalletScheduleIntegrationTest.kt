package ai.friday.billpayment.integration.api

import DynamoDBUtils
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.ScheduleResultTO
import ai.friday.billpayment.adapters.api.ScheduledBillsTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.cashIn.CashInExecutor
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAdded2
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.fingerprint
import ai.friday.billpayment.integration.CNPJ_1
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.scheduledBill
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldNotContain
import io.kotest.matchers.date.shouldHaveSameDayAs
import io.kotest.matchers.shouldBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.mockk.mockk
import java.math.BigInteger
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

abstract class WalletScheduleIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
    val lockProvider: InternalLock,
    val updateBillService: UpdateBillService,
) {

    protected val client: RxHttpClient =
        embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    open fun buildCookie(account: Account): Cookie {
        return ai.friday.billpayment.integration.buildCookie(account = account)
    }

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val dynamoDbEnhancedAsyncClient = DynamoDBUtils.getDynamoDBAsync()

    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val walletRepository: WalletRepository = WalletDbRepository(
        dynamoDbDAO = dynamoDbDAO,
        accountRepository = accountRepository,
    )
    protected val billEventRepository = BillEventDBRepository(
        dynamoDbDAO,
        allFalseFeatureConfiguration,
    )
    private val scheduledBillRepository = ScheduledBillDBRepository(dynamoDbDAO)

    private val scheduledWalletDynamoDAO = ScheduledWalletDynamoDAO(dynamoDbEnhancedAsyncClient)
    private val scheduledWalletRepository = ScheduledWalletDBRepository(scheduledWalletDynamoDAO)

    @MockBean(ScheduledBillPaymentService::class)
    fun getScheduledBillPaymentServiceMock() = scheduledBillPaymentServiceMock
    private val scheduledBillPaymentServiceMock: ScheduledBillPaymentService = mockk(relaxed = true)

    @MockBean(CashInExecutor::class)
    fun getCashInExecutorMock() = cashInExecutor
    private val cashInExecutor: CashInExecutor = mockk(relaxUnitFun = true)

    protected lateinit var wallet: Wallet

    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    lateinit var bankAccount: AccountPaymentMethod

    @BeforeEach
    open fun setup() {
        DynamoDBUtils.setupDynamoDB()
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
        accountRepository.create(walletFixture.founderAccount)
        accountRepository.create(walletFixture.participantAccount)
        accountRepository.create(walletFixture.limitedParticipantAccount)
        accountRepository.create(walletFixture.ultraLimitedParticipantAccount)
        accountRepository.create(walletFixture.cantPayParticipantAccount)

        bankAccount = accountRepository.createAccountPaymentMethod(
            accountId = walletFixture.founderAccount.accountId,
            bankAccount = BankAccount(
                accountType = AccountType.CHECKING,
                bankNo = 1L,
                routingNo = 2L,
                accountNo = BigInteger("3"),
                accountDv = "X",
                document = DOCUMENT,
            ),
            position = 2,
        )
        wallet = walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.participant,
                walletFixture.limitedParticipant,
                walletFixture.ultraLimitedParticipant,
                walletFixture.cantPayParticipant,
            ),
            accountPaymentMethodId = bankAccount.id,
        )
        walletRepository.save(wallet)
    }

    @ParameterizedTest
    @MethodSource("canScheduleAllBills")
    fun `should return schedule true`(memberAccount: Account, billsAdded: List<BillEvent>) {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)
        withGivenDateTime(now) {
            val responseBody = getScheduleResponse(billsAdded, memberAccount)

            responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
                ScheduleResultTO(
                    billId = it.billId.value,
                    scheduled = true,
                )
            }
        }
    }

    @Test
    fun `should schedule pix for today on non workdays`() {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 10, 23, 12, 0), brazilTimeZone)
        val billsAdded = listOf(
            pixAdded.copy(
                walletId = walletFixture.defaultWalletId,
                actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
            ),
        )
        withGivenDateTime(now) {
            val responseBody = getScheduleResponse(billsAdded, walletFixture.founderAccount)

            responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
                ScheduleResultTO(
                    billId = it.billId.value,
                    scheduled = true,
                )
            }
        }
    }

    @ParameterizedTest
    @MethodSource("allSchedulableBills")
    fun `should schedule for next workday when bill is after hours`(billEvent: BillEvent, now: ZonedDateTime) {
        withGivenDateTime(now) {
            val billsAdded = listOf(billEvent)

            val responseBody = getScheduleResponse(billsAdded, walletFixture.founderAccount)

            responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
                ScheduleResultTO(
                    billId = it.billId.value,
                    scheduled = true,
                )
            }

            val bill = billEventRepository.getBillById(billsAdded.single().billId)
                .getOrElse { throw NoStackTraceException("teste") }
            val expectedDate = now.toLocalDate().plusDays(1)
            bill.schedule!!.date shouldHaveSameDayAs expectedDate
        }
    }

    @ParameterizedTest
    @ValueSource(ints = [12, 23])
    fun `should schedule TED for DUE_DATE when DUE_DATE is future`(hour: Int) {
        val fixedDate = ZonedDateTime.of(LocalDate.of(2021, 9, 9), LocalTime.of(hour, 0), brazilTimeZone)
        val dueDate = LocalDate.of(2021, 9, 16)

        withGivenDateTime(fixedDate) {
            val billsAdded = listOf(
                invoiceAdded.copy(
                    walletId = wallet.id,
                    actionSource = ActionSource.WalletRecurrence(
                        accountId = walletFixture.founderAccount.accountId,
                        recurrenceId = RecurrenceId("RECURRENCE_ID"),
                    ),
                    dueDate = dueDate,
                    effectiveDueDate = dueDate,
                ),
            )

            val responseBody =
                getScheduleResponse(billsAdded, walletFixture.founderAccount, scheduleTo = ScheduleTo.DUE_DATE)

            responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
                ScheduleResultTO(
                    billId = it.billId.value,
                    scheduled = true,
                )
            }
            with(billsAdded.single()) {
                val bill = billEventRepository.getBillById(billId).getOrElse { throw NoStackTraceException("teste") }
                bill.schedule!!.date shouldHaveSameDayAs dueDate
            }
        }
    }

    @Test
    fun `should return scheduled false when cannot acquire lock`() {
        val billAdded = listOf(
            billAddedFicha.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
            ),
        )

        val lock = lockProvider.acquireLock(billAdded.single().billId.value)

        val responseBody = getScheduleResponse(billAdded, walletFixture.founderAccount)
        responseBody.billList shouldContainExactlyInAnyOrder billAdded.map {
            ScheduleResultTO(
                billId = it.billId.value,
                scheduled = false,
            )
        }
        billAdded.forEach { assertBillNotScheduled(it) }
        lock!!.unlock()
    }

    @ParameterizedTest
    @MethodSource("canScheduleNoBills")
    fun `should return scheduled false`(memberAccount: Account, billsAdded: List<BillEvent>) {
        val responseBody = getScheduleResponse(billsAdded, walletFixture.limitedParticipantAccount)

        responseBody.billList shouldContainExactlyInAnyOrder billsAdded.map {
            ScheduleResultTO(
                billId = it.billId.value,
                scheduled = false,
            )
        }
        billsAdded.forEach { assertBillNotScheduled(it) }
        scheduledWalletRepository.findAllWalletsWithScheduledBills().toIterable()
            .toList() shouldNotContain walletFixture.founderAccount.accountId
    }

    @Test
    fun `should return scheduled true and false with bill scheduled by participant with limited permission`() {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)

        val billsAdded = listOf(
            billAddedFicha.copy(
                walletId = wallet.id,
                actionSource = ActionSource.WalletMailBox(
                    accountId = walletFixture.limitedParticipantAccount.accountId,
                    from = walletFixture.limitedParticipantAccount.emailAddress.value,
                ),
            ),
            billAdded2.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
            ),
        )

        withGivenDateTime(now.withHour(12)) {
            val responseBody = getScheduleResponse(billsAdded, walletFixture.limitedParticipantAccount)
            responseBody.billList shouldContainExactlyInAnyOrder listOf(
                ScheduleResultTO(
                    billId = billAddedFicha.billId.value,
                    scheduled = true,
                ),
                ScheduleResultTO(billId = billAdded2.billId.value, scheduled = false),
            )
        }
    }

    @ParameterizedTest
    @MethodSource("canCancelScheduledBill")
    fun `should cancel schedule`(memberAccount: Account, billevents: List<BillEvent>) {
        val cookie = buildCookie(memberAccount)

        billevents.forEach { billEventRepository.save(it) }
        scheduledWalletRepository.save(wallet.id, getLocalDate())
        scheduledBillRepository.save(scheduledBill)

        val request = HttpRequest.DELETE("/schedule/${billevents[0].billId.value}", "{}")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)
        val result = client.toBlocking().exchange(request, Argument.of(ScheduleResultTO::class.java))

        result.status shouldBe HttpStatus.OK
        with(result.getBody(ScheduleResultTO::class.java).get()) {
            billId shouldBe billevents[0].billId.value
            scheduled shouldBe false
        }
        assertBillNotScheduled(billevents[0])
    }

    @ParameterizedTest
    @MethodSource("cannotCancelScheduledBill")
    fun `should not cancel schedule`(memberAccount: Account, billevents: List<BillEvent>) {
        val cookie = buildCookie(memberAccount)

        billevents.forEach { billEventRepository.save(it) }
        scheduledWalletRepository.save(wallet.id, getLocalDate())
        scheduledBillRepository.save(
            scheduledBill.copy(
                walletId = wallet.id,
                scheduledDate = getLocalDate(),
            ),
        )

        val request = HttpRequest.DELETE("/schedule/${billevents[0].billId.value}", "{}")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)
        val result = client.toBlocking().exchange(request, Argument.of(ScheduleResultTO::class.java))

        result.status shouldBe HttpStatus.OK
        with(result.getBody(ScheduleResultTO::class.java).get()) {
            billId shouldBe billevents[0].billId.value
            scheduled shouldBe true
        }
        assertBillScheduled(billevents[0], getZonedDateTime())
    }

    @Test
    fun `should not cancel schedule on bill locked`() {
        val memberAccount = walletFixture.founderAccount
        val billEvents = listOf(
            billAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
            ),
            billPaymentScheduled.copy(
                walletId = wallet.id,
                scheduledDate = getLocalDate(),
                actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
            ),
        )

        val cookie = buildCookie(memberAccount)

        billEvents.forEach { billEventRepository.save(it) }
        scheduledWalletRepository.save(wallet.id, getLocalDate())
        scheduledBillRepository.save(
            scheduledBill.copy(
                walletId = wallet.id,
                scheduledDate = getLocalDate(),
            ),
        )

        val lock = lockProvider.acquireLock(billAdded.billId.value)

        val request = HttpRequest.DELETE("/schedule/${billEvents[0].billId.value}", "{}")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)

        val result = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(ScheduleResultTO::class.java))
        }

        result.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        assertBillScheduled(billEvents[0], getZonedDateTime())

        lock!!.unlock()
    }

    @Test
    fun `should not cancel schedule on bill processing`() {
        val memberAccount = walletFixture.founderAccount
        val billEvents = listOf(
            billAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                created = getZonedDateTime().minusSeconds(10).toInstant().toEpochMilli(),
            ),
            billPaymentScheduled.copy(
                walletId = wallet.id,
                scheduledDate = getLocalDate(),
                actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                created = getZonedDateTime().minusSeconds(5).toInstant().toEpochMilli(),
            ),
            billPaymentStart.copy(
                walletId = wallet.id,
                actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                created = getZonedDateTime().minusSeconds(3).toInstant().toEpochMilli(),
            ),
        )

        val cookie = buildCookie(memberAccount)

        billEvents.forEach { billEventRepository.save(it) }
        scheduledWalletRepository.save(wallet.id, getLocalDate())
        scheduledBillRepository.save(
            scheduledBill.copy(
                walletId = wallet.id,
                scheduledDate = getLocalDate(),
            ),
        )

        val request = HttpRequest.DELETE("/schedule/${billEvents[0].billId.value}", "{}")
            .cookie(cookie)
            .header("X-API-VERSION", "2")
            .header("X-WALLET-ID", wallet.id.value)

        val result = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(ScheduleResultTO::class.java))
        }

        result.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        assertBillScheduled(billEvents[0], getZonedDateTime())
    }

    abstract fun getScheduleResponse(
        billsAdded: List<BillEvent>,
        account: Account,
        scheduleTo: ScheduleTo = ScheduleTo.TODAY,
    ): ScheduledBillsTO

    private fun assertBillScheduled(billEvent: BillEvent, zonedDateTime: ZonedDateTime) {
        val expectedDate = zonedDateTime.toLocalDate()
        val bill =
            billEventRepository.getBillById(billEvent.billId).getOrElse { throw NoStackTraceException("teste") }
        bill.isPaymentScheduled() shouldBe true
        bill.schedule?.date shouldBe expectedDate
        scheduledBillRepository.findScheduledBillById(billEvent.billId)
            .first().scheduledDate shouldBe expectedDate
    }

    private fun assertBillNotScheduled(billEvent: BillEvent) {
        val bill =
            billEventRepository.getBillById(billEvent.billId).getOrElse { throw NoStackTraceException("teste") }
        bill.isPaymentScheduled() shouldBe false
    }

    fun addCreditCard(
        accountId: AccountId,
        creditCard: CreditCard,
        status: AccountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
    ) =
        accountRepository.createAccountPaymentMethod(
            accountId = accountId,
            creditCard = creditCard,
            position = 1,
            status = status,
        ).id

    companion object {

        val walletFixture = WalletFixture()

        @JvmStatic
        fun allSchedulableBills(): Stream<Arguments> {
            val actionSource = ActionSource.WalletRecurrence(
                accountId = walletFixture.founderAccount.accountId,
                recurrenceId = RecurrenceId("RECURRENCE_ID"),
            )
            val today = LocalDate.of(2021, 9, 9)
            return Stream.of(
                Arguments.arguments(
                    invoiceAdded.copy(walletId = walletFixture.defaultWalletId, actionSource = actionSource),
                    ZonedDateTime.of(today, LocalTime.of(17, 0), brazilTimeZone),
                ),
                Arguments.arguments(
                    billAdded.copy(walletId = walletFixture.defaultWalletId, actionSource = actionSource),
                    ZonedDateTime.of(today, billAdded.paymentLimitTime, brazilTimeZone).plusMinutes(1),
                ),
                Arguments.arguments(
                    billAddedFicha.copy(walletId = walletFixture.defaultWalletId, actionSource = actionSource),
                    ZonedDateTime.of(today, billAddedFicha.paymentLimitTime, brazilTimeZone)
                        .plusMinutes(1),
                ),
            )
        }

        @JvmStatic
        fun allCreditCardSchedulableBills(): Stream<Arguments> {
            val actionSource = ActionSource.WalletRecurrence(
                accountId = walletFixture.founderAccount.accountId,
                recurrenceId = RecurrenceId("RECURRENCE_ID"),
            )
            val today = LocalDate.of(2021, 9, 9)
            return Stream.of(
                Arguments.arguments(
                    billAdded.copy(
                        walletId = walletFixture.defaultWalletId,
                        actionSource = actionSource,
                        recipient = billAddedFicha.recipient.copy(document = CNPJ_1),
                    ),
                    ZonedDateTime.of(today, billAdded.paymentLimitTime, brazilTimeZone)
                        .minusMinutes(1),
                    fingerprint,
                ),
                Arguments.arguments(
                    billAddedFicha.copy(
                        walletId = walletFixture.defaultWalletId,
                        actionSource = actionSource,
                        recipient = billAddedFicha.recipient.copy(document = CNPJ_1),
                    ),
                    ZonedDateTime.of(today, billAddedFicha.paymentLimitTime, brazilTimeZone)
                        .minusMinutes(1),
                    null,
                ),
            )
        }

        @JvmStatic
        fun allCreditCardNonSchedulableBills(): Stream<Arguments> {
            val actionSource = ActionSource.WalletRecurrence(
                accountId = walletFixture.founderAccount.accountId,
                recurrenceId = RecurrenceId("RECURRENCE_ID"),
            )
            val today = LocalDate.of(2021, 9, 9)
            return Stream.of(
                Arguments.arguments(
                    pixAdded.copy(
                        walletId = walletFixture.defaultWalletId,
                        actionSource = actionSource,
                        recipient = pixAdded.recipient!!.copy(document = CNPJ_1),
                    ),
                    ZonedDateTime.of(today, pixAdded.paymentLimitTime, brazilTimeZone).minusMinutes(1),
                ),
                Arguments.arguments(
                    invoiceAdded.copy(
                        walletId = walletFixture.defaultWalletId,
                        actionSource = actionSource,
                        recipient = invoiceAdded.recipient!!.copy(document = CNPJ_1),
                    ),
                    ZonedDateTime.of(today, invoiceAdded.paymentLimitTime, brazilTimeZone)
                        .minusMinutes(1),
                ),
                Arguments.arguments(
                    fichaCompensacaoCreditCardAdded.copy(
                        walletId = walletFixture.defaultWalletId,
                        actionSource = actionSource,
                        recipient = fichaCompensacaoCreditCardAdded.recipient.copy(document = CNPJ_1),
                    ),
                    ZonedDateTime.of(
                        today,
                        fichaCompensacaoCreditCardAdded.paymentLimitTime,
                        brazilTimeZone,
                    ).minusMinutes(1),
                ),
                Arguments.arguments(
                    billAddedFicha.copy(
                        walletId = walletFixture.defaultWalletId,
                        actionSource = actionSource,
                        recipient = billAddedFicha.recipient.copy(document = CNPJ_1),
                        fichaCompensacaoType = FichaCompensacaoType.DEPOSITO_E_APORTE,
                    ),
                    ZonedDateTime.of(
                        today,
                        fichaCompensacaoCreditCardAdded.paymentLimitTime,
                        brazilTimeZone,
                    ).minusMinutes(1),
                ),
                Arguments.arguments(
                    billAddedFicha.copy(
                        walletId = walletFixture.defaultWalletId,
                        actionSource = actionSource,
                        document = "***********",
                    ),
                    ZonedDateTime.of(today, billAddedFicha.paymentLimitTime, brazilTimeZone)
                        .minusMinutes(1),
                ),
            )
        }

        @JvmStatic
        fun canScheduleAllBills(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    walletFixture.founderAccount,
                    listOf(
                        billAddedFicha.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                        billAdded2.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.participantAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.founderAccount,
                    listOf(
                        invoiceAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.participantAccount,
                    listOf(
                        billAddedFicha.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                        billAdded2.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.participantAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.limitedParticipantAccount,
                    listOf(
                        billAddedFicha.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.limitedParticipantAccount.accountId),
                        ),
                        billAdded2.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.WalletRecurrence(
                                accountId = walletFixture.limitedParticipantAccount.accountId,
                                recurrenceId = RecurrenceId("RECURRENCE_ID"),
                            ),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.ultraLimitedParticipantAccount,
                    listOf(
                        billAddedFicha.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipantAccount.accountId),
                        ),
                        billAdded2.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.WalletMailBox(
                                accountId = walletFixture.ultraLimitedParticipantAccount.accountId,
                                from = walletFixture.ultraLimitedParticipantAccount.emailAddress.value,
                            ),
                        ),
                    ),
                ),
            )
        }

        @JvmStatic
        fun canScheduleNoBills(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    walletFixture.limitedParticipantAccount,
                    listOf(
                        billAddedFicha.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.WalletRecurrence(
                                accountId = walletFixture.founderAccount.accountId,
                                recurrenceId = RecurrenceId("RECURRENCE_ID"),
                            ),
                        ),
                        billAdded2.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.ultraLimitedParticipantAccount,
                    listOf(
                        billAddedFicha.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.WalletRecurrence(
                                accountId = walletFixture.founderAccount.accountId,
                                recurrenceId = RecurrenceId("RECURRENCE_ID"),
                            ),
                        ),
                        billAdded2.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.cantPayParticipantAccount,
                    listOf(
                        billAddedFicha.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.cantPayParticipantAccount.accountId),
                        ),
                        billAdded2.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.cantPayParticipantAccount.accountId),
                        ),
                    ),
                ),
            )
        }

        @JvmStatic
        fun canCancelScheduledBill(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    walletFixture.founderAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.founderAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.participantAccount.accountId),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.participantAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.participantAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.participantAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.participantAccount.accountId),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.participantAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.limitedParticipantAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.limitedParticipantAccount.accountId),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.limitedParticipantAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.limitedParticipantAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.WalletRecurrence(
                                accountId = walletFixture.limitedParticipantAccount.accountId,
                                recurrenceId = RecurrenceId("RECURRENCE_ID"),
                            ),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.limitedParticipantAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.ultraLimitedParticipantAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipantAccount.accountId),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipantAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.ultraLimitedParticipantAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.WalletMailBox(
                                accountId = walletFixture.ultraLimitedParticipantAccount.accountId,
                                from = walletFixture.ultraLimitedParticipantAccount.emailAddress.value,
                            ),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipantAccount.accountId),
                        ),
                    ),
                ),
            )
        }

        @JvmStatic
        fun cannotCancelScheduledBill(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    walletFixture.ultraLimitedParticipantAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            scheduledDate = getLocalDate(),
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                    ),
                ),
                Arguments.arguments(
                    walletFixture.ultraLimitedParticipantAccount,
                    listOf(
                        billAdded.copy(
                            walletId = walletFixture.defaultWalletId,
                            actionSource = ActionSource.WalletMailBox(
                                accountId = walletFixture.founderAccount.accountId,
                                from = walletFixture.founderAccount.emailAddress.value,
                            ),
                        ),
                        billPaymentScheduled.copy(
                            walletId = walletFixture.defaultWalletId,
                            scheduledDate = getLocalDate(),
                            actionSource = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
                        ),
                    ),
                ),
            )
        }
    }
}