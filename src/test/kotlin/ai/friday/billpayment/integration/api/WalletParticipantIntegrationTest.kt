package ai.friday.billpayment.integration.api

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.CASHIN_TRANSACTIONID
import ai.friday.billpayment.RECIPIENT_ID
import ai.friday.billpayment.RECIPIENT_PIX_EMAIL_KEY
import ai.friday.billpayment.adapters.api.ActionSourceType
import ai.friday.billpayment.adapters.api.AddConcessionariaTO
import ai.friday.billpayment.adapters.api.AddFichaDeCompensacaoTO
import ai.friday.billpayment.adapters.api.BillSourceTO
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.BoletoReceiptTO
import ai.friday.billpayment.adapters.api.CashInRequestTO
import ai.friday.billpayment.adapters.api.CreateInvoiceTO
import ai.friday.billpayment.adapters.api.CreatePixTO
import ai.friday.billpayment.adapters.api.EntriesTO
import ai.friday.billpayment.adapters.api.PixKeyRequestTO
import ai.friday.billpayment.adapters.api.ReceiptFilesTO
import ai.friday.billpayment.adapters.api.ReceiptTO
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.RecurrenceRequestTO
import ai.friday.billpayment.adapters.api.RequestPixRecipientTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.TransactionPaymentMethodTO
import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.celcoin.CelcoinBillValidationResponse
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDetailsEntity
import ai.friday.billpayment.adapters.dynamodb.BillEventEntity
import ai.friday.billpayment.adapters.dynamodb.BillIgnoredEntity
import ai.friday.billpayment.adapters.dynamodb.BillMarkedAsPaidCanceledEntity
import ai.friday.billpayment.adapters.dynamodb.BillMarkedAsPaidEntity
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.DescriptionUpdatedEntity
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.FeaturesDbRepository
import ai.friday.billpayment.adapters.dynamodb.FeaturesDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ReceiptDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.s3.S3LinkGenerator
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.AddBillError
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillMarkedAsPaid
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillError
import ai.friday.billpayment.app.bill.CreateBoletoRequest
import ai.friday.billpayment.app.bill.billLockProvider
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.contact.SavedPixKey
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.manualentry.ManualEntryService
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyException
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.createInvoiceTO
import ai.friday.billpayment.createSavedRecipient
import ai.friday.billpayment.fichaRegisterData
import ai.friday.billpayment.getActiveBill
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.BILL_ID_4
import ai.friday.billpayment.integration.CONCESSIONARIA_DIGITABLE_LINE
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.UpdateBillDescriptionIntegrationTest
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.addBillEventIntoDb
import ai.friday.billpayment.integration.addBillIntoDb
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.configureDailyLimits
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.onWallet
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.successConcessionariaValidationResponse
import ai.friday.billpayment.sundayDate
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import ai.friday.billpayment.withPixParticipants
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.assertions.assertSoftly
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldNotBeEmpty
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifyOrder
import jakarta.inject.Named
import java.math.BigInteger
import java.time.Instant
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.time.temporal.TemporalAdjusters
import java.util.UUID
import java.util.stream.Stream
import kotlin.test.assertNull
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@Property(name = "recurrence.limitDate", value = limitDate)
@MicronautTest(environments = [FRIDAY_ENV])
class WalletParticipantIntegrationTest(
    embeddedServer: EmbeddedServer,
    private val dynamoDB: AmazonDynamoDB,
    @Named(billLockProvider) private val lockProvider: InternalLock,
) {
    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    private val securityFixture = SecurityFixture()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val featuresDbRepository = FeaturesDbRepository(client = FeaturesDynamoDAO(cli = dynamoDbEnhancedClient))

    @MockBean(FeaturesDbRepository::class)
    fun featuresDbRepository() = featuresDbRepository

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(ManualEntryService::class)
    fun manualEntryService() = manualEntryService
    private val manualEntryService: ManualEntryService = mockk {
        every { listAllForWalletMember(any(), any()) } returns emptyList()
    }

    private val arbiValidationResponse =
        ArbiValidationResponse(billRegisterData = fichaRegisterData, paymentStatus = 12, resultado = "SUCESSO")

    @MockBean(BillValidationService::class)
    fun getArbiAdapter(): BillValidationService = arbiAdapter
    private val arbiAdapter: BillValidationService = mockk()
    private val billEventRepository = BillEventDBRepository(
        dynamoDbDAO,
        allFalseFeatureConfiguration,
    )
    private val recipientDbRepository = ContactDbRepository(dynamoDbDAO)

    @MockBean(PixKeyManagement::class)
    fun pixKeyManagementFun(): PixKeyManagement = pixKeyManagement
    private val pixKeyManagement: PixKeyManagement = mockk()

    @MockBean(BoletoSettlementService::class)
    fun openBankingMock(): BoletoSettlementService = mockk {
        every { validateBill(any<CreateBoletoRequest>()) } answers { successConcessionariaValidationResponse }
        every { validateBill(any<Bill>()) } answers { successConcessionariaValidationResponse }
    }

    @MockBean(S3LinkGenerator::class)
    fun getS3LinkGenerator() = s3LinkGenerator
    private val s3LinkGenerator: S3LinkGenerator = mockk()

    @MockBean(ObjectRepository::class)
    fun getObjectRepository() = objectRepository
    private val objectRepository: ObjectRepository = mockk(relaxed = true)

    private val billRepository = DynamoDbBillRepository(dynamoDbDAO)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val walletRepository: WalletRepository = WalletDbRepository(dynamoDbDAO, mockk())
    private val recurrenceRepository = BillRecurrenceDBRepository(dynamoDbDAO, limitDate)
    private val receiptRepository = ReceiptDbRepository(dynamoDbDAO)
    private val recurrenceDBRepository = BillRecurrenceDBRepository(dynamoDbDAO, "2021-12-31")

    private val walletLimitsService = WalletLimitsService(
        billRepository = mockk(),
        walletRepository = walletRepository,
        accountService = mockk(),
        paymentSchedulingService = mockk(),
        internalLock = lockProvider,
    )

    private val walletLimit: Long = 999

    private val wallet = walletFixture.buildWallet(
        otherMembers = listOf(
            walletFixture.participant,
            walletFixture.ultraLimitedParticipant,
            walletFixture.assistant,
        ),
    )

    private val pixKeyDocument = "***********"
    private val pixKeyDetails = PixKeyDetails(
        key = PixKey(value = pixKeyDocument, type = PixKeyType.CPF),
        holder = PixKeyHolder(
            accountNo = BigInteger("12345"),
            accountDv = "X",
            ispb = "********",
            institutionName = "banco fake",
            accountType = AccountType.CHECKING,
            routingNo = 1L,
        ),
        owner = PixKeyOwner("Ze", pixKeyDocument),
    )

    private val bankAccountRequestTO = CreatePixTO(
        amount = 10,
        description = "description",
        dueDate = getLocalDate().with(TemporalAdjusters.firstDayOfNextMonth()).format(dateFormat),
        recurrence = RecurrenceRequestTO(
            frequency = RecurrenceFrequency.MONTHLY,
            endDate = getLocalDate().with(TemporalAdjusters.firstDayOfNextMonth()).plusMonths(1).format(dateFormat),
        ),
        recipient = RequestPixRecipientTO(
            id = RECIPIENT_ID,
            name = "Ze",
            document = "***********",
            documentType = "CPF",
            bankDetails = RecipientBankDetailsTO(
                accountType = AccountType.CHECKING,
                routingNo = 1,
                accountNo = BigInteger("*********"),
                accountDv = "2",
                ispb = "********",
            ),
            alias = "Ze",
        ),
    )

    private val pixKeyRequestTO = CreatePixTO(
        amount = 10,
        description = "description",
        dueDate = getLocalDate().with(TemporalAdjusters.firstDayOfNextMonth()).format(dateFormat),
        recipient = RequestPixRecipientTO(
            id = RECIPIENT_ID,
            name = "Ze",
            document = "***********",
            documentType = "CPF",
            pixKey = PixKeyRequestTO(RECIPIENT_PIX_EMAIL_KEY, PixKeyType.EMAIL),
            alias = "Ze",
            bankDetails = null,
        ),
    )

    private fun postPix(request: Any, dryRun: Boolean, account: Account) =
        HttpRequest.POST("/bill/pix?dryRun=$dryRun", request)
            .onWallet(wallet, account)

    private fun retrieveBills(account: Account) = client.toBlocking()
        .retrieve(
            HttpRequest
                .GET<EntriesTO>("/entries")
                .onWallet(wallet, account),
            Argument.of(EntriesTO::class.java),
        ).bills

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
        accountRepository.create(walletFixture.ultraLimitedParticipantAccount)
        accountRepository.create(walletFixture.participantAccount)
        accountRepository.create(walletFixture.founderAccount)
        accountRepository.create(walletFixture.assistantAccount)
        walletRepository.save(wallet)
        configureDailyLimits(
            walletLimitsService = walletLimitsService,
            accountId = walletFixture.assistantAccount.accountId,
            walletId = wallet.id,
            dailyLimit = walletLimit,
            nighttimeLimit = walletLimit,
        )

        every {
            pfmWalletCategoryService.findWalletCategories(any())
        } returns emptyList()
    }

    @ParameterizedTest
    @MethodSource("billRequests")
    fun `should return not found when wallet id does not exist`(billRequest: MutableHttpRequest<*>) {
        val thrown = assertThrows<HttpClientResponseException> {
            val request = billRequest
                .cookie(securityFixture.cookieAuthOwner)
                .header("X-API-VERSION", "2")
                .header("X-WALLET-ID", "INVALID-WALLET-ID")
            client.exchange(request, String::class.java).firstOrError().blockingGet()
        }
        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @ParameterizedTest
    @MethodSource("billRequests")
    fun `should return forbidden when user is not a wallet member`(billRequest: MutableHttpRequest<*>) {
        val thrown = assertThrows<HttpClientResponseException> {
            val request = billRequest.onWallet(wallet, walletFixture.cantPayParticipantAccount)
            client.exchange(request, String::class.java).firstOrError().blockingGet()
        }
        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @ParameterizedTest
    @MethodSource("billRequests")
    fun `should return conflict when user is a wallet member but x-wallet-id header is missing`(
        billRequest: MutableHttpRequest<*>,
    ) {
        val cookie = buildCookie(walletFixture.founderAccount)
        val thrown = assertThrows<HttpClientResponseException> {
            val request = billRequest
                .cookie(cookie)
                .header("X-API-VERSION", "2")
            client.exchange(request, String::class.java).firstOrError().blockingGet()
        }
        thrown.status shouldBe HttpStatus.CONFLICT
    }

    @Test
    fun `should return all bills when user is the wallet founder`() {
        val founderBill = getActiveBill(wallet.id).copy(
            billId = BillId("BILL-${UUID.randomUUID()}"),
            source = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
        )
        val participantBill = getActiveBill(wallet.id).copy(
            billId = BillId("BILL-${UUID.randomUUID()}"),
            source = ActionSource.Api(accountId = walletFixture.participantAccount.accountId),
        )
        loadAccountIntoDb(dynamoDB, accountId = walletFixture.founderAccount.accountId)
        addBillIntoDb(dynamoDbDAO, founderBill)
        addBillIntoDb(dynamoDbDAO, participantBill)

        val bills = retrieveBills(walletFixture.founderAccount)
        bills.size shouldBe 2
        bills.map { it.id } shouldContainExactlyInAnyOrder listOf(founderBill.billId.value, participantBill.billId.value)
    }

    @Test
    fun `should only return bills created by the member when member has member permission to view ONLY_BILLS_ADDED_BY_USER `() {
        val founderBill = getActiveBill(wallet.id).copy(
            billId = BillId("BILL-${UUID.randomUUID()}"),
            source = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
        )
        val limitedParticipantBill = getActiveBill(wallet.id).copy(
            billId = BillId("BILL-${UUID.randomUUID()}"),
            source = ActionSource.Api(accountId = walletFixture.ultraLimitedParticipantAccount.accountId),
        )
        loadAccountIntoDb(dynamoDB, accountId = walletFixture.founderAccount.accountId)
        addBillIntoDb(dynamoDbDAO, founderBill)
        addBillIntoDb(dynamoDbDAO, limitedParticipantBill)

        val bills = retrieveBills(walletFixture.ultraLimitedParticipantAccount)
        bills.size shouldBe 1
        bills.single().id shouldBe limitedParticipantBill.billId.value
    }

    @Test
    fun `should not return moved bills`() {
        val movedBill = getActiveBill(wallet.id).copy(
            billId = BillId("BILL-${UUID.randomUUID()}"),
            source = ActionSource.Api(accountId = walletFixture.founderAccount.accountId),
            status = BillStatus.MOVED,
        )

        loadAccountIntoDb(dynamoDB, accountId = walletFixture.founderAccount.accountId)
        addBillIntoDb(dynamoDbDAO, movedBill)

        val bills = retrieveBills(walletFixture.founderAccount)
        bills.size shouldBe 0
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on successful create pix by key`(memberAccount: Account) {
        recipientDbRepository.save(createSavedRecipient().copy(accountId = memberAccount.accountId))
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val response =
            client.exchange(postPix(pixKeyRequestTO, false, memberAccount), BillTO::class.java).firstOrError()
                .blockingGet()

        response.status shouldBe HttpStatus.CREATED
        validatePixByKeyResponse(response, memberAccount.accountId)
        val billId = BillId(response.body()!!.id)
        val getBillResponse = billEventRepository.getBill(wallet.id, billId)
        verify {
            pixKeyManagement.findKeyDetailsCacheable(
                PixKey(
                    pixKeyRequestTO.recipient.pixKey!!.value,
                    pixKeyRequestTO.recipient.pixKey!!.type,
                ),
                memberAccount.document,
            )
        }
        getBillResponse.isRight() shouldBe true
        getBillResponse.map {
            it.recipient?.pixKeyDetails shouldBe pixKeyDetails
            it.source shouldBe ActionSource.Api(memberAccount.accountId)
        }

        val recipient = recipientDbRepository.findByIdAndAccountId(
            ContactId(pixKeyRequestTO.recipient.id!!),
            memberAccount.accountId,
        )
        recipient.pixKeys shouldContain SavedPixKey(pixKeyDetails.key.value, pixKeyDetails.key.type)

        with(billRepository.findBill(billId, wallet.id)) {
            this.recipient?.pixKeyDetails shouldBe pixKeyDetails
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on dryrun create pix by key and list possibleDuplicateBills`(memberAccount: Account) {
        recipientDbRepository.save(createSavedRecipient().copy(accountId = memberAccount.accountId))
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val duplicatedBill =
            Bill.build(
                pixAdded.copy(
                    walletId = wallet.id,
                    billId = BillId(UUID.randomUUID().toString()),
                    amountTotal = bankAccountRequestTO.amount,
                    effectiveDueDate = getLocalDate(),
                    recipient = pixAdded.recipient!!.copy(document = bankAccountRequestTO.recipient.document),
                ),
            )
        billRepository.save(duplicatedBill)

        val response =
            client.exchange(
                postPix(
                    pixKeyRequestTO.copy(
                        dueDate = getLocalDate().format(
                            dateFormat,
                        ),
                    ),
                    true,
                    memberAccount,
                ),
                BillTO::class.java,
            ).firstOrError()
                .blockingGet()

        response.status shouldBe HttpStatus.CREATED
        with(response.body()!!) {
            possibleDuplicateBills.shouldNotBeEmpty()
            possibleDuplicateBills.first().billId shouldBe duplicatedBill.billId.value
            possibleDuplicateBills.first().dueDate shouldBe duplicatedBill.effectiveDueDate.format(
                dateFormat,
            )
        }
    }

    @Test
    fun `should create contact on founder account when assistant creates pix by key`() {
        recipientDbRepository.save(createSavedRecipient().copy(accountId = walletFixture.founderAccount.accountId))
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val response =
            client.exchange(postPix(pixKeyRequestTO, false, walletFixture.assistantAccount), BillTO::class.java)
                .firstOrError()
                .blockingGet()

        response.status shouldBe HttpStatus.CREATED
        validatePixByKeyResponse(response, walletFixture.assistantAccount.accountId)
        val billId = BillId(response.body()!!.id)
        val getBillResponse = billEventRepository.getBill(wallet.id, billId)
        verify {
            pixKeyManagement.findKeyDetailsCacheable(
                PixKey(
                    pixKeyRequestTO.recipient.pixKey!!.value,
                    pixKeyRequestTO.recipient.pixKey!!.type,
                ),
                walletFixture.assistantAccount.document,
            )
        }
        getBillResponse.isRight() shouldBe true
        getBillResponse.map {
            it.recipient?.pixKeyDetails shouldBe pixKeyDetails
            it.source shouldBe ActionSource.Api(walletFixture.assistantAccount.accountId)
        }

        val recipient =
            recipientDbRepository.findByIdAndAccountId(ContactId(RECIPIENT_ID), walletFixture.founderAccount.accountId)
        recipient.pixKeys shouldContain SavedPixKey(pixKeyDetails.key.value, pixKeyDetails.key.type)

        with(billRepository.findBill(billId, wallet.id)) {
            this.recipient?.pixKeyDetails shouldBe pixKeyDetails
        }
    }

    @Test
    fun `should return error when assistant creates pix using own contacts`() {
        recipientDbRepository.save(createSavedRecipient().copy(accountId = walletFixture.assistantAccount.accountId))
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(postPix(pixKeyRequestTO, false, walletFixture.assistantAccount), BillTO::class.java)
                .firstOrError()
                .blockingGet()
        }
        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }

    @Test
    fun `should return error when founder creates pix using an assistant contact`() {
        recipientDbRepository.save(createSavedRecipient().copy(accountId = walletFixture.assistantAccount.accountId))
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails, "")

        val thrown = assertThrows<HttpClientResponseException> {
            client.exchange(postPix(pixKeyRequestTO, false, walletFixture.founderAccount), BillTO::class.java)
                .firstOrError()
                .blockingGet()
        }
        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on successful create pix with recurrence`(memberAccount: Account) {
        recipientDbRepository.save(createSavedRecipient().copy(accountId = memberAccount.accountId))

        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val response = client.exchange(
                postPix(bankAccountRequestTO, false, memberAccount),
                BillTO::class.java,
            ).firstOrError().blockingGet()

            response.status shouldBe HttpStatus.CREATED
            validatePixRecurrenceResponse(response.body()!!, bankAccountRequestTO, memberAccount.accountId)
            val recurrenceId = RecurrenceId(response.body()!!.recurrence!!.id)
            val recurrence = recurrenceRepository.find(recurrenceId, wallet.id)
            assertSoftly {
                val responseBody = response.body()!!
                with(responseBody) {
                    id shouldBe recurrence.bills.first().value
                    this.recurrence!!.startDate shouldBe bankAccountRequestTO.dueDate
                    this.recurrence!!.frequency shouldBe RecurrenceFrequency.MONTHLY.name
                }
                with(recurrence) {
                    this.actionSource shouldBe ActionSource.Api(accountId = memberAccount.accountId)
                    this.rule.frequency shouldBe RecurrenceFrequency.MONTHLY
                }
            }
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on dry run pix with recurrence and show possibleDuplicateBills`(memberAccount: Account) {
        recipientDbRepository.save(createSavedRecipient().copy(accountId = memberAccount.accountId))
        val duedate = getLocalDate().plusMonths(1).withDayOfMonth(27)
        val duplicatedBill =
            Bill.build(
                pixAdded.copy(
                    walletId = wallet.id,
                    billId = BillId(UUID.randomUUID().toString()),
                    amountTotal = bankAccountRequestTO.amount,
                    dueDate = duedate,
                    effectiveDueDate = duedate,
                    recipient = pixAdded.recipient!!.copy(document = bankAccountRequestTO.recipient.document),
                ),
            )
        billRepository.save(duplicatedBill)

        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val response = client.exchange(
                postPix(
                    bankAccountRequestTO.copy(
                        dueDate = duedate.format(dateFormat),
                    ),
                    true,
                    memberAccount,
                ),
                BillTO::class.java,
            ).firstOrError().blockingGet()

            response.status shouldBe HttpStatus.CREATED

            assertSoftly {
                val responseBody = response.body()!!
                with(responseBody) {
                    this.possibleDuplicateBills.shouldNotBeEmpty()
                    this.possibleDuplicateBills.first().billId shouldBe duplicatedBill.billId.value
                    this.possibleDuplicateBills.first().dueDate shouldBe duplicatedBill.effectiveDueDate.format(
                        dateFormat,
                    )
                }
            }
        }
    }

    @Test
    fun `should return 201 when assistant create pix with recurrence using founders contact`() {
        recipientDbRepository.save(createSavedRecipient().copy(accountId = walletFixture.founderAccount.accountId))

        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val response = client.exchange(
                postPix(bankAccountRequestTO, false, walletFixture.assistantAccount),
                BillTO::class.java,
            ).firstOrError().blockingGet()

            response.status shouldBe HttpStatus.CREATED
            validatePixRecurrenceResponse(
                response.body()!!,
                bankAccountRequestTO,
                walletFixture.assistantAccount.accountId,
            )
            val recurrenceId = RecurrenceId(response.body()!!.recurrence!!.id)
            val recurrence = recurrenceRepository.find(recurrenceId, wallet.id)
            assertSoftly {
                val responseBody = response.body()!!
                with(responseBody) {
                    id shouldBe recurrence.bills.first().value
                    this.recurrence!!.startDate shouldBe bankAccountRequestTO.dueDate
                    this.recurrence!!.frequency shouldBe RecurrenceFrequency.MONTHLY.name
                }
                with(recurrence) {
                    this.actionSource shouldBe ActionSource.Api(accountId = walletFixture.assistantAccount.accountId)
                    this.rule.frequency shouldBe RecurrenceFrequency.MONTHLY
                }
            }
        }
    }

    @Test
    fun `should bad request and invalidate recipient key on pix key error when assistant create pix using founders contact`() {
        every {
            pixKeyManagement.findKeyDetailsCacheable(any(), any())
        } throws PixKeyException(PixKeyError.KeyNotFound)

        val savedRecipient = createSavedRecipient().copy(accountId = walletFixture.founderAccount.accountId)
        recipientDbRepository.save(savedRecipient)

        withPixParticipants(FinancialInstitution(name = "Banco do Brasil SA", compe = 1L, ispb = "********")) {
            val call = client.exchange(
                postPix(pixKeyRequestTO, false, walletFixture.assistantAccount),
                BillTO::class.java,
            )
            val thrown = assertThrows<HttpClientResponseException> { call.firstOrError().blockingGet() }

            thrown.status shouldBe HttpStatus.BAD_REQUEST
            with(thrown.response.getBody(ResponseTO::class.java).get()) {
                this.code shouldBe CreateBillError.PIX_KEY_NOT_FOUND.code
            }
            val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
                accountId = walletFixture.founderAccount.accountId,
                savedRecipient.document,
            )
            recipient.isRight() shouldBe true
            recipient.map {
                it.pixKeys[0].invalidated shouldBe true
            }
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on successful create invoice`(memberAccount: Account) {
        val request = HttpRequest.POST("/bill/invoice", createInvoiceTO)
            .onWallet(wallet, memberAccount)

        withGivenDateTime(ZonedDateTime.of(sundayDate.plusDays(1), LocalTime.NOON, brazilTimeZone)) {
            val response = client.toBlocking()
                .exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

            val billTO = response.getBody(BillTO::class.java).get()
            val billView = billRepository.findBill(BillId(billTO.id), wallet.id)
            response.status shouldBe HttpStatus.CREATED
            billView.source shouldBe ActionSource.Api(memberAccount.accountId)

            billTO.shouldHaveSameContentAs(createInvoiceTO, memberAccount.accountId)
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on successful dry run create invoice and list possibleDuplicateBills`(memberAccount: Account) {
        withGivenDateTime(ZonedDateTime.of(2022, 2, 25, 15, 0, 0, 0, brazilTimeZone)) {
            val createInvoiceTO = createInvoiceTO.copy(
                dueDate = getLocalDate().format(dateFormat),
            )

            val duplicatedBill =
                Bill.build(
                    invoiceAdded.copy(
                        walletId = wallet.id,
                        billId = BillId(UUID.randomUUID().toString()),
                        amountTotal = createInvoiceTO.amount,
                        effectiveDueDate = getLocalDate(),
                        recipient = invoiceAdded.recipient!!.copy(document = createInvoiceTO.recipient.document),
                    ),
                )
            billRepository.save(duplicatedBill)

            val request = HttpRequest.POST("/bill/invoice?dryRun=true", createInvoiceTO)
                .onWallet(wallet, memberAccount)
            val response = client.toBlocking()
                .exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))

            val billTO = response.getBody(BillTO::class.java).get()

            response.status shouldBe HttpStatus.CREATED
            billTO.possibleDuplicateBills.shouldNotBeEmpty()
            billTO.possibleDuplicateBills.first().billId shouldBe duplicatedBill.billId.value
            billTO.possibleDuplicateBills.first().dueDate shouldBe duplicatedBill.effectiveDueDate.format(
                dateFormat,
            )
        }
    }

    @Test
    fun `should create contact on founder account when assistant create invoice`() {
        val request = HttpRequest.POST("/bill/invoice", createInvoiceTO)
            .onWallet(wallet, walletFixture.assistantAccount)

        withGivenDateTime(ZonedDateTime.of(sundayDate.plusDays(1), LocalTime.NOON, brazilTimeZone)) {
            val response = client.toBlocking()
                .exchange(request, Argument.of(BillTO::class.java), Argument.of(ResponseTO::class.java))
            val billTO = response.getBody(BillTO::class.java).get()
            response.status shouldBe HttpStatus.CREATED
            billTO.shouldHaveSameContentAs(createInvoiceTO, walletFixture.assistantAccount.accountId)
            val recipient = recipientDbRepository.findRecipientByAccountIDAndDocument(
                walletFixture.founderAccount.accountId,
                createInvoiceTO.recipient.document,
            )
            recipient.isRight() shouldBe true
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on successful create invoice with recurrence`(memberAccount: Account) {
        val createInvoiceTO = createInvoiceTO.copy(
            dueDate = limitDate,
            recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
        )

        val request = HttpRequest.POST("/bill/invoice", createInvoiceTO)
            .onWallet(wallet, memberAccount)

        val response = client.toBlocking().exchange(request, BillTO::class.java)

        response.status shouldBe HttpStatus.CREATED
        val recurrence =
            recurrenceRepository.find(RecurrenceId(response.body()!!.recurrence!!.id), wallet.id)
        assertSoftly {
            val responseBody = response.body()!!
            with(responseBody) {
                id shouldBe recurrence.bills.first().value
                source.type shouldBe "Recurrence"
                source.accountId shouldBe memberAccount.accountId.value
                this.recurrence!!.startDate shouldBe createInvoiceTO.dueDate
                this.recurrence!!.frequency shouldBe RecurrenceFrequency.WEEKLY.name
                this.possibleDuplicateBills.shouldBeEmpty()
            }
            with(recurrence) {
                this.actionSource shouldBe ActionSource.Api(accountId = memberAccount.accountId)
                this.rule.frequency shouldBe RecurrenceFrequency.WEEKLY
            }
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on successful dry run invoice with recurrence and list similar bills`(memberAccount: Account) {
        val createInvoiceTO = createInvoiceTO.copy(
            dueDate = getLocalDate().format(dateFormat),
            recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
        )

        val duplicatedBill =
            Bill.build(
                invoiceAdded.copy(
                    walletId = wallet.id,
                    billId = BillId(UUID.randomUUID().toString()),
                    amountTotal = createInvoiceTO.amount,
                    effectiveDueDate = getLocalDate(),
                    recipient = invoiceAdded.recipient!!.copy(document = createInvoiceTO.recipient.document),
                ),
            )
        billRepository.save(duplicatedBill)

        val request = HttpRequest.POST("/bill/invoice?dryRun=true", createInvoiceTO)
            .onWallet(wallet, memberAccount)

        val response = client.toBlocking().exchange(request, BillTO::class.java)

        response.status shouldBe HttpStatus.CREATED

        val responseBody = response.body()!!
        with(responseBody) {
            this.possibleDuplicateBills.shouldNotBeEmpty()
            this.possibleDuplicateBills.first().billId shouldBe duplicatedBill.billId.value
            this.possibleDuplicateBills.first().dueDate shouldBe duplicatedBill.effectiveDueDate.format(
                dateFormat,
            )
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on successful create concessionaria`(memberAccount: Account) {
        val request = HttpRequest.POST("/bill/concessionaria", concessionariaTO)
            .onWallet(wallet, memberAccount)

        val response = client.toBlocking().exchange(request, BillTO::class.java)

        val billTO = response.getBody(BillTO::class.java).get()
        val billView = billRepository.findBill(BillId(billTO.id), wallet.id)
        response.status shouldBe HttpStatus.CREATED
        billView.source shouldBe ActionSource.Api(memberAccount.accountId)

        assertConcessionariaBillTO(
            billTO,
            concessionariaTO,
            successConcessionariaValidationResponse,
            memberAccount.accountId,
        )
    }

    @ParameterizedTest
    @MethodSource("memberAccountsWithVisibilityPermission")
    fun `should not create concessionaria when digitable line already exists`(memberAccount: Account) {
        val alreadyExistsBill = getPaidBill().copy(walletId = wallet.id)
        addBillIntoDb(dynamoDbDAO, alreadyExistsBill)
        billEventRepository.save(
            billAdded.copy(
                walletId = wallet.id,
                actionSource = alreadyExistsBill.source,
                barcode = alreadyExistsBill.barCode,
            ),
        )

        val request = HttpRequest.POST("/bill/concessionaria", concessionariaTO)
            .onWallet(wallet, memberAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, BillTO::class.java)
        }
        thrown.response.status shouldBe HttpStatus.BAD_REQUEST
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe AddBillError.BILL_ALREADY_INCLUDED.code
            message shouldBe AddBillError.BILL_ALREADY_INCLUDED.description
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should return 201 on successful create ficha de compensacao`(memberAccount: Account) {
        every { arbiAdapter.validate(ofType(BarCode::class)) } answers { arbiValidationResponse }

        val validAddFichaCompensacaoRequest = HttpRequest.POST("/bill/ficha-compensacao", fichaCompensacaoTO)
            .onWallet(wallet, memberAccount)

        val response = client.toBlocking().exchange(validAddFichaCompensacaoRequest, BillTO::class.java)

        val billTO = response.getBody(BillTO::class.java).get()
        val billView = billRepository.findBill(BillId(billTO.id), wallet.id)
        response.status shouldBe HttpStatus.CREATED
        billView.source shouldBe ActionSource.Api(memberAccount.accountId)

        assertFichaDeCompensacaoBillTO(
            billTO,
            fichaCompensacaoTO,
            arbiValidationResponse,
            FichaCompensacaoType.OUTROS,
            memberAccount.accountId,
        )
    }

    fun buildUpdateDescriptionRequest(account: Account) = HttpRequest.PUT(
        "/bill/id/$BILL_ID_4/description",
        "{\"description\": \"${UpdateBillDescriptionIntegrationTest.NEW_DESCRIPTION}\"}",
    )
        .onWallet(wallet, account)

    fun buildIgnoreBillRequest(account: Account) = HttpRequest.PUT("/bill/id/$BILL_ID_4/ignore", "")
        .onWallet(wallet, account)

    fun buildMarkAsPaidRequest(account: Account) = HttpRequest.PUT("/bill/id/$BILL_ID_4/mark-as-paid", "")
        .onWallet(wallet, account)

    fun buildCancelMarkAsPaidRequest(account: Account) =
        HttpRequest.PUT("/bill/id/$BILL_ID_4/cancel-marked-as-paid", "")
            .onWallet(wallet, account)

    fun buildIgnoreRecurrenceBillRequest(account: Account) =
        HttpRequest.PUT("/bill/id/$BILL_ID_4/ignore?range=THIS_AND_FUTURE", "")
            .onWallet(wallet, account)

    fun buildReactivateBillRequest(account: Account) = HttpRequest.PUT("/bill/id/$BILL_ID_4/reactivate", "")
        .onWallet(wallet, account)

    fun buildGetReceiptBillRequest(account: Account) = HttpRequest.GET<BoletoReceiptTO>("/bill/id/$BILL_ID_4/receipt")
        .onWallet(wallet, account)

    fun buildGetReceiptFilesBillRequest(account: Account) = HttpRequest.GET<String>("/bill/id/$BILL_ID_4/receipt/files")
        .onWallet(wallet, account)

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should update description on member request`(memberAccount: Account) {
        val activeBill =
            getActiveBill(wallet.id, accountId = memberAccount.accountId)
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val response = client.toBlocking().exchange(buildUpdateDescriptionRequest(memberAccount), Unit::class.java)

        response.status shouldBe HttpStatus.OK

        val itemList = dynamoDbDAO.queryTableOnHashKey(activeBill.billId.value, BillEventEntity::class.java)
        val billEntity = itemList[1]
        val event = getObjectMapper().readerFor(BillEventDetailsEntity::class.java)
            .readValue<BillEventDetailsEntity>(billEntity.details)
        event.shouldBeTypeOf<DescriptionUpdatedEntity>()

        val bill: BillView = billRepository.findBill(activeBill.billId, wallet.id)
        bill.billDescription shouldBe UpdateBillDescriptionIntegrationTest.NEW_DESCRIPTION
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should ignore bill on member request`(memberAccount: Account) {
        val activeBill =
            getActiveBill(wallet.id, accountId = memberAccount.accountId)
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val response = client.toBlocking().exchange(buildIgnoreBillRequest(memberAccount), Unit::class.java)

        response.status shouldBe HttpStatus.OK

        val itemList = dynamoDbDAO.queryTableOnHashKey(activeBill.billId.value, BillEventEntity::class.java)
        val billEntity = itemList[1]
        val event =
            getObjectMapper().readerFor(BillEventDetailsEntity::class.java)
                .readValue<BillEventDetailsEntity>(billEntity.details)
        event.shouldBeTypeOf<BillIgnoredEntity>()

        val bill: BillView = billRepository.findBill(activeBill.billId, wallet.id)
        bill.status shouldBe BillStatus.IGNORED
    }

    @Test
    fun `should return locked when cannot acquire lock on mark as paid bill request`() {
        val activeBill = getActiveBill(
            wallet.id,
            accountId = walletFixture.founderAccount.accountId,
        )
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val lock = lockProvider.acquireLock(billAdded.billId.value)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(buildMarkAsPaidRequest(walletFixture.founderAccount), Unit::class.java)
        }

        thrown.status shouldBe HttpStatus.LOCKED
        lock!!.unlock()
    }

    @Test
    fun `should return locked when cannot acquire lock on cancel mark as paid bill request`() {
        val activeBill = getActiveBill(
            wallet.id,
            accountId = walletFixture.founderAccount.accountId,
        )
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val lock = lockProvider.acquireLock(billAdded.billId.value)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(buildCancelMarkAsPaidRequest(walletFixture.founderAccount), Unit::class.java)
        }

        thrown.status shouldBe HttpStatus.LOCKED
        lock!!.unlock()
    }

    @Test
    fun `should return locked when cannot acquire lock on ignore bill request`() {
        val activeBill = getActiveBill(
            wallet.id,
            accountId = walletFixture.founderAccount.accountId,
        )
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val lock = lockProvider.acquireLock(billAdded.billId.value)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(buildIgnoreBillRequest(walletFixture.founderAccount), Unit::class.java)
        }

        thrown.status shouldBe HttpStatus.LOCKED
        lock!!.unlock()
    }

    @ParameterizedTest
    @MethodSource("billEditRequests")
    fun `should return forbidden when limited participant edit another member's bill`(
        billRequest: MutableHttpRequest<*>,
    ) {
        val activeBill = getActiveBill(
            wallet.id,
            accountId = walletFixture.founderAccount.accountId,
        )
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            val request = billRequest
                .onWallet(wallet, walletFixture.ultraLimitedParticipantAccount)
            client.exchange(request, String::class.java).firstOrError().blockingGet()
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return forbidden when limited participant try to mark bill as paid`() {
        val activeBill = getActiveBill(
            wallet.id,
            accountId = walletFixture.founderAccount.accountId,
        )
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            val request = buildMarkAsPaidRequest(
                walletFixture.ultraLimitedParticipantAccount,
            )
                .onWallet(wallet, walletFixture.ultraLimitedParticipantAccount)
            client.exchange(request, String::class.java).firstOrError().blockingGet()
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return forbidden when limited participant try to cancel mark bill as paid`() {
        val activeBill = getActiveBill(
            wallet.id,
            accountId = walletFixture.founderAccount.accountId,
        )
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            val request = buildCancelMarkAsPaidRequest(
                walletFixture.ultraLimitedParticipantAccount,
            )
                .onWallet(wallet, walletFixture.ultraLimitedParticipantAccount)
            client.exchange(request, String::class.java).firstOrError().blockingGet()
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return ok when mark bill as paid worked`() {
        val activeBill = getActiveBill(
            wallet.id,
            accountId = walletFixture.founderAccount.accountId,
        )
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val request = buildMarkAsPaidRequest(
            walletFixture.founderAccount,
        )
            .onWallet(wallet, walletFixture.founderAccount)
        val response = client.exchange(request, BillTO::class.java).firstOrError().blockingGet()

        response.status shouldBe HttpStatus.OK

        with(response.body()) {
            this.markedAsPaidBy shouldBe wallet.founder.accountId.value
        }

        val lastEvent = dynamoDbDAO.queryTableOnHashKey(activeBill.billId.value, BillEventEntity::class.java).last()
        val event =
            getObjectMapper().readerFor(BillEventDetailsEntity::class.java)
                .readValue<BillEventDetailsEntity>(lastEvent.details)
        event.shouldBeTypeOf<BillMarkedAsPaidEntity>()

        val bill: BillView = billRepository.findBill(activeBill.billId, wallet.id)
        bill.status shouldBe BillStatus.ALREADY_PAID
        bill.markedAsPaidBy shouldBe wallet.founder.accountId
    }

    @Test
    fun `should return ok when cancel mark bill as paid worked`() {
        loadAccountIntoDb(dynamoDB)
        val billMarkedAsPaid = BillMarkedAsPaid(
            billId = billAdded.billId,
            created = billAdded.created + 1,
            walletId = billAdded.walletId,
            amountPaid = billAdded.amountTotal,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        )

        val events = mutableListOf(billAdded.copy(walletId = wallet.id), billMarkedAsPaid)
        events.forEach {
            billEventRepository.save(it)
        }

        val request = buildCancelMarkAsPaidRequest(walletFixture.founderAccount)

        val response = client.exchange(request, String::class.java).firstOrError().blockingGet()

        response.status shouldBe HttpStatus.OK

        val eventEntity = dynamoDbDAO.queryTableOnHashKey(billAdded.billId.value, BillEventEntity::class.java)
            .single { it.eventType == BillEventType.CANCELED_MARKED_AS_PAID }
        val event =
            getObjectMapper().readerFor(BillEventDetailsEntity::class.java)
                .readValue<BillEventDetailsEntity>(eventEntity.details)
        event.shouldBeTypeOf<BillMarkedAsPaidCanceledEntity>()

        val bill: BillView = billRepository.findBill(BillId(billAdded.billId.value), wallet.id)
        bill.status shouldBe BillStatus.ACTIVE
    }

    @Test
    fun `should return CONFLICT when bill can't be marked as paid`() {
        val activeBill = getActiveBill(
            wallet.id,
            accountId = walletFixture.founderAccount.accountId,
        )
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )
        addBillEventIntoDb(
            dynamoDB,
            billPaymentScheduled.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            val request = buildMarkAsPaidRequest(
                walletFixture.founderAccount,
            )
                .onWallet(wallet, walletFixture.founderAccount)
            client.exchange(request, String::class.java).firstOrError().blockingGet()
        }

        thrown.status shouldBe HttpStatus.CONFLICT
    }

    @Test
    fun `should return CONFLICT when bill can't be canceled marked as paid`() {
        val activeBill = getActiveBill(
            wallet.id,
            accountId = walletFixture.founderAccount.accountId,
        )
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            val request = buildCancelMarkAsPaidRequest(
                walletFixture.founderAccount,
            )
            client.exchange(request, String::class.java).firstOrError().blockingGet()
        }

        thrown.status shouldBe HttpStatus.CONFLICT
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should reactivate bill on member request`(memberAccount: Account) {
        val activeBill =
            getActiveBill(wallet.id, accountId = memberAccount.accountId)
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill.copy(status = BillStatus.IGNORED))
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )
        addBillEventIntoDb(
            dynamoDB,
            billIgnored.copy(walletId = wallet.id, actionSource = activeBill.source),
        )

        val response = client.toBlocking().exchange(buildReactivateBillRequest(memberAccount), Unit::class.java)

        response.status shouldBe HttpStatus.OK

        val itemList = dynamoDbDAO.queryTableOnHashKey(activeBill.billId.value, BillEventEntity::class.java)
        val billEntity = itemList[1]
        val event =
            getObjectMapper().readerFor(BillEventDetailsEntity::class.java)
                .readValue<BillEventDetailsEntity>(billEntity.details)
        event.shouldBeTypeOf<BillIgnoredEntity>()

        val bill: BillView = billRepository.findBill(activeBill.billId, wallet.id)
        bill.status shouldBe BillStatus.ACTIVE
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should get bill receipt on member request`(memberAccount: Account) {
        val activeBill =
            getActiveBill(wallet.id, accountId = memberAccount.accountId)
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(
            dynamoDbDAO,
            activeBill.copy(
                status = BillStatus.PAID,
                paidDate = LocalDateTime.now(),
            ),
        )
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )
        addBillEventIntoDb(
            dynamoDB,
            billPaid.copy(walletId = wallet.id, actionSource = activeBill.source),
        )
        val boletoReceiptData = BoletoReceiptData(
            billId = activeBill.billId,
            walletId = wallet.id,
            source = activeBill.source,
            authentication = "AAAAA-BBBBB-CCCCC-DDDDD-EEEEE",
            dateTime = Instant.ofEpochMilli(billPaid.created).atZone(brazilTimeZone),
            assignor = billAddedFicha.assignor,
            recipient = billAddedFicha.recipient,
            totalAmount = billAddedFicha.amountTotal,
            payer = BillPayer(document = billAddedFicha.document),
            dueDate = billAddedFicha.dueDate,
            barcode = billAddedFicha.barcode,
            walletName = wallet.name,
            scheduledBy = wallet.founder.name,
            paymentPartnerName = null,
            transactionId = TransactionId("transactionId"),
        )
        receiptRepository.save(boletoReceiptData)

        val response =
            client.toBlocking().exchange(buildGetReceiptBillRequest(memberAccount), BoletoReceiptTO::class.java)

        response.status shouldBe HttpStatus.OK
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should get bill receipt files on member request`(memberAccount: Account) {
        val activeBill =
            getActiveBill(wallet.id, accountId = memberAccount.accountId)
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(
            dynamoDbDAO,
            activeBill.copy(
                status = BillStatus.PAID,
                paidDate = LocalDateTime.now(),
            ),
        )
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )
        addBillEventIntoDb(
            dynamoDB,
            billPaid.copy(walletId = wallet.id, actionSource = activeBill.source),
        )
        val boletoReceiptData = BoletoReceiptData(
            billId = activeBill.billId,
            walletId = wallet.id,
            source = activeBill.source,
            authentication = "AAAAA-BBBBB-CCCCC-DDDDD-EEEEE",
            dateTime = Instant.ofEpochMilli(billPaid.created).atZone(brazilTimeZone),
            assignor = billAddedFicha.assignor,
            recipient = billAddedFicha.recipient,
            totalAmount = billAddedFicha.amountTotal,
            payer = BillPayer(document = billAddedFicha.document),
            dueDate = billAddedFicha.dueDate,
            barcode = billAddedFicha.barcode,
            walletName = wallet.name,
            scheduledBy = wallet.founder.name,
            paymentPartnerName = null,
            transactionId = TransactionId("transactionId"),
        )
        receiptRepository.save(boletoReceiptData)
        every { s3LinkGenerator.generate(any(), any(), MediaType.APPLICATION_PDF, any()) } returns "pdfS3Link"
        every { s3LinkGenerator.generate(any(), any(), MediaType.IMAGE_PNG, any()) } returns "imageS3Link"

        val response =
            client.toBlocking().exchange(buildGetReceiptFilesBillRequest(memberAccount), ReceiptFilesTO::class.java)
        verifyOrder {
            objectRepository.hasKeyPrefix(any())
            objectRepository.putObject(any(), any(), MediaType.APPLICATION_PDF_TYPE)
            objectRepository.putObject(any(), any(), MediaType.IMAGE_PNG_TYPE)
            s3LinkGenerator.generate(any(), any(), MediaType.APPLICATION_PDF, any())
            s3LinkGenerator.generate(any(), any(), MediaType.IMAGE_PNG, any())
        }
        with(response.body.get()) {
            imageUrl shouldBe "imageS3Link"
            pdfUrl shouldBe "pdfS3Link"
        }
    }

    @ParameterizedTest
    @MethodSource("memberAccounts")
    fun `should ignore recurring bills on member request`(memberAccount: Account) {
        val source = ActionSource.WalletRecurrence(accountId = memberAccount.accountId, weeklyRecurrenceNoEndDate.id)
        val activeBill = getActiveBill(wallet.id).copy(source = source)
        loadAccountIntoDb(dynamoDB)
        addBillIntoDb(dynamoDbDAO, activeBill)
        addBillEventIntoDb(
            dynamoDB,
            billAdded.copy(walletId = wallet.id, actionSource = activeBill.source),
        )
        recurrenceDBRepository.save(
            weeklyRecurrenceNoEndDate.copy(walletId = wallet.id).plusBillId(billAdded.billId),
        )

        val response = client.toBlocking().exchange(buildIgnoreRecurrenceBillRequest(memberAccount), Unit::class.java)

        response.status shouldBe HttpStatus.OK
    }

    private fun validatePixByKeyResponse(response: HttpResponse<BillTO>, sourceAccountId: AccountId) {
        with(response.body()!!) {
            assertSoftly {
                amount shouldBe pixKeyRequestTO.amount
                description shouldBe pixKeyRequestTO.description
                dueDate shouldBe pixKeyRequestTO.dueDate
                billType shouldBe BillType.PIX.name
                billRecipient!!.document shouldBe pixKeyDetails.owner.document
                billRecipient!!.alias shouldBe pixKeyRequestTO.recipient.alias
                billRecipient!!.name shouldBe pixKeyDetails.owner.name
                billRecipient!!.pixKey!!.type shouldBe pixKeyDetails.key.type
                billRecipient!!.pixKey!!.value shouldBe pixKeyDetails.key.value
                billRecipient!!.pixKey!!.accountNo shouldBe pixKeyDetails.holder.accountNo
                billRecipient!!.pixKey!!.accountDv shouldBe pixKeyDetails.holder.accountDv
                billRecipient!!.pixKey!!.accountType shouldBe pixKeyDetails.holder.accountType
                billRecipient!!.pixKey!!.routingNo shouldBe pixKeyDetails.holder.routingNo
                billRecipient!!.pixKey!!.ispb shouldBe pixKeyDetails.holder.ispb
                billRecipient!!.pixKey!!.institutionName shouldBe pixKeyDetails.holder.institutionName
                billRecipient!!.pixKey!!.document shouldBe pixKeyDetails.owner.document
                billRecipient!!.pixKey!!.name shouldBe pixKeyDetails.owner.name
                source.type shouldBe ActionSourceType.Webapp.name
                source.accountId shouldBe sourceAccountId.value
            }
        }
    }

    private fun assertFichaDeCompensacaoBillTO(
        billTO: BillTO,
        addFichaDeCompensacaoTO: AddFichaDeCompensacaoTO,
        validationResponse: BillValidationResponse,
        fichaCompensacaoType: FichaCompensacaoType? = null,
        sourceAccountId: AccountId,
    ) {
        Assertions.assertEquals(validationResponse.billRegisterData!!.amount, billTO.amount)
        Assertions.assertEquals(validationResponse.billRegisterData!!.amountTotal, billTO.amountTotal)
        Assertions.assertEquals(validationResponse.billRegisterData!!.dueDate!!.format(dateFormat), billTO.dueDate)
        Assertions.assertEquals(addFichaDeCompensacaoTO.digitableLine, billTO.barcode)
        Assertions.assertEquals(addFichaDeCompensacaoTO.description, billTO.description)
        Assertions.assertEquals(validationResponse.billRegisterData!!.recipient!!.name, billTO.recipient?.name)
        Assertions.assertEquals(validationResponse.billRegisterData!!.recipient!!.document, billTO.recipient?.document)
        Assertions.assertEquals(validationResponse.billRegisterData!!.recipient!!.name, billTO.billRecipient?.name)
        Assertions.assertEquals(
            validationResponse.billRegisterData!!.recipient!!.document,
            billTO.billRecipient?.document,
        )
        Assertions.assertEquals(validationResponse.billRegisterData!!.payerDocument, billTO.payer!!.document)
        Assertions.assertEquals(validationResponse.billRegisterData!!.payerName, billTO.payer!!.name)
        Assertions.assertEquals(
            validationResponse.billRegisterData!!.amountCalculationModel.name,
            billTO.amountCalculationModel,
        )
        Assertions.assertEquals(
            BillSourceTO(type = ActionSourceType.Webapp.name, accountId = sourceAccountId.value),
            billTO.source,
        )

        val currentFichaCompensacaoType =
            fichaCompensacaoType ?: validationResponse.billRegisterData!!.fichaCompensacaoType
        billTO.fichaCompensacaoType shouldBe currentFichaCompensacaoType
    }

    private fun assertConcessionariaBillTO(
        billTO: BillTO,
        addConcessionariaTO: AddConcessionariaTO,
        validationResponse: CelcoinBillValidationResponse,
        sourceAccountId: AccountId,
    ) {
        Assertions.assertEquals(validationResponse.billRegisterData!!.amount, billTO.amount)
        Assertions.assertEquals(validationResponse.billRegisterData!!.amountTotal, billTO.amountTotal)
        Assertions.assertEquals(validationResponse.billRegisterData!!.assignor, billTO.assignor)
        Assertions.assertEquals(addConcessionariaTO.dueDate, billTO.dueDate)
        Assertions.assertEquals(addConcessionariaTO.digitableLine, addConcessionariaTO.digitableLine)
        Assertions.assertEquals(addConcessionariaTO.description, billTO.description)
        assertNull(billTO.billRecipient)
        Assertions.assertEquals(
            BillSourceTO(type = ActionSourceType.Webapp.name, accountId = sourceAccountId.value),
            billTO.source,
        )
    }

    private fun BillTO.shouldHaveSameContentAs(createInvoiceTO: CreateInvoiceTO, sourceAccountId: AccountId) {
        val billRecipient = billRecipient!!
        Assertions.assertEquals(createInvoiceTO.amount, amount)
        Assertions.assertEquals(createInvoiceTO.amount, amountTotal)
        Assertions.assertEquals(createInvoiceTO.dueDate, dueDate)
        Assertions.assertEquals(createInvoiceTO.description, description)
        Assertions.assertEquals(createInvoiceTO.recipient.alias, recipient?.alias)
        Assertions.assertEquals(createInvoiceTO.recipient.document, recipient?.document)
        Assertions.assertEquals(createInvoiceTO.recipient.name, recipient?.name)
        Assertions.assertEquals(createInvoiceTO.recipient.alias, recipient?.alias)
        Assertions.assertEquals(createInvoiceTO.recipient.bankDetails, recipient?.bankDetails)
        Assertions.assertEquals(createInvoiceTO.recipient.alias, billRecipient.alias)
        Assertions.assertEquals(createInvoiceTO.recipient.document, billRecipient.document)
        Assertions.assertEquals(createInvoiceTO.recipient.name, billRecipient.name)
        Assertions.assertEquals(createInvoiceTO.recipient.alias, billRecipient.alias)
        Assertions.assertEquals(createInvoiceTO.recipient.bankDetails, billRecipient.bankDetails)
        Assertions.assertEquals(
            BillSourceTO(type = ActionSourceType.Webapp.name, accountId = sourceAccountId.value),
            source,
        )
    }

    private fun validatePixRecurrenceResponse(billTO: BillTO, createRequest: CreatePixTO, sourceAccountId: AccountId) {
        assertSoftly {
            billTO.amount shouldBe createRequest.amount
            billTO.description shouldBe createRequest.description
            billTO.dueDate shouldBe createRequest.dueDate
            billTO.billType shouldBe BillType.PIX.name
            billTO.source shouldBe BillSourceTO(type = "Recurrence", accountId = sourceAccountId.value)
            with(billTO.billRecipient!!) {
                alias shouldBe createRequest.recipient.alias
                if (pixKey != null) {
                    document shouldBe pixKeyDetails.owner.document
                    name shouldBe pixKeyDetails.owner.name
                    pixKey!!.type shouldBe pixKeyDetails.key.type
                    pixKey!!.value shouldBe pixKeyDetails.key.value
                    pixKey!!.name shouldBe pixKeyDetails.owner.name
                    pixKey!!.document shouldBe pixKeyDetails.owner.document
                    pixKey!!.institutionName shouldBe pixKeyDetails.holder.institutionName
                    pixKey!!.ispb shouldBe pixKeyDetails.holder.ispb
                    pixKey!!.routingNo shouldBe pixKeyDetails.holder.routingNo
                    pixKey!!.accountNo shouldBe pixKeyDetails.holder.accountNo
                    pixKey!!.accountDv shouldBe pixKeyDetails.holder.accountDv
                    pixKey!!.accountType shouldBe pixKeyDetails.holder.accountType
                } else {
                    document shouldBe createRequest.recipient.document
                    name shouldBe createRequest.recipient.name
                    bankDetails!!.ispb shouldBe createRequest.recipient.bankDetails!!.ispb
                    bankDetails!!.accountNo shouldBe createRequest.recipient.bankDetails!!.accountNo
                    bankDetails!!.accountDv shouldBe createRequest.recipient.bankDetails!!.accountDv
                    bankDetails!!.routingNo shouldBe createRequest.recipient.bankDetails!!.routingNo
                    bankDetails!!.accountType shouldBe createRequest.recipient.bankDetails!!.accountType
                }
            }
        }
    }

    companion object {

        private val pixKeyRequestTO = CreatePixTO(
            amount = 10,
            description = "description",
            dueDate = getLocalDate().plusDays(1).format(dateFormat),
            recipient = RequestPixRecipientTO(
                id = RECIPIENT_ID,
                name = null,
                document = null,
                documentType = null,
                bankDetails = null,
                pixKey = PixKeyRequestTO(
                    type = PixKeyType.CPF,
                    value = "***********",
                ),
                alias = "Ze",
            ),
        )

        val concessionariaTO = AddConcessionariaTO(
            digitableLine = CONCESSIONARIA_DIGITABLE_LINE,
            description = "description",
            dueDate = sundayDate.format(dateFormat),
        )

        private val fichaCompensacaoTO =
            AddFichaDeCompensacaoTO(digitableLine = FICHA_DE_COMPENSACAO_DIGITABLE_LINE, description = "description")

        private val walletFixture = WalletFixture()

        private val cashInRequestTO = CashInRequestTO(
            id = CASHIN_TRANSACTIONID.toString(),
            paymentMethod = TransactionPaymentMethodTO(
                id = PAYMENT_METHOD_ID,
                type = PaymentMethodType.CREDIT_CARD,
            ),
            netAmount = 1,
            feeAmount = 0,
        )

        @JvmStatic
        fun billRequests(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(HttpRequest.POST("/bill/pix", pixKeyRequestTO)),
                Arguments.arguments(HttpRequest.POST("/bill/invoice", createInvoiceTO)),
                Arguments.arguments(HttpRequest.POST("/bill/concessionaria", concessionariaTO)),
                Arguments.arguments(HttpRequest.POST("/bill/ficha-compensacao", fichaCompensacaoTO)),
                Arguments.arguments(
                    HttpRequest.PUT(
                        "/bill/id/$BILL_ID_4/description",
                        "{\"description\": \"${UpdateBillDescriptionIntegrationTest.NEW_DESCRIPTION}\"}",
                    ),
                ),
                Arguments.arguments(HttpRequest.PUT("/bill/id/$BILL_ID_4/ignore", "")),
                Arguments.arguments(HttpRequest.PUT("/bill/id/$BILL_ID_4/reactivate", "")),
                Arguments.arguments(HttpRequest.GET<ReceiptTO>("/bill/id/$BILL_ID_4/receipt")),
                Arguments.arguments(HttpRequest.GET<ReceiptFilesTO>("/bill/id/$BILL_ID_4/receipt/files")),
                Arguments.arguments(HttpRequest.POST("/transaction/cash-in", cashInRequestTO)),
                Arguments.arguments(HttpRequest.POST("/v2/schedule", "{ \"billList\": [], \"scheduleTo\": \"TODAY\"  }")),
                Arguments.arguments(HttpRequest.DELETE("/schedule/$BILL_ID_4", "")),
                Arguments.arguments(HttpRequest.GET<Any>("/recipient")),
                Arguments.arguments(HttpRequest.GET<Any>("/balance/amount")),
                Arguments.arguments(HttpRequest.GET<Any>("/balance/cashin")),
                Arguments.arguments(HttpRequest.GET<Any>("/balance/checkout")),
            )
        }

        @JvmStatic
        fun billEditRequests(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    HttpRequest.PUT(
                        "/bill/id/$BILL_ID_4/description",
                        "{\"description\": \"${UpdateBillDescriptionIntegrationTest.NEW_DESCRIPTION}\"}",
                    ),
                ),
                Arguments.arguments(HttpRequest.PUT("/bill/id/$BILL_ID_4/ignore", "")),
                Arguments.arguments(HttpRequest.PUT("/bill/id/$BILL_ID_4/reactivate", "")),
                Arguments.arguments(HttpRequest.GET<BoletoReceiptTO>("/bill/id/$BILL_ID_4/receipt")),
                Arguments.arguments(HttpRequest.GET<String>("/bill/id/$BILL_ID_4/receipt/files")),
                Arguments.arguments(HttpRequest.PUT("/bill/id/$BILL_ID_4/ignore?range=THIS_AND_FUTURE", "")),
            )
        }

        @JvmStatic
        fun memberAccounts(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(walletFixture.founderAccount),
                Arguments.arguments(walletFixture.participantAccount),
                Arguments.arguments(walletFixture.ultraLimitedParticipantAccount),
            )
        }

        @JvmStatic
        fun memberAccountsWithVisibilityPermission(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(walletFixture.founderAccount),
                Arguments.arguments(walletFixture.participantAccount),
            )
        }
    }
}

private const val limitDate = "2025-12-31"