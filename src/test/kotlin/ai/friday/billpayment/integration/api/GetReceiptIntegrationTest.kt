package ai.friday.billpayment.integration.api

import ai.friday.billpayment.adapters.api.BoletoReceiptTO
import ai.friday.billpayment.adapters.api.InvoiceReceiptTO
import ai.friday.billpayment.adapters.api.PixQrCodeDataTO
import ai.friday.billpayment.adapters.api.PixReceiptTO
import ai.friday.billpayment.adapters.api.ReceiptFilesTO
import ai.friday.billpayment.adapters.api.ReceiptTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.celcoin.payerBank
import ai.friday.billpayment.adapters.celcoin.payerDocument
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.ReceiptDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.s3.S3LinkGenerator
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPayer
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.integrations.ObjectRepository
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BillPaymentReceipt
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.INVOICE_PURPOSE
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.settlement.receipt.BoletoSettlementReceiptService
import ai.friday.billpayment.balance
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.addBillEventIntoDb
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixKeyPaid
import ai.friday.billpayment.pixKeyPaymentStarted
import ai.friday.billpayment.pixPaid
import ai.friday.billpayment.pixPaymentStarted
import ai.friday.billpayment.transactionId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.verifyOrder
import java.math.BigInteger
import java.time.Instant
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class GetReceiptIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val billEventRepository = BillEventDBRepository(
        dynamoDbDAO,
        allFalseFeatureConfiguration,
    )
    private val receiptRepository = ReceiptDbRepository(dynamoDbDAO)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val walletRepository = WalletDbRepository(dynamoDbDAO, mockk())
    private val transactionRepository = DynamoDbTransactionRepository(
        dynamoDbDAO = dynamoDbDAO,
        accountRepository = accountRepository,
        transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, mockk()),
    )

    @MockBean(S3LinkGenerator::class)
    fun getS3LinkGenerator() = s3LinkGenerator
    private val s3LinkGenerator: S3LinkGenerator = mockk()

    @MockBean(ObjectRepository::class)
    fun getObjectRepository() = objectRepository
    private val objectRepository: ObjectRepository = mockk(relaxed = true)

    @MockBean(BoletoSettlementReceiptService::class)
    fun getBoletoSettlementReceiptService() = boletoSettlementReceiptService
    private val boletoSettlementReceiptService: BoletoSettlementReceiptService = mockk()

    private val boletoReceiptData = BoletoReceiptData(
        billId = billAddedFicha.billId,
        walletId = wallet.id,
        source = billAddedFicha.actionSource,
        authentication = "AAAAA-BBBBB-CCCCC-DDDDD-EEEEE",
        dateTime = Instant.ofEpochMilli(billPaid.created).atZone(brazilTimeZone),
        assignor = billAddedFicha.assignor,
        recipient = billAddedFicha.recipient,
        totalAmount = billAddedFicha.amountTotal,
        payer = BillPayer(document = billAddedFicha.document),
        dueDate = billAddedFicha.dueDate,
        barcode = billAddedFicha.barcode,
        walletName = null,
        scheduledBy = null,
        paymentPartnerName = null,
        transactionId = TransactionId("transactionId"),
    )

    private val pixReceiptData = PixReceiptData(
        billId = pixAdded.billId,
        walletId = wallet.id,
        source = pixAdded.actionSource,
        authentication = "AAAAA-BBBBB-CCCCC-DDDDD-EEEEE",
        dateTime = getZonedDateTime(),
        recipient = pixAdded.recipient!!,
        totalAmount = pixAdded.amountTotal,
        purpose = INVOICE_PURPOSE,
        payer = BillPayer(document = DOCUMENT, name = NAME),
        payeeFinancialInstitution = FinancialInstitution(
            name = "payee bank",
            ispb = pixAdded.recipient!!.bankAccount!!.ispb,
            compe = pixAdded.recipient!!.bankAccount!!.bankNo,
        ),
        payerFinancialInstitution = FinancialInstitution(name = "payer bank", ispb = "********", compe = 123),
        payeeRoutingNo = pixAdded.recipient!!.bankAccount!!.routingNo,
        payeeAccountNo = pixAdded.recipient!!.bankAccount!!.accountNo,
        payeeAccountDv = pixAdded.recipient!!.bankAccount!!.accountDv,
        payeeAccountType = pixAdded.recipient!!.bankAccount!!.accountType,
        payerBankAccount = InternalBankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 123,
            routingNo = 1,
            accountNo = 12345,
            accountDv = "2",
            document = "***********",
            bankAccountMode = BankAccountMode.PHYSICAL,
        ),
        walletName = null,
        scheduledBy = null,
        transactionId = null,
    )

    private val pixKeyReceiptData = PixReceiptData(
        billId = pixKeyAdded.billId,
        walletId = wallet.id,
        source = pixKeyAdded.actionSource,
        authentication = "AAAAA-BBBBB-CCCCC-DDDDD-EEEEE",
        dateTime = getZonedDateTime(),
        recipient = pixKeyAdded.recipient!!,
        totalAmount = pixKeyAdded.amountTotal,
        purpose = INVOICE_PURPOSE,
        payer = BillPayer(document = DOCUMENT, name = NAME),
        payeeFinancialInstitution = FinancialInstitution(name = "payee bank", ispb = "********", compe = null),
        payerFinancialInstitution = FinancialInstitution(name = "payer bank", ispb = "********", compe = 123),
        payeeRoutingNo = 1,
        payeeAccountNo = BigInteger("54321"),
        payeeAccountDv = "1",
        payeeAccountType = AccountType.CHECKING,
        payerBankAccount = InternalBankAccount(
            accountType = AccountType.CHECKING,
            bankNo = 123,
            routingNo = 1,
            accountNo = 12345,
            accountDv = "2",
            document = "***********",
            bankAccountMode = BankAccountMode.PHYSICAL,
        ),
        walletName = null,
        scheduledBy = null,
        transactionId = TransactionId("transactionId"),
    )

    private val invoiceReceiptData = InvoiceReceiptData(
        billId = invoiceAdded.billId,
        walletId = wallet.id,
        source = invoiceAdded.actionSource,
        authentication = "AAAAA-BBBBB-CCCCC-DDDDD-EEEEE",
        dateTime = getZonedDateTime(),
        recipient = invoiceAdded.recipient!!,
        totalAmount = invoiceAdded.amountTotal,
        payer = BillPayer(document = DOCUMENT, name = NAME),
        purpose = "1 - crédito em conta",
        payeeBank = "1 - Banco do Brasil S.A",
        paymentPartnerName = payerBank,
        paymentPartnerDocument = payerDocument,
        walletName = null,
        scheduledBy = null,
        transactionId = TransactionId("transactionId"),
    )

    private val directInvoiceReceiptData = InvoiceReceiptData(
        billId = invoiceAdded.billId,
        walletId = wallet.id,
        source = invoiceAdded.actionSource,
        authentication = "AAAAA-BBBBB-CCCCC-DDDDD-EEEEE",
        dateTime = getZonedDateTime(),
        recipient = invoiceAdded.recipient!!,
        totalAmount = invoiceAdded.amountTotal,
        payer = BillPayer(document = DOCUMENT, name = NAME),
        purpose = "1 - crédito em conta",
        payeeBank = "1 - Banco do Brasil S.A",
        paymentPartnerName = null,
        paymentPartnerDocument = null,
        payerBankAccount = internalBankAccount,
        payerFinancialInstitution = FinancialInstitution(
            name = "Banco ${internalBankAccount.bankNo}",
            ispb = null,
            compe = internalBankAccount.bankNo,
        ),
        walletName = null,
        scheduledBy = null,
        transactionId = TransactionId("transactionId"),
    )

    private val settlementTarget = Bill.build(billAddedFicha)
    private val transaction = Transaction(
        id = transactionId,
        type = TransactionType.INVOICE_PAYMENT,
        payer = walletFixture.founderAccount.toPayer(),
        paymentData = createSinglePaymentDataWithBalance(
            balance.copy(id = wallet.paymentMethodId, accountId = wallet.founder.accountId),
            settlementTarget.amountTotal,
            calculationId = null,
            BalanceAuthorization(
                status = BankOperationStatus.ERROR,
                timeout = false,
                amount = 100,
                paymentGateway = FinancialServiceGateway.ARBI,
            ),
        ),
        settlementData = SettlementData(
            settlementTarget = settlementTarget,
            serviceAmountTax = 0L,
            billAddedFicha.amountTotal,
            settlementOperation = BoletoSettlementResult(
                FinancialServiceGateway.CELCOIN,
                BoletoSettlementStatus.CONFIRMED,
                "1",
                0,
                "",
                "",
                "",
            ),
        ),
        nsu = 0L,
        actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
        walletId = billAddedFicha.walletId,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        accountRepository.create(walletFixture.founderAccount)
        walletRepository.save(wallet)
    }

    @Test
    fun `should return NOT FOUND on get receipt for a non existent bill`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        val thrown = assertThrows<HttpClientResponseException> {
            retrieveReceipt<BoletoReceiptTO>(cookie, billAddedFicha.billId)
        }
        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return NOT FOUND on get receipt files for a non existent bill`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        val thrown = assertThrows<HttpClientResponseException> {
            retrieveReceiptFiles(cookie, billAddedFicha.billId)
        }
        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return BAD REQUEST on get receipt for an active bill`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(dynamoDB, billAddedFicha.copy(walletId = wallet.id))
        val thrown = assertThrows<HttpClientResponseException> {
            retrieveReceipt<BoletoReceiptTO>(cookie, billAddedFicha.billId)
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "4001"
            message shouldBe "Cannot get receipt cause bill was not paid"
        }
    }

    @Test
    fun `should return BAD REQUEST on get receipt files for an active bill`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(dynamoDB, billAddedFicha.copy(walletId = wallet.id))
        val thrown = assertThrows<HttpClientResponseException> {
            retrieveReceiptFiles(cookie, billAddedFicha.billId)
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        with(thrown.response.getBody(ResponseTO::class.java).get()) {
            code shouldBe "4001"
            message shouldBe "Cannot get receipt cause bill was not paid"
        }
    }

    @Test
    fun `should return receipt on get receipt for a paid boleto`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(dynamoDB, billAddedFicha.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaymentStart.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaid.copy(walletId = wallet.id))
        receiptRepository.save(boletoReceiptData)
        val response = retrieveReceipt<BoletoReceiptTO>(cookie, billAddedFicha.billId)
        with(response) {
            totalAmount shouldBe boletoReceiptData.totalAmount
            dateTime shouldBe boletoReceiptData.dateTime.format(dateTimeFormat)
            recipient?.name shouldBe boletoReceiptData.recipient?.name
            recipient?.document shouldBe boletoReceiptData.recipient?.document
            assignorName shouldBe boletoReceiptData.assignor
            payerName shouldBe boletoReceiptData.payer?.name
            payerDocument shouldBe boletoReceiptData.payer?.document
            payerDocumentType shouldBe "CPF"
            typeableLine shouldBe boletoReceiptData.barcode.digitable
            authentication shouldBe boletoReceiptData.authentication
            dueDate shouldBe boletoReceiptData.dueDate.format(dateFormat)
            billType shouldBe BillType.FICHA_COMPENSACAO
        }
    }

    @Test
    fun `should create receipt data and return files on get receipt files for a paid boleto without receipt data`() {
        every { boletoSettlementReceiptService.getReceipt(any()) } returns BillPaymentReceipt(
            "",
            "",
            boletoReceiptData.authentication,
            "",
        )
        val cookie = buildCookie(walletFixture.founderAccount)
        loadBalancePaymentMethod(
            accountRepository,
            paymentMethodId = wallet.paymentMethodId.value,
            accountId = wallet.founder.accountId.value,
        )
        addBillEventIntoDb(dynamoDB, billAddedFicha.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaymentStart.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaid.copy(walletId = wallet.id))
        transactionRepository.save(transaction)
        every { s3LinkGenerator.generate(any(), any(), MediaType.APPLICATION_PDF, any()) } returns "pdfS3Link"
        every { s3LinkGenerator.generate(any(), any(), MediaType.IMAGE_PNG, any()) } returns "imageS3Link"

        val response = retrieveReceiptFiles(cookie, billAddedFicha.billId)
        with(response) {
            imageUrl shouldBe "imageS3Link"
            pdfUrl shouldBe "pdfS3Link"
        }
    }

    @Test
    fun `should create and return receipt data on get receipt for a paid boleto without receipt data`() {
        every { boletoSettlementReceiptService.getReceipt(any()) } returns BillPaymentReceipt(
            "",
            "",
            boletoReceiptData.authentication,
            "",
        )
        val cookie = buildCookie(walletFixture.founderAccount)
        loadBalancePaymentMethod(
            accountRepository,
            paymentMethodId = wallet.paymentMethodId.value,
            accountId = wallet.founder.accountId.value,
        )

        accountRepository.findAccountPaymentMethodsByAccountId(wallet.founder.accountId)
        addBillEventIntoDb(dynamoDB, billAddedFicha.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaymentStart.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaid.copy(walletId = wallet.id))
        transactionRepository.save(transaction)
        val response = retrieveReceipt<BoletoReceiptTO>(cookie, billAddedFicha.billId)
        with(response) {
            totalAmount shouldBe boletoReceiptData.totalAmount
            dateTime shouldBe boletoReceiptData.dateTime.format(dateTimeFormat)
            recipient?.name shouldBe boletoReceiptData.recipient?.name
            recipient?.document shouldBe boletoReceiptData.recipient?.document
            assignorName shouldBe boletoReceiptData.assignor
            payerName shouldBe boletoReceiptData.payer?.name
            payerDocument shouldBe boletoReceiptData.payer?.document
            payerDocumentType shouldBe "CPF"
            typeableLine shouldBe boletoReceiptData.barcode.digitable
            authentication shouldBe boletoReceiptData.authentication
            dueDate shouldBe boletoReceiptData.dueDate.format(dateFormat)
            billType shouldBe BillType.FICHA_COMPENSACAO
        }
    }

    @Test
    fun `should return receipt files for a paid boleto`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(dynamoDB, billAddedFicha.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaymentStart.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaid.copy(walletId = wallet.id))
        receiptRepository.save(boletoReceiptData)
        every { s3LinkGenerator.generate(any(), any(), MediaType.APPLICATION_PDF, any()) } returns "pdfS3Link"
        every { s3LinkGenerator.generate(any(), any(), MediaType.IMAGE_PNG, any()) } returns "imageS3Link"

        val response = retrieveReceiptFiles(cookie, billAddedFicha.billId)
        with(response) {
            imageUrl shouldBe "imageS3Link"
            pdfUrl shouldBe "pdfS3Link"
        }
    }

    @Test
    fun `should create receipt files on get receipt files for a paid boleto if the receipt files are not present`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(dynamoDB, billAddedFicha.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaymentStart.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, billPaid.copy(walletId = wallet.id))
        receiptRepository.save(boletoReceiptData)
        every { s3LinkGenerator.generate(any(), any(), MediaType.APPLICATION_PDF, any()) } returns "pdfS3Link"
        every { s3LinkGenerator.generate(any(), any(), MediaType.IMAGE_PNG, any()) } returns "imageS3Link"

        val response = retrieveReceiptFiles(cookie, billAddedFicha.billId)
        verifyOrder {
            objectRepository.hasKeyPrefix(any())
            objectRepository.putObject(any(), any(), MediaType.APPLICATION_PDF_TYPE)
            objectRepository.putObject(any(), any(), MediaType.IMAGE_PNG_TYPE)
            s3LinkGenerator.generate(any(), any(), MediaType.APPLICATION_PDF, any())
            s3LinkGenerator.generate(any(), any(), MediaType.IMAGE_PNG, any())
        }
        with(response) {
            imageUrl shouldBe "imageS3Link"
            pdfUrl shouldBe "pdfS3Link"
        }
    }

    @Test
    fun `should return receipt on get receipt for a paid pix with bank account`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(dynamoDB, pixAdded.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, pixPaymentStarted.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, pixPaid.copy(walletId = wallet.id))
        receiptRepository.save(pixReceiptData)
        val response = retrieveReceipt<PixReceiptTO>(cookie, pixAdded.billId)
        with(response) {
            totalAmount shouldBe pixReceiptData.totalAmount
            dateTime shouldBe pixReceiptData.dateTime.format(dateTimeFormat)
            recipient.name shouldBe pixReceiptData.recipient.name
            recipient.document shouldBe pixReceiptData.recipient.document
            recipient.bankDetails?.ispb shouldBe pixReceiptData.recipient.bankAccount?.ispb
            recipient.bankDetails?.routingNo shouldBe pixReceiptData.recipient.bankAccount!!.routingNo
            recipient.bankDetails?.accountNo shouldBe pixReceiptData.recipient.bankAccount!!.accountNo
            recipient.bankDetails?.accountDv shouldBe pixReceiptData.recipient.bankAccount!!.accountDv
            purpose shouldBe pixReceiptData.purpose
            payerName shouldBe pixReceiptData.payer.name
            payerDocument shouldBe pixReceiptData.payer.document
            payerDocumentType shouldBe "CPF"
            payerBankNumber shouldBe pixReceiptData.payerBankAccount.bankNo
            payerRoutingNumber shouldBe pixReceiptData.payerBankAccount.routingNo
            payerAccountNumber shouldBe pixReceiptData.payerBankAccount.accountNo
            payerAccountDv shouldBe pixReceiptData.payerBankAccount.accountDv
            transactionId shouldBe pixReceiptData.authentication
            billType shouldBe BillType.PIX
        }
    }

    @Test
    fun `should return receipt on get receipt for a paid pix with pix key`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(dynamoDB, pixKeyAdded.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, pixKeyPaymentStarted.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, pixKeyPaid.copy(walletId = wallet.id))
        receiptRepository.save(pixKeyReceiptData)
        val response = retrieveReceipt<PixReceiptTO>(cookie, pixKeyAdded.billId)
        with(response) {
            totalAmount shouldBe pixKeyReceiptData.totalAmount
            dateTime shouldBe pixKeyReceiptData.dateTime.format(dateTimeFormat)
            recipient.name shouldBe pixKeyReceiptData.recipient.name
            recipient.document shouldBe pixKeyReceiptData.recipient.document
            recipient.pixKey?.ispb shouldBe pixKeyReceiptData.recipient.pixKeyDetails?.holder?.ispb
            recipient.pixKey?.institutionName shouldBe pixKeyReceiptData.recipient.pixKeyDetails?.holder?.institutionName
            recipient.pixKey?.routingNo shouldBe pixKeyReceiptData.recipient.pixKeyDetails!!.holder.routingNo
            recipient.pixKey?.accountNo shouldBe pixKeyReceiptData.recipient.pixKeyDetails!!.holder.accountNo
            recipient.pixKey?.accountDv shouldBe pixKeyReceiptData.recipient.pixKeyDetails!!.holder.accountDv
            purpose shouldBe pixKeyReceiptData.purpose
            payerName shouldBe pixKeyReceiptData.payer.name
            payerDocument shouldBe pixKeyReceiptData.payer.document
            payerDocumentType shouldBe "CPF"
            payerBankNumber shouldBe pixKeyReceiptData.payerBankAccount.bankNo
            payerRoutingNumber shouldBe pixKeyReceiptData.payerBankAccount.routingNo
            payerAccountNumber shouldBe pixKeyReceiptData.payerBankAccount.accountNo
            payerAccountDv shouldBe pixKeyReceiptData.payerBankAccount.accountDv
            transactionId shouldBe pixKeyReceiptData.authentication
            billType shouldBe BillType.PIX
        }
    }

    @Test
    fun `não deve retornar os dados da chave pix se for um pagamento QRCode`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(
            dynamoDB,
            pixKeyAdded.copy(
                walletId = wallet.id,
                pixQrCodeData = PixQrCodeData(
                    type = PixQrCodeType.STATIC,
                    info = "info",
                    additionalInfo = mapOf("additionalInfo" to "value"),
                    pixId = "TxId",
                    fixedAmount = null,
                    expiration = null,
                    originalAmount = pixKeyAdded.amountTotal,
                ),
            ),
        )
        addBillEventIntoDb(dynamoDB, pixKeyPaymentStarted.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, pixKeyPaid.copy(walletId = wallet.id))
        receiptRepository.save(
            pixKeyReceiptData.copy(
                recipient = pixKeyReceiptData.recipient.copy(
                    pixQrCodeData = PixQrCodeData(
                        type = PixQrCodeType.STATIC,
                        info = "info",
                        additionalInfo = mapOf("additionalInfo" to "value"),
                        pixId = "TxId",
                        fixedAmount = null,
                        expiration = null,
                        originalAmount = pixKeyAdded.amountTotal,
                    ),
                ),
            ),
        )
        val response = retrieveReceipt<PixReceiptTO>(cookie, pixKeyAdded.billId)
        with(response) {
            totalAmount shouldBe pixKeyReceiptData.totalAmount
            dateTime shouldBe pixKeyReceiptData.dateTime.format(dateTimeFormat)
            recipient.name shouldBe pixKeyReceiptData.recipient.name
            recipient.document shouldBe pixKeyReceiptData.recipient.document
            recipient.pixKey shouldBe null
            recipient.bankDetails shouldBe null
            purpose shouldBe pixKeyReceiptData.purpose
            payerName shouldBe pixKeyReceiptData.payer.name
            payerDocument shouldBe pixKeyReceiptData.payer.document
            payerDocumentType shouldBe "CPF"
            payerBankNumber shouldBe pixKeyReceiptData.payerBankAccount.bankNo
            payerRoutingNumber shouldBe pixKeyReceiptData.payerBankAccount.routingNo
            payerAccountNumber shouldBe pixKeyReceiptData.payerBankAccount.accountNo
            payerAccountDv shouldBe pixKeyReceiptData.payerBankAccount.accountDv
            transactionId shouldBe pixKeyReceiptData.authentication
            billType shouldBe BillType.PIX
            recipient.pixQrCodeData shouldBe PixQrCodeDataTO(
                type = PixQrCodeType.STATIC,
                info = "info",
                additionalInfo = mapOf("additionalInfo" to "value"),
                pixId = "TxId",
                fixedAmount = null,
                expiration = null,
                originalAmount = pixKeyAdded.amountTotal,
            )
        }
    }

    @Test
    fun `should return receipt on get receipt for a paid invoice`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(dynamoDB, invoiceAdded.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, invoicePaymentStarted.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, invoicePaid.copy(walletId = wallet.id))
        receiptRepository.save(invoiceReceiptData)
        val response = retrieveReceipt<InvoiceReceiptTO>(cookie, invoiceAdded.billId)
        with(response) {
            totalAmount shouldBe invoiceReceiptData.totalAmount
            dateTime shouldBe invoiceReceiptData.dateTime.format(dateTimeFormat)
            recipient.name shouldBe invoiceReceiptData.recipient.name
            recipient.document shouldBe invoiceReceiptData.recipient.document
            recipient.bankDetails?.bankNo shouldBe invoiceReceiptData.recipient.bankAccount!!.bankNo
            recipient.bankDetails?.routingNo shouldBe invoiceReceiptData.recipient.bankAccount!!.routingNo
            recipient.bankDetails?.accountNo shouldBe invoiceReceiptData.recipient.bankAccount!!.accountNo
            recipient.bankDetails?.accountDv shouldBe invoiceReceiptData.recipient.bankAccount!!.accountDv
            payerName shouldBe invoiceReceiptData.payer.name
            payerDocument shouldBe invoiceReceiptData.payer.document
            payerDocumentType shouldBe "CPF"
            authentication shouldBe invoiceReceiptData.authentication
            purpose shouldBe invoiceReceiptData.purpose
            paymentPartnerName shouldBe invoiceReceiptData.paymentPartnerName
            paymentPartnerDocument shouldBe invoiceReceiptData.paymentPartnerDocument
            paymentPartnerDocumentType shouldBe "CNPJ"
            billType shouldBe BillType.INVOICE
        }
    }

    @Test
    fun `should return receipt on get receipt for a paid direct invoice`() {
        val cookie = buildCookie(walletFixture.founderAccount)
        addBillEventIntoDb(dynamoDB, invoiceAdded.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, invoicePaymentStarted.copy(walletId = wallet.id))
        addBillEventIntoDb(dynamoDB, invoicePaid.copy(walletId = wallet.id))
        receiptRepository.save(directInvoiceReceiptData)
        val response = retrieveReceipt<InvoiceReceiptTO>(cookie, invoiceAdded.billId)
        with(response) {
            totalAmount shouldBe directInvoiceReceiptData.totalAmount
            dateTime shouldBe directInvoiceReceiptData.dateTime.format(dateTimeFormat)
            recipient.name shouldBe directInvoiceReceiptData.recipient.name
            recipient.document shouldBe directInvoiceReceiptData.recipient.document
            recipient.bankDetails?.bankNo shouldBe directInvoiceReceiptData.recipient.bankAccount!!.bankNo
            recipient.bankDetails?.routingNo shouldBe directInvoiceReceiptData.recipient.bankAccount!!.routingNo
            recipient.bankDetails?.accountNo shouldBe directInvoiceReceiptData.recipient.bankAccount!!.accountNo
            recipient.bankDetails?.accountDv shouldBe directInvoiceReceiptData.recipient.bankAccount!!.accountDv
            payerName shouldBe directInvoiceReceiptData.payer.name
            payerDocument shouldBe directInvoiceReceiptData.payer.document
            payerDocumentType shouldBe "CPF"
            authentication shouldBe directInvoiceReceiptData.authentication
            purpose shouldBe directInvoiceReceiptData.purpose
            paymentPartnerName shouldBe null
            paymentPartnerDocument shouldBe null
            paymentPartnerDocumentType shouldBe ""
            billType shouldBe BillType.INVOICE
            payerBankNumber shouldBe directInvoiceReceiptData.payerBankAccount!!.bankNo
            payerRoutingNumber shouldBe directInvoiceReceiptData.payerBankAccount!!.routingNo
            payerAccountNumber shouldBe directInvoiceReceiptData.payerBankAccount!!.accountNo
            payerAccountDv shouldBe directInvoiceReceiptData.payerBankAccount!!.accountDv
        }
    }

    private fun retrieveReceiptFiles(cookie: Cookie, billId: BillId) = client.toBlocking()
        .retrieve(
            HttpRequest.GET<String>("/bill/id/${billId.value}/receipt/files").cookie(cookie)
                .header("X-API-VERSION", "2").header("X-WALLET-ID", wallet.id.value),
            Argument.of(ReceiptFilesTO::class.java),
            Argument.of(ResponseTO::class.java),
        )

    private inline fun <reified T : ReceiptTO> retrieveReceipt(cookie: Cookie, billId: BillId): T {
        return client.toBlocking()
            .retrieve(
                HttpRequest.GET<String>("/bill/id/${billId.value}/receipt").cookie(cookie).header("X-API-VERSION", "2")
                    .header("X-WALLET-ID", wallet.id.value),
                Argument.of(T::class.java),
                Argument.of(ResponseTO::class.java),
            )
    }
}