package ai.friday.billpayment.integration.api

import ai.friday.billpayment.adapters.api.ContactTO
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.contact.Contact
import ai.friday.billpayment.app.contact.InvalidationCode
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.createSavedRecipient
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.onWallet
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.util.stream.Stream
import kotlin.test.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class GetContactIntegrationTest(embeddedServer: EmbeddedServer, private val dynamoDB: AmazonDynamoDB) {

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val recipientDbRepository = ContactDbRepository(dynamoDbDAO)
    private val wallet =
        walletFixture.buildWallet(otherMembers = listOf(walletFixture.assistant, walletFixture.participant))
    private val request =
        HttpRequest.GET<Any>("/recipient").onWallet(wallet)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository
    private val walletRepository: WalletRepository = mockk() {
        every {
            findWalletOrNull(wallet.id)
        } returns wallet
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    @Test
    fun `should return ok and empty list when user does not have any contact`() {
        val contactTOS = client.toBlocking().retrieve(request, Argument.listOf(ContactTO::class.java))
        assertEquals(0, contactTOS.size)
    }

    @Test
    fun `should return ok when a contact exist`() {
        val savedRecipient = createSavedRecipient(accountId = wallet.founder.accountId).copy(
            bankAccounts = listOf(),
            pixKeys = listOf(),
        )
        recipientDbRepository.save(savedRecipient)

        val contactTOS = client.toBlocking().retrieve(request, Argument.listOf(ContactTO::class.java))

        assertEquals(1, contactTOS.size)
        assertSavedRecipientTO(savedRecipient, contactTOS[0])
    }

    @ParameterizedTest
    @MethodSource("walletContacts")
    fun `should return collaborators contact on collaborator request`(
        member: Member,
        contactOwnerAccount: Account,
    ) {
        val savedRecipient =
            createSavedRecipient(accountId = wallet.founder.accountId).copy(bankAccounts = listOf(), pixKeys = listOf())
        recipientDbRepository.save(savedRecipient.copy(accountId = contactOwnerAccount.accountId))

        val walletRequest = HttpRequest.GET<Any>("/recipient")
            .onWallet(wallet, member)
        val contactTOS = client.toBlocking().retrieve(walletRequest, Argument.listOf(ContactTO::class.java))

        assertEquals(1, contactTOS.size)
        assertSavedRecipientTO(savedRecipient, contactTOS[0])
    }

    @Test
    fun `should return ok when a contact and both bank account and pix key exist`() {
        val savedRecipient = createSavedRecipient(accountId = wallet.founder.accountId)
        recipientDbRepository.save(savedRecipient)

        val contactTOS = client.toBlocking().retrieve(request, Argument.listOf(ContactTO::class.java))

        assertEquals(1, contactTOS.size)
        assertSavedRecipientTO(savedRecipient, contactTOS[0])
    }

    @Test
    fun `should return ok with invalidated message`() {
        val savedRecipient = createSavedRecipient(accountId = wallet.founder.accountId)
        savedRecipient.bankAccounts[0].apply {
            invalidated = true
            invalidationMessage = "Error"
            invalidationCode = InvalidationCode.INVALID_DATA
        }
        savedRecipient.bankAccounts[1].apply {
            invalidated = true
            invalidationMessage = ""
            invalidationCode = InvalidationCode.INVALID_TYPE
        }
        savedRecipient.pixKeys[0].apply {
            invalidated = true
            invalidationCode = InvalidationCode.INVALID_DATA
        }
        recipientDbRepository.save(savedRecipient)

        val contactTOS = client.toBlocking().retrieve(request, Argument.listOf(ContactTO::class.java))

        assertEquals(1, contactTOS.size)
        assertSavedRecipientTO(savedRecipient, contactTOS[0])
    }

    private fun assertSavedRecipientTO(contact: Contact, contactTO: ContactTO) {
        contactTO.id shouldBe contact.id.value
        contactTO.name shouldBe contact.name
        contactTO.alias shouldBe contact.alias
        contactTO.document shouldBe contact.document
        contactTO.bankAccounts.size shouldBe contact.bankAccounts.size

        contact.bankAccounts.forEachIndexed { i, savedBankAccount ->
            with(contactTO.bankAccounts[i]) {
                id shouldBe savedBankAccount.id.value
                ispb shouldBe savedBankAccount.ispb
                bankNo shouldBe savedBankAccount.bankNo
                routingNo shouldBe savedBankAccount.routingNo
                accountNo shouldBe savedBankAccount.accountNo
                accountDv shouldBe savedBankAccount.accountDv
                accountType shouldBe savedBankAccount.accountType.name
                invalidated shouldBe savedBankAccount.invalidated
                invalidationMessage shouldBe savedBankAccount.invalidationMessage
                invalidationCode shouldBe savedBankAccount.invalidationCode?.name
            }
        }

        contact.pixKeys.forEachIndexed { i, pixKey ->
            contactTO.pixKeys[i].type shouldBe pixKey.type
            contactTO.pixKeys[i].value shouldBe pixKey.value
            contactTO.pixKeys[i].invalidated shouldBe pixKey.invalidated
            contactTO.pixKeys[i].invalidationCode shouldBe pixKey.invalidationCode
        }
    }

    companion object {

        private val walletFixture = WalletFixture()

        @JvmStatic
        fun walletContacts(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(walletFixture.assistant, walletFixture.founderAccount),
                Arguments.arguments(walletFixture.founder, walletFixture.founderAccount),
                Arguments.arguments(walletFixture.participant, walletFixture.participantAccount),
            )
        }
    }
}