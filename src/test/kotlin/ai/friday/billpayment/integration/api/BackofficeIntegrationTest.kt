package ai.friday.billpayment.integration.api

import ai.friday.billpayment.MockUserPoolAdapter
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.accountRegisterMissingOnlyAgreement
import ai.friday.billpayment.adapters.api.AccountClosureDetailsTO
import ai.friday.billpayment.adapters.api.InternalAccountTO
import ai.friday.billpayment.adapters.api.RegisterZipCodeTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.UserAccountRegisterTO
import ai.friday.billpayment.adapters.arbi.ArbiAccountAdapter
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.LoginDbRepository
import ai.friday.billpayment.adapters.intercom.IntercomAdapterException
import ai.friday.billpayment.adapters.jobs.SendFinancialReportsService
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ZipCode
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.AdService
import ai.friday.billpayment.app.account.AgreementData
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PepQuery
import ai.friday.billpayment.app.account.PepQueryResult
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SendAccountRegisterDocumentsRequest
import ai.friday.billpayment.app.account.SendDocumentsResponse
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.account.UploadedDocumentImages
import ai.friday.billpayment.app.account.generateSignatureKey
import ai.friday.billpayment.app.account.toContractForm
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.DatabaseObjectRepository
import ai.friday.billpayment.app.integrations.DocumentType
import ai.friday.billpayment.app.integrations.ECMProvider
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.integrations.ZipCodeService
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.msisdnauth.createWhatsappEmail
import ai.friday.billpayment.app.notification.ChatBotMessagePublisher
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.register.kyc.KycDossier
import ai.friday.billpayment.app.register.kyc.KycDossierMepQuery
import ai.friday.billpayment.app.register.kyc.KycDossierOfficialDocumentData
import ai.friday.billpayment.app.register.kyc.KycDossierTaxIdRegion
import ai.friday.billpayment.app.register.kyc.KycService
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.SecurityFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createPartialAccount
import ai.friday.billpayment.integration.loadUserIntoDb
import ai.friday.billpayment.rgDocumentInfo
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.message.ForwardMessageException
import java.io.InputStream
import java.time.LocalDate
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource

private const val emailDomain = "fake.mail.com"

private val kycDossier = KycDossier(
    taxId = KycDossierTaxIdRegion(region = "SP-RJ"),
    sanctions = listOf(),
    globalSanctionsList = listOf(),
    pep = PepQuery(at = ZonedDateTime.now(), result = PepQueryResult.NOT_PEP),
    phones = listOf(),
    emails = listOf(),
    gender = null,
    motherName = null,
    fatherName = null,
    mep = KycDossierMepQuery(
        exposureLevel = null,
        celebrityLevel = null,
        unpopularityLevel = null,
    ),
    officialData = KycDossierOfficialDocumentData(
        provider = "",
        name = "",
        birthDate = LocalDate.of(1980, 1, 1),
        socialName = null,
        hasObitIndication = false,
        deathYear = null,
        regular = false,
        status = "",
    ),

)

@Property(name = "accountRegister.pixKey.emailDomain", value = emailDomain)
@MicronautTest(environments = [FRIDAY_ENV])
class BackofficeIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val loginRepository = LoginDbRepository(dynamoDbDAO)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(DatabaseObjectRepository::class)
    fun databaseObjectRepository(): DatabaseObjectRepository = databaseObjectRepository
    private val databaseObjectRepository: DatabaseObjectRepository = mockk(relaxed = true)

    private val accountRegisterRepository = AccountRegisterDbRepository(dynamoDbDAO, databaseObjectRepository)

    @MockBean(PixKeyManagement::class)
    fun arbiPixKeyManagement(): PixKeyManagement = pixKeyManagement
    private val pixKeyManagement: PixKeyManagement = mockk(relaxed = true)

    @MockBean(DDAService::class)
    fun ddaService(): DDAService = ddaService
    private val ddaService: DDAService = mockk(relaxed = true)

    @MockBean(NotificationAdapter::class)
    fun mockDependency(): NotificationAdapter = notificationMock
    private val notificationMock: NotificationAdapter = mockk(relaxed = true)

    @MockBean(MockUserPoolAdapter::class)
    fun userPoolMock(): UserPoolAdapter = userPoolMock
    private val userPoolMock: UserPoolAdapter = mockk(relaxed = true) {
        every { doesUserExist(any()) } returns true
        every { isUserEnabled(any()) } returns true
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(CrmService::class)
    fun crmService(): CrmService = crmService
    private val crmService: CrmService = mockk(relaxed = true)

    @MockBean(AdService::class)
    fun adService(): AdService = adService
    private val adService: AdService = mockk(relaxed = true)

    @MockBean(ECMProvider::class)
    fun ecmProvider(): ECMProvider = ecmProvider
    private val ecmProvider: ECMProvider = mockk(relaxed = true)

    @MockBean(ArbiAccountAdapter::class)
    fun externalAccountRegister(): ExternalAccountRegister = externalAccountRegister
    private val externalAccountRegister: ExternalAccountRegister = mockk(relaxed = true)

    @MockBean(KycService::class)
    fun kycServiceProvider(): KycService = kycService
    private val kycService: KycService = mockk()

    @MockBean(EmailSenderService::class)
    fun emailSenderService(): EmailSenderService = emailSenderService
    private val emailSenderService: EmailSenderService = mockk(relaxUnitFun = true)

    @MockBean(RegisterInstrumentationService::class)
    fun registerInstrumentationService(): RegisterInstrumentationService = registerInstrumentationService
    private val registerInstrumentationService: RegisterInstrumentationService = mockk(relaxUnitFun = true)

    @MockBean(ZipCodeService::class)
    fun zipCodeService(): ZipCodeService = zipCodeService
    private val zipCodeService: ZipCodeService = mockk(relaxUnitFun = true)

    @MockBean(UserJourneyService::class)
    fun userJourneyService(): UserJourneyService = userJourneyService
    private val userJourneyService: UserJourneyService = mockk(relaxUnitFun = true)

    @MockBean(SystemActivityService::class)
    fun systemActivityService(): SystemActivityService = systemActivityService
    private val systemActivityService: SystemActivityService = mockk(relaxUnitFun = true)

    @MockBean(ChatBotMessagePublisher::class)
    fun chatBotMessagePublisher(): ChatBotMessagePublisher = chatBotMessagePublisher
    private val chatBotMessagePublisher: ChatBotMessagePublisher = mockk(relaxed = true)

    @MockBean(SendFinancialReportsService::class)
    fun sendFinancialReportsService(): SendFinancialReportsService = sendFinancialReportsService
    private val sendFinancialReportsService: SendFinancialReportsService = mockk(relaxed = true)

    @field:Property(name = "internal-auth.identity")
    lateinit var identity: String

    @field:Property(name = "internal-auth.secret")
    lateinit var secret: String

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadUserIntoDb(dynamoDB)
    }

    @Test
    fun `should return a specific user register with groups`() {
        val account = accountRepository.findById(AccountId(ACCOUNT_ID))
        val updatedAccount =
            account.copy(configuration = account.configuration.copy(groups = listOf(AccountGroup.ALPHA)))
        accountRepository.save(updatedAccount)

        val request = HttpRequest.GET<Any>("/backoffice/register/$ACCOUNT_ID")
            .basicAuth(identity, secret)

        val response = client.toBlocking().exchange(request, UserAccountRegisterTO::class.java)

        response.status shouldBe HttpStatus.OK

        response.body()!!.groups shouldBe updatedAccount.configuration.groups.map { it.value }
    }

    @Test
    fun `should not return a specific user register when backoffice credentials are wrong`() {
        val request = HttpRequest.GET<Any>("/backoffice/register/***********")
            .basicAuth("anyuser", "anysecret")

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.UNAUTHORIZED
    }

    @Test
    fun `should not return list of user registers when backoffice credentials are wrong`() {
        val request = HttpRequest.GET<Any>("/backoffice/register")
            .basicAuth("anyuser", "anysecret")

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.UNAUTHORIZED
    }

    @ParameterizedTest
    @ValueSource(strings = ["deny", "approve", "review", "internalApprove", "activate"])
    fun `should return unauthorized on status changes when backoffice credentials are wrong`(operation: String) {
        val request = HttpRequest.POST("/backoffice/register/***********/$operation", "{}")
            .basicAuth("anyuser", "anysecret")

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.UNAUTHORIZED
    }

    @MethodSource("regularUserCookieProvider")
    @ParameterizedTest
    fun `should return forbidden for register reads when it is a regular logged user`(cookie: Cookie) {
        val request = HttpRequest.GET<Any>("/backoffice/register")
            .cookie(cookie)

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    /*
        @MethodSource("cookiesProvider")
        @ParameterizedTest
        @Disabled
        fun `should return forbidden for status changes when it is a regular logged user`(
            cookie: Cookie,
            operation: String,
        ) {
            val request = HttpRequest.POST("/backoffice/register/***********/$operation", "{}")
                .cookie(cookie)

            val thrown =
                assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

            thrown.status shouldBe HttpStatus.FORBIDDEN
        }
    */

    @ParameterizedTest
    @ValueSource(strings = ["deny"])
    fun `should return bad_request when on empty body`(operation: String) {
        val request = HttpRequest.POST("/backoffice/register/***********/$operation", "{ \"reason\": \"FOO\" }")
            .basicAuth(identity, secret)

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @ParameterizedTest
    @MethodSource("accountDoesNotExist")
    fun `should return not_found when account does not exist`(operation: String, requestTO: String) {
        val request = HttpRequest.POST("/backoffice/register/***********/$operation", requestTO)
            .basicAuth(identity, secret)

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4001"
    }

    @ParameterizedTest
    @MethodSource("accountDoesNotExist")
    fun `should return conflict on wrong account status for operation`(operation: String, requestTO: String) {
        val (partialAccount) = createPartialAccount(dynamoDbDAO, AccountStatus.DENIED)
        val request = HttpRequest.POST("/backoffice/register/${partialAccount.id.value}/$operation", requestTO)
            .basicAuth(identity, secret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4002"

        verify {
            notificationMock wasNot called
        }
    }

    @Test
    fun `should return ok when user is UNDER_REVIEW for deny operation`() {
        val (partialAccount) = createPartialAccount(dynamoDbDAO, AccountStatus.UNDER_REVIEW)

        val accountRegister = assertDoesNotThrow { accountRegisterRepository.findByAccountId(partialAccount.id) }

        val request =
            HttpRequest.POST(
                "/backoffice/register/${partialAccount.id.value}/deny",
                AccountClosureDetailsTO("USER_REQUEST", null),
            )
                .basicAuth(identity, secret)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val repositoryStatus = accountRepository.findPartialAccountById(partialAccount.id)
        repositoryStatus.status shouldBe AccountStatus.DENIED

        assertThrows<ItemNotFoundException> { accountRegisterRepository.findByDocument(accountRegister.documentInfo!!.cpf) }

        verify {
            registerInstrumentationService.rejected(
                partialAccount.id,
                partialAccount.registrationType,
                emptyList(),
            )
            notificationMock.notifyRegisterDenied(partialAccount.id, any())
        }
    }

    @Test
    fun `should return no content when user is UNDER_REVIEW for review operation`() {
        val (partialAccount) = createPartialAccount(dynamoDbDAO, AccountStatus.UNDER_REVIEW)
        val accountRegister = accountRegisterMissingOnlyAgreement.copy(
            accountId = partialAccount.id,
            agreementData = AgreementData(
                acceptedAt = getZonedDateTime(),
                userContractFile = StoredObject("region", "bucket", "userContract"),
                userContractSignature = accountRegisterMissingOnlyAgreement.toContractForm().generateSignatureKey(),
                declarationOfResidencyFile = StoredObject("region", "bucket", "declarationOfResidencyFile"),

            ),
        )
        accountRegisterRepository.save(accountRegister)

        val request = HttpRequest.POST("/backoffice/register/${partialAccount.id.value}/review", "")
            .basicAuth(identity, secret)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val repositoryStatus = accountRepository.findPartialAccountById(partialAccount.id)
        repositoryStatus.status shouldBe AccountStatus.REGISTER_INCOMPLETE

        val register = accountRegisterRepository.findByAccountId(partialAccount.id)
        register.agreementData shouldBe null
        register.openForUserReview shouldBe true
        register.openedForUserReviewAt shouldNotBe null

        verify {
            registerInstrumentationService.reopened(
                partialAccount.id,
                accountRegister.registrationType,
                emptyList(),
            )
        }
    }

    @Test
    fun `should return internal server error on error sending documents to paperoff approving account internally`() {
        val (partialAccount) = createPartialAccount(dynamoDbDAO, AccountStatus.UNDER_REVIEW)

        mockDocumentStream()
        mockSendAccountRegisterDocuments(success = false)

        val request = buildInternalApproveRequest(partialAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "5005"

        val updatedPartialAccount = accountRepository.findPartialAccountById(partialAccount.id)
        updatedPartialAccount.status shouldBe AccountStatus.UNDER_REVIEW
    }

    private fun mockSendAccountRegisterDocuments(success: Boolean) {
        every {
            ecmProvider.sendAccountRegisterDocuments(any())
        } returns SendDocumentsResponse(success = success, status = "fake status")
    }

    @Test
    fun `should return internal server error on error querying kyc approving account internally`() {
        val (partialAccount) = createPartialAccount(dynamoDbDAO, AccountStatus.UNDER_REVIEW)

        mockDocumentStream()
        mockSendAccountRegisterDocuments(success = true)
        mockNotifyAccountRegisterDocumentsSent(success = false)

        val request = buildInternalApproveRequest(partialAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "5006"

        val updatedPartialAccount = accountRepository.findPartialAccountById(partialAccount.id)
        updatedPartialAccount.status shouldBe AccountStatus.UNDER_REVIEW
    }

    @Test
    fun `should return ok when user is UNDER_REVIEW for internal approve operation, send documents, query KYC and update user status to EXTERNAL_REVIEW`() {
        val pdfStoredObject = StoredObject(
            region = "",
            bucket = "",
            key = "file.pdf",
        )
        val (partialAccount, accountRegisterData) = createPartialAccount(
            dynamoDbDAO = dynamoDbDAO,
            accountStatus = AccountStatus.UNDER_REVIEW,
            accountRegisterData = accountRegisterDataMissingAcceptedAt.copy(
                uploadedCNH = StoredObject(
                    region = "",
                    bucket = "",
                    key = "file.jpg",
                ),
                uploadedSelfie = StoredObject(
                    region = "",
                    bucket = "",
                    key = "file.png",
                ),
                kycFile = pdfStoredObject,
                agreementData = accountRegisterDataMissingAcceptedAt.agreementData!!.copy(
                    declarationOfResidencyFile = pdfStoredObject,
                    userContractFile = pdfStoredObject,
                ),
            ),
        )

        mockDocumentStream()
        mockSendAccountRegisterDocuments(success = true)
        mockNotifyAccountRegisterDocumentsSent(success = true)

        val request = buildInternalApproveRequest(partialAccount)

        val response = client.toBlocking().exchange(request, String::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        with(accountRepository.findPartialAccountById(partialAccount.id)) {
            status shouldBe AccountStatus.UNDER_EXTERNAL_REVIEW
        }

        val slot = slot<SendAccountRegisterDocumentsRequest>()
        verify(exactly = 1) {
            ecmProvider.sendAccountRegisterDocuments(capture(slot))
        }

        slot.captured.userContract.extension shouldBe "pdf"
        slot.captured.userSelfie.extension shouldBe "png"
        slot.captured.userDocument.first().extension shouldBe "jpg"
        slot.captured.userKYC?.extension shouldBe "pdf"
        slot.captured.declarationOfResidency.extension shouldBe "pdf"

        verify(exactly = 1) {
            externalAccountRegister.notifyAccountRegisterDocumentsSent(
                name = accountRegisterData.documentInfo!!.name,
                document = accountRegisterData.documentInfo!!.cpf,
            )
        }
    }

    @Test
    fun `should return internal server error on error activating account`() {
        val (partialAccount, accountRegisterData) = createPartialAccount(dynamoDbDAO, AccountStatus.APPROVED)
        setupLoginProviders(partialAccount)
        saveNewAccount(partialAccount, accountRegisterData)

        every {
            ddaService.findByAccount(any())
        } returns DDARegister(accountId = partialAccount.id, document = "", created = ZonedDateTime.now(), status = DDAStatus.ACTIVE, lastUpdated = ZonedDateTime.now(), lastSuccessfullExecution = null, provider = DDAProvider.ARBI, migrated = null)

        every {
            crmService.contactExists(any())
        } returns true

        val request = buildActivateRequest(partialAccount)

        every {
            crmService.upsertContact(any<Account>())
        } throws IntercomAdapterException("contact not found")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.response.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "5002"
    }

    @Test
    fun `should return internal server error on DDA error activating account`() {
        val (partialAccount, accountRegisterData) = createPartialAccount(dynamoDbDAO, AccountStatus.APPROVED)
        saveNewAccount(partialAccount, accountRegisterData)
        val account = accountRepository.findById(partialAccount.id)

        every {
            userPoolMock.doesUserExist(any())
        } throws NoStackTraceException("fake error")

        val request = buildActivateRequest(partialAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "-1"

        assertDoesNotThrow { accountRepository.findPartialAccountById(partialAccount.id) }

        verify(exactly = 0) {
            notificationMock.notifyUserActivated(account)
            userJourneyService.registerAsync(any())
        }
    }

    @Test
    fun `should return conflict on account activation when crm user does not exits`() {
        val (partialAccount, accountRegisterData) = createPartialAccount(dynamoDbDAO, AccountStatus.APPROVED)
        setupLoginProviders(partialAccount)
        saveNewAccount(partialAccount, accountRegisterData)

        every {
            ddaService.findByAccount(any())
        } returns DDARegister(accountId = partialAccount.id, document = "", created = ZonedDateTime.now(), status = DDAStatus.ACTIVE, lastUpdated = ZonedDateTime.now(), lastSuccessfullExecution = null, provider = DDAProvider.ARBI, migrated = null)

        every {
            crmService.contactExists(any())
        } returns false

        val request = buildActivateRequest(partialAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4004"

        verify(exactly = 0) {
            notificationMock.notifyUserActivated(any())
        }
    }

    @ParameterizedTest
    @MethodSource("creditCardQuotaByDocumentType")
    fun `should return ok on account activation, register DDA and update set credit card quota`(
        quota: Long,
        accountRegister: AccountRegisterData,
    ) {
        val (partialAccount, accountRegisterData) = createPartialAccount(
            dynamoDbDAO,
            AccountStatus.APPROVED,
            accountRegisterData = accountRegister,
        )
        setupLoginProviders(partialAccount) // TODO deixar claro que é uma CNH
        saveNewAccount(partialAccount, accountRegisterData)

        every {
            ddaService.findByAccount(any())
        } returns DDARegister(accountId = partialAccount.id, document = "", created = ZonedDateTime.now(), status = DDAStatus.ACTIVE, lastUpdated = ZonedDateTime.now(), lastSuccessfullExecution = null, provider = DDAProvider.ARBI, migrated = null)

        every {
            crmService.contactExists(any())
        } returns true

        val request = buildActivateRequest(partialAccount)

        val response =
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK

        assertThrows<AccountNotFoundException> { accountRepository.findPartialAccountById(partialAccount.id) }

        val persistedAccount = accountRepository.findById(partialAccount.id)
        persistedAccount.status shouldBe AccountStatus.ACTIVE

        with(persistedAccount.configuration) {
            receiveDDANotification shouldBe true
            creditCardConfiguration.quota shouldBe quota
        }

        val slot = mutableListOf<Account>()
        verify {
            crmService.upsertContact(capture(slot))
            chatBotMessagePublisher.publishWelcomeMessage(any(), any())
            userJourneyService.registerAsync(any())
            systemActivityService.setAccountActivated(accountRegisterData.accountId, any())
            adService.publishAccountActivated(accountId = partialAccount.id)
        }
        // before credit card cannot assert groups
        with(slot.first()) {
            accountId shouldBe persistedAccount.accountId
            emailAddress shouldBe persistedAccount.emailAddress
            name shouldBe persistedAccount.name
            mobilePhone shouldBe persistedAccount.mobilePhone
            document shouldBe persistedAccount.document
        }
        // after credit card should assert groups
        with(slot.last()) {
            accountId shouldBe persistedAccount.accountId
            emailAddress shouldBe persistedAccount.emailAddress
            name shouldBe persistedAccount.name
            mobilePhone shouldBe persistedAccount.mobilePhone
            document shouldBe persistedAccount.document
            configuration.groups shouldBe persistedAccount.configuration.groups
        }

        verify {
            registerInstrumentationService.activated(partialAccount.id, partialAccount.registrationType)
        }
    }

    @Test
    fun `should return not found when account does not exist for internal sensitive data generation`() {
        val request = HttpRequest.POST("/backoffice/register/***********/submitInternalReview", Unit)
            .basicAuth(identity, secret)

        val thrown =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, String::class.java) }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4001"
    }

    @Test
    fun `should return conflict when user is not UNDER_REVIEW for internal sensitive data generation`() {
        val (partialAccount) = createPartialAccount(dynamoDbDAO, AccountStatus.DENIED)
        val request = HttpRequest.POST("/backoffice/register/${partialAccount.id.value}/submitInternalReview", Unit)
            .basicAuth(identity, secret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4002"

        verify {
            notificationMock wasNot called
        }
    }

    @Test
    fun `should return ok and send email for internal sensitive data generation when kyc generation fails`() {
        val (partialAccount) = createPartialAccount(dynamoDbDAO, AccountStatus.UNDER_REVIEW)

        every {
            kycService.generate(any())
        } returns Exception().left()

        val request = HttpRequest.POST("/backoffice/register/${partialAccount.id.value}/submitInternalReview", Unit)
            .basicAuth(identity, secret)

        val response =
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK

        verify {
            emailSenderService.sendRawEmail(any(), any(), any(), any())
            emailSenderService.sendRawEmail(any(), any(), any(), any(), any())
        }
    }

    @Test
    fun `should return internal server error for internal sensitive data generation when email fails`() {
        val (partialAccount) = createPartialAccount(dynamoDbDAO, AccountStatus.UNDER_REVIEW)
        val kycFile = StoredObject("region_fake", "bucket", "key")

        every {
            kycService.generate(any())
        } returns Pair(kycFile, kycDossier).right()

        every {
            emailSenderService.sendRawEmail(any(), any(), any(), any(), any())
        } throws ForwardMessageException("", Exception())

        val request = HttpRequest.POST("/backoffice/register/${partialAccount.id.value}/submitInternalReview", Unit)
            .basicAuth(identity, secret)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))
        }

        with(accountRegisterRepository.findByAccountId(partialAccount.id)) {
            this.kycFile shouldBe kycFile
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "-1"
    }

    @Test
    fun `should return ok for internal sensitive data generation when kyc data is generated and email is sent`() {
        val (partialAccount) = createPartialAccount(dynamoDbDAO, AccountStatus.UNDER_REVIEW)
        val kycFile = StoredObject("region_fake", "bucket", "key")

        every {
            kycService.generate(any())
        } returns Pair(kycFile, kycDossier).right()

        val request = HttpRequest.POST("/backoffice/register/${partialAccount.id.value}/submitInternalReview", Unit)
            .basicAuth(identity, secret)

        val response =
            client.toBlocking().exchange(request, Argument.of(String::class.java), Argument.of(ResponseTO::class.java))

        with(accountRegisterRepository.findByAccountId(partialAccount.id)) {
            this.kycFile shouldBe kycFile
        }

        verify {
            emailSenderService.sendRawEmail(any(), any(), any(), any())
            emailSenderService.sendRawEmail(any(), any(), any(), any(), any())
        }

        response.status shouldBe HttpStatus.OK
    }

    @ParameterizedTest
    @EnumSource(value = AccountStatus::class)
    fun `should return partial accounts with received account status`(current: AccountStatus) {
        val account = accountRepository.create(
            "meu nome",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.UPGRADED,
        )
        accountRepository.updatePartialAccountStatus(account.id, current)

        val request = HttpRequest.GET<Any>("/backoffice/register/partialAccounts?status=${current.name}")
            .basicAuth(identity, secret)

        val response = client.toBlocking()
            .exchange(request, Argument.listOf(InternalAccountTO::class.java), Argument.of(ResponseTO::class.java))

        response.body().shouldHaveSize(1)
    }

    @Test
    fun `should not return partial accounts when registration type is BASIC`() {
        val account = accountRepository.create(
            "meu nome",
            EmailAddress("<EMAIL>"),
            registrationType = RegistrationType.BASIC,
        )
        accountRepository.updatePartialAccountStatus(account.id, AccountStatus.ACTIVE)

        val request = HttpRequest.GET<Any>("/backoffice/register/partialAccounts?status=${AccountStatus.ACTIVE.name}")
            .basicAuth(identity, secret)

        val response = client.toBlocking()
            .exchange(request, Argument.listOf(InternalAccountTO::class.java), Argument.of(ResponseTO::class.java))

        response.body().shouldHaveSize(0)
    }

    @Test
    fun `should return list of registers`() {
        withGivenDateTime(getZonedDateTime().minusMonths(1)) {
            createPartialAccount(dynamoDbDAO, AccountStatus.DENIED, accountRegisterCompleted)

            val (staleActivePartialAccount, staleActiveAccountRegisterData) = createPartialAccount(
                dynamoDbDAO,
                AccountStatus.ACTIVE,
                accountRegisterCompleted,
            )

            saveNewAccount(staleActivePartialAccount, staleActiveAccountRegisterData)
        }

        val (underReviewPartialAccount) = createPartialAccount(
            dynamoDbDAO,
            AccountStatus.UNDER_REVIEW,
            accountRegisterCompleted,
        )

        val request = HttpRequest.GET<Any>("/backoffice/register")
            .basicAuth(identity, secret)

        val response = client.toBlocking().exchange(request, Argument.listOf(UserAccountRegisterTO::class.java))
        val registers = response.body.get()

        response.status shouldBe HttpStatus.OK
        registers.map { it.accountId } shouldContainExactlyInAnyOrder listOf(
            underReviewPartialAccount.id.value,
        )
    }

    @Test
    fun `should register a ZipCode`() {
        val request =
            HttpRequest.POST("/backoffice/zipcode", RegisterZipCodeTO("********"))
                .basicAuth(identity, secret)
        val response = client.toBlocking().exchange(request, String::class.java)
        response.status shouldBe HttpStatus.CREATED
        val slot = slot<ZipCode>()
        verify {
            zipCodeService.register(capture(slot))
        }
        with(slot.captured) {
            value shouldBe "********"
        }
    }

    @Test
    fun `should not register a invalid ZipCode`() {
        val request =
            HttpRequest.POST("/backoffice/zipcode", RegisterZipCodeTO("********1"))
                .basicAuth(identity, secret)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, String::class.java)
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        verify {
            zipCodeService wasNot called
        }
    }

    private fun saveNewAccount(partialAccount: PartialAccount, accountRegisterData: AccountRegisterData): Account {
        val newAccount = Account(
            accountId = partialAccount.id,
            name = partialAccount.name,
            emailAddress = partialAccount.emailAddress,
            document = accountRegisterData.documentInfo!!.cpf,
            documentType = "CPF",
            mobilePhone = accountRegisterData.mobilePhone!!.msisdn,
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            status = AccountStatus.APPROVED,
            configuration = LegacyAccountConfiguration(
                creditCardConfiguration = CreditCardConfiguration(),
                defaultWalletId = null,
                receiveDDANotification = false,
            ),
            imageUrlSmall = "image_url_small",
            imageUrlLarge = "image_url_large",
            subscriptionType = SubscriptionType.PIX,
        )
        accountRepository.save(newAccount)
        return newAccount
    }

    private fun mockNotifyAccountRegisterDocumentsSent(success: Boolean) {
        every {
            externalAccountRegister.notifyAccountRegisterDocumentsSent(any(), any())
        } returns success
    }

    private fun mockDocumentStream() {
        every {
            databaseObjectRepository.getObjectInputStream(any())
        } answers {
            InputStream.nullInputStream()
        }
    }

    private fun buildInternalApproveRequest(partialAccount: PartialAccount) =
        HttpRequest.POST("/backoffice/register/${partialAccount.id.value}/internalApprove", Unit)
            .basicAuth(identity, secret)

    private fun buildActivateRequest(partialAccount: PartialAccount) =
        HttpRequest.POST(
            "/backoffice/register/${partialAccount.id.value}/activate",
            "",
        )
            .basicAuth(identity, secret)

    private fun setupLoginProviders(partialAccount: PartialAccount): ProviderUser {
        val googleProviderUser =
            ProviderUser("GOOGLE_ID_XXXXX", ProviderName.MSISDN, "John doe", partialAccount.emailAddress)
        loginRepository.createLogin(googleProviderUser, partialAccount.id, Role.GUEST)

        val whatsappProviderUser =
            ProviderUser(partialAccount.id.value, ProviderName.WHATSAPP, "John doe", createWhatsappEmail(accountRegisterDataMissingAcceptedAt.mobilePhone!!))
        loginRepository.createLogin(whatsappProviderUser, partialAccount.id, Role.GUEST)

        return googleProviderUser
    }

    companion object {
        private val securityFixture = SecurityFixture()

        @JvmStatic
        fun regularUserCookieProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(securityFixture.cookieGuest),
                Arguments.of(securityFixture.cookieGuest),
                Arguments.of(securityFixture.cookieGuest),
                Arguments.of(securityFixture.cookieGuest),
                Arguments.of(securityFixture.cookieGuest),
                Arguments.of(securityFixture.cookieAuthOwner),
                Arguments.of(securityFixture.cookieAuthOwner),
                Arguments.of(securityFixture.cookieAuthOwner),
                Arguments.of(securityFixture.cookieAuthOwner),
                Arguments.of(securityFixture.cookieAuthOwner),
            )
        }

        @JvmStatic
        fun creditCardQuotaByDocumentType(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    0,
                    accountRegisterDataMissingAcceptedAt.copy(
                        uploadedCNH = null,
                        uploadedDocument = UploadedDocumentImages(
                            front = StoredObject("region", "bucket", "front.png"),
                            documentType = DocumentType.RG,
                            back = StoredObject("region", "bucket", "back.png"),
                        ),
                        documentInfo = rgDocumentInfo,
                    ),
                ),
                Arguments.of(
                    2_000_00,
                    accountRegisterDataMissingAcceptedAt.copy(
                        uploadedCNH = StoredObject("region", "bucket", "front.png"),
                    ),
                ),
            )
        }

        @JvmStatic
        fun cookiesProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(securityFixture.cookieGuest, "deny"),
                Arguments.of(securityFixture.cookieGuest, "approve"),
                Arguments.of(securityFixture.cookieGuest, "review"),
                Arguments.of(securityFixture.cookieGuest, "internalApprove"),
                Arguments.of(securityFixture.cookieGuest, "activate"),
                Arguments.of(securityFixture.cookieAuthOwner, "deny"),
                Arguments.of(securityFixture.cookieAuthOwner, "approve"),
                Arguments.of(securityFixture.cookieAuthOwner, "review"),
                Arguments.of(securityFixture.cookieAuthOwner, "internalApprove"),
                Arguments.of(securityFixture.cookieAuthOwner, "activate"),
            )
        }

        @JvmStatic
        fun accountDoesNotExist(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(
                    "deny",
                    getObjectMapper().writeValueAsString(AccountClosureDetailsTO("USER_REQUEST", null)),
                ),
                Arguments.of("review", "{}"),
                Arguments.of("internalApprove", "{}"),
                Arguments.of("activate", "{}"),
            )
        }
    }
}