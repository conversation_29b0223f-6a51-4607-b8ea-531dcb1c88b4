package ai.friday.billpayment.integration

import ai.friday.billpayment.BANK_ACCOUNT_ID
import ai.friday.billpayment.RECIPIENT_ID
import ai.friday.billpayment.RECIPIENT_PIX_EVP_KEY
import ai.friday.billpayment.adapters.api.ContactErrors
import ai.friday.billpayment.adapters.api.ContactTO
import ai.friday.billpayment.adapters.api.FinancialInstitutionController
import ai.friday.billpayment.adapters.api.FinancialInstitutionListTO
import ai.friday.billpayment.adapters.api.FinancialInstitutionTO
import ai.friday.billpayment.adapters.api.PixKeyRequestTO
import ai.friday.billpayment.adapters.api.RecipientBankDetailsTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.UpdateContactTO
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.contact.BankAccountId
import ai.friday.billpayment.app.contact.LastUsed
import ai.friday.billpayment.app.contact.SavedBankAccount
import ai.friday.billpayment.app.contact.SavedPixKey
import ai.friday.billpayment.app.contact.toBankAccount
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.SweepingAccountServiceInterface
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.createSavedRecipient
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixIgnored
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.pixKeyDetails
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.timeFormat
import arrow.core.Either
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldContain
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.math.BigInteger
import java.time.Instant
import java.time.LocalTime
import java.util.UUID
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource

@MicronautTest(environments = [FRIDAY_ENV])
class ContactIntegrationTest(
    embeddedServer: EmbeddedServer,
    private val createBillService: CreateBillService,
    val dynamoDB: AmazonDynamoDB,
) {

    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(otherMembers = listOf(walletFixture.assistant, walletFixture.participant))

    @MockBean(PixKeyManagement::class)
    fun pixKeyManagementFun(): PixKeyManagement = pixKeyManagement

    @MockBean(FinancialInstitutionController::class)
    fun financialInstitutionController(): FinancialInstitutionController = mockk {
        every { getFinancialInstitutions() } returns FinancialInstitutionListTO(
            listOf(
                FinancialInstitutionTO(
                    code = 1,
                    name = "Via1",
                    ispb = "********",
                    isPixEnabled = true,
                    isTedEnabled = true,
                    isCommon = false,
                ),
                FinancialInstitutionTO(
                    code = 111,
                    name = "Via2",
                    ispb = "********",
                    isPixEnabled = true,
                    isTedEnabled = true,
                    isCommon = false,
                ),
            ),
        )
    }

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository
    private val walletRepository: WalletRepository = mockk() {
        every {
            findWalletOrNull(wallet.id)
        } returns wallet
    }

    @MockBean(SweepingAccountServiceInterface::class)
    fun sweepingAccountService() = sweepingAccountService
    private val sweepingAccountService: SweepingAccountServiceInterface = mockk(relaxed = true)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val validPixKeyRequest = PixKeyRequestTO(value = pixKeyDetails.key.value, type = pixKeyDetails.key.type)

    private val pixKeyManagement: PixKeyManagement = mockk {
        every {
            findKeyDetails(any(), any())
        } returns Either.Right(PixKeyDetailsResult(pixKeyDetails = pixKeyDetails, e2e = UUID.randomUUID().toString()))
    }

    val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val contactDbRepository = ContactDbRepository(dynamoDbDAO)
    private val recurrenceRepository = BillRecurrenceDBRepository(dynamoDbDAO, "2021-12-31")

    private val billEventDBRepository = BillEventDBRepository(
        dynamoDbDAO,
        allFalseFeatureConfiguration,
    )

    private val accountId = wallet.founder.accountId

    private val savedRecipient = createSavedRecipient(accountId = accountId)

    private val validBankDetailsTO = RecipientBankDetailsTO(
        accountType = AccountType.CHECKING,
        bankNo = 1,
        routingNo = 1,
        accountNo = BigInteger("1"),
        accountDv = "1",
        ispb = "********",
    )

    private val validUpdateContactTO = UpdateContactTO("valid alias fake")

    fun createAddPixKeyRequest(pixKeyRequestTO: PixKeyRequestTO): HttpRequest<*> {
        return HttpRequest.POST("/recipient/$RECIPIENT_ID/pixKey", pixKeyRequestTO)
            .onWallet(wallet)
    }

    fun createAddBankAccount(bankDetailsTO: RecipientBankDetailsTO): HttpRequest<*> {
        return HttpRequest.POST("/recipient/$RECIPIENT_ID/bankAccount", bankDetailsTO)
            .onWallet(wallet)
    }

    fun createUpdateBankAccount(
        bankDetailsTO: RecipientBankDetailsTO,
        bankAccountId: String = BANK_ACCOUNT_ID,
    ): HttpRequest<*> {
        return HttpRequest.PUT("/recipient/$RECIPIENT_ID/bankAccount/$bankAccountId", bankDetailsTO)
            .onWallet(wallet)
    }

    fun createUpdateRecipient(updateContactTO: UpdateContactTO): HttpRequest<*> {
        return HttpRequest.PUT("/recipient/$RECIPIENT_ID", updateContactTO).onWallet(wallet)
    }

    fun createGetContact(): HttpRequest<*> {
        return HttpRequest.GET<List<ContactTO>>("/recipient/")
            .onWallet(wallet)
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, accountId = accountId)
    }

    @Test
    fun `should return contact with lastUsed bankaccount correctly`() {
        contactDbRepository.save(savedRecipient.copy(lastUsed = LastUsed(bankAccountId = savedRecipient.bankAccounts.first().id)))

        val request = createGetContact()
        val response = client.toBlocking().exchange(request, Argument.listOf(ContactTO::class.java))

        response.status shouldBe HttpStatus.OK

        with(response.body().first()) {
            this.lastUsed?.bankAccountId shouldBe savedRecipient.bankAccounts.first().id.value
            this.lastUsed?.pixKey.shouldBeNull()
        }
    }

    @Test
    fun `should return contact with lastUsed pixKey correctly`() {
        contactDbRepository.save(savedRecipient.copy(lastUsed = LastUsed(pixKey = savedRecipient.pixKeys.first().value)))

        val request = createGetContact()
        val response = client.toBlocking().exchange(request, Argument.listOf(ContactTO::class.java))

        response.status shouldBe HttpStatus.OK

        with(response.body().first()) {
            this.lastUsed?.pixKey?.value shouldBe savedRecipient.pixKeys.first().value
            this.lastUsed?.bankAccountId.shouldBeNull()
        }
    }

    @Test
    fun `should return contact with lastUsed null when bankaccount does not exists`() {
        contactDbRepository.save(savedRecipient.copy(lastUsed = LastUsed(bankAccountId = BankAccountId(savedRecipient.bankAccounts.first().id.value + "opa"))))

        val request = createGetContact()
        val response = client.toBlocking().exchange(request, Argument.listOf(ContactTO::class.java))

        response.status shouldBe HttpStatus.OK

        with(response.body().first()) {
            this.lastUsed.shouldBeNull()
        }
    }

    @Test
    fun `should return contact with lastUsed null when pixkey does not exists`() {
        contactDbRepository.save(savedRecipient.copy(lastUsed = LastUsed(pixKey = savedRecipient.pixKeys.first().value + "opa")))

        val request = createGetContact()
        val response = client.toBlocking().exchange(request, Argument.listOf(ContactTO::class.java))

        response.status shouldBe HttpStatus.OK

        with(response.body().first()) {
            this.lastUsed.shouldBeNull()
        }
    }

    @ParameterizedTest
    @CsvSource(
        "CNPJ,***********",
        "PHONE,*************",
        "EMAIL,teste@@com",
        "EVP,XXXXX-XXXXX-XXXX",
        "CPF,1339759XXXX",
        "CPF,********000199",
    )
    fun `should return bad request on invalid pix key format`(type: PixKeyType, key: String) {
        val request = createAddPixKeyRequest(PixKeyRequestTO(value = key, type = type))

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Void::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe ContactErrors.INVALID_PIX_KEY_FORMAT.code
    }

    @Test
    fun `should return not found on valid pix key but nonexistent contact`() {
        val request = createAddPixKeyRequest(validPixKeyRequest)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe ContactErrors.RESOURCE_NOT_FOUND.code
    }

    @Test
    fun `should return not found when pix key does not exist`() {
        every {
            pixKeyManagement.findKeyDetails(any(), any())
        } returns Either.Left(PixKeyError.KeyNotFound)

        val request = createAddPixKeyRequest(validPixKeyRequest)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Void::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe ContactErrors.PIX_KEY_NOT_FOUND.code
    }

    @Test
    fun `should return NO CONTENT when pix key is created`() {
        contactDbRepository.save(recipient = savedRecipient)

        val request = createAddPixKeyRequest(validPixKeyRequest)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)

        databaseRecipient.pixKeys shouldContain SavedPixKey(
            value = validPixKeyRequest.value,
            type = validPixKeyRequest.type,
        )

        databaseRecipient.pixKeys.size shouldBe savedRecipient.pixKeys.size + 1
    }

    @Test
    fun `should return NO CONTENT when pix key is added by assistant`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        loadAccountIntoDb(amazonDynamoDB = dynamoDB, accountId = walletFixture.assistantAccount.accountId)
        contactDbRepository.save(recipient = savedRecipient.copy(accountId = walletFixture.founderAccount.accountId))

        val request = HttpRequest.POST("/recipient/$RECIPIENT_ID/pixKey", validPixKeyRequest)
            .onWallet(wallet, walletFixture.assistantAccount)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val databaseRecipient =
            contactDbRepository.findByIdAndAccountId(savedRecipient.id, walletFixture.founderAccount.accountId)

        databaseRecipient.pixKeys shouldBe savedRecipient.pixKeys + SavedPixKey(
            value = validPixKeyRequest.value,
            type = validPixKeyRequest.type,
        )
    }

    @Test
    fun `should return error when pix key is added by founder on contact assistant`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        loadAccountIntoDb(amazonDynamoDB = dynamoDB, accountId = walletFixture.founderAccount.accountId)
        contactDbRepository.save(recipient = savedRecipient.copy(accountId = walletFixture.assistantAccount.accountId))

        val request = HttpRequest.POST("/recipient/$RECIPIENT_ID/pixKey", validPixKeyRequest)
            .onWallet(wallet, walletFixture.founderAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND

        val databaseRecipient =
            contactDbRepository.findByIdAndAccountId(savedRecipient.id, walletFixture.assistantAccount.accountId)
        databaseRecipient.pixKeys shouldBe savedRecipient.pixKeys
    }

    @Test
    fun `should return NO CONTENT when pix key is already exists`() {
        val savedPixKey = SavedPixKey(
            value = validPixKeyRequest.value,
            type = validPixKeyRequest.type,
        )
        contactDbRepository.save(recipient = savedRecipient)
        contactDbRepository.append(
            savedPixKey = savedPixKey,
            contactId = savedRecipient.id,
            accountId = accountId,
        )

        val request = createAddPixKeyRequest(validPixKeyRequest)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)

        databaseRecipient.pixKeys shouldContain savedPixKey

        databaseRecipient.pixKeys.size shouldBe savedRecipient.pixKeys.size + 1
    }

    @Test
    fun `should return BAD REQUEST when pix key document is not the same as the contact`() {
        contactDbRepository.save(recipient = savedRecipient)

        every {
            pixKeyManagement.findKeyDetails(any(), any())
        } returns Either.Right(
            PixKeyDetailsResult(
                pixKeyDetails = pixKeyDetails.copy(owner = pixKeyDetails.owner.copy(document = "********909")),
                e2e = UUID.randomUUID().toString(),
            ),
        )

        val request = createAddPixKeyRequest(validPixKeyRequest)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Void::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe ContactErrors.INVALID_PIX_KEY_OWNER.code
    }

    @ParameterizedTest
    @CsvSource(
        "CHECKING,-11111,02,123,1,,400",
        "CHECKING,111111,-22,123,X,,400",
        "CHECKING,111111,10000,123,1,,400",
        "CHECKING,111111,02,-12,x,,400",
        "CHECKING,111111,02,,1,,406",
        "CHECKING,111111,02,123,111,,400",
        "CHECKING,111111,02,,1,********9,400",
    )
    fun `should return bad request when adding invalid bank account`(
        accountType: AccountType,
        accountNo: BigInteger,
        routingNo: Long,
        bankNo: Long?,
        accountDv: String,
        ispb: String?,
        code: String,
    ) {
        val bankDetailsTO = RecipientBankDetailsTO(
            accountType = accountType,
            bankNo = bankNo,
            routingNo = routingNo,
            accountNo = accountNo,
            accountDv = accountDv,
            ispb = ispb,
        )

        val request = createAddBankAccount(bankDetailsTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Void::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        val responseTO = thrown.response.getBody(ResponseTO::class.java).get()
        responseTO.code shouldBe code
    }

    @ParameterizedTest
    @CsvSource(
        ",********",
        "999,",
        "999,********",
        "1,********",
        "99,********",
    )
    fun `should return BAD REQUEST when adding bank account with financial institution not found or inconsistent`(
        bankNo: Long?,
        ispb: String?,
    ) {
        val bankDetailsTO = RecipientBankDetailsTO(
            accountType = AccountType.CHECKING,
            bankNo = bankNo,
            routingNo = 1,
            accountNo = BigInteger("1"),
            accountDv = "1",
            ispb = ispb,
        )

        val request = createAddBankAccount(bankDetailsTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe ContactErrors.INVALID_BANK_NO_AND_OR_ISPB.code
    }

    @Test
    fun `should return NOT FOUND when adding valid bank account but non existing contact`() {
        val request = createAddBankAccount(validBankDetailsTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe ContactErrors.RESOURCE_NOT_FOUND.code
    }

    @Test
    fun `should return NO CONTENT when adding valid bank account`() {
        contactDbRepository.save(savedRecipient)
        val request = createAddBankAccount(validBankDetailsTO)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)

        databaseRecipient.bankAccounts shouldContain SavedBankAccount(
            accountType = validBankDetailsTO.accountType,
            bankNo = validBankDetailsTO.bankNo,
            routingNo = validBankDetailsTO.routingNo,
            accountNo = validBankDetailsTO.accountNo,
            accountDv = validBankDetailsTO.accountDv,
            ispb = validBankDetailsTO.ispb,
        )

        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size + 1
    }

    @Test
    fun `should return NO CONTENT when adding duplicated bank account`() {
        val savedBankAccount = SavedBankAccount(
            accountType = validBankDetailsTO.accountType,
            bankNo = validBankDetailsTO.bankNo,
            routingNo = validBankDetailsTO.routingNo,
            accountNo = validBankDetailsTO.accountNo,
            accountDv = validBankDetailsTO.accountDv,
            ispb = validBankDetailsTO.ispb,
        )
        contactDbRepository.save(savedRecipient)
        contactDbRepository.append(
            savedBankAccount = savedBankAccount,
            contactId = savedRecipient.id,
            accountId = accountId,
        )

        val request = createAddBankAccount(validBankDetailsTO)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)

        databaseRecipient.bankAccounts shouldContain savedBankAccount

        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size + 1
    }

    @ParameterizedTest
    @CsvSource(
        "CHECKING,-11111,02,123,1,,400",
        "CHECKING,111111,-22,123,1,,400",
        "CHECKING,111111,10000,123,1,,400",
        "CHECKING,111111,02,-12,1,,400",
        "CHECKING,111111,02,,1,,406",
        "CHECKING,111111,02,123,111,,400",
        "CHECKING,111111,02,,1,********9,400",
    )
    fun `should return bad request when updating invalid bank account`(
        accountType: AccountType,
        accountNo: BigInteger,
        routingNo: Long,
        bankNo: Long?,
        accountDv: String,
        ispb: String?,
        code: String,
    ) {
        val bankDetailsTO = RecipientBankDetailsTO(
            accountType = accountType,
            bankNo = bankNo,
            routingNo = routingNo,
            accountNo = accountNo,
            accountDv = accountDv,
            ispb = ispb,
        )

        val request = createUpdateBankAccount(bankDetailsTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(Void::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        val responseTO = thrown.response.getBody(ResponseTO::class.java).get()
        responseTO.code shouldBe code
    }

    @ParameterizedTest
    @CsvSource(
        ",********",
        "999,",
        "999,********",
        "1,********",
        "99,********",
    )
    fun `should return BAD REQUEST when updating bank account with financial institution not found or inconsistent`(
        bankNo: Long?,
        ispb: String?,
    ) {
        val bankDetailsTO = RecipientBankDetailsTO(
            accountType = AccountType.CHECKING,
            bankNo = bankNo,
            routingNo = 1,
            accountNo = BigInteger("1"),
            accountDv = "1",
            ispb = ispb,
        )

        val request = createUpdateBankAccount(bankDetailsTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe ContactErrors.INVALID_BANK_NO_AND_OR_ISPB.code
    }

    @Test
    fun `should return NOT FOUND when updating valid bank account but non existing contact`() {
        val request = createUpdateBankAccount(validBankDetailsTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe ContactErrors.RESOURCE_NOT_FOUND.code
    }

    @Test
    fun `should return NOT FOUND when updating non existing bank account`() {
        contactDbRepository.save(savedRecipient)
        val request = createUpdateBankAccount(bankDetailsTO = validBankDetailsTO, bankAccountId = "NONEXISTING_ID")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe ContactErrors.RESOURCE_NOT_FOUND.code
    }

    @Test
    fun `should return NO CONTENT when updating valid bank account`() {
        contactDbRepository.save(savedRecipient)
        val expectedSavedBankAccount = SavedBankAccount(
            accountType = validBankDetailsTO.accountType,
            bankNo = validBankDetailsTO.bankNo,
            routingNo = validBankDetailsTO.routingNo,
            accountNo = validBankDetailsTO.accountNo,
            accountDv = validBankDetailsTO.accountDv,
            ispb = validBankDetailsTO.ispb,
        )
        val request = createUpdateBankAccount(validBankDetailsTO)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size

        val actualSavedBankAccount = databaseRecipient.bankAccounts.find { it == expectedSavedBankAccount }
        actualSavedBankAccount!!.id shouldBe savedRecipient.bankAccounts[0].id
    }

    @Test
    fun `should update all recurrences and its bills when recipient alias is changed`() {
        listOf(
            invoiceAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.WalletRecurrence(
                    recurrenceId = weeklyRecurrenceNoEndDate.id,
                    accountId = accountId,
                ),
                recurrenceRule = weeklyRecurrenceNoEndDate.rule,
            ),
            pixAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.WalletRecurrence(
                    recurrenceId = weeklyRecurrenceNoEndDate.id,
                    accountId = accountId,
                ),
                recurrenceRule = weeklyRecurrenceNoEndDate.rule,
            ),
            pixIgnored.copy(walletId = wallet.id),
        ).forEach {
            billEventDBRepository.save(it)
        }

        val localRecurrence = weeklyRecurrenceNoEndDate.copy(
            walletId = wallet.id,
            bills = listOf(invoiceAdded.billId, pixIgnored.billId),
            contactId = savedRecipient.id,
        )
        recurrenceRepository.save(localRecurrence)

        val localSavedRecipient = savedRecipient.copy()
        contactDbRepository.save(localSavedRecipient)

        val expectedRecipient = localSavedRecipient.copy(alias = "changed alias")

        val request = createUpdateRecipient(UpdateContactTO(expectedRecipient.alias))

        val response = client.toBlocking().exchange(request, ContactTO::class.java)

        response.status shouldBe HttpStatus.OK
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.alias shouldBe expectedRecipient.alias

        val savedRecurrence =
            recurrenceRepository.find(walletId = wallet.id, recurrenceId = localRecurrence.id)
        savedRecurrence.recipientAlias shouldBe expectedRecipient.alias

        var result = billEventDBRepository.getBillById(invoiceAdded.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.alias shouldBe expectedRecipient.alias
        }

        result = billEventDBRepository.getBillById(pixIgnored.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.alias shouldBe expectedRecipient.alias
        }
    }

    @Test
    fun `should update all bills with contact when contact alias is changed`() {
        listOf(invoiceAdded, pixAdded, pixIgnored).forEach {
            billEventDBRepository.save(it)
        }
        val billRepository = DynamoDbBillRepository(dynamoDbDAO)
        billRepository.save(Bill.build(invoiceAdded))
        billRepository.save(Bill.build(pixAdded, pixIgnored))

        contactDbRepository.save(savedRecipient)

        val expectedAlias = "changed alias"

        val request = createUpdateRecipient(UpdateContactTO(expectedAlias))

        val response = client.toBlocking().exchange(request, ContactTO::class.java)

        response.status shouldBe HttpStatus.OK
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.alias shouldBe expectedAlias

        var result = billEventDBRepository.getBillById(invoiceAdded.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.alias shouldBe expectedAlias
        }

        result = billEventDBRepository.getBillById(pixIgnored.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.alias shouldBe expectedAlias
        }
    }

    @Test
    fun `should update all bills with contact when contact alias is changed by assistant`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        val founderInvoiceAdded = invoiceAdded.copy(walletId = wallet.id)

        billEventDBRepository.save(founderInvoiceAdded)
        val billRepository = DynamoDbBillRepository(dynamoDbDAO)
        billRepository.save(Bill.build(founderInvoiceAdded))

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.founderAccount.accountId))

        val expectedAlias = "changed alias"

        val request = HttpRequest.PUT("/recipient/$RECIPIENT_ID", UpdateContactTO(expectedAlias))
            .onWallet(wallet, walletFixture.assistantAccount)

        val response = client.toBlocking().exchange(request, ContactTO::class.java)

        response.status shouldBe HttpStatus.OK
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.alias shouldBe expectedAlias

        val result = billEventDBRepository.getBillById(founderInvoiceAdded.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.alias shouldBe expectedAlias
        }
    }

    @Test
    fun `should return not found when founder update alias on an assistant contact`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.assistantAccount.accountId))
        val expectedAlias = "changed alias"
        val request = HttpRequest.PUT("/recipient/$RECIPIENT_ID", UpdateContactTO(expectedAlias))
            .onWallet(wallet, walletFixture.founderAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, ContactTO::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.alias shouldBe savedRecipient.alias
    }

    @Test
    fun `should return NO CONTENT when assistant updates bank account`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.founderAccount.accountId))
        val expectedSavedBankAccount = SavedBankAccount(
            accountType = validBankDetailsTO.accountType,
            bankNo = validBankDetailsTO.bankNo,
            routingNo = validBankDetailsTO.routingNo,
            accountNo = validBankDetailsTO.accountNo,
            accountDv = validBankDetailsTO.accountDv,
            ispb = validBankDetailsTO.ispb,
        )
        val request = HttpRequest.PUT("/recipient/$RECIPIENT_ID/bankAccount/$BANK_ACCOUNT_ID", validBankDetailsTO)
            .onWallet(wallet, walletFixture.assistantAccount)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size

        val actualSavedBankAccount = databaseRecipient.bankAccounts.find { it == expectedSavedBankAccount }
        actualSavedBankAccount!!.id shouldBe savedRecipient.bankAccounts[0].id
    }

    @Test
    fun `should return not found when founder update bank account on an assistant contact`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet
        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.assistantAccount.accountId))
        val request = HttpRequest.PUT("/recipient/$RECIPIENT_ID/bankAccount/$BANK_ACCOUNT_ID", validBankDetailsTO)
            .onWallet(wallet, walletFixture.founderAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, ContactTO::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts shouldBe savedRecipient.bankAccounts
    }

    @Test
    fun `should return NO CONTENT when assistant adds valid bank account`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.founderAccount.accountId))
        val request = HttpRequest.POST("/recipient/$RECIPIENT_ID/bankAccount", validBankDetailsTO)
            .onWallet(wallet, walletFixture.assistantAccount)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)

        databaseRecipient.bankAccounts shouldContain SavedBankAccount(
            accountType = validBankDetailsTO.accountType,
            bankNo = validBankDetailsTO.bankNo,
            routingNo = validBankDetailsTO.routingNo,
            accountNo = validBankDetailsTO.accountNo,
            accountDv = validBankDetailsTO.accountDv,
            ispb = validBankDetailsTO.ispb,
        )

        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size + 1
    }

    @Test
    fun `should return not found when founder adds bank account to assistant contact`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.assistantAccount.accountId))
        val request = HttpRequest.POST("/recipient/$RECIPIENT_ID/bankAccount", validBankDetailsTO)
            .onWallet(wallet, walletFixture.founderAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts shouldBe savedRecipient.bankAccounts
    }

    @Test
    fun `should update all recurrence ACTIVE bills with this saved recipient and bank account when bank account of a contact is updated`() {
        val currentBankAccount = savedRecipient.bankAccounts.first().toBankAccount(savedRecipient.document)
        val otherBankAccount = savedRecipient.bankAccounts.last().toBankAccount(savedRecipient.document)

        val otherBill = BillAdded(
            billId = BillId(UUID.randomUUID().toString()),
            created = Instant.now().toEpochMilli(),
            walletId = wallet.id,
            description = "ACTIVE INVOICE",
            dueDate = getLocalDate().plusDays(3),
            amount = 15591L,
            billType = BillType.INVOICE,
            recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = otherBankAccount),
            document = "***********",
            paymentLimitTime = LocalTime.parse("17:00", timeFormat),
            actionSource = ActionSource.Webapp(role = Role.OWNER),
        )

        listOf(
            invoiceAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.WalletRecurrence(
                    recurrenceId = weeklyRecurrenceNoEndDate.id,
                    accountId = accountId,
                ),
                recurrenceRule = weeklyRecurrenceNoEndDate.rule,
                recipient = invoiceAdded.recipient!!.copy(bankAccount = currentBankAccount),
            ),
            pixAdded.copy(
                walletId = wallet.id,
                actionSource = ActionSource.WalletRecurrence(
                    recurrenceId = weeklyRecurrenceNoEndDate.id,
                    accountId = accountId,
                ),
                recurrenceRule = weeklyRecurrenceNoEndDate.rule,
                recipient = invoiceAdded.recipient!!.copy(bankAccount = currentBankAccount),
            ),
            pixIgnored.copy(walletId = wallet.id),
            otherBill,
        ).forEach {
            billEventDBRepository.save(it)
        }

        val localRecurrence = weeklyRecurrenceNoEndDate.copy(
            walletId = wallet.id,
            bills = listOf(invoiceAdded.billId, pixIgnored.billId),
            recipientBankAccount = currentBankAccount,
            contactId = savedRecipient.id,
        )
        recurrenceRepository.save(localRecurrence)

        val localRecurrenceOther = weeklyRecurrenceNoEndDate.copy(
            walletId = wallet.id,
            id = RecurrenceId(UUID.randomUUID().toString()),
            bills = listOf(otherBill.billId),
            recipientBankAccount = otherBankAccount,
            contactId = savedRecipient.id,
        )
        recurrenceRepository.save(localRecurrenceOther)

        val localSavedRecipient = savedRecipient
        contactDbRepository.save(localSavedRecipient)

        val expectedSavedBankAccount = SavedBankAccount(
            accountType = validBankDetailsTO.accountType,
            bankNo = validBankDetailsTO.bankNo,
            routingNo = validBankDetailsTO.routingNo,
            accountNo = validBankDetailsTO.accountNo,
            accountDv = validBankDetailsTO.accountDv,
            ispb = validBankDetailsTO.ispb,
        )

        val request = createUpdateBankAccount(validBankDetailsTO)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size

        val actualSavedBankAccount = databaseRecipient.bankAccounts.find { it == expectedSavedBankAccount }
        actualSavedBankAccount!!.id shouldBe savedRecipient.bankAccounts[0].id

        val savedRecurrence =
            recurrenceRepository.find(walletId = wallet.id, recurrenceId = localRecurrence.id)
        savedRecurrence.recipientBankAccount!! shouldHaveBankAccount expectedSavedBankAccount

        var result = billEventDBRepository.getBillById(invoiceAdded.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.bankAccount!! shouldHaveBankAccount expectedSavedBankAccount
        }

        result = billEventDBRepository.getBillById(pixAdded.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.bankAccount!! shouldHaveBankAccount currentBankAccount
        }

        val savedRecurrenceOther =
            recurrenceRepository.find(walletId = wallet.id, recurrenceId = localRecurrenceOther.id)
        savedRecurrenceOther.recipientBankAccount!! shouldHaveBankAccount otherBankAccount

        result = billEventDBRepository.getBillById(otherBill.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.bankAccount!! shouldHaveBankAccount otherBankAccount
        }
    }

    @Test
    fun `when bank account of a saved recipient is updated then should update all ACTIVE bills with this contact and bank account`() {
        val currentBankAccount = savedRecipient.bankAccounts.first().toBankAccount(savedRecipient.document)
        val otherBankAccount = savedRecipient.bankAccounts.last().toBankAccount(savedRecipient.document)

        val otherBill = BillAdded(
            billId = BillId(UUID.randomUUID().toString()),
            created = Instant.now().toEpochMilli(),
            walletId = wallet.id,
            description = "ACTIVE INVOICE",
            dueDate = getLocalDate().plusDays(3),
            amount = 15591L,
            billType = BillType.INVOICE,
            recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = otherBankAccount),
            document = "***********",
            paymentLimitTime = LocalTime.parse("17:00", timeFormat),
            actionSource = ActionSource.Webapp(role = Role.OWNER),
            contactId = savedRecipient.id,
        )

        contactDbRepository.save(savedRecipient)

        val contactInvoiceAdded = invoiceAdded.copy(
            contactId = savedRecipient.id,
            recipient = invoiceAdded.recipient!!.copy(bankAccount = currentBankAccount),
        )
        val contactPixAdded = pixAdded.copy(
            contactId = savedRecipient.id,
            recipient = pixAdded.recipient!!.copy(bankAccount = currentBankAccount),
        )
        val contactPixKeyAdded = pixKeyAdded.copy(contactId = savedRecipient.id)

        val events = listOf(contactInvoiceAdded, contactPixAdded, pixIgnored, otherBill, contactPixKeyAdded)
        events.forEach {
            billEventDBRepository.save(it)
        }

        val billRepository = DynamoDbBillRepository(dynamoDbDAO)
        billRepository.save(Bill.build(contactInvoiceAdded))
        billRepository.save(Bill.build(contactPixAdded, pixIgnored))
        billRepository.save(Bill.build(otherBill))
        billRepository.save(Bill.build(contactPixKeyAdded))

        val expectedSavedBankAccount = SavedBankAccount(
            accountType = validBankDetailsTO.accountType,
            bankNo = validBankDetailsTO.bankNo,
            routingNo = validBankDetailsTO.routingNo,
            accountNo = validBankDetailsTO.accountNo,
            accountDv = validBankDetailsTO.accountDv,
            ispb = validBankDetailsTO.ispb,
        )
        val request = createUpdateBankAccount(validBankDetailsTO)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT
        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size

        val actualSavedBankAccount = databaseRecipient.bankAccounts.find { it == expectedSavedBankAccount }
        actualSavedBankAccount!!.id shouldBe savedRecipient.bankAccounts[0].id

        var result = billEventDBRepository.getBillById(contactInvoiceAdded.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.bankAccount!! shouldHaveBankAccount expectedSavedBankAccount
        }

        result = billEventDBRepository.getBillById(contactPixAdded.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.bankAccount!! shouldHaveBankAccount currentBankAccount
        }

        result = billEventDBRepository.getBillById(otherBill.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient?.bankAccount!! shouldHaveBankAccount otherBankAccount
        }

        result = billEventDBRepository.getBillById(contactPixKeyAdded.billId)
        result.isRight() shouldBe true
        result.map {
            it.recipient!!.bankAccount shouldBe null
            it.recipient?.pixKeyDetails shouldBe contactPixKeyAdded.recipient!!.pixKeyDetails
        }
    }

    private infix fun BankAccount.shouldHaveBankAccount(savedBankAccount: SavedBankAccount) {
        accountType shouldBe savedBankAccount.accountType
        bankNo shouldBe savedBankAccount.bankNo
        routingNo shouldBe savedBankAccount.routingNo
        accountNo shouldBe savedBankAccount.accountNo
        accountDv shouldBe savedBankAccount.accountDv
        ispb shouldBe savedBankAccount.ispb
    }

    private infix fun BankAccount.shouldHaveBankAccount(bankAccount: BankAccount) {
        accountType shouldBe bankAccount.accountType
        bankNo shouldBe bankAccount.bankNo
        routingNo shouldBe bankAccount.routingNo
        accountNo shouldBe bankAccount.accountNo
        accountDv shouldBe bankAccount.accountDv
        ispb shouldBe bankAccount.ispb
    }

    @Test
    fun `should return NO CONTENT and revalidate account when an invalidated account is edited`() {
        contactDbRepository.save(createSavedRecipient(accountId = accountId, invalidBankAccount = true))

        val request = createUpdateBankAccount(validBankDetailsTO)

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size

        val actualSavedBankAccount = databaseRecipient.bankAccounts.find { it.id.value == BANK_ACCOUNT_ID }
        actualSavedBankAccount?.invalidated shouldBe false
    }

    @Test
    fun `should return NO CONTENT when updating bank account with no change`() {
        contactDbRepository.save(savedRecipient)

        val request = createUpdateBankAccount(savedRecipient.bankAccounts[0].convertToBankDetailsTO())

        val response = client.toBlocking().exchange(request, Void::class.java)

        response.status shouldBe HttpStatus.NO_CONTENT

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size

        val actualSavedBankAccount = databaseRecipient.bankAccounts.find { it == savedRecipient.bankAccounts[0] }
        actualSavedBankAccount!!.id shouldBe savedRecipient.bankAccounts[0].id
    }

    @Test
    fun `should return BAD REQUEST when updating bank account with other bank account data`() {
        contactDbRepository.save(savedRecipient)

        val request = createUpdateBankAccount(
            savedRecipient.bankAccounts[0].convertToBankDetailsTO(),
            savedRecipient.bankAccounts[1].id.value,
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe ContactErrors.BANK_ACCOUNT_ALREADY_EXISTS.code

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)

        databaseRecipient.bankAccounts.size shouldBe savedRecipient.bankAccounts.size
        databaseRecipient.bankAccounts shouldContainExactlyInAnyOrder savedRecipient.bankAccounts
    }

    @Test
    fun `should return BAD REQUEST when alias length is bigger than 60`() {
        val request =
            createUpdateRecipient(UpdateContactTO("Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed."))

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java)
            .get().code shouldBe ContactErrors.CONSTRAINT_VALIDATION_ERROR.code
    }

    @Test
    fun `should return NOT FOUND when contact is not found`() {
        val request = createUpdateRecipient(validUpdateContactTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe ContactErrors.RESOURCE_NOT_FOUND.code
    }

    @Test
    fun `should return NOT FOUND when contact is not from the authenticated user`() {
        contactDbRepository.save(savedRecipient.copy(accountId = AccountId(ACCOUNT_ID_2)))

        val request = createUpdateRecipient(validUpdateContactTO)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Void::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe ContactErrors.RESOURCE_NOT_FOUND.code
    }

    @Test
    fun `should return OK when contact is edited`() {
        contactDbRepository.save(savedRecipient)

        val request = createUpdateRecipient(validUpdateContactTO)

        val response = client.toBlocking().exchange(request, ContactTO::class.java)

        response.status shouldBe HttpStatus.OK
        response.getBody(ContactTO::class.java).get().alias shouldBe validUpdateContactTO.alias

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts shouldContainExactlyInAnyOrder savedRecipient.bankAccounts
        databaseRecipient.pixKeys shouldContainExactlyInAnyOrder savedRecipient.pixKeys
        databaseRecipient.alias shouldBe validUpdateContactTO.alias
    }

    @Test
    fun `should return OK when alias is null and contact is edited`() {
        contactDbRepository.save(savedRecipient)

        val request = createUpdateRecipient(UpdateContactTO(alias = null))

        val response = client.toBlocking().exchange(request, ContactTO::class.java)

        response.status shouldBe HttpStatus.OK
        response.getBody(ContactTO::class.java).get().alias shouldBe ""

        val databaseRecipient = contactDbRepository.findById(savedRecipient.id)
        databaseRecipient.bankAccounts shouldContainExactlyInAnyOrder savedRecipient.bankAccounts
        databaseRecipient.pixKeys shouldContainExactlyInAnyOrder savedRecipient.pixKeys
        databaseRecipient.alias shouldBe null
    }

    @Test
    fun `should return accepted on delete contact by assistant`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.founderAccount.accountId))

        val request = HttpRequest.DELETE("/recipient/$RECIPIENT_ID", "")
            .onWallet(wallet, walletFixture.assistantAccount)

        val response = client.toBlocking().exchange(request, ContactTO::class.java)

        response.status shouldBe HttpStatus.ACCEPTED
        assertThrows<ItemNotFoundException> {
            contactDbRepository.findById(savedRecipient.id)
        }
    }

    @Test
    fun `should return not found on delete contact by founder on assistant contact`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.assistantAccount.accountId))

        val request = HttpRequest.DELETE("/recipient/$RECIPIENT_ID", "")
            .onWallet(wallet, walletFixture.founderAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, ContactTO::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        contactDbRepository.findById(savedRecipient.id) shouldNotBe null
    }

    @Test
    fun `should return not found on delete pix key by founder on assistant contact`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.assistantAccount.accountId))

        val request = HttpRequest.DELETE("/recipient/$RECIPIENT_ID/pixKey/$RECIPIENT_PIX_EVP_KEY", "")
            .onWallet(wallet, walletFixture.founderAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, ContactTO::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        contactDbRepository.findById(savedRecipient.id).pixKeys shouldBe savedRecipient.pixKeys
    }

    @Test
    fun `should return accepted on delete pix key by assistant`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.founderAccount.accountId))

        val request = HttpRequest.DELETE("/recipient/$RECIPIENT_ID/pixKey/$RECIPIENT_PIX_EVP_KEY", "")
            .onWallet(wallet, walletFixture.assistantAccount)

        val response = client.toBlocking().exchange(request, ContactTO::class.java)

        response.status shouldBe HttpStatus.ACCEPTED
        contactDbRepository.findById(savedRecipient.id).pixKeys shouldBe savedRecipient.pixKeys - SavedPixKey(
            value = RECIPIENT_PIX_EVP_KEY,
            type = PixKeyType.EVP,
        )
    }

    @Test
    fun `should return accepted on delete bank account by assistant`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.founderAccount.accountId))

        val request = HttpRequest.DELETE("/recipient/$RECIPIENT_ID/bankAccount/$BANK_ACCOUNT_ID", "")
            .onWallet(wallet, walletFixture.assistantAccount)

        val response = client.toBlocking().exchange(request, ContactTO::class.java)

        response.status shouldBe HttpStatus.ACCEPTED
        contactDbRepository.findById(savedRecipient.id).bankAccounts.any { it.id == BankAccountId(BANK_ACCOUNT_ID) } shouldBe false
    }

    @Test
    fun `should return not found on delete bank account by founder on assistant contact`() {
        every {
            walletRepository.findWalletOrNull(wallet.id)
        } returns wallet

        contactDbRepository.save(savedRecipient.copy(accountId = walletFixture.assistantAccount.accountId))

        val request = HttpRequest.DELETE("/recipient/$RECIPIENT_ID/bankAccount/$BANK_ACCOUNT_ID", "")
            .onWallet(wallet, walletFixture.founderAccount)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, ContactTO::class.java)
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
        contactDbRepository.findById(savedRecipient.id).bankAccounts shouldBe savedRecipient.bankAccounts
    }

    /*
        @Test
        @Disabled
        fun `should update bills on update contact alias`() {
            contactDbRepository.save(savedRecipient)
            (1..50L).forEach {
                createBillService.createInvoice(
                    CreateInvoiceRequest(
                        description = "teste",
                        walletId = wallet.id,
                        dueDate = getLocalDate().plusDays(it),
                        amount = it,
                        recipient = RecipientRequest(
                            id = ContactId(RECIPIENT_ID),
                            accountId = accountId,
                            name = savedRecipient.name,
                            document = savedRecipient.document,
                            alias = savedRecipient.alias!!,
                            bankAccount = BankAccount(
                                savedRecipient.bankAccounts[0].accountType,
                                savedRecipient.bankAccounts[0].bankNo,
                                savedRecipient.bankAccounts[0].routingNo,
                                savedRecipient.bankAccounts[0].accountNo,
                                savedRecipient.bankAccounts[0].accountDv,
                                savedRecipient.document,
                                "********",
                            ),
                            qrCode = null,
                        ),
                        source = ActionSource.Api(accountId = accountId),
                        recurrenceRule = null,
                        contactId = ContactId(RECIPIENT_ID),
                    ),
                )
            }

            val expectedSavedBankAccount = SavedBankAccount(
                accountType = validBankDetailsTO.accountType,
                bankNo = validBankDetailsTO.bankNo,
                routingNo = validBankDetailsTO.routingNo,
                accountNo = validBankDetailsTO.accountNo,
                accountDv = validBankDetailsTO.accountDv,
                ispb = validBankDetailsTO.ispb,
            )
            val request = createUpdateBankAccount(validBankDetailsTO)

            val response = client.toBlocking().exchange(request, Void::class.java)

            response.status shouldBe HttpStatus.NO_CONTENT

            val bills = DynamoDbBillRepository(dynamoDbDAO).findByContactId(ContactId(RECIPIENT_ID))
            bills.size shouldBe 50
            bills.forEach { bill ->
                bill.recipient?.bankAccount!! shouldHaveBankAccount expectedSavedBankAccount
            }
        }
    */

    private fun SavedBankAccount.convertToBankDetailsTO(): RecipientBankDetailsTO {
        return RecipientBankDetailsTO(
            accountType = accountType,
            bankNo = bankNo,
            routingNo = routingNo,
            accountNo = accountNo,
            accountDv = accountDv,
            ispb = ispb,
        )
    }
}