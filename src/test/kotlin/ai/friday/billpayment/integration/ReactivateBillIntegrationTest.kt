package ai.friday.billpayment.integration

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.adapters.api.BillTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.celcoin.CelcoinBillValidationResponse
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.FeaturesDbRepository
import ai.friday.billpayment.adapters.dynamodb.FeaturesDynamoDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillDenied
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillIgnored
import ai.friday.billpayment.app.bill.BillReactivated
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.BoletoSettlementException
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedValidationResponse
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoiceIgnored
import ai.friday.billpayment.invoicePaid
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.assertions.assertSoftly
import io.kotest.assertions.fail
import io.kotest.inspectors.forAll
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNot
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class ReactivateBillIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.assistant,
                walletFixture.participant,
            ),
        )

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository
    private val walletRepository: WalletRepository = mockk() {
        every {
            findWalletOrNull(wallet.id)
        } returns wallet

        every {
            findWallet(wallet.id)
        } returns wallet
    }

    private val dynamoDbEnhancedClient = setupDynamoDB()

    private val featuresDbRepository = FeaturesDbRepository(client = FeaturesDynamoDAO(cli = dynamoDbEnhancedClient))

    private val billEventRepository = BillEventDBRepository(
        DynamoDbDAO(dynamoDB),
        allFalseFeatureConfiguration,
    )

    private val invoice = invoiceAdded.copy(
        walletId = wallet.id,
    )

    private val billAddedEvent = billAdded.copy(
        walletId = wallet.id,
    )

    private val billIgnoredEvent = billIgnored.copy(
        walletId = wallet.id,
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val boletoSettlementMock: BoletoSettlementService = mockk()

    @MockBean(BoletoSettlementService::class)
    fun boletoSettlement() = boletoSettlementMock

    @MockBean(FeaturesDbRepository::class)
    fun featuresDbRepository() = featuresDbRepository

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
    }

    private fun buildReactivateRequest(billId: BillId) =
        HttpRequest.PUT("/bill/id/${billId.value}/reactivate", "{}").onWallet(wallet)

    @Test
    fun `should return 404 when bill is not found`() {
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(buildReactivateRequest(invoice.billId), BillTO::class.java)
        }
        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return bad request when invoice is paid`() {
        addBillEventIntoDb(dynamoDB, invoice)
        addBillEventIntoDb(dynamoDB, invoicePaid)

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(buildReactivateRequest(invoice.billId), BillTO::class.java)
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should return ok when invoice is reactivated`() {
        addBillEventIntoDb(dynamoDB, invoice)
        addBillEventIntoDb(dynamoDB, invoiceIgnored)

        val result = client.toBlocking().exchange(buildReactivateRequest(invoice.billId), BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()
        val bill = billEventRepository.getBillById(invoice.billId)
            .getOrElse { throw NoStackTraceException("teste") }
        assertSoftly {
            result.status shouldBe HttpStatus.OK
            billTO.id shouldBe invoice.billId.value
            billTO.status shouldBe BillStatus.ACTIVE.name
            bill.status shouldBe BillStatus.ACTIVE
            bill.history.last().shouldBeTypeOf<BillReactivated>()
            bill.ignoredAt.shouldBeNull()
        }
    }

    @Test
    fun `deve reativar uma conta ignorada como pendente`() {
        addBillEventIntoDb(dynamoDB, invoice.copy(securityValidationResult = listOf("test"), created = getZonedDateTime().minusSeconds(10).toInstant().toEpochMilli()))
        addBillEventIntoDb(
            dynamoDB,
            BillDenied(
                billId = invoice.billId,
                created = getZonedDateTime().minusSeconds(9).toInstant().toEpochMilli(),
                walletId = invoice.walletId,
                actionSource = ActionSource.Api(ACCOUNT.accountId),
            ),
        )

        val result = client.toBlocking().exchange(buildReactivateRequest(invoice.billId), BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()
        val bill = billEventRepository.getBillById(invoice.billId).getOrElse { fail("deveria ser right") }

        assertSoftly {
            result.status shouldBe HttpStatus.OK
            billTO.id shouldBe invoice.billId.value
            billTO.status shouldBe "PENDING"
            bill.status shouldBe BillStatus.WAITING_APPROVAL
            bill.history.last().shouldBeTypeOf<BillReactivated>()
            bill.ignoredAt.shouldBeNull()
        }
    }

    @Test
    fun `should return ok when invoice is already active`() {
        addBillEventIntoDb(dynamoDB, invoice)

        val result = client.toBlocking().exchange(buildReactivateRequest(invoice.billId), BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()
        val bill = billEventRepository.getBillById(invoice.billId)
            .getOrElse { throw NoStackTraceException("teste") }
        assertSoftly {
            result.status shouldBe HttpStatus.OK
            billTO.id shouldBe invoice.billId.value
            billTO.status shouldBe BillStatus.ACTIVE.name
            bill.status shouldBe BillStatus.ACTIVE
            bill.history.forAll { it shouldNot io.kotest.matchers.types.beOfType(BillReactivated::class) }
            bill.ignoredAt.shouldBeNull()
        }
    }

    @Test
    fun `should return ok when boleto validate fails`() {
        every { boletoSettlementMock.validateBill(ofType(Bill::class)) } throws BoletoSettlementException(
            FinancialServiceGateway.CELCOIN,
            null,
            Exception(),
        )
        addBillEventIntoDb(dynamoDB, billAddedEvent)
        addBillEventIntoDb(dynamoDB, billIgnoredEvent)

        val result = client.toBlocking().exchange(buildReactivateRequest(billAddedEvent.billId), BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()
        val bill = billEventRepository.getBillById(billAddedEvent.billId)
            .getOrElse { throw NoStackTraceException("teste") }
        assertSoftly {
            result.status shouldBe HttpStatus.OK
            billTO.id shouldBe billAddedEvent.billId.value
            billTO.status shouldBe BillStatus.ACTIVE.name
            bill.status shouldBe BillStatus.ACTIVE
            bill.history.last().shouldBeTypeOf<BillReactivated>()
            bill.ignoredAt.shouldBeNull()
        }
    }

    @Test
    fun `should return ok when boleto is reactivated`() {
        every { boletoSettlementMock.validateBill(ofType(Bill::class)) } returns billAddedValidationResponse
        addBillEventIntoDb(dynamoDB, billAddedEvent)
        addBillEventIntoDb(dynamoDB, billIgnoredEvent)

        val result = client.toBlocking().exchange(buildReactivateRequest(billAddedEvent.billId), BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()
        val bill = billEventRepository.getBillById(billAddedEvent.billId)
            .getOrElse { throw NoStackTraceException("teste") }
        assertSoftly {
            result.status shouldBe HttpStatus.OK
            billTO.id shouldBe billAddedEvent.billId.value
            billTO.status shouldBe BillStatus.ACTIVE.name
            bill.status shouldBe BillStatus.ACTIVE
            bill.history.last().shouldBeTypeOf<BillReactivated>()
            bill.ignoredAt.shouldBeNull()
        }
    }

    @Test
    fun `should return ok when outdated boleto is reactivated with already paid status`() {
        every { boletoSettlementMock.validateBill(ofType(Bill::class)) } returns CelcoinBillValidationResponse(
            "634",
            "CONTA JA PAGA",
        )
        addBillEventIntoDb(dynamoDB, billAddedEvent)
        addBillEventIntoDb(dynamoDB, billIgnoredEvent)
        loadAccountIntoDb(dynamoDB, accountId = wallet.founder.accountId)

        val result = client.toBlocking().exchange(buildReactivateRequest(billAddedEvent.billId), BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()
        val bill = billEventRepository.getBillById(billAddedEvent.billId)
            .getOrElse { throw NoStackTraceException("teste") }
        assertSoftly {
            result.status shouldBe HttpStatus.OK
            billTO.id shouldBe billAddedEvent.billId.value
            billTO.status shouldBe BillStatus.ALREADY_PAID.name
            bill.status shouldBe BillStatus.ALREADY_PAID
            bill.history.last().shouldBeTypeOf<RegisterUpdated>()
        }
    }

    @Test
    fun `should return ok when outdated boleto is reactivated with active status`() {
        val updatedAmountValidationResponse = billAddedValidationResponse.copy(
            billRegisterData = billAddedValidationResponse.billRegisterData!!.copy(amountTotal = 15600L),
        )
        every { boletoSettlementMock.validateBill(ofType(Bill::class)) } returns updatedAmountValidationResponse
        addBillEventIntoDb(dynamoDB, billAddedEvent)
        addBillEventIntoDb(dynamoDB, billIgnoredEvent)

        val result = client.toBlocking().exchange(buildReactivateRequest(billAddedEvent.billId), BillTO::class.java)

        val billTO = result.getBody(BillTO::class.java).get()
        val bill = billEventRepository.getBillById(billAddedEvent.billId)
            .getOrElse { throw NoStackTraceException("teste") }
        assertSoftly {
            result.status shouldBe HttpStatus.OK
            billTO.id shouldBe billAddedEvent.billId.value
            billTO.status shouldBe BillStatus.ACTIVE.name
            billTO.amountTotal shouldBe updatedAmountValidationResponse.billRegisterData!!.amountTotal
            bill.status shouldBe BillStatus.ACTIVE
            bill.history.last().shouldBeTypeOf<RegisterUpdated>()
            bill.amountTotal shouldBe updatedAmountValidationResponse.billRegisterData!!.amountTotal
        }
    }

    @Test
    fun `should return conflict when another bill with same barcode and newer dueDate exists`() {
        val newerBillAddedEvent =
            billAddedEvent.copy(billId = BillId(BILL_ID_8), dueDate = billAddedEvent.dueDate.plusDays(1))
        billEventRepository.save(billAddedEvent)
        billEventRepository.save(billIgnoredEvent)
        billEventRepository.save(newerBillAddedEvent)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(buildReactivateRequest(billAddedEvent.billId), BillTO::class.java)
        }

        thrown.status shouldBe HttpStatus.CONFLICT
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4050"

        var result = billEventRepository.getBillById(billAddedEvent.billId)
        result.isRight() shouldBe true
        result.map {
            it.history.first() shouldBe billAddedEvent
            it.history.last().shouldBeTypeOf<BillIgnored>()
            it.ignoredAt.shouldNotBeNull()
        }

        result = billEventRepository.getBillById(newerBillAddedEvent.billId)
        result.isRight() shouldBe true
        result.map {
            it.history shouldContainExactly listOf(newerBillAddedEvent)
        }
    }
}