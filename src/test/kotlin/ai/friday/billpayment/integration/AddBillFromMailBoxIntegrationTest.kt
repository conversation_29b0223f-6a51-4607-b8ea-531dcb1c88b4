package ai.friday.billpayment.integration

import ai.friday.billpayment.adapters.arbi.ArbiValidationResponse
import ai.friday.billpayment.adapters.arbi.CIPSitTitPgto
import ai.friday.billpayment.adapters.bill.DefaultBillEventPublisher
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillPermissionUpdated
import ai.friday.billpayment.app.bill.ConcessionariaService
import ai.friday.billpayment.app.bill.CreateBillService
import ai.friday.billpayment.app.bill.CreateFichaDeCompensacaoRequest
import ai.friday.billpayment.app.bill.DefaultMailBoxService
import ai.friday.billpayment.app.bill.FichaCompensacaoService
import ai.friday.billpayment.app.bill.PermissionUpdated
import ai.friday.billpayment.app.bill.ResolveScheduledBill
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.BoletoSettlementService
import ai.friday.billpayment.app.integrations.MailBoxService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billAddedFicha
import ai.friday.billpayment.fichaRegisterData
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

class AddBillFromMailBoxIntegrationTest {
    private val walletFixture = WalletFixture()
    val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.ultraLimitedParticipant))

    private val dynamoDB: AmazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val billRepository = DynamoDbBillRepository(dynamoDbDAO)
    private val billEventRepository =
        BillEventDBRepository(
            dynamoDbDAO,
            allFalseFeatureConfiguration,
        )

    private val accountService: AccountService = mockk(relaxed = true)
    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    private val validationService =
        mockk<BillValidationService> {
            every {
                validate(any<BarCode>())
            } returns ArbiValidationResponse(
                billRegisterData = fichaRegisterData,
                paymentStatus = CIPSitTitPgto.SUCESSO.code,
            )
        }

    private val walletService: WalletService = mockk()
    private val resolveScheduledBill: ResolveScheduledBill = mockk()

    private val billEventPublisher =
        DefaultBillEventPublisher(
            eventRepository = billEventRepository,
            billRepository = billRepository,
            eventPublisher = mockk(relaxUnitFun = true),
        )

    private val boletoSettlementService =
        mockk<BoletoSettlementService> {
            every {
                validateBill(any<CreateFichaDeCompensacaoRequest>())
            } returns ArbiValidationResponse(
                billRegisterData = fichaRegisterData.copy(payerDocument = DOCUMENT_2),
                paymentStatus = 12,
                resultado = "SUCESSO",
            )
        }

    private val updateBillService =
        UpdateBillService(
            billEventRepository = billEventRepository,
            billRepository = billRepository,
            billTrackingRepository = mockk(relaxed = true),
            eventPublisher = mockk(relaxUnitFun = true),
            boletoSettlementService = boletoSettlementService,
            billValidationService = validationService,
            lockProvider = mockk(relaxed = true),
            walletLimitsService = mockk(),
            walletService = mockk(),
            approveBillService = mockk(),
            denyBillService = mockk(),
            resolveScheduledBill = mockk(),
            cancelSchedulePaymentService = mockk {
                every { cancelScheduledPayment(any(), any(), any()) } returns true
                every { userCancelScheduledPayment(any(), any(), any(), any()) } returns true
            },
            mailboxWalletDataRepository = mockk(),
        )

    private val concessionariaService = ConcessionariaService(
        boletoSettlementService = boletoSettlementService,
        billEventRepository = billEventRepository,
        billRepository = billRepository,
        updateBillService = updateBillService,
        resolveScheduledBill = resolveScheduledBill,
    )

    private val createBillService =
        CreateBillService(
            billRepository = mockk(),
            contactService = mockk(),
            accountService = accountService,
            pixKeyManagement = mockk(),
            updateBillService = updateBillService,
            tedConfiguration = mockk(),
            walletLimitsService = mockk(),
            possibleDuplicateBillService = mockk(),
            pixQRCodeParserService = mockk(),
            categoryService = mockk(relaxed = true),
            concessionariaService = concessionariaService,
        )

    private val fichaCompensacaoService =
        FichaCompensacaoService(
            validationService = validationService,
            billEventRepository = billEventRepository,
            updateBillService = updateBillService,
            featureConfiguration = allFalseFeatureConfiguration,
            possibleDuplicateBillService = mockk(),
            walletService = mockk(),
            scheduleBillService = mockk(),
            billEventPublisher = billEventPublisher,
            blockEmptyAmountCreationService = mockk(),
            enrichmentRules = mockk(),
        )

    private val mailBoxService: MailBoxService =
        DefaultMailBoxService(
            createBillService = createBillService,
            notificationAdapter = notificationAdapter,
            walletService = walletService,
            billInstrumentationService = mockk(relaxUnitFun = true),
            mailboxSecurityService =
            mockk {
                every { execute(any()) } returns emptyList()
            },
            fichaCompensacaoService = fichaCompensacaoService,
            accountService = mockk {
                every {
                    findAccountById(any())
                } returns walletFixture.founderAccount
            },
        )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        loadAccountIntoDb(dynamoDB)
        every { walletService.findWallet(wallet.id) } returns wallet
    }

    @Test
    fun `should add visibility to member when send a not visible already exists bill from mail box and bill is a ficha compensacao`() {
        val billAdded =
            billAddedFicha.copy(
                walletId = wallet.id,
                dueDate = fichaRegisterData.dueDate!!,
                created = getZonedDateTime().minusMinutes(1).toInstant().toEpochMilli(),
            )
        billEventRepository.save(billAdded)
        val bill = Bill.build(billAdded)
        billRepository.save(bill)

        bill.visibleBy shouldHaveSize 0

        mailBoxService.addBill(
            walletId = wallet.id,
            barCode = BarCode.of(FICHA_DE_COMPENSACAO_BARCODE),
            dueDate = null,
            from = walletFixture.ultraLimitedParticipant.emailAddress,
            subject = "mocked-subject",
            shouldNotifyOnRetryableError = false,
        )

        val billView =
            billRepository.findByWallet(wallet.id)
                .first { it.billId == bill.billId }

        billView.visibleBy shouldHaveSize 1
        billView.visibleBy.first() shouldBe walletFixture.ultraLimitedParticipant.accountId

        val result = billEventRepository.getBillById(bill.billId)
        result.isRight() shouldBe true
        result.map { billResult ->
            with(billResult) {
                history shouldHaveSize 2
                history.filterIsInstance<BillPermissionUpdated>() shouldHaveSize 1
                val event = history.single { it is BillPermissionUpdated } as BillPermissionUpdated
                event shouldBe io.kotest.matchers.types.beOfType(BillPermissionUpdated::class)
                event.permissionUpdated shouldBe io.kotest.matchers.types.beOfType(PermissionUpdated.VisibilityAdded::class)
            }
        }
    }

    @Test
    fun `should add visibility to member when send a not visible already exists bill from mail box and bill is a concessionaria`() {
        val billAdded =
            billAdded.copy(
                walletId = wallet.id,
                created = getZonedDateTime().minusMinutes(1).toInstant().toEpochMilli(),
            )
        billEventRepository.save(billAdded)
        val bill = Bill.build(billAdded)
        billRepository.save(bill)

        bill.visibleBy shouldHaveSize 0

        mailBoxService.addBill(
            walletId = wallet.id,
            barCode = BarCode.ofDigitable(PREFECTURE_BARCODE_LINE),
            dueDate = getLocalDate(),
            from = walletFixture.ultraLimitedParticipant.emailAddress,
            subject = "mocked-subject",
            shouldNotifyOnRetryableError = false,
        )

        val billView =
            billRepository.findByWallet(wallet.id)
                .first { it.billId == bill.billId }

        billView.visibleBy shouldHaveSize 1
        billView.visibleBy.first() shouldBe walletFixture.ultraLimitedParticipant.accountId

        val result = billEventRepository.getBillById(bill.billId)
        result.isRight() shouldBe true
        result.map { bill ->
            with(bill) {
                history shouldHaveSize 2
                history.filterIsInstance<BillPermissionUpdated>() shouldHaveSize 1
                history.last() shouldBe io.kotest.matchers.types.beOfType(BillPermissionUpdated::class)
                val lastEvent = history.last() as BillPermissionUpdated
                lastEvent.permissionUpdated shouldBe io.kotest.matchers.types.beOfType(PermissionUpdated.VisibilityAdded::class)
            }
        }
    }
}