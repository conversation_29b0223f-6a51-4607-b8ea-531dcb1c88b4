package ai.friday.billpayment.integration

import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.adapters.api.AddCreditCardErrorResponses.HIGH_RISK_CREDIT_CARD
import ai.friday.billpayment.adapters.api.AddCreditCardErrorResponses.INVALID_CREDIT_CARD
import ai.friday.billpayment.adapters.api.AddCreditCardErrorResponses.MAX_LIMIT_REACHED
import ai.friday.billpayment.adapters.api.AddCreditCardErrorResponses.SERVER_ERROR
import ai.friday.billpayment.adapters.api.AddCreditCardErrorResponses.UNAUTHORIZED_CREDIT_CARD
import ai.friday.billpayment.adapters.api.CreateCreditCardTO
import ai.friday.billpayment.adapters.api.CreditCardController
import ai.friday.billpayment.adapters.api.GetChallengeTO
import ai.friday.billpayment.adapters.api.PaymentMethodTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.ValidateChallengeTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.CreditCardChallengeDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.PaymentMethodEntity
import ai.friday.billpayment.adapters.micronaut.FeaturesConfigurationMicronaut
import ai.friday.billpayment.adapters.softwareexpress.SoftwareExpressAdapter
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AddCreditCardResult
import ai.friday.billpayment.app.account.AddCreditCardResult.HighRiskCreditCard
import ai.friday.billpayment.app.account.AddCreditCardResult.InvalidCreditCard
import ai.friday.billpayment.app.account.AddCreditCardResult.MaxLimitReached
import ai.friday.billpayment.app.account.AddCreditCardResult.ServerError
import ai.friday.billpayment.app.account.AddCreditCardResult.UnauthorizedCreditCard
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBinDetails
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardChallenge
import ai.friday.billpayment.app.account.CreditCardChallengeStatus
import ai.friday.billpayment.app.account.CreditCardToken
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.account.hmacLockTag
import ai.friday.billpayment.app.account.maskPan
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.CreditCardInformationService
import ai.friday.billpayment.app.integrations.CreditCardOwnership
import ai.friday.billpayment.app.integrations.CreditCardScore
import ai.friday.billpayment.app.integrations.CreditCardScoreService
import ai.friday.billpayment.app.integrations.CreditCardValidationService
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.integrations.HMacService
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.limit.LimitIncrementResult
import ai.friday.billpayment.app.limit.LimitService
import ai.friday.billpayment.authorizedCreditCardAuthorization
import ai.friday.billpayment.creditCard
import ai.friday.billpayment.externalCreditCard
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.right
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.HttpStatus.BAD_REQUEST
import io.micronaut.http.HttpStatus.CREATED
import io.micronaut.http.HttpStatus.INTERNAL_SERVER_ERROR
import io.micronaut.http.HttpStatus.NOT_FOUND
import io.micronaut.http.HttpStatus.NO_CONTENT
import io.micronaut.http.HttpStatus.OK
import io.micronaut.http.HttpStatus.TOO_MANY_REQUESTS
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import jakarta.inject.Named
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class CreditCardIntegrationTest(embeddedServer: EmbeddedServer) {

    private val securityFixture = SecurityFixture()

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val accountRegisterRepository = AccountRegisterDbRepository(dynamoDbDAO, mockk())
    private val creditCardChallengeDbRepository = CreditCardChallengeDbRepository(dynamoDbDAO)

    @MockBean(SoftwareExpressAdapter::class)
    fun acquirerMock(): AcquirerService = acquirerMock
    private val acquirerMock: AcquirerService = mockk {}

    @MockBean(CreditCardInformationService::class)
    fun creditCardInformationService(): CreditCardInformationService = creditCardInformationServiceMock
    private val creditCardInformationServiceMock = mockk<CreditCardInformationService> {
        every {
            retrieveBinDetails(any())
        } returns defaultBinDetails
    }

    @MockBean(LimitService::class)
    fun limitService(): LimitService = limitService

    private val limitService = mockk<LimitService>()

    @MockBean(InternalLock::class, named = hmacLockTag)
    @Named(hmacLockTag)
    fun lockService(): InternalLock = lockService
    private val lockService = mockk<InternalLock>(relaxed = true)

    @MockBean(HMacService::class)
    fun hmacService(): HMacService = hmacService
    private val hmacService = mockk<HMacService> {
        every { sign(any(), any()) } returns "signature"
    }

    @MockBean(FeaturesConfigurationMicronaut::class)
    fun getFeaturesConfigurationMicronaut(): FeatureConfiguration = featureConfiguration
    private val featureConfiguration = mockk<FeatureConfiguration>() {
        every { zeroAuthEnabled } returns true
        every { creditCardChallenge } returns false
        every { creditCardChallenge } returns false
        every { updateScheduleOnAmountLowered } returns false
    }

    @MockBean(CreditCardValidationService::class)
    fun creditCardValidationService(): CreditCardValidationService = creditCardValidationService
    private val creditCardValidationService = mockk<CreditCardValidationService>() {
        every {
            validateOwnership(any())
        } returns CreditCardOwnership.IS_OWNER.right()
    }

    @MockBean(CreditCardScoreService::class)
    fun creditCardScoreService(): CreditCardScoreService = creditCardScoreService
    private val creditCardScoreService = mockk<CreditCardScoreService>() {
        every {
            creditcardScore(any())
        } returns CreditCardScore.MATCH.right()
    }

    private val defaultBinDetails = CreditCardBinDetails(
        provider = "provider",
        cardType = "cardType",
        foreignCard = true,
        corporateCard = "corporateCard",
        issuer = "issuer",
        issuerCode = "",
        prepaid = "prepaid",
        status = "status",
    )

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        loadAccountIntoDb(dynamoDB, position = 1)
        accountRegisterRepository.save(accountRegisterCompleted)
    }

    @Test
    fun `should save credit card`() {
        loadBalancePaymentMethod(
            accountRepository = accountRepository,
            position = 2,
            paymentMethodStatus = AccountPaymentMethodStatus.INACTIVE,
        )
        loadCreditCard(dynamoDB, position = 3)
        val mockToken = CreditCardToken(UUID.randomUUID().toString())

        every {
            acquirerMock.tokenize(any(), any(), any(), any(), any())
        } returns mockToken.right()

        every {
            acquirerMock.validate(any(), "000")
        } returns true.right()

        every {
            creditCardInformationServiceMock.retrieveBinDetails(any())
        } returns defaultBinDetails

        val creditCardTO = CreateCreditCardTO(
            brand = "VISA",
            pan = "****************",
            expiryDate = "01/2050",
            cvv = "000",
        )
        val response = callAddCreditCard(creditCardTO)

        val accountId = AccountId(ACCOUNT_ID)
        response.status shouldBe CREATED

        val responsePayload = response.body()!!
        with(responsePayload) {
            type shouldBe PaymentMethodType.CREDIT_CARD.name
            bankDetails shouldBe null
            creditCard?.pan shouldBe "************${creditCardTO.pan.takeLast(4)}"
            creditCard!!.brand shouldBe "visa"
            creditCard?.expiryDate shouldBe creditCardTO.expiryDate
        }

        val paymentMethodList = accountRepository.findAccountPaymentMethodsByAccountId(accountId)
        paymentMethodList.shouldHaveSize(4)

        val creditCardList = paymentMethodList.filter { it.method.type == PaymentMethodType.CREDIT_CARD }
        creditCardList.shouldHaveSize(3)

        with(creditCardList.last { it.status == AccountPaymentMethodStatus.ACTIVE }.method as CreditCard) {
            brand shouldBe CreditCardBrand.VISA
            maskedPan shouldBe maskPan(creditCardTO.pan)
            expiryDate shouldBe creditCardTO.expiryDate
            token shouldBe mockToken
            binDetails shouldBe defaultBinDetails
            hmac shouldNotBe null
        }

        responsePayload.id shouldBe creditCardList.last().id.value
    }

    @Test
    fun `deve salvar o cartão de crédito com o status pendente quando a feature flag de verificação de cartão estiver ligada`() {
        every { featureConfiguration.creditCardChallenge } returns true

        val mockToken = CreditCardToken(UUID.randomUUID().toString())

        every {
            acquirerMock.tokenize(any(), any(), any(), any(), any())
        } returns mockToken.right()

        every {
            acquirerMock.validate(any(), "000")
        } returns true.right()

        val creditCardTO = CreateCreditCardTO(
            brand = "VISA",
            pan = "****************",
            expiryDate = "01/2050",
            cvv = "000",
        )
        val response = callAddCreditCard(creditCardTO)
        response.status shouldBe CREATED

        val accountId = AccountId(ACCOUNT_ID)
        val paymentMethodList = accountRepository.findAccountPaymentMethodsByAccountId(accountId)
        paymentMethodList.shouldHaveSize(2)

        val pendingPaymentMethod = dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = accountId.value,
            scanKey = "PAYMENT-METHOD#",
            type = PaymentMethodEntity::class.java,
        ).single { it.status == AccountPaymentMethodStatus.PENDING }

        pendingPaymentMethod.activatedAt.shouldBeNull()

        val creditCardList = paymentMethodList.filter { it.method.type == PaymentMethodType.CREDIT_CARD }
        creditCardList.shouldHaveSize(2)

        with(creditCardList.last { it.status == AccountPaymentMethodStatus.PENDING }.method as CreditCard) {
            brand.name shouldBe creditCardTO.brand
            maskedPan shouldBe maskPan(creditCardTO.pan)
            expiryDate shouldBe creditCardTO.expiryDate
            token shouldBe mockToken
        }

        response.body()!!.id shouldBe creditCardList.last().id.value
    }

    @Test
    fun `deve devolver too many requests quando passar do limite de chamadas de validacão de cartão`() {
        val paymentMethod = accountRepository.createAccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            creditCard = creditCard.method as CreditCard,
            position = 2,
            status = AccountPaymentMethodStatus.PENDING,
        )

        every { limitService.increment(any(), any()) } returns LimitIncrementResult.Exceeded

        val request = HttpRequest.POST(
            "/payment-method/credit-card/${paymentMethod.id.value}/challenge",
            Unit,
        )
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = assertThrows<HttpClientResponseException> {
            client.exchange(request, String::class.java).firstOrError().blockingGet()
        }

        response.status shouldBe TOO_MANY_REQUESTS
    }

    @Test
    fun `deve iniciar a validação de um cartão de crédito pendente`() {
        val paymentMethod = accountRepository.createAccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            creditCard = creditCard.method as CreditCard,
            position = 2,
            status = AccountPaymentMethodStatus.PENDING,
        )

        every { limitService.increment(any(), any()) } returns LimitIncrementResult.Ok

        every {
            acquirerMock.authorize(
                accountId = AccountId(ACCOUNT_ID),
                orderId = any(),
                amount = any(),
                creditCard = any(),
                softDescriptor = any(),
            )
        } returns authorizedCreditCardAuthorization andThen authorizedCreditCardAuthorization.copy(amount = 1001)

        val request = HttpRequest.POST(
            "/payment-method/credit-card/${paymentMethod.id.value}/challenge",
            Unit,
        )
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = client.exchange(request, String::class.java).firstOrError().blockingGet()

        response.status shouldBe NO_CONTENT

        val challenge = creditCardChallengeDbRepository.find(paymentMethodId = paymentMethod.id).shouldNotBeNull()

        challenge.paymentMethodId shouldBe paymentMethod.id
        challenge.status shouldBe CreditCardChallengeStatus.ACTIVE
        challenge.value shouldBe authorizedCreditCardAuthorization.amount
        challenge.alternativeValue shouldBe 1001

        verify(exactly = 2) {
            acquirerMock.authorize(
                accountId = AccountId(ACCOUNT_ID),
                orderId = any(),
                amount = any(),
                creditCard = any(),
                softDescriptor = any(),
            )
        }
    }

    @Test
    fun `ao ativar um cartão de crédito com o valor alternativo, deve salvar com o status correto`() {
        val paymentMethod = accountRepository.createAccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            creditCard = creditCard.method as CreditCard,
            position = 2,
            status = AccountPaymentMethodStatus.PENDING,
        )
        val now = getZonedDateTime()

        every {
            acquirerMock.cancel(orderId = any())
        } just runs

        creditCardChallengeDbRepository.save(
            CreditCardChallenge(
                paymentMethodId = paymentMethod.id,
                accountId = AccountId(ACCOUNT_ID),
                value = 1000,
                alternativeValue = 999,
                attempts = 0,
                status = CreditCardChallengeStatus.ACTIVE,
                createdAt = now,
                updatedAt = now,
                expiresAt = now.plusDays(1),
                authorizationCode = "000",
                acquirerOrderId = "000",
                alternativeAcquirerOrderId = "001",
            ),
        )

        val request = HttpRequest.PUT(
            "/payment-method/credit-card/${paymentMethod.id.value}/validate",
            ValidateChallengeTO(token = 999),
        )
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = client.exchange(request, Unit::class.java).firstOrError().blockingGet()

        response.status shouldBe NO_CONTENT

        val paymentMethods = accountRepository.findAccountPaymentMethodsByAccountId(paymentMethod.accountId)
        paymentMethods.shouldHaveSize(2)

        paymentMethods.single { it.id == paymentMethod.id }.status shouldBe AccountPaymentMethodStatus.ACTIVE

        creditCardChallengeDbRepository.find(
            paymentMethodId = paymentMethod.id,
            status = CreditCardChallengeStatus.SUCCESS,
        ).shouldNotBeNull()

        creditCardChallengeDbRepository.find(
            paymentMethodId = paymentMethod.id,
            status = CreditCardChallengeStatus.ACTIVE,
        ).shouldBeNull()

        verify {
            acquirerMock.cancel(orderId = "000")
            acquirerMock.cancel(orderId = "001")
        }
    }

    @Test
    fun `ao ativar um cartão de crédito, deve salvar com o status correto`() {
        val paymentMethod = accountRepository.createAccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            creditCard = creditCard.method as CreditCard,
            position = 2,
            status = AccountPaymentMethodStatus.PENDING,
        )
        val now = getZonedDateTime()

        every {
            acquirerMock.cancel(orderId = any())
        } just runs

        creditCardChallengeDbRepository.save(
            CreditCardChallenge(
                paymentMethodId = paymentMethod.id,
                accountId = AccountId(ACCOUNT_ID),
                value = 1000,
                alternativeValue = 1001,
                attempts = 0,
                status = CreditCardChallengeStatus.ACTIVE,
                createdAt = now,
                updatedAt = now,
                expiresAt = now.plusDays(1),
                authorizationCode = "000",
                acquirerOrderId = "000",
                alternativeAcquirerOrderId = "001",
            ),
        )

        val request = HttpRequest.PUT(
            "/payment-method/credit-card/${paymentMethod.id.value}/validate",
            ValidateChallengeTO(token = 1000),
        )
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = client.exchange(request, Unit::class.java).firstOrError().blockingGet()

        response.status shouldBe NO_CONTENT

        val paymentMethods = accountRepository.findAccountPaymentMethodsByAccountId(paymentMethod.accountId)
        paymentMethods.shouldHaveSize(2)

        paymentMethods.single { it.id == paymentMethod.id }.status shouldBe AccountPaymentMethodStatus.ACTIVE

        creditCardChallengeDbRepository.find(
            paymentMethodId = paymentMethod.id,
            status = CreditCardChallengeStatus.SUCCESS,
        ).shouldNotBeNull()

        creditCardChallengeDbRepository.find(
            paymentMethodId = paymentMethod.id,
            status = CreditCardChallengeStatus.ACTIVE,
        ).shouldBeNull()
    }

    @Test
    fun `ao salvar um cartão de crédito externo, deve retornar corretamente`() {
        val paymentMethod = accountRepository.createAccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            creditCard = externalCreditCard.method as CreditCard,
            position = 2,
            status = AccountPaymentMethodStatus.PENDING,
        )
        val now = getZonedDateTime()

        creditCardChallengeDbRepository.save(
            CreditCardChallenge(
                paymentMethodId = paymentMethod.id,
                accountId = AccountId(ACCOUNT_ID),
                value = 1000,
                alternativeValue = 1001,
                attempts = 0,
                status = CreditCardChallengeStatus.ACTIVE,
                createdAt = now,
                updatedAt = now,
                expiresAt = now.plusDays(1),
                authorizationCode = "000",
                acquirerOrderId = "000",
                alternativeAcquirerOrderId = "001",
            ),
        )

        val request = HttpRequest.GET<Unit>("/payment-method/credit-card/${paymentMethod.id.value}/challenge")
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = client.exchange(request, GetChallengeTO::class.java).firstOrError().blockingGet()

        response.status shouldBe OK

        response.body()!!.maxAttemptsReached shouldBe false

        val paymentMethods = accountRepository.findAccountPaymentMethodsByAccountId(AccountId(ACCOUNT_ID))

        (paymentMethods.first { it.id == paymentMethod.id }.method as CreditCard).also { created ->
            (externalCreditCard.method as CreditCard).run {
                created.lastFourDigits shouldBe lastFourDigits
                created.mainCard shouldBe mainCard
                created.externalId shouldBe externalId
            }
        }
    }

    @Test
    fun `ao buscar uma validação de cartão de crédito ativa, deve retornar corretamente`() {
        val paymentMethod = accountRepository.createAccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            creditCard = creditCard.method as CreditCard,
            position = 2,
            status = AccountPaymentMethodStatus.PENDING,
        )
        val now = getZonedDateTime()

        creditCardChallengeDbRepository.save(
            CreditCardChallenge(
                paymentMethodId = paymentMethod.id,
                accountId = AccountId(ACCOUNT_ID),
                value = 1000,
                alternativeValue = 1001,
                attempts = 0,
                status = CreditCardChallengeStatus.ACTIVE,
                createdAt = now,
                updatedAt = now,
                expiresAt = now.plusDays(1),
                authorizationCode = "000",
                acquirerOrderId = "000",
                alternativeAcquirerOrderId = "001",
            ),
        )

        val request = HttpRequest.GET<Unit>("/payment-method/credit-card/${paymentMethod.id.value}/challenge")
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = client.exchange(request, GetChallengeTO::class.java).firstOrError().blockingGet()

        response.status shouldBe OK

        response.body()!!.maxAttemptsReached shouldBe false
    }

    @Test
    fun `ao buscar uma validação de cartão de crédito com o limite de tentativas atingido, deve retornar o maxAttemptsReached true`() {
        val paymentMethod = accountRepository.createAccountPaymentMethod(
            accountId = AccountId(ACCOUNT_ID),
            creditCard = creditCard.method as CreditCard,
            position = 2,
            status = AccountPaymentMethodStatus.PENDING,
        )
        val now = getZonedDateTime()

        creditCardChallengeDbRepository.save(
            CreditCardChallenge(
                paymentMethodId = paymentMethod.id,
                accountId = AccountId(ACCOUNT_ID),
                value = 1000,
                alternativeValue = 1001,
                attempts = 3,
                status = CreditCardChallengeStatus.ACTIVE,
                createdAt = now,
                updatedAt = now,
                expiresAt = now.plusDays(1),
                authorizationCode = "000",
                acquirerOrderId = "000",
                alternativeAcquirerOrderId = "001",
            ),
        )

        val request = HttpRequest.GET<Unit>("/payment-method/credit-card/${paymentMethod.id.value}/challenge")
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = client.exchange(request, GetChallengeTO::class.java).firstOrError().blockingGet()

        response.status shouldBe OK

        response.body()!!.maxAttemptsReached shouldBe true
    }

    @ParameterizedTest
    @MethodSource("valuesProvider")
    fun `test handleAddCreditCardResult status codes`(
        result: AddCreditCardResult,
        httpStatus: HttpStatus,
        responseTOCode: String,
    ) {
        with(
            CreditCardController(mockk(), mockk()).handleAddCreditCardResult(
                result,
                mockk(relaxed = true),
            ),
        ) {
            status shouldBe httpStatus
            getBody(ResponseTO::class.java).get().code shouldBe responseTOCode
        }
    }

    @Test
    fun `should return not found when credit card does not exist for user`() {
        val createdCreditCard = accountRepository.createAccountPaymentMethod(
            accountId = AccountId("c41c7a76-7b5c-11eb-908b-1f923d91534a"),
            creditCard = CreditCard(
                CreditCardBrand.VISA,
                "****************",
                "02/2050",
                "123",
                binDetails = null,
                riskLevel = RiskLevel.LOW,
            ),
            position = 1,
        )

        val request = deleteCreditCard(createdCreditCard.id)

        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().retrieve(request, Unit::class.java)
        }

        thrown.status shouldBe NOT_FOUND
    }

    @Test
    fun `should return NO_CONTENT when credit card is removed`() {
        val request = deleteCreditCard(AccountPaymentMethodId(PAYMENT_METHOD_ID))

        val response = client.exchange(request, Unit::class.java).firstOrError().blockingGet()

        response.status shouldBe NO_CONTENT
        accountRepository.findCreditCardsByAccountIdAndStatus(
            AccountId(ACCOUNT_ID),
            AccountPaymentMethodStatus.INACTIVE,
        ) shouldHaveSize 1
    }

    @Test
    fun `deve deletar cartão pendente`() {
        val paymentMethodId = UUID.randomUUID().toString()
        loadCreditCard(
            dynamoDB,
            position = 2,
            paymentMethodStatus = AccountPaymentMethodStatus.PENDING,
            paymentMethodId = paymentMethodId,
        )
        val request = deleteCreditCard(AccountPaymentMethodId(paymentMethodId))

        client.exchange(request, Unit::class.java).firstOrError().blockingGet().status shouldBe NO_CONTENT

        accountRepository.findCreditCardsByAccountIdAndStatus(
            AccountId(ACCOUNT_ID),
            AccountPaymentMethodStatus.INACTIVE,
        ) shouldHaveSize 1
    }

    @Test
    fun `should return NO_CONTENT when credit card is already removed`() {
        loadCreditCard(dynamoDB, AccountPaymentMethodStatus.INACTIVE, 2)

        val request = deleteCreditCard(AccountPaymentMethodId(PAYMENT_METHOD_ID_4))

        val response = client.exchange(request, Unit::class.java).firstOrError().blockingGet()

        response.status shouldBe NO_CONTENT
        val paymentMethods = accountRepository.findAccountPaymentMethodsByAccountId(AccountId(ACCOUNT_ID))
        paymentMethods.single { it.status == AccountPaymentMethodStatus.ACTIVE } shouldNotBe null
        paymentMethods.single { it.status == AccountPaymentMethodStatus.INACTIVE } shouldNotBe null
    }

    @Test
    fun `should return NOT FOUND when trying to remove a bank account`() {
        loadVirtualBalance(dynamoDB)

        val request = deleteCreditCard(AccountPaymentMethodId(PAYMENT_METHOD_ID_4))

        val response = assertThrows<HttpClientResponseException> {
            client.exchange(request, Unit::class.java).firstOrError().blockingGet()
        }

        response.status shouldBe NOT_FOUND
        accountRepository.findAccountPaymentMethodsByAccountId(AccountId(ACCOUNT_ID)) shouldHaveSize 2
        accountRepository.findAccountPaymentMethodsByAccountId(AccountId(ACCOUNT_ID))
            .all { it.status == AccountPaymentMethodStatus.ACTIVE } shouldBe true
    }

    private fun callAddCreditCard(createCreditCardTO: CreateCreditCardTO): HttpResponse<PaymentMethodTO> {
        val request = HttpRequest.POST(
            "/payment-method/credit-card",
            createCreditCardTO,
        )
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        return client.exchange(request, PaymentMethodTO::class.java).firstOrError().blockingGet()
    }

    private fun deleteCreditCard(paymentMethodId: AccountPaymentMethodId): HttpRequest<Unit> {
        return HttpRequest.DELETE<Unit>(
            "/payment-method/credit-card/${paymentMethodId.value}",
        )
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")
    }

    companion object {
        @JvmStatic
        fun valuesProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(MaxLimitReached, BAD_REQUEST, MAX_LIMIT_REACHED.responseTO.code),
                Arguments.of(InvalidCreditCard, BAD_REQUEST, INVALID_CREDIT_CARD.responseTO.code),
                Arguments.of(UnauthorizedCreditCard, BAD_REQUEST, UNAUTHORIZED_CREDIT_CARD.responseTO.code),
                Arguments.of(HighRiskCreditCard(RiskLevel.HIGH), BAD_REQUEST, HIGH_RISK_CREDIT_CARD.responseTO.code),
                Arguments.of(ServerError(), INTERNAL_SERVER_ERROR, SERVER_ERROR.responseTO.code),
            )
        }
    }
}