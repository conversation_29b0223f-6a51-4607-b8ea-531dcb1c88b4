package ai.friday.billpayment.integration

import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.BillEventDetailsEntity
import ai.friday.billpayment.adapters.dynamodb.BillEventEntity
import ai.friday.billpayment.adapters.dynamodb.DescriptionUpdatedEntity
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.billAdded
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

@MicronautTest(environments = [FRIDAY_ENV])
class UpdateBillDescriptionIntegrationTest(embeddedServer: EmbeddedServer) {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()

    private var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val billRepository = DynamoDbBillRepository(dynamoDbDAO)
    private val walletRepository = WalletDbRepository(dynamoDbDAO, mockk())

    private val bill = Bill.build(billAdded.copy(walletId = wallet.id))

    private val validUpdateDescriptionRequest =
        HttpRequest.PUT("/bill/id/$BILL_ID_4/description", "{\"description\": \"$NEW_DESCRIPTION\"}")
            .onWallet(wallet)

    private fun createUpdateDescriptionRequest(description: String) =
        HttpRequest.PUT("/bill/id/$BILL_ID_4/description", "{\"description\": \"$description\"}")
            .onWallet(wallet)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        walletRepository.save(wallet)
    }

    @Test
    fun `should return 404 on bill not found`() {
        loadAccountIntoDb(dynamoDB)
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(validUpdateDescriptionRequest, ResponseTO::class.java)
        }
        assertEquals(HttpStatus.NOT_FOUND, thrown.status)
    }

    @ParameterizedTest
    @ValueSource(strings = [LONG_DESCRIPTION, INVALID_SHORT_DESCRIPTION, INVALID_SHORT_DESCRIPTION2])
    fun `should return bad request on invalid description`(invalidDescription: String) {
        val thrown = Assertions.assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking().exchange(createUpdateDescriptionRequest(invalidDescription), ResponseTO::class.java)
        }
        assertEquals(HttpStatus.BAD_REQUEST, thrown.status)
    }

    @Test
    fun `should update description on owner request`() {
        loadAccountIntoDb(dynamoDB)
        billRepository.save(bill)
        addBillEventIntoDb(dynamoDB, billAdded.copy(walletId = wallet.id))

        val response = client.toBlocking().exchange(validUpdateDescriptionRequest, Unit::class.java)
        assertEquals(HttpStatus.OK, response.status)
        val itemList = dynamoDbDAO.queryTableOnHashKey(bill.billId.value, BillEventEntity::class.java)
        val billEntity = itemList[1]
        val event = getObjectMapper().readerFor(BillEventDetailsEntity::class.java)
            .readValue<BillEventDetailsEntity>(billEntity.details)
        assertTrue(event is DescriptionUpdatedEntity)

        val bill: BillView = billRepository.findBill(bill.billId, wallet.id)
        assertEquals(NEW_DESCRIPTION, bill.billDescription)
    }

    companion object {
        const val NEW_DESCRIPTION = "my new bill description"
    }
}