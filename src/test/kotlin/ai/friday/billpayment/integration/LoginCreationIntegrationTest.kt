package ai.friday.billpayment.integration

import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.LoginDbRepository
import ai.friday.billpayment.adapters.dynamodb.LoginEntity
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.login.LoginResult
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.wallet.WalletId
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBScanExpression
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import kotlin.test.assertEquals
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class LoginCreationIntegrationTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val loginRepository = LoginDbRepository(dynamoDbDAO)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val accountRegisterRepository = AccountRegisterDbRepository(dynamoDbDAO, mockk())
    private val crmService: CrmService = mockk(relaxed = true)
    private val walletRepository = WalletDbRepository(dynamoDbDAO, accountRepository)

    private val loginService = LoginService(
        loginRepository = loginRepository,
        accountRepository = accountRepository,
        accountRegisterRepository = accountRegisterRepository,
        crmService = crmService,
        automaticUserRegister = true,
        inAppSubscription = 0.1f,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        dynamoDB
    }

    private val mapper: DynamoDBMapper = DynamoDBMapper(dynamoDB)

    private val externalOauthUserId = "***********"

    @ParameterizedTest
    @EnumSource(ProviderName::class)
    fun `Should find owner principal`(provider: ProviderName) {
        loadAccountIntoDb(dynamoDB)
        loadLoginsIntoDb(dynamoDB, provider = provider)
        val providerUser = ProviderUser(SOCIAL_LOGIN_ID, provider, "francisco", EmailAddress(EMAIL))

        val key: MutableMap<String, AttributeValue> = HashMap()
        key[":sk"] = AttributeValue().withS("LOGIN#" + provider.name)

        val scanExpression = DynamoDBScanExpression()
            .withFilterExpression("ScanKey = :sk")
            .withExpressionAttributeValues(key)

        assertEquals(1, countExistingLogins(scanExpression))

        val loginResult: LoginResult = loginService.resolveLogin(providerUser)
        assertEquals(Role.OWNER, loginResult.login.role)
        assertEquals(1, mapper.count(LoginEntity::class.java, scanExpression))
        loginResult.created shouldBe false

        loginRepository.findUserLogin(providerUser) shouldBe loginResult.login
    }

    private fun countExistingLogins(scanExpression: DynamoDBScanExpression) =
        mapper.count(LoginEntity::class.java, scanExpression)

    @ParameterizedTest
    @EnumSource(ProviderName::class, mode = EnumSource.Mode.EXCLUDE, names = ["MODATTA"])
    fun `should create account for new user login when ff_automaticUserRegister is active`(provider: ProviderName) {
        val providerUser = ProviderUser(externalOauthUserId, provider, "francisco", EmailAddress(EMAIL))

        val loginResult: LoginResult = loginService.resolveLogin(providerUser)
        loginResult.created shouldBe true
        loginResult.login.accountId.value shouldNotBe "ACCOUNT-${providerUser.id}"

        val dbLogin = loginRepository.findUserLogin(providerUser)
        dbLogin shouldBe loginResult.login
        dbLogin?.role shouldBe Role.GUEST

        val dbAccount = accountRepository.findPartialAccountById(loginResult.login.accountId)
        dbAccount.status shouldBe AccountStatus.REGISTER_INCOMPLETE

        val accountRegister = accountRegisterRepository.findByAccountId(loginResult.login.accountId)
        accountRegister.accountId shouldBe loginResult.login.accountId
        accountRegister.emailAddress shouldBe loginResult.login.emailAddress
        accountRegister.nickname shouldBe "francisco"

        val wallet = walletRepository.findWalletOrNull(WalletId(dbAccount.id.value))
        wallet shouldBe null

        val paymentMethods =
            accountRepository.findAccountPaymentMethodsByAccountId(dbAccount.id)
        paymentMethods.shouldBeEmpty()

        val slot = slot<PartialAccount>()
        verify {
            crmService.upsertContact(capture(slot))
        }
        slot.captured shouldBe dbAccount
    }

    @Test
    fun `should throw exception for new user login when ff_automaticUserRegister is deactivated`() {
        val loginServiceFFDeactivated =
            LoginService(
                loginRepository = loginRepository,
                accountRepository = accountRepository,
                accountRegisterRepository = accountRegisterRepository,
                crmService = crmService,
                automaticUserRegister = false,
                inAppSubscription = 0.1f,
            )
        val providerUser = ProviderUser(externalOauthUserId, ProviderName.MSISDN, "francisco", EmailAddress(EMAIL))

        assertThrows<AccountNotFoundException> {
            loginServiceFFDeactivated.resolveLogin(providerUser)
        }
    }
}