package ai.friday.billpayment.integration

import ai.friday.billpayment.adapters.api.AccountConfigTO
import ai.friday.billpayment.adapters.api.AccountReferrerTO
import ai.friday.billpayment.adapters.api.AccountTO
import ai.friday.billpayment.adapters.api.InviteRequestTO
import ai.friday.billpayment.adapters.api.InviteResponseTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.SimpleAccountSubscriptionTO
import ai.friday.billpayment.adapters.api.SimpleAccountTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.InAppSubscriptionDbRepository
import ai.friday.billpayment.adapters.dynamodb.ReferrerDbRepository
import ai.friday.billpayment.adapters.dynamodb.SubscriptionDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.revenuecat.RevenueCatAdapter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountConfigurationName
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.fix
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAdapter
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProductId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProducts
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.billpayment.app.integrations.ReferrerRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.referrer.ReferrerRegister
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionStatus
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.mobilePhone
import ai.friday.billpayment.paymentMethodId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest.GET
import io.micronaut.http.HttpRequest.POST
import io.micronaut.http.HttpRequest.PUT
import io.micronaut.http.HttpStatus.BAD_REQUEST
import io.micronaut.http.HttpStatus.FORBIDDEN
import io.micronaut.http.HttpStatus.NOT_FOUND
import io.micronaut.http.HttpStatus.NO_CONTENT
import io.micronaut.http.HttpStatus.OK
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.time.LocalDate
import java.time.LocalTime
import java.time.Period
import java.time.ZonedDateTime
import org.junit.jupiter.api.Assertions
import org.junit.jupiter.api.Assertions.assertThrows
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

@MicronautTest(environments = [FRIDAY_ENV])
class AccountIntegrationTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val securityFixture = SecurityFixture()
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private fun buildInviteRequest(cookie: Cookie): MutableHttpRequest<InviteRequestTO> {
        return GET<InviteRequestTO>("/account/invite")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildUpdateDefaultWalletRequest(cookie: Cookie, value: String): MutableHttpRequest<AccountConfigTO> {
        return PUT("/account/config/${AccountConfigurationName.DEFAULT_WALLET_ID.name}", AccountConfigTO(value))
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val subscriptionRepository = InAppSubscriptionDbRepository(dynamoDbDAO)
    private val accountDbRepository: AccountDbRepository = AccountDbRepository(dynamoDbDAO)
    private val walletRepository: WalletRepository = WalletDbRepository(dynamoDbDAO, mockk())
    private val referrerRepository: ReferrerRepository = ReferrerDbRepository(dynamoDbDAO)
    private val subscriptionDbRepository: SubscriptionDbRepository = SubscriptionDbRepository(dynamoDbDAO)

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet(
        name = "carteira",
        otherMembers = listOf(walletFixture.assistant),
        accountPaymentMethodId = paymentMethodId,
    )

    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService

    private val revenueCatAdapterMock: InAppSubscriptionAdapter = mockk()

    @MockBean(RevenueCatAdapter::class)
    fun revenueCatAdapter() = revenueCatAdapterMock

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve retornar os dados do usuário simplificado sem assinatura`() {
        loadAccountIntoDb(dynamoDB)
        val cookie = securityFixture.cookieAuthOwner
        val response = fetchAccountDetails(cookie)
        assertSimpleAccountDetails(response)
        Assertions.assertNull(response.subscription)
    }

    @Test
    fun `deve retornar os dados do usuário simplificar com assinatura PIX ativa`() {
        loadAccountIntoDb(dynamoDB)
        val expectedNextEffectiveDueDate = getLocalDate().plusMonths(1)
        subscriptionDbRepository.save(
            Subscription(
                accountId = AccountId(value = ACCOUNT_ID),
                document = Document(value = DOCUMENT),
                status = SubscriptionStatus.ACTIVE,
                amount = 999,
                dayOfMonth = 10,
                recurrenceId = RecurrenceId(value = "recurrence"),
                paymentStatus = SubscriptionPaymentStatus.PAID,
                walletId = WalletId(value = WALLET_ID),
                nextEffectiveDueDate = expectedNextEffectiveDueDate,
            ),
        )

        val cookie = securityFixture.cookieAuthOwner
        val response = fetchAccountDetails(cookie)
        assertSimpleAccountDetails(response)
        with(response.subscription!!) {
            this.type shouldBe SubscriptionType.PIX
            this.autoRenew.shouldBeTrue()
            this.endsAt shouldBe expectedNextEffectiveDueDate.format(dateFormat)
            this.status shouldBe "ACTIVE"
            this.reason shouldBe "PAID"
            this.price shouldBe 999
            this.duration shouldBe "P1M"
        }
    }

    @Test
    fun `deve retornar os dados do usuário simplificado com assinatura PIX inativa`() {
        loadAccountIntoDb(dynamoDB)
        val expectedNextEffectiveDueDate = getLocalDate().minusDays(2)
        subscriptionDbRepository.save(
            Subscription(
                accountId = AccountId(value = ACCOUNT_ID),
                document = Document(value = DOCUMENT),
                status = SubscriptionStatus.INACTIVE,
                amount = 999,
                dayOfMonth = 10,
                recurrenceId = RecurrenceId(value = "recurrence"),
                paymentStatus = SubscriptionPaymentStatus.OVERDUE,
                walletId = WalletId(value = WALLET_ID),
                nextEffectiveDueDate = expectedNextEffectiveDueDate,
            ),
        )

        val cookie = securityFixture.cookieAuthOwner
        val response = fetchAccountDetails(cookie)
        assertSimpleAccountDetails(response)
        response.subscription.shouldBeNull()
    }

    @Test
    fun `deve retornar os dados do usuário simplificado com in app subscription`() {
        every { revenueCatAdapterMock.getProducts() } returns listOf(
            InAppSubscriptionProducts(
                id = InAppSubscriptionProductId(value = ""),
                displayName = null,
                storeIdentifier = "friday_990_1m_2w0",
                duration = Period.ofMonths(1),
                gracePeriodDuration = null,
                type = "",
            ),
        )
        subscriptionRepository.save(
            InAppSubscription(
                accountId = AccountId(ACCOUNT_ID),
                status = InAppSubscriptionStatus.ACTIVE,
                endsAt = ZonedDateTime.of(LocalDate.parse("2024-02-01"), LocalTime.NOON, brazilTimeZone),
                store = InAppSubscriptionStore.PLAY_STORE,
                reason = InAppSubscriptionReason.TRIAL,
                inAppSubscriptionAccessConcessionId = null,
                createdAt = ZonedDateTime.of(LocalDate.parse("2021-01-01"), LocalTime.NOON, brazilTimeZone),
                autoRenew = true,
                price = 0,
                productId = InAppSubscriptionProductId("friday_990_1m_2w0"),
                offStoreProductId = null,
            ),
        )
        loadAccountIntoDb(dynamoDB, subscriptionType = SubscriptionType.IN_APP)
        val cookie = securityFixture.cookieAuthOwner

        val response = fetchAccountDetails(cookie)

        assertSimpleAccountDetails(response)
        Assertions.assertNotNull(response.subscription)
        assertSimpleAccountSubscriptionDetails(response.subscription!!)
    }

    @Test
    fun `should return UserAccount on owner principal`() {
        loadAccountIntoDb(dynamoDB)
        val cookie = securityFixture.cookieAuthOwner
        val accountTO = fetchAccount(cookie)
        assertAccountDetails(accountTO)
    }

    @Test
    fun `should return partial account in REGISTER_INCOMPLETE status when register is incomplete`() {
        loadPartialAccountIntoDb(dynamoDB, status = AccountStatus.REGISTER_INCOMPLETE)
        val cookie = securityFixture.cookieGuest

        val accountTO = fetchAccount(cookie)

        accountTO.id shouldBe ACCOUNT_ID
        accountTO.fullName shouldBe NAME
        accountTO.status shouldBe AccountStatus.REGISTER_INCOMPLETE
    }

    @Test
    fun `should return partial account in UNDER_REVIEW status when register in under external review`() {
        loadPartialAccountIntoDb(dynamoDB, status = AccountStatus.UNDER_EXTERNAL_REVIEW)
        val cookie = securityFixture.cookieGuest

        val accountTO = fetchAccount(cookie)

        accountTO.id shouldBe ACCOUNT_ID
        accountTO.fullName shouldBe NAME
        accountTO.status shouldBe AccountStatus.UNDER_REVIEW
    }

    @Test
    fun `should return partial account in UNDER_REVIEW status when register is approved`() {
        loadPartialAccountIntoDb(dynamoDB, status = AccountStatus.APPROVED)
        val cookie = securityFixture.cookieGuest

        val accountTO = fetchAccount(cookie)

        accountTO.id shouldBe ACCOUNT_ID
        accountTO.fullName shouldBe NAME
        accountTO.status shouldBe AccountStatus.UNDER_REVIEW
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING"])
    fun `should return empty invite list when theres is no pending invite`(inviteStatus: InviteStatus) {
        loadAccountIntoDb(dynamoDB)

        val invite = Invite(
            walletId = wallet.id,
            memberDocument = DOCUMENT,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                viewBalance = true,
                notification = true,
            ),
            status = inviteStatus,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = walletFixture.founder.name,
            founderDocument = walletFixture.founder.document,
            created = getZonedDateTime(),
        )
        walletRepository.save(invite)

        val request = buildInviteRequest(securityFixture.cookieAuthOwner)
        val response = client.toBlocking()
            .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe OK
        response.body.get().size shouldBe 0
    }

    @Test
    fun `should not return invite when user is guest`() {
        val request = buildInviteRequest(securityFixture.cookieGuest)

        val thrown = assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking()
                .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe FORBIDDEN
    }

    @Test
    fun `should return pending invite list when theres is a pending invite`() {
        loadAccountIntoDb(dynamoDB)

        val invite = Invite(
            walletId = wallet.id,
            memberDocument = DOCUMENT,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                viewBalance = true,
                notification = true,
            ),
            status = InviteStatus.PENDING,
            validUntil = getLocalDate(),
            walletName = wallet.name,
            founderName = walletFixture.founder.name,
            founderDocument = walletFixture.founder.document,
            created = getZonedDateTime(),
        )
        walletRepository.save(invite)

        val request = buildInviteRequest(securityFixture.cookieAuthOwner)
        val response = client.toBlocking()
            .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe OK
        response.body.get().size shouldBe 1
        with(response.body.get().first()) {
            this.walletId shouldBe invite.walletId.value
            this.memberDocument shouldBe invite.memberDocument
            this.memberName shouldBe invite.memberName
            this.status shouldBe invite.status.name
            this.permissions.scheduleBills shouldBe invite.permissions.scheduleBills
            this.permissions.viewBills shouldBe invite.permissions.viewBills
        }
    }

    @Test
    fun `não deve retornar convites expirados`() {
        loadAccountIntoDb(dynamoDB)

        val invite = Invite(
            walletId = wallet.id,
            memberDocument = DOCUMENT,
            memberName = "Alexandre",
            memberType = MemberType.COLLABORATOR,
            permissions = MemberPermissions(
                viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
                notification = true,
                viewBalance = true,
            ),
            status = InviteStatus.PENDING,
            validUntil = getLocalDate().minusDays(1),
            walletName = wallet.name,
            founderName = walletFixture.founder.name,
            founderDocument = walletFixture.founder.document,
            created = getZonedDateTime(),
        )
        walletRepository.save(invite)

        val request = buildInviteRequest(securityFixture.cookieAuthOwner)
        val response = client.toBlocking()
            .exchange(request, Argument.listOf(InviteResponseTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe OK
        response.body.get().size shouldBe 0
    }

    @Test
    fun `should return BAD REQUEST on update account config with invalid config name`() {
        loadAccountIntoDb(dynamoDB, defaultWalletId = WalletId("RANDOM-WALLET-ID"))

        val request = PUT("/account/config/wrongAccountConfigName", AccountConfigTO(wallet.id.value))
            .cookie(securityFixture.cookieAuthOwner).header("X-API-VERSION", "2")
        val thrown = assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking()
                .exchange(request, Argument.listOf(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4000"
    }

    @Test
    fun `should return NOT_FOUND on update default wallet to a non existent wallet`() {
        loadAccountIntoDb(dynamoDB, defaultWalletId = WalletId("RANDOM-WALLET-ID"))

        val request = buildUpdateDefaultWalletRequest(securityFixture.cookieAuthOwner, "Wallet-Id-Not-Found")

        val thrown = assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking()
                .exchange(request, Argument.listOf(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe NOT_FOUND
    }

    @Test
    fun `should return FORBIDDEN on update default wallet to a wallet without membership`() {
        loadAccountIntoDb(
            dynamoDB,
            accountId = walletFixture.participantAccount.accountId,
            defaultWalletId = WalletId("RANDOM-WALLET-ID"),
        )

        val request = buildUpdateDefaultWalletRequest(buildCookie(walletFixture.participantAccount), wallet.id.value)
        walletRepository.save(wallet)

        val thrown = assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking()
                .exchange(request, Argument.listOf(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe FORBIDDEN
    }

    @Test
    fun `should return BAD_REQUEST on update default wallet to a wallet that user is Assistant`() {
        loadAccountIntoDb(
            dynamoDB,
            accountId = walletFixture.assistantAccount.accountId,
            defaultWalletId = WalletId("RANDOM-WALLET-ID"),
        )

        val request = buildUpdateDefaultWalletRequest(buildCookie(walletFixture.assistantAccount), wallet.id.value)
        walletRepository.save(wallet)

        val thrown = assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking()
                .exchange(request, Argument.listOf(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4001"
    }

    @Test
    fun `should return NO CONTENT on update default wallet to a wallet that user is the founder`() {
        loadAccountIntoDb(
            dynamoDB,
            accountId = walletFixture.founderAccount.accountId,
            defaultWalletId = WalletId("RANDOM-WALLET-ID"),
        )

        val request = buildUpdateDefaultWalletRequest(buildCookie(walletFixture.founderAccount), wallet.id.value)
        walletRepository.save(wallet)

        val response = client.toBlocking()
            .exchange(request, Argument.listOf(String::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe NO_CONTENT
        val account = accountDbRepository.findById(walletFixture.founderAccount.accountId)
        account.configuration.defaultWalletId shouldBe wallet.id
    }

    @Test
    fun `should return BAD REQUEST on update ACCESS TOKEN when user isnt XP`() {
        loadAccountIntoDb(dynamoDB)

        val request = PUT("/account/config/ACCESS_TOKEN", AccountConfigTO("access-token-abc-123"))
            .cookie(securityFixture.cookieAuthOwner).header("X-API-VERSION", "2")
        val thrown = assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking()
                .exchange(request, Argument.listOf(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4002"
    }

    @Test
    fun `should return BAD REQUEST on update REFRESH TOKEN when user isnt XP`() {
        loadAccountIntoDb(dynamoDB)

        val request = PUT("/account/config/REFRESH_TOKEN", AccountConfigTO("refresh-token-abc-123"))
            .cookie(securityFixture.cookieAuthOwner).header("X-API-VERSION", "2")
        val thrown = assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking()
                .exchange(request, Argument.listOf(String::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4002"
    }

    @Test
    fun `should return NO CONTENT on create account referrer`() {
        loadPartialAccountIntoDb(dynamoDB)

        val accountReferrerTO = AccountReferrerTO(
            referrer = "Fulano",
            referrerUrl = null,
            platform = "android",
            appVersion = "1.0",
        )

        val request = POST("/account/referrer", accountReferrerTO)
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountReferrerTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe NO_CONTENT

        val referrerRegister = referrerRepository.find(accountId = AccountId(ACCOUNT_ID))

        referrerRegister.shouldNotBeNull()
        with(referrerRegister) {
            referrer shouldBe accountReferrerTO.referrer
            platform shouldBe accountReferrerTO.platform
            appVersion shouldBe accountReferrerTO.appVersion
        }
    }

    @Test
    fun `should return NO CONTENT on create account referrer when already exists but should not update actual referrer`() {
        loadPartialAccountIntoDb(dynamoDB)

        referrerRepository.save(
            ReferrerRegister(
                accountId = AccountId(ACCOUNT_ID),
                referrer = "Fulano",
                referrerUrl = null,
                platform = "android",
                appVersion = "1.0",
            ),
        )

        val accountReferrerTO = AccountReferrerTO(
            referrer = "Beltrano",
            referrerUrl = null,
            platform = "android",
            appVersion = "2.0",
        )

        val request = POST("/account/referrer", accountReferrerTO)
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountReferrerTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe NO_CONTENT

        val referrerRegister = referrerRepository.find(accountId = AccountId(ACCOUNT_ID))

        referrerRegister.shouldNotBeNull()
        with(referrerRegister) {
            referrer shouldBe "Fulano"
            platform shouldBe "android"
            appVersion shouldBe "1.0"
        }
    }

    @Test
    fun `should return NOT FOUND on create account referrer when account does not exists`() {
        val accountReferrerTO = AccountReferrerTO(
            referrer = "Beltrano",
            referrerUrl = null,
            platform = "android",
            appVersion = "2.0",
        )

        val request = POST("/account/referrer", accountReferrerTO)
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val thrown = assertThrows(HttpClientResponseException::class.java) {
            client.toBlocking()
                .exchange(request, Argument.of(AccountReferrerTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe NOT_FOUND

        val referrerRegister = referrerRepository.find(accountId = AccountId(ACCOUNT_ID))

        referrerRegister.shouldBeNull()
    }

    @Test
    fun `should return NO CONENT on create account referrer with both referrer and referrerUrl empty`() {
        val accountReferrerTO = AccountReferrerTO(
            referrer = "",
            referrerUrl = null,
            platform = "android",
            appVersion = "2.0",
        )

        val request = POST("/account/referrer", accountReferrerTO)
            .cookie(securityFixture.cookieAuthOwner)
            .header("X-API-VERSION", "2")

        val response = client.toBlocking()
            .exchange(request, Argument.of(AccountReferrerTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe NO_CONTENT

        val referrerRegister = referrerRepository.find(accountId = AccountId(ACCOUNT_ID))

        referrerRegister.shouldBeNull()
    }

    private fun fetchAccount(cookie: Cookie): AccountTO {
        return client.toBlocking().retrieve(
            GET<AccountTO>("/account")
                .cookie(cookie)
                .header("X-API-VERSION", "2"),
            AccountTO::class.java,
        )
    }

    private fun fetchAccountDetails(cookie: Cookie): SimpleAccountTO {
        return client.toBlocking().retrieve(
            GET<AccountTO>("/account/details")
                .cookie(cookie)
                .header("X-API-VERSION", "2"),
            SimpleAccountTO::class.java,
        )
    }

    private fun assertSimpleAccountDetails(actual: SimpleAccountTO) {
        Assertions.assertEquals(NAME, actual.fullName)
        Assertions.assertEquals(EMAIL, actual.email)
        Assertions.assertEquals(mobilePhone, MobilePhone(actual.msisdn).fix())
    }

    private fun assertSimpleAccountSubscriptionDetails(actual: SimpleAccountSubscriptionTO) {
        Assertions.assertEquals("IN_APP", actual.type.name)
        Assertions.assertEquals("ACTIVE", actual.status)
        Assertions.assertEquals("TRIAL", actual.reason)
        Assertions.assertEquals("2024-02-01", actual.endsAt)
        Assertions.assertEquals(true, actual.autoRenew)
        Assertions.assertEquals("P1M", actual.duration)
        Assertions.assertEquals(0, actual.price)
        Assertions.assertEquals("friday_990_1m_2w0", actual.productId)
    }

    private fun assertAccountDetails(actual: AccountTO) {
        Assertions.assertEquals(ACCOUNT_ID, actual.id)
        Assertions.assertEquals(DOCUMENT, actual.document)
        Assertions.assertEquals("CPF", actual.documentType)
        Assertions.assertEquals(NAME, actual.fullName)
        Assertions.assertEquals(AccountStatus.ACTIVE, actual.status)
    }
}