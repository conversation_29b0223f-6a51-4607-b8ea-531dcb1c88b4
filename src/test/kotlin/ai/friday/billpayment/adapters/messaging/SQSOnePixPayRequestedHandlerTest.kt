package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.integrations.EndToEnd
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayRequestedMessage
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.onepixpay.OnePixPayStatus
import ai.friday.billpayment.app.pix.PixStatement
import ai.friday.billpayment.getActiveBill
import ai.friday.morning.messaging.MessageHandlerResponse
import arrow.core.getOrElse
import io.kotest.matchers.shouldBe
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.verify
import java.lang.RuntimeException
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource
import software.amazon.awssdk.services.sqs.model.Message

class SQSOnePixPayRequestedHandlerTest {

    private val onePixPayService: OnePixPayService = mockk()
    private val pixPaymentService: PixPaymentService = mockk()

    private val handler = SQSOnePixPayRequestedHandler(
        onePixPayService = onePixPayService,
        pixPaymentService = pixPaymentService,
    )

    private fun createOnePixPay(status: OnePixPayStatus): OnePixPay {
        val onePixPay = OnePixPay.create(
            listOf(getActiveBill()),
            EndToEnd(),
        ).getOrElse {
            throw RuntimeException(it.toString())
        }

        return when (status) {
            OnePixPayStatus.CREATED -> onePixPay
            OnePixPayStatus.REQUESTED -> onePixPay.asRequested()
            OnePixPayStatus.PROCESSED -> onePixPay.asRequested().asProcessed()
            OnePixPayStatus.EXPIRED -> onePixPay.asRequested().asExpired()
            OnePixPayStatus.ERROR -> onePixPay.asRequested().asError("mocked error description")
        }
    }

    private fun setupOnePixPay(status: OnePixPayStatus): OnePixPay {
        val onePixPay = createOnePixPay(status)

        every {
            onePixPayService.find(onePixPay.id)
        } returns onePixPay

        return onePixPay
    }

    private fun OnePixPay.toMessage() = Message.builder().body(
        getObjectMapper().writeValueAsString(
            OnePixPayRequestedMessage(onePixPayId = id.value, fullAccountNumber = accountNumber.fullAccountNumber),
        ),
    ).build()

    val accountNumber = AccountNumber(1.toBigInteger(), "9")

    @Nested
    @DisplayName("quando o estado do one pix pay é diferente de REQUESTED")
    inner class OnePixPayNotRequested {
        @ParameterizedTest
        @EnumSource(value = OnePixPayStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["REQUESTED"])
        fun `deve remover da fila`(status: OnePixPayStatus) {
            val onePixPay = setupOnePixPay(status)

            val result = handler.handleMessage(onePixPay.toMessage())

            result shouldBe MessageHandlerResponse.delete()
        }
    }

    @Nested
    @DisplayName("quando o estado do one pix pay é REQUESTED")
    inner class OnePixPayRequested {

        val onePixPay = setupOnePixPay(OnePixPayStatus.REQUESTED)

        @Nested
        @DisplayName("e o pix ainda não foi recebido")
        inner class PixNotReceived {
            @Test
            fun `deve manter a msg na fila`() {
                every {
                    pixPaymentService.getStatement(accountNumber)
                } returns listOf(
                    PixStatement(flow = BankStatementItemFlow.DEBIT, transactionId = null, endToEnd = onePixPay.id.source as EndToEnd),
                )

                val result = handler.handleMessage(onePixPay.toMessage())

                result shouldBe MessageHandlerResponse.keep()
            }
        }

        @Nested
        @DisplayName("e o pix foi recebido")
        inner class PixReceived {
            @Test
            fun `deve remover a msg da fila quando conseguir processar o one pix pay`() {
                every {
                    pixPaymentService.getStatement(accountNumber)
                } returns listOf(
                    PixStatement(flow = BankStatementItemFlow.DEBIT, transactionId = null, endToEnd = null),
                    PixStatement(flow = BankStatementItemFlow.CREDIT, transactionId = null, endToEnd = onePixPay.id.source as EndToEnd),
                )

                every {
                    onePixPayService.processSchedule(onePixPay)
                } just Runs

                val result = handler.handleMessage(onePixPay.toMessage())

                result shouldBe MessageHandlerResponse.delete()

                verify {
                    onePixPayService.processSchedule(onePixPay)
                }
            }
        }
    }
}