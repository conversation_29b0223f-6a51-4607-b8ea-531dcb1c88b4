package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountEvent
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.DeviceBinded
import ai.friday.billpayment.app.account.MFAEnabled
import ai.friday.billpayment.app.account.MFARequired
import ai.friday.billpayment.app.account.UserCreated
import ai.friday.billpayment.app.account.UserPoolProvider
import ai.friday.billpayment.app.fingerprint.DeviceFingerprint
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.RegisterDevicesResult
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import software.amazon.awssdk.services.sqs.model.Message

class SQSAccountEventsHandlerTest {

    private val userPoolAdapter = mockk<UserPoolAdapter>(relaxed = true)
    private val eventPublisher = mockk<EventPublisher>(relaxed = true)
    private val accountRepository = mockk<AccountRepository>(relaxed = true)
    private val mockedDeviceFingerprintService = mockk<DeviceFingerprintService>()

    private val handler = SQSAccountEventsHandler(
        amazonSQS = mockk(),
        amazonSNS = mockk(),
        configuration = AccountMessageHandlerConfiguration(
            sqsWaitTime = 3675,
            sqsCoolDownTime = 5596,
            dlqArn = "quo",
            visibilityTimeout = 5261,
            maxNumberOfMessages = 7375,
            accountEventsQueueName = "Corine Odom",
        ),
        topicArn = "partiendo",
        userPoolAdapter = userPoolAdapter,
        eventPublisher = eventPublisher,
        accountRepository = accountRepository,
        deviceFingerprintService = mockedDeviceFingerprintService,
    )

    @BeforeEach
    fun setUp() {
        every {
            accountRepository.findById(any())
        } returns ACCOUNT
    }

    @Test
    fun `quando receber evento de USER_CREATED deve enviar evento de MFA_REQUIRED se for cadastro simplificado`() {
        val response = handler.handleMessage(
            buildMessage(
                UserCreated(
                    accountId = AccountId(),
                    username = DOCUMENT,
                    userPoolProvider = UserPoolProvider.COGNITO,
                    mfaEnabled = false,
                ),
            ),
        )

        response.shouldDeleteMessage shouldBe true

        verify {
            eventPublisher.publish(
                event = any<MFARequired>(),
            )
        }
    }

    @Test
    fun `quando receber evento de USER_CREATED nao deve enviar evento de MFA_REQUIRED se for cadastro completo`() {
        val response = handler.handleMessage(
            buildMessage(
                UserCreated(
                    accountId = AccountId(),
                    username = DOCUMENT,
                    userPoolProvider = UserPoolProvider.COGNITO,
                    mfaEnabled = true,
                ),
            ),
        )

        response.shouldDeleteMessage shouldBe true

        verify(exactly = 0) {
            eventPublisher.publish(
                event = any<MFARequired>(),
            )
        }
    }

    @Nested
    @DisplayName("quando receber evento MFA_REQUIRED")
    inner class MFARequiredTest {
        @Test
        fun `deve habilitar o MFA no cognito e publicar evento de MFA_ENABLED se tiver passado 60 minutos`() {
            val response = handler.handleMessage(
                buildMessage(
                    MFARequired(
                        accountId = ACCOUNT.accountId,
                        created = getZonedDateTime().minusMinutes(61).toInstant().toEpochMilli(),
                    ),
                ),
            )

            response.shouldDeleteMessage shouldBe true

            verify {
                userPoolAdapter.setMfaPreference(ACCOUNT.document, true)
                eventPublisher.publish(
                    event = any<MFAEnabled>(),
                )
            }
        }

        @Test
        fun `não deve habilitar o MFA no cognito e publicar o evento de MFA_ENABLED se ainda não tiver passado 60 minutos`() {
            val response = handler.handleMessage(
                buildMessage(
                    MFARequired(
                        accountId = ACCOUNT.accountId,
                        created = getZonedDateTime().minusMinutes(59).toInstant().toEpochMilli(),
                    ),
                ),
            )

            response.shouldDeleteMessage shouldBe false

            verify(exactly = 0) {
                userPoolAdapter.setMfaPreference(ACCOUNT.document, any())
                eventPublisher.publish(
                    event = any<MFAEnabled>(),
                )
            }
        }

        @Test
        fun `quando chegar um evento de device binded, deve criar um deviceId para cada carteira que ele pertence`() {
            val expectedDeviceFingerprint = DeviceFingerprint("fingerprint")
            every {
                mockedDeviceFingerprintService.registerDevices(
                    accountId = ACCOUNT.accountId,
                    deviceFingerprint = expectedDeviceFingerprint,
                )
            } returns RegisterDevicesResult.Success
            val response = handler.handleMessage(
                buildMessage(
                    DeviceBinded(
                        accountId = ACCOUNT.accountId,
                        deviceFingerprint = expectedDeviceFingerprint,
                    ),
                ),
            )

            response.shouldDeleteMessage.shouldBeTrue()

            verify {
                mockedDeviceFingerprintService.registerDevices(
                    accountId = ACCOUNT.accountId,
                    deviceFingerprint = expectedDeviceFingerprint,
                )
            }
        }

        @Test
        fun `quando falhar o registro de device nao deve remover da fila`() {
            val expectedDeviceFingerprint = DeviceFingerprint("fingerprint")
            every {
                mockedDeviceFingerprintService.registerDevices(
                    accountId = ACCOUNT.accountId,
                    deviceFingerprint = expectedDeviceFingerprint,
                )
            } returns RegisterDevicesResult.AddDeviceFailure()

            val response = handler.handleMessage(
                buildMessage(
                    DeviceBinded(
                        accountId = ACCOUNT.accountId,
                        deviceFingerprint = expectedDeviceFingerprint,
                    ),
                ),
            )

            response.shouldDeleteMessage.shouldBeFalse()
        }
    }

    private fun buildMessage(event: AccountEvent) =
        Message.builder().body(getObjectMapper().writeValueAsString(event)).build()
}