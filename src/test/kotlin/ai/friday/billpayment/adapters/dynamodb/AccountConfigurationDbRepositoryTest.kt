package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.shouldBe
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class AccountConfigurationDbRepositoryTest {
    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDAO = DynamoDbDAO(dynamoDB)
    private val accountConfigurationDbRepository = AccountConfigurationDbRepository(dynamoDAO)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve salvar um accountConfiguration sem tokens`() {
        val legacyAccountConfiguration = AccountConfiguration(
            accountId = AccountId(ACCOUNT_ID),
            pushNotificationTokens = setOf(),
            receiveMonthlyStatement = false,
            freeOfFridaySubscription = false,
        )

        accountConfigurationDbRepository.save(legacyAccountConfiguration)

        val savedConfiguration = accountConfigurationDbRepository.find(AccountId(ACCOUNT_ID))

        savedConfiguration shouldBe legacyAccountConfiguration
    }
}