package ai.friday.billpayment.adapters.btg

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.integrations.BlipConfiguration
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.integration.ACCOUNT_ID
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class ChatbotCallbackControllerIntegrationTest(val embeddedServer: EmbeddedServer) {

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)
    private val clientIP = "************"

    @MockBean(BlipConfiguration::class)
    fun getBlipConfiguration() = blipConfiguration
    private val blipConfiguration: BlipConfiguration = mockk()

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(LoginService::class)
    fun getLoginService() = loginService
    private val loginService: LoginService = mockk() {
        every {
            findProviderUser(any(), any())
        } returns ProviderUser(
            ACCOUNT_ID,
            ProviderName.WHATSAPP,
            "*************",
            EmailAddress("<EMAIL>"),
        )
    }

    @ParameterizedTest
    @MethodSource("createPayloads")
    fun `deve aceitar uma requisição se não houver IP na lista de liberados`(currentPayload: String) {
        every {
            blipConfiguration.allowedIps
        } returns emptyList()

        val response = callServer(clientIP, currentPayload)

        response.status shouldBe HttpStatus.NO_CONTENT
    }

    @Test
    fun `deve recusar uma requisição de um IP não liberado`() {
        every {
            blipConfiguration.allowedIps
        } returns listOf(clientIP)

        val thrown = assertThrows<HttpClientResponseException> {
            callServer("***********", payload)
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `deve aceitar uma requisição de um IP liberado`() {
        every {
            blipConfiguration.allowedIps
        } returns listOf(clientIP)

        val response = callServer(clientIP, payload)

        response.status shouldBe HttpStatus.NO_CONTENT
    }

    private fun callServer(clientIP: String, payload: String): HttpResponse<String> {
        val request = HttpRequest.POST("/chatbot/blip/webhook", payload)
            .header("Forwarded", "by=***********;for=$clientIP;host=127.0.0.1;proto=http")
        return client.toBlocking()
            .exchange(
                request,
                Argument.of(String::class.java),
                Argument.of(String::class.java),
            )
    }

    companion object {
        val payload = """
                {
                	"type": "text/plain",
                	"content": "*Saldo atual suficiente* para os pagamentos de hoje.",
                	"id": "8e961915-84b5-42dc-8d07-8ec389b8d930",
                	"from": "<EMAIL>/iris-hosted-1",
                	"to": "<EMAIL>",
                	"metadata": {
                		"#stateName": "V3.1 - Exibir mensagem de saldo suficiente",
                		"#stateId": "f8555b75-0ea1-46a4-a2a3-1591beeac6e5",
                		"#messageId": "ABGHVSGYAAdJTwIQODiToLCUnWGRRVM31U-ikw",
                		"#previousStateId": "002c05a6-b01e-43cd-8847-bb88c27b1f63",
                		"#previousStateName": "V3 - Envia notificação de saldo",
                		"uber-trace-id": "6814fd77492f5674%3A698737c6341bfd5e%3Ae10b67f4ac7064ad%3A1",
                		"#uniqueId": "b93e4d3d-3947-48bd-b036-5e4cd685eb54",
                		"date_created": "1672160569998",
                		"#date_processed": "1672160569998",
                		"#tunnel.owner": "<EMAIL>",
                		"#tunnel.originator": "<EMAIL>",
                		"#tunnel.originalFrom": "<EMAIL>/iris-hosted-1",
                		"#tunnel.originalTo": "<EMAIL>",
                		"#envelope.storageDate": "2022-12-27T17:02:50Z"
                	}
                }
        """.trimIndent()

        @JvmStatic
        fun createPayloads(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(payload),
                Arguments.arguments(
                    """
                    {"content":{"contacts":[{"name":{"first_name":"Friday","formatted_name":"Friday - Atendimento","last_name":"- Atendimento"},"org":{"company":"Friday Pagamentos Digitais Ltda"},"phones":[{"phone":"+*************","type":"CELL","wa_id":"*************"}],"urls":[{"type":"WORK","url":"https://friday.ai"}]}],"type":"contacts"},"from":"<EMAIL>/iris-hosted-6","id":"df8587c6-b0b0-4f06-b0d4-79a64bbeefc7","metadata":{"#date_processed":"1672320226848","#envelope.storageDate":"2022-12-29T13:23:46Z","#messageId":"ABEGVUeRgXFhAgo-sG-ziYUmK08k","#previousStateId":"eaff48a3-d6d6-42fe-96ad-9163ea2d6739","#previousStateName":"V3 - Roteador","#stateId":"b82b424f-d88b-4ec5-9077-4ec0c044085b","#stateName":"V3 - Atendimento","#tunnel.originalFrom":"<EMAIL>/iris-hosted-6","#tunnel.originalTo":"<EMAIL>","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"e455d775-f631-48d3-8d84-4d8b5be1d21b","date_created":"1672320226848","uber-trace-id":"6f051bf381e0ee78%3A631475e98b219b70%3A373a521c469e1270%3A1"},"to":"<EMAIL>","type":"application/json"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """
                    {"content":"Atendimento","from":"<EMAIL>","id":"ABEGVUeRgXFhAgo-sG-ziYUmK08k","metadata":{"#date_processed":"1672320225726","#envelope.storageDate":"2022-12-29T13:23:45Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-6","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"21309e9a-e9a4-47d8-aeb1-3f1565f70af0","#wa.context.from":"*************","#wa.context.id":"gBEGVUeRgXFhAglpKtv4lge7AEU","#wa.interactive.button.id":"Atendimento","#wa.message.id":"gBEGVUeRgXFhAglpKtv4lge7AEU","#wa.timestamp":"1672320225","date_created":"1672320225634","uber-trace-id":"6f051bf381e0ee78%3A6f051bf381e0ee78%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """
                    {"extras":{"tunnel.originator":"<EMAIL>","tunnel.owner":"<EMAIL>"},"identity":"<EMAIL>","lastMessageDate":"2022-12-29T13:23:38.648Z","phoneNumber":"+*************","source":"WhatsApp"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """{"content":{"options":[{"text":"Consultar saldo"},{"text":"Atendimento"}],"scope":"immediate","text":"Como posso te ajudar? "},"from":"<EMAIL>/iris-hosted-5","id":"e91a9c03-6932-4ef4-a4b1-566558863b00","metadata":{"#date_processed":"1672320218798","#envelope.storageDate":"2022-12-29T13:23:38Z","#messageId":"ABEGVUeRgXFhAgo-sB4W-P4Lpc3R","#previousStateId":"eaff48a3-d6d6-42fe-96ad-9163ea2d6739","#previousStateName":"V3 - Roteador","#stateId":"a77cb6ad-ca41-4c17-94f0-8268ba64430b","#stateName":"V3 - Menu inicial","#tunnel.originalFrom":"<EMAIL>/iris-hosted-5","#tunnel.originalTo":"<EMAIL>","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"1953fc30-2f38-440b-b594-201727baeaff","date_created":"1672320218798","uber-trace-id":"7b8375feee84c5e9%3A5730add301f1ed99%3A1414de6765d4add6%3A1"},"to":"<EMAIL>","type":"application/vnd.lime.select+json"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """{"content":"descadastrar","from":"<EMAIL>","id":"ABEGVUeRgXFhAgo-sB4W-P4Lpc3R","metadata":{"#date_processed":"1672320218220","#envelope.storageDate":"2022-12-29T13:23:38Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-5","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"e585b7ed-8631-4d0d-a929-8a2c5bae7bb4","#wa.timestamp":"1672320217","date_created":"1672320217994","uber-trace-id":"7b8375feee84c5e9%3A7b8375feee84c5e9%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """{"content":{"template":{"components":[{"parameters":[{"text":"Itaú","type":"text"},{"text":"9077","type":"text"},{"text":"90002-6","type":"text"}],"type":"body"},{"index":0,"parameters":[{"text":"web/adicionar-saldo/PAYMENT-INTENT-d3d89e2f-bb66-43fa-9f4f-4a5ca59c3b62","type":"text"}],"sub_type":"url","type":"button"}],"language":{"code":"pt_BR","policy":"deterministic"},"name":"open_my_bank_app__1_0_0","namespace":"1d3afeae_c48c_4c2a_8d65_02b4bbf01f83"},"type":"template"},"from":"<EMAIL>/iris-hosted-2","id":"a6990ca7-ea67-4927-b2cb-c236bd87e971","metadata":{"#date_processed":"*************","#envelope.storageDate":"2022-12-29T13:17:00Z","#messageId":"ABEGVWKZZJMxAgo6ED3vDjLuDlHV","#previousStateId":"98b19d2d-f9b6-4644-a405-b0d5b816d5a9","#previousStateName":"V3 - Pagar com meu banco","#stateId":"********-a9b0-4001-9122-b8b2a2c71c18","#stateName":"V3 - Exibir link do último banco utilizado","#tunnel.originalFrom":"<EMAIL>/iris-hosted-2","#tunnel.originalTo":"<EMAIL>","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"109c7a25-dfc9-4f11-ac33-6c2e899f7c14","date_created":"*************","uber-trace-id":"30c37af2455c178f%3A84b196b41955a08f%3A4d8487a372ba7d00%3A1"},"to":"<EMAIL>","type":"application/json"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """{"content":"Pagar com meu banco","from":"<EMAIL>","id":"ABEGVWKZZJMxAgo6ED3vDjLuDlHV","metadata":{"#date_processed":"1672319818611","#envelope.storageDate":"2022-12-29T13:16:58Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-2","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"6cbb04ec-5a66-4e43-8924-92768e8a4e9f","#wa.context.from":"*************","#wa.context.id":"gBEGVWKZZJMxAgnqhzeRCk4q_uQ","#wa.interactive.button.id":"Pagarcommeubanco","#wa.message.id":"gBEGVWKZZJMxAgnqhzeRCk4q_uQ","#wa.timestamp":"1672319818","date_created":"1672319818522","uber-trace-id":"30c37af2455c178f%3A30c37af2455c178f%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"extras":{"tunnel.originator":"<EMAIL>","tunnel.owner":"<EMAIL>"},"identity":"<EMAIL>","lastMessageDate":"2022-12-29T13:16:46.120Z","name":"Luciano Veiga","phoneNumber":"+*************","source":"WhatsApp"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"content":{"options":[{"text":"Pix copia e cola"},{"text":"Pagar com meu banco"},{"text":"Usar outro método"}],"scope":"immediate","text":"Como prefere adicionar?"},"from":"<EMAIL>/iris-hosted-1","id":"8324a355-7a14-45d6-93bf-aa99749e6620","metadata":{"#date_processed":"*************","#envelope.storageDate":"2022-12-29T13:16:48Z","#messageId":"ABEGVWKZZJMxAgo6nCdJjj3Q-3un","#previousStateId":"af5139f2-125f-4b61-a942-e0c8a4677cbd","#previousStateName":"V3 - Verifica contas para cashin","#stateId":"465abf54-71f8-4556-b844-c4d4e1da50b9","#stateName":"V3 - Escolher forma de Cash-in com ITP","#tunnel.originalFrom":"<EMAIL>/iris-hosted-1","#tunnel.originalTo":"<EMAIL>","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"eaab7346-39f5-464d-97c5-d31b3e5e348d","date_created":"*************","uber-trace-id":"4cd62c7fdb872120%3A586d4bb28722584b%3A5091777cc1a7ffd8%3A1"},"to":"<EMAIL>","type":"application/vnd.lime.select+json"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """{"content":"ACCOUNT-7f59bdc2-a8ed-468f-aa46-eeeee25e5cb9#SEVEN_DAYS","from":"<EMAIL>","id":"ABEGVWKZZJMxAgo6nCdJjj3Q-3un","metadata":{"#blip.payload.content":"Saldo para 7 dias","#date_processed":"*************","#envelope.storageDate":"2022-12-29T13:16:45Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-1","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"3151433a-5150-44f6-bfce-a4a21677e285","#wa.context.from":"*************","#wa.context.id":"gBEGVWKZZJMxAgkp-yxpQkn3h4Q","#wa.timestamp":"**********","date_created":"*************","uber-trace-id":"4cd62c7fdb872120%3A4cd62c7fdb872120%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"content":"Usar outro método","from":"<EMAIL>","id":"ABGHVRGZlARSbwIKOkBdSaSTK-qYMw","metadata":{"#date_processed":"*************","#envelope.storageDate":"2022-12-29T11:58:00Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-1","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"12034c13-a378-4f24-b719-611902caade9","#wa.context.from":"*************","#wa.context.id":"gBGHVRGZlARSbwIJywO8WAgM2n3o","#wa.interactive.button.id":"Usaroutrométodo","#wa.message.id":"gBGHVRGZlARSbwIJywO8WAgM2n3o","#wa.timestamp":"1672315080","date_created":"1672315080458","uber-trace-id":"b1512d001b50714d%3Ab1512d001b50714d%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"content":{"template":{"language":{"code":"pt_BR","policy":"deterministic"},"name":"test_open_friday_app","namespace":"1d3afeae_c48c_4c2a_8d65_02b4bbf01f83"},"type":"template"},"from":"<EMAIL>/iris-hosted-1","id":"5ee93d24-da46-4c1e-9fca-665d7c6cd439","metadata":{"#date_processed":"1672315080972","#envelope.storageDate":"2022-12-29T11:58:01Z","#messageId":"ABGHVRGZlARSbwIKOkBdSaSTK-qYMw","#previousStateId":"465abf54-71f8-4556-b844-c4d4e1da50b9","#previousStateName":"V3 - Escolher forma de Cash-in com ITP","#stateId":"a74ed3bf-b46c-42ff-af4c-dcfab8303377","#stateName":"V3 - Abrir App Friday","#tunnel.originalFrom":"<EMAIL>/iris-hosted-1","#tunnel.originalTo":"<EMAIL>","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"837f1f4d-1d0a-491d-827d-c0a1f78b06d2","date_created":"1672315080972","uber-trace-id":"b1512d001b50714d%3A4af30390b70e0681%3A5372c87d41267bdd%3A1"},"to":"<EMAIL>","type":"application/json"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"content":{"options":[{"text":"Pix copia e cola"},{"text":"Pagar com meu banco"},{"text":"Usar outro método"}],"scope":"immediate","text":"Como prefere adicionar?"},"from":"<EMAIL>/iris-hosted-4","id":"6d469d91-02a1-4769-ad98-fd084a6a19fd","metadata":{"#date_processed":"*************","#envelope.storageDate":"2022-12-29T11:57:52Z","#messageId":"ABGHVRGZlARSbwIKOr4cD7WzxoBrJA","#previousStateId":"af5139f2-125f-4b61-a942-e0c8a4677cbd","#previousStateName":"V3 - Verifica contas para cashin","#stateId":"465abf54-71f8-4556-b844-c4d4e1da50b9","#stateName":"V3 - Escolher forma de Cash-in com ITP","#tunnel.originalFrom":"<EMAIL>/iris-hosted-4","#tunnel.originalTo":"<EMAIL>","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"0d2ccdf0-006a-4950-954f-77dd772cba35","date_created":"*************","uber-trace-id":"79ae88b04faf85c8%3Aa9633f1566a8c422%3A186f20ed95d60e83%3A1"},"to":"<EMAIL>","type":"application/vnd.lime.select+json"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"content":"ACCOUNT-90dc316c-6709-417f-9229-d914a0a150f3#TODAY","from":"<EMAIL>","id":"ABGHVRGZlARSbwIKOr4cD7WzxoBrJA","metadata":{"#blip.payload.content":"Saldo para hoje","#date_processed":"**********900","#envelope.storageDate":"2022-12-29T11:57:48Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-4","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"a25df246-ecf6-416b-a1f1-2c383d514b7d","#wa.context.from":"*************","#wa.context.id":"gBGHVRGZlARSbwIJf07PFaKo84ol","#wa.timestamp":"**********","date_created":"**********814","uber-trace-id":"79ae88b04faf85c8%3A79ae88b04faf85c8%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"content":"{\"onePixPayId\":\"OPP-d3b67f8c-2fec-487d-9831-fddbde627615\",\"paymentLimitTimestamp\":**********}","from":"<EMAIL>","id":"ABGHVRSZFjAQHwIKOoavacoHhUb9DA","metadata":{"#blip.payload.content":"Pagar tudo com 1 Pix","#date_processed":"1672314309975","#envelope.storageDate":"2022-12-29T11:45:10Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-6","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"c1b06639-301b-4099-9fbc-bd6eed852b81","#wa.context.from":"*************","#wa.context.id":"gBGHVRSZFjAQHwIJamaS6fpx-9Cg","#wa.timestamp":"1672314309","date_created":"1672314309744","uber-trace-id":"ed5bd7c05dc2148d%3Aed5bd7c05dc2148d%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """{"content":{"options":[{"text":"Pix copia e cola"},{"text":"Pagar com meu banco"},{"text":"Usar outro método"}],"scope":"immediate","text":"Como prefere adicionar?"},"from":"<EMAIL>/iris-hosted-4","id":"2ab1defd-6702-4eda-b760-9aaa9ee44633","metadata":{"#date_processed":"1672312908082","#envelope.storageDate":"2022-12-29T11:21:48Z","#messageId":"ABGHVRSZEjRInwIQ4rskk18i17yTMrkbd5lE0w","#previousStateId":"af5139f2-125f-4b61-a942-e0c8a4677cbd","#previousStateName":"V3 - Verifica contas para cashin","#stateId":"465abf54-71f8-4556-b844-c4d4e1da50b9","#stateName":"V3 - Escolher forma de Cash-in com ITP","#tunnel.originalFrom":"<EMAIL>/iris-hosted-4","#tunnel.originalTo":"<EMAIL>","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"0103688a-8072-4ee4-82de-149cb920d87b","date_created":"1672312908082","uber-trace-id":"b0b79e53f85faaba%3Af830d0b26a7a68c3%3A7328d2a1fc89a1b3%3A1"},"to":"<EMAIL>","type":"application/vnd.lime.select+json"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"extras":{"tunnel.originator":"<EMAIL>","tunnel.owner":"<EMAIL>"},"identity":"<EMAIL>","lastMessageDate":"2022-12-29T11:21:45.265Z","phoneNumber":"+*************","source":"WhatsApp"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"content":"ACCOUNT-b12f49a5-2928-41fe-b44b-63733b64cf08#SEVEN_DAYS","from":"<EMAIL>","id":"ABGHVRSZEjRInwIQ4rskk18i17yTMrkbd5lE0w","metadata":{"#blip.payload.content":"Saldo para 7 dias","#date_processed":"**********809","#envelope.storageDate":"2022-12-29T11:21:45Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-4","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"ab122d50-e5ef-42bd-83dd-f7c9b2a120a3","#wa.context.from":"*************","#wa.context.id":"gBGHVRSZEjRInwIJL4kjbd9-BWxs","#wa.timestamp":"**********","date_created":"*************","uber-trace-id":"b0b79e53f85faaba%3Ab0b79e53f85faaba%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"content":{"options":[{"text":"Consultar saldo"},{"text":"Atendimento"}],"scope":"immediate","text":"Como posso te ajudar? "},"from":"<EMAIL>/iris-hosted-2","id":"ad53ff0f-a52d-48fa-be29-c55b98fb1900","metadata":{"#date_processed":"1672311745358","#envelope.storageDate":"2022-12-29T11:02:25Z","#messageId":"ABGHVSGXBhaZDwIQzZSxvyl_ekVwRpvW7Y6Yyg","#previousStateId":"eaff48a3-d6d6-42fe-96ad-9163ea2d6739","#previousStateName":"V3 - Roteador","#stateId":"a77cb6ad-ca41-4c17-94f0-8268ba64430b","#stateName":"V3 - Menu inicial","#tunnel.originalFrom":"<EMAIL>/iris-hosted-2","#tunnel.originalTo":"<EMAIL>","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"a2bb6a28-28c7-47a1-8d39-0506d4ddba0f","date_created":"1672311745358","uber-trace-id":"d17a66151fd28a35%3A4ec6abe76e1ac787%3A6889b940ea5fded6%3A1"},"to":"<EMAIL>","type":"application/vnd.lime.select+json"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """{"content":"Muito obrigado pelo seu contato!\nResponderei assim que possível.\n\nEm caso de roubo, furto ou assistência 24h ligue 0800-605-4381.\n\nPara realizar uma cotação me informe:\n\n• Nome;\n• Modelo e ano (ex: Palio 1.0 Fire 4p 12/13) ou placa;\n• Localidade.\n \n*Se for urgente me ligue.*\n\n*_Rodrigo Fernandes_*\n_APVS Brasil_","from":"<EMAIL>","id":"ABGHVSGXBhaZDwIQzZSxvyl_ekVwRpvW7Y6Yyg","metadata":{"#date_processed":"1672311745077","#envelope.storageDate":"2022-12-29T11:02:25Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-2","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"51f8206d-2308-4ddb-b941-1a30829724f8","#wa.timestamp":"1672311744","date_created":"1672311744740","uber-trace-id":"d17a66151fd28a35%3Ad17a66151fd28a35%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}

                    """.trimIndent(),
                ),
                Arguments.arguments(
                    """{"content":{"options":[{"text":"Consultar saldo"},{"text":"Atendimento"}],"scope":"immediate","text":"Como posso te ajudar? "},"from":"<EMAIL>/iris-hosted-4","id":"9cfb071e-9027-42b7-b274-9679bbd8fad2","metadata":{"#date_processed":"1672305611630","#envelope.storageDate":"2022-12-29T09:20:11Z","#messageId":"ABGHVRmZJ3WIfwIKL7zF2IpCGRHhpQ","#previousStateId":"eaff48a3-d6d6-42fe-96ad-9163ea2d6739","#previousStateName":"V3 - Roteador","#stateId":"a77cb6ad-ca41-4c17-94f0-8268ba64430b","#stateName":"V3 - Menu inicial","#tunnel.originalFrom":"<EMAIL>/iris-hosted-4","#tunnel.originalTo":"<EMAIL>","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"d2b91f8f-a346-4a3b-97bc-ec2e25edc5dd","date_created":"1672305611630","uber-trace-id":"299f357bc6526977%3Acc802bb946e65e53%3A70c11c58c51d6e85%3A1"},"to":"<EMAIL>","type":"application/vnd.lime.select+json"}
                    """.trimIndent(),
                ),

                Arguments.arguments(
                    """
{"content":"Olá, como vai? Me conta como posso ajudar você, assim que possível responderei! Preferimos o recebimento de mensagens escritas (sem áudios).\n\n☺️������������⚖️������\n\nLeandro Antunes","from":"<EMAIL>","id":"ABGHVRmZJ3WIfwIKL7zF2IpCGRHhpQ","metadata":{"#date_processed":"1672305611315","#envelope.storageDate":"2022-12-29T09:20:11Z","#tunnel.originalFrom":"<EMAIL>/iris-hosted-4","#tunnel.originalTo":"<EMAIL>/*************%40wa.gw.msging.net","#tunnel.originator":"<EMAIL>","#tunnel.owner":"<EMAIL>","#uniqueId":"c9922bf7-dc6e-4a56-b6c6-31241778a595","#wa.timestamp":"1672305610","date_created":"1672305611099","uber-trace-id":"299f357bc6526977%3A299f357bc6526977%3A0%3A1"},"to":"<EMAIL>","type":"text/plain"}
                    """.trimIndent(),
                ),
            )
        }
    }
}