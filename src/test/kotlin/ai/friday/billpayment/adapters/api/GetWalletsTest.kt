package ai.friday.billpayment.adapters.api

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDbRepository
import ai.friday.billpayment.adapters.dynamodb.WalletBillCategoryDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.integrations.PixKeyRepository
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletPersonType
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.generateCookie
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.paymentMethodId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.withGivenDateTime
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

@MicronautTest(environments = [FRIDAY_ENV])
class GetWalletsTest(embeddedServer: EmbeddedServer, val dynamoDB: AmazonDynamoDB) {

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val walletRepository = WalletDbRepository(dynamoDbDAO, mockk())

    private val walletBillCategoryRepository = WalletBillCategoryDbRepository(WalletBillCategoryDynamoDAO(setupDynamoDB()))

    private val walletBillCategories = listOf(
        WalletBillCategory(
            walletId = primaryWalletId,
            categoryId = PFMCategoryId(value = "EDUCACAO"),
            name = "EDUCACAO",
            icon = "EDUCACAO",
            enabled = true,
            default = true,
        ),
    )

    private val walletBillCategoryTOs = listOf(
        WalletBillCategoryTO(
            billCategoryId = "EDUCACAO",
            name = "EDUCACAO",
            icon = "EDUCACAO",
            enabled = true,
            default = true,
        ),
    )

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk {
        every {
            findWalletCategories(any())
        } returns walletBillCategoryTOs.map { category ->
            WalletBillCategory(name = category.name, walletId = primaryWalletId, categoryId = PFMCategoryId(category.billCategoryId), icon = category.icon, enabled = category.enabled, default = category.default)
        }
    }

    @MockBean(PixKeyRepository::class)
    fun pixKeyRepository() = pixKeyRepository
    private val pixKeyRepository: PixKeyRepository = mockk {
        every {
            list(any())
        } returns listOf(PixKey("7f4eb143-4f88-4723-a421-47441572799f", PixKeyType.EVP), PixKey("***********", PixKeyType.CPF))
    }

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        setupDynamoDB()
        walletBillCategories.forEach {
            walletBillCategoryRepository.save(it)
        }
    }

    @Test
    fun `should list primary wallet with only founder member`() {
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, defaultWalletId = primaryWalletId)

        val now = getZonedDateTime()
        withGivenDateTime(now) {
            walletRepository.save(primaryWallet)
            walletRepository.saveLimit(
                walletId = primaryWallet.id,
                type = DailyPaymentLimitType.DAILY,
                currentAmount = 4000,
                lastAmount = 2000,
            )
            walletRepository.saveLimit(
                walletId = primaryWallet.id,
                type = DailyPaymentLimitType.NIGHTTIME,
                currentAmount = 4000,
                lastAmount = 2000,
            )

            val request = buildListRequest(generateCookie(founderAccountId, Role.OWNER))
            val response = client.toBlocking()
                .exchange(request, Argument.listOf(WalletTO::class.java), Argument.of(ResponseTO::class.java))

            response.status() shouldBe HttpStatus.OK
            val wallets = response.body.get()
            wallets.size shouldBe 1

            with(wallets.find(primaryWallet.id)) {
                name shouldBe primaryWallet.name
                members.shouldContainExactlyInAnyOrder(founder.asMemberTO())
                theme shouldBe 1
                thumbnailUrl shouldBe ""
                isDefault shouldBe true
                canBeSetAsDefault shouldBe true
                type shouldBe WalletType.PRIMARY
                billCategories.shouldContainExactlyInAnyOrder(walletBillCategoryTOs)
                evpPixKey shouldBe PixKeyRequestTO("7f4eb143-4f88-4723-a421-47441572799f", PixKeyType.EVP)
            }
        }
    }

    @Test
    fun `should list secondary wallet with founder and participant`() {
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, defaultWalletId = primaryWalletId)
        walletRepository.save(secondaryWallet)

        val request = buildListRequest(generateCookie(founderAccountId, Role.OWNER))
        val response = client.toBlocking()
            .exchange(request, Argument.listOf(WalletTO::class.java), Argument.of(ResponseTO::class.java))

        response.status() shouldBe HttpStatus.OK
        val wallets = response.body.get()
        wallets.size shouldBe 1
        with(wallets.find(secondaryWallet.id)) {
            name shouldBe secondaryWallet.name
            members.shouldContainExactlyInAnyOrder(founder.asMemberTO(), assistant.asMemberTO())
            isDefault shouldBe false
            canBeSetAsDefault shouldBe true
            type shouldBe WalletType.SECONDARY
            evpPixKey shouldBe PixKeyRequestTO("7f4eb143-4f88-4723-a421-47441572799f", PixKeyType.EVP)
        }
    }

    @Test
    fun `should list all wallets and members for FOUNDER`() {
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, defaultWalletId = primaryWalletId)
        walletRepository.save(primaryWallet)
        walletRepository.save(secondaryWallet)

        val request = buildListRequest(generateCookie(founderAccountId, Role.OWNER))
        val response = client.toBlocking()
            .exchange(request, Argument.listOf(WalletTO::class.java), Argument.of(ResponseTO::class.java))

        response.status() shouldBe HttpStatus.OK
        val wallets = response.body.get()
        wallets.size shouldBe 2
        with(wallets.find(primaryWallet.id)) {
            name shouldBe primaryWallet.name
            members.shouldContainExactlyInAnyOrder(founder.asMemberTO())
            isDefault shouldBe true
            canBeSetAsDefault shouldBe true
            type shouldBe WalletType.PRIMARY
            personType shouldBe WalletPersonType.PHYSICAL_PERSON
            evpPixKey shouldBe PixKeyRequestTO("7f4eb143-4f88-4723-a421-47441572799f", PixKeyType.EVP)
        }
        with(wallets.find(secondaryWallet.id)) {
            name shouldBe secondaryWallet.name
            members.shouldContainExactlyInAnyOrder(founder.asMemberTO(), assistant.asMemberTO())
            isDefault shouldBe false
            canBeSetAsDefault shouldBe true
            type shouldBe WalletType.SECONDARY
            personType shouldBe WalletPersonType.PHYSICAL_PERSON
            evpPixKey shouldBe PixKeyRequestTO("7f4eb143-4f88-4723-a421-47441572799f", PixKeyType.EVP)
        }
    }

    @Test
    fun `deve retornar o personType da carteira de acordo com o tipo de documento do founder`() {
        loadAccountIntoDb(dynamoDB, accountId = legalPersonFounder.accountId, defaultWalletId = legalPersonWallet.id)
        val now = getZonedDateTime()
        withGivenDateTime(now) {
            walletRepository.save(legalPersonWallet)
            walletRepository.saveLimit(
                walletId = legalPersonWallet.id,
                type = DailyPaymentLimitType.DAILY,
                currentAmount = 4000,
                lastAmount = 2000,
            )
            walletRepository.saveLimit(
                walletId = legalPersonWallet.id,
                type = DailyPaymentLimitType.NIGHTTIME,
                currentAmount = 4000,
                lastAmount = 2000,
            )

            val request = buildListRequest(generateCookie(legalPersonFounder.accountId, Role.OWNER))
            val response = client.toBlocking()
                .exchange(request, Argument.listOf(WalletTO::class.java), Argument.of(ResponseTO::class.java))

            response.status() shouldBe HttpStatus.OK
            val wallets = response.body.get()
            wallets.size shouldBe 1

            with(wallets.find(legalPersonWallet.id)) {
                personType shouldBe WalletPersonType.LEGAL_PERSON
            }
        }
    }

    @Test
    fun `should not list wallet when member is removed`() {
        loadAccountIntoDb(dynamoDB, accountId = assistantAccountId)
        val tertiaryWallet = Wallet(
            WalletId("WALLET-${UUID.randomUUID()}"),
            "carteira compartilhada",
            listOf(founder, assistant),
            10,
            WalletStatus.ACTIVE,
            WalletType.PRIMARY,
            paymentMethodId,
        )

        walletRepository.save(secondaryWallet)
        walletRepository.save(tertiaryWallet)
        walletRepository.upsertMember(secondaryWallet.id, assistant.copy(status = MemberStatus.REMOVED))

        val request = buildListRequest(generateCookie(assistantAccountId, Role.OWNER))
        val response = client.toBlocking()
            .exchange(request, Argument.listOf(WalletTO::class.java), Argument.of(ResponseTO::class.java))

        response.status() shouldBe HttpStatus.OK
        val wallets = response.body.get()
        wallets.size shouldBe 1
        with(wallets.find(tertiaryWallet.id)) {
            members.shouldContainExactlyInAnyOrder(founder.asMemberTO(), assistant.asMemberTO())
        }
    }

    @Test
    fun `should list secondary wallet and members for PARTICIPANT`() {
        loadAccountIntoDb(dynamoDB, accountId = assistantAccountId)
        walletRepository.save(primaryWallet)
        walletRepository.save(secondaryWallet)

        val request = buildListRequest(generateCookie(assistantAccountId, Role.OWNER))
        val response = client.toBlocking()
            .exchange(request, Argument.listOf(WalletTO::class.java), Argument.of(ResponseTO::class.java))

        response.status() shouldBe HttpStatus.OK
        val wallets = response.body.get()
        wallets.size shouldBe 1
        with(wallets.find(secondaryWallet.id)) {
            name shouldBe secondaryWallet.name
            members.shouldContainExactlyInAnyOrder(founder.asMemberTO(), assistant.asMemberTO())
            isDefault shouldBe false
            canBeSetAsDefault shouldBe false
            type shouldBe WalletType.SECONDARY
        }
    }

    @Test
    fun `should return not found when wallet does not exist`() {
        val request = buildGetRequest(generateCookie(assistantAccountId, Role.OWNER), WalletId("DOES_NOT_EXIST"))

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(WalletTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return forbidden when user is not an wallet member`() {
        walletRepository.save(primaryWallet)

        val request = buildGetRequest(generateCookie(AccountId("NOT_A_MEMBER"), Role.OWNER), primaryWalletId)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(WalletTO::class.java), Argument.of(ResponseTO::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return isDefault true and canBeSetAsDefault true when wallet is default for founder`() {
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, defaultWalletId = primaryWalletId)
        walletRepository.save(primaryWallet)

        val request = buildGetRequest(generateCookie(founderAccountId, Role.OWNER), primaryWalletId)

        val response = client.toBlocking()
            .exchange(request, Argument.of(WalletTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body()!!) {
            isDefault shouldBe true
            canBeSetAsDefault shouldBe true
            name shouldBe primaryWallet.name
            type shouldBe WalletType.PRIMARY
            members.shouldContainExactlyInAnyOrder(founder.asMemberTO())
            billCategories.shouldContainExactlyInAnyOrder(walletBillCategoryTOs)
        }
    }

    @Test
    fun `deve retornar a chave evp quando existir`() {
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, defaultWalletId = primaryWalletId)
        walletRepository.save(primaryWallet)

        val request = buildGetRequest(generateCookie(founderAccountId, Role.OWNER), primaryWalletId)

        val response = client.toBlocking()
            .exchange(request, Argument.of(WalletTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body()!!.evpPixKey!!) {
            value shouldBe "7f4eb143-4f88-4723-a421-47441572799f"
            type shouldBe PixKeyType.EVP
        }
    }

    @ParameterizedTest
    @MethodSource("canBeSetAsDefaultValues")
    fun `should return isDefault false when wallet is not default for member`(
        accountId: AccountId,
        canBeSetAsDefaultForAccount: Boolean,
    ) {
        loadAccountIntoDb(dynamoDB, accountId = founderAccountId, defaultWalletId = primaryWalletId)
        loadAccountIntoDb(dynamoDB, accountId = assistantAccountId)

        walletRepository.save(secondaryWallet)

        val request = buildGetRequest(generateCookie(accountId, Role.OWNER), secondaryWallet.id)

        val response = client.toBlocking()
            .exchange(request, Argument.of(WalletTO::class.java), Argument.of(ResponseTO::class.java))

        response.status shouldBe HttpStatus.OK
        with(response.body()!!) {
            isDefault shouldBe false
            canBeSetAsDefault shouldBe canBeSetAsDefaultForAccount
            name shouldBe secondaryWallet.name
            type shouldBe WalletType.SECONDARY
            members.shouldContainExactlyInAnyOrder(founder.asMemberTO(), assistant.asMemberTO())
        }
    }

    private fun List<WalletTO>.find(walletId: WalletId) = this.filter { it.id == walletId.value }
        .ifEmpty { throw AssertionError("List does not wave a wallet with id ${walletId.value}. Available ids: ${this.map { it.id }}") }
        .first()

    private fun Member.asMemberTO() = MemberTO(
        name = name,
        alias = name,
        document = document,
        accountId = accountId.value,
        type = type,
        permissions = MemberPermissionsTO(
            viewBills = permissions.viewBills,
            scheduleBills = permissions.scheduleBills,
            cashinEnabled = permissions.cashinEnabled,
            founderContactsEnabled = permissions.founderContactsEnabled,
            manageMembers = permissions.manageMembers,
            viewBalance = permissions.viewBalance,
            viewStatement = permissions.viewBills == BillPermission.ALL_BILLS && permissions.viewBalance,
        ),
    )

    private fun buildListRequest(cookie: Cookie): MutableHttpRequest<WalletTO> {
        return HttpRequest.GET<WalletTO>("/wallet")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    private fun buildGetRequest(cookie: Cookie, walletId: WalletId): MutableHttpRequest<WalletTO> {
        return HttpRequest.GET<WalletTO>("/wallet/${walletId.value}")
            .cookie(cookie).header("X-API-VERSION", "2")
    }

    companion object {
        private val walletFixture = WalletFixture()
        private val founder = walletFixture.founder
        private val legalPersonFounder = walletFixture.founder.copy(document = "**************")
        private val assistant = walletFixture.assistant
        private val primaryWallet = walletFixture.buildWallet()
        private val secondaryWallet =
            walletFixture.buildWallet(otherMembers = listOf(walletFixture.assistant), type = WalletType.SECONDARY)
        private val legalPersonWallet = walletFixture.buildWallet(walletFounder = legalPersonFounder)
        private val primaryWalletId = primaryWallet.id
        private val assistantAccountId = walletFixture.assistantAccount.accountId
        private val founderAccountId = walletFixture.founderAccount.accountId

        @JvmStatic
        fun canBeSetAsDefaultValues(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(founderAccountId, true),
                Arguments.arguments(assistantAccountId, false),
            )
        }
    }
}