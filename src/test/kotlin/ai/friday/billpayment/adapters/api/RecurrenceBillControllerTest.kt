package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.BillImageProvider
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import ai.friday.billpayment.app.recurrence.RecurrenceCreationError
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceResult
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.generateDateSequence
import ai.friday.billpayment.createInvoiceTO
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.pixKeyDetails
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.dateFormat
import arrow.core.Either
import io.kotest.matchers.collections.shouldContainExactlyInAnyOrder
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.micronaut.security.authentication.Authentication
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import java.math.BigInteger
import java.time.LocalDate
import java.time.temporal.TemporalAdjusters
import java.util.stream.Stream
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

class RecurrenceBillControllerTest {

    val recurrenceService: BillRecurrenceService = mockk()

    val jsonArray = listOf(Role.OWNER.name)

    private val walletFixture = WalletFixture()
    private val wallet =
        walletFixture.buildWallet(
            otherMembers = listOf(
                walletFixture.assistant,
                walletFixture.participant,
            ),
        )

    val authentication: Authentication = Authentication.build(
        wallet.founder.accountId.value,
        jsonArray,
        mapOf<String, Any>(AUTHENTICATION_ATTRIBUTE_WALLET to wallet),
    )

    val accountService: AccountService = mockk() {
        every {
            findAccountById(any())
        } returns walletFixture.founderAccount
    }

    val recurrence = weeklyRecurrenceNoEndDate

    val pixKeyManagement: PixKeyManagement = mockk() {
        every {
            findKeyDetailsCacheable(any(), any())
        } returns PixKeyDetailsResult(pixKeyDetails = pixKeyDetails, e2e = "123")
    }

    private val billController = BillController(
        createBillService = mockk(),
        updateBillService = mockk(),
        recurrenceService = recurrenceService,
        pixKeyManagement = pixKeyManagement,
        accountService = accountService,
        tedConfiguration = mockk(relaxed = true),
        featuresRepository = mockk(relaxed = true),
        fichaCompensacaoService = mockk(),
        billImageProvider = BillImageProvider(emptyList()),
    )

    val dueDate = getLocalDate().with(TemporalAdjusters.firstDayOfNextMonth())

    private val limitDate = "2021-12-31"

    private val pixBankAccountRequestTO = CreatePixTO(
        amount = 10,
        description = "description",
        dueDate = dueDate.format(dateFormat),
        recipient = RequestPixRecipientTO(
            id = "RECIPIENT-123",
            name = "Ze",
            document = "***********",
            documentType = "CPF",
            bankDetails = RecipientBankDetailsTO(
                accountType = AccountType.CHECKING,
                routingNo = 1,
                accountNo = BigInteger("*********"),
                accountDv = "2",
                ispb = "********",
            ),
            alias = "Ze",
        ),
        recurrence = RecurrenceRequestTO(
            frequency = RecurrenceFrequency.MONTHLY,
            endDate = dueDate.plusMonths(1).format(dateFormat),
        ),
    )

    @ParameterizedTest
    @MethodSource("createRecurrenceCreationError")
    fun `should return bad request when recurrenceCreationError occurs`(
        recurrenceCreationError: RecurrenceCreationError,
    ) {
        every {
            recurrenceService.create(any(), true)
        } returns Either.Left(recurrenceCreationError)

        val response = billController.postPix(
            createPixTO = pixBankAccountRequestTO,
            dryRun = true,
            authentication = authentication,
        )
        response.status shouldBe HttpStatus.BAD_REQUEST
    }

    @Test
    fun `should send recipientId when available on pix recurrence`() {
        val recurrenceSlot = slot<BillRecurrence>()
        every {
            recurrenceService.create(capture(recurrenceSlot), false)
        } returns Either.Right(RecurrenceResult(recurrence))

        billController.postPix(createPixTO = pixBankAccountRequestTO, dryRun = false, authentication = authentication)

        recurrenceSlot.captured.contactId?.value shouldBe pixBankAccountRequestTO.recipient.id
    }

    @Test
    fun `should send recipientId when available on invoice recurrence`() {
        val recurrenceSlot = slot<BillRecurrence>()
        every {
            recurrenceService.create(capture(recurrenceSlot), false)
        } returns Either.Right(RecurrenceResult(recurrence))

        val createInvoiceTO = createInvoiceTO.copy(
            recipient = createInvoiceTO.recipient.copy(id = "RECIPIENT-456"),
            dueDate = limitDate,
            recurrence = RecurrenceRequestTO(frequency = RecurrenceFrequency.WEEKLY),
        )

        billController.postInvoice(createInvoiceTO = createInvoiceTO, dryRun = false, authentication = authentication)

        recurrenceSlot.captured.contactId?.value shouldBe createInvoiceTO.recipient.id
    }

    @Test
    fun `deve calcular corretamente as ocorrencias`() {
        val rule = RecurrenceRule(
            startDate = LocalDate.of(2023, 9, 27),
            frequency = RecurrenceFrequency.MONTHLY,
            pattern = "COUNT=12",
        )

        val occurrence2 = rule.calculateOccurrence(LocalDate.of(2023, 10, 27))
        val occurrence3 = rule.calculateOccurrence(LocalDate.of(2023, 11, 27))
        val occurrence4 = rule.calculateOccurrence(LocalDate.of(2023, 12, 27))
        val occurrence5 = rule.calculateOccurrence(LocalDate.of(2024, 1, 27))

        occurrence2 shouldBe 2
        occurrence3 shouldBe 3
        occurrence4 shouldBe 4
        occurrence5 shouldBe 5
    }

    @Test
    fun `deve deve gerar a sequencia de datas para a recorrência semanal`() {
        val rule = RecurrenceRule(
            startDate = LocalDate.of(2024, 7, 4),
            frequency = RecurrenceFrequency.WEEKLY,
            pattern = "COUNT=6",
        )

        val occurrences = rule.generateDateSequence(
            limitDate = LocalDate.of(2025, 12, 31),
        )

        occurrences.size shouldBe 6

        occurrences shouldContainExactlyInAnyOrder listOf(
            LocalDate.of(2024, 7, 4),
            LocalDate.of(2024, 7, 11),
            LocalDate.of(2024, 7, 18),
            LocalDate.of(2024, 7, 25),
            LocalDate.of(2024, 8, 1),
            LocalDate.of(2024, 8, 8),
        )
    }

    @Test
    fun `deve deve gerar a sequencia de datas para a recorrência bisemanal`() {
        val rule = RecurrenceRule(
            startDate = LocalDate.of(2024, 7, 4),
            frequency = RecurrenceFrequency.BIWEEKLY,
            pattern = "COUNT=4",
        )

        val occurrences = rule.generateDateSequence(
            limitDate = LocalDate.of(2025, 12, 31),
        )

        occurrences.size shouldBe 4

        occurrences shouldContainExactlyInAnyOrder listOf(
            LocalDate.of(2024, 7, 4),
            LocalDate.of(2024, 7, 18),
            LocalDate.of(2024, 8, 1),
            LocalDate.of(2024, 8, 15),
        )
    }

    @Test
    fun `deve deve gerar a sequencia de datas para a recorrência mensal`() {
        val rule = RecurrenceRule(
            startDate = LocalDate.of(2024, 7, 4),
            frequency = RecurrenceFrequency.MONTHLY,
            pattern = "COUNT=10",
        )

        val occurrences = rule.generateDateSequence(
            limitDate = LocalDate.of(2025, 12, 31),
        )

        occurrences.size shouldBe 10

        occurrences shouldContainExactlyInAnyOrder listOf(
            LocalDate.of(2024, 7, 4),
            LocalDate.of(2024, 8, 4),
            LocalDate.of(2024, 9, 4),
            LocalDate.of(2024, 10, 4),
            LocalDate.of(2024, 11, 4),
            LocalDate.of(2024, 12, 4),
            LocalDate.of(2025, 1, 4),
            LocalDate.of(2025, 2, 4),
            LocalDate.of(2025, 3, 4),
            LocalDate.of(2025, 4, 4),
        )
    }

    @Test
    fun `deve deve gerar a sequencia de datas para a recorrência mensal sem ultrapassar a data limite`() {
        val rule = RecurrenceRule(
            startDate = LocalDate.of(2024, 7, 4),
            frequency = RecurrenceFrequency.MONTHLY,
            pattern = "COUNT=10",
        )

        val occurrences = rule.generateDateSequence(
            limitDate = LocalDate.of(2024, 12, 31),
        )

        occurrences.size shouldBe 6

        occurrences shouldContainExactlyInAnyOrder listOf(
            LocalDate.of(2024, 7, 4),
            LocalDate.of(2024, 8, 4),
            LocalDate.of(2024, 9, 4),
            LocalDate.of(2024, 10, 4),
            LocalDate.of(2024, 11, 4),
            LocalDate.of(2024, 12, 4),
        )
    }

    @Test
    fun `deve deve gerar a sequencia de datas para a recorrência mensal se o count for menor que 2`() {
        assertThrows<IllegalArgumentException> {
            val rule = RecurrenceRule(
                startDate = LocalDate.of(2024, 7, 4),
                frequency = RecurrenceFrequency.MONTHLY,
                pattern = "COUNT=1",
            )
            rule.generateDateSequence(
                limitDate = LocalDate.of(2024, 12, 31),
            )
        }
    }

    companion object {

        @JvmStatic
        fun createRecurrenceCreationError(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(RecurrenceCreationError.DueDateAfterLimit),
                Arguments.of(RecurrenceCreationError.DueDateInThePast),
            )
        }
    }
}