package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.LoginDbRepository
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.createBillPaymentTable
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class ChatbotSecurityTimelineEntriesFilterTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {
    @Controller("/chatbot")
    @Secured(SecurityRule.IS_ANONYMOUS)
    open class ChatbotTestController {
        @Get("/echo")
        fun echo(authentication: Authentication): HttpResponse<*> {
            return HttpResponse.ok(authentication.name)
        }
    }

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val loginRepository = LoginDbRepository(dynamoDbDAO = DynamoDbDAO(amazonDynamoDB = dynamoDB))

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `deve retornar forbidden quando nao tiver o header user external id`() {
        val request = buildRequest(null)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `deve retornar forbidden quando nao encontrar o usuario`() {
        val request = buildRequest("UNKNOWN-USER")

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking()
                .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `deve retornar o account id associado ao usuario externo`() {
        val externalUserId = "<EMAIL>"
        val accountId = AccountId(ACCOUNT_ID)

        loginRepository.createLogin(
            providerUser = ProviderUser(
                id = accountId.value,
                providerName = ProviderName.WHATSAPP,
                username = "",
                emailAddress = EmailAddress(email = externalUserId),
            ),
            id = accountId,
            role = Role.OWNER,
        )

        val request = buildRequest(externalUserId)

        val response = client.toBlocking()
            .exchange(request, Argument.of(String::class.java), Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.body.get() shouldBe accountId.value
    }

    private fun buildRequest(externalUserId: String?): MutableHttpRequest<*> {
        return HttpRequest.GET<Any>("/chatbot/echo")
            .also { request ->
                externalUserId?.let { request.header("X-EXTERNAL-USER-ID", it) }
            }
    }
}