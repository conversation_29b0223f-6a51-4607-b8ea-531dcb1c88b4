package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.DDABillTO
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.app.integrations.MessagePublisher
import io.kotest.matchers.shouldBe
import io.micronaut.http.HttpStatus
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.Test

class DDAControllerTest {
    private val messagePublisher = mockk<MessagePublisher>(relaxed = true)

    private val sqsConfiguration = mockk<SQSMessageHandlerConfiguration> {
        every { ddaBills } returns "test-dda-bills-queue"
    }

    private val controller = DDAController(messagePublisher, sqsConfiguration)

    private val ddaBillTO = mockk<DDABillTO>(relaxed = true)

    @Test
    fun `should receive DDA bill and send to queue successfully`() {
        // When
        val response = controller.receiveDDABill(ddaBillTO)

        // Then
        response.status shouldBe HttpStatus.NO_CONTENT

        verify(exactly = 1) {
            messagePublisher.sendMessage(
                queueName = "test-dda-bills-queue",
                body = ddaBillTO,
            )
        }
    }
}