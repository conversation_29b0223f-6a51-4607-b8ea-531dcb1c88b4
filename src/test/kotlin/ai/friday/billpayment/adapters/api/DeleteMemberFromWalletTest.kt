package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.MockEventPublisher
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberRemoved
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletEvent
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.toMember
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.mockk
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows

@MicronautTest(environments = [FRIDAY_ENV])
class DeleteMemberFromWalletTest(embeddedServer: EmbeddedServer) {

    @MockBean(CrmService::class)
    fun crmService() = crmService
    private val crmService = mockk<CrmService>(relaxed = true)

    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val accountRepository = AccountDbRepository(dynamoDbDAO)

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant))

    private val participantWallet = walletFixture.buildPrimaryWallet(walletFixture.participantAccount)

    @MockBean(WalletRepository::class)
    fun walletRepository(): WalletRepository = walletRepository
    private val walletRepository: WalletRepository = spyk(WalletDbRepository(dynamoDbDAO, mockk()))

    @MockBean(MockEventPublisher::class)
    fun eventPublisher(): EventPublisher = eventPublisher
    private val eventPublisher: EventPublisher = mockk(relaxUnitFun = true)

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk(relaxUnitFun = true)

    @MockBean(DeviceFingerprintService::class)
    fun deviceFingerprintService() = deviceFingerprintService
    private val deviceFingerprintService: DeviceFingerprintService = mockk(relaxed = true)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        walletRepository.save(wallet)
        walletRepository.save(participantWallet)
        accountRepository.save(
            walletFixture.participantAccount.copy(
                configuration = walletFixture.participantAccount.configuration.copy(
                    defaultWalletId = wallet.id,
                ),
            ),
        )
    }

    @Test
    fun `should return not found when wallet is not found`() {
        val request = buildRequest(
            walletId = WalletId("123"),
            currentAccount = walletFixture.founderAccount,
            targetAccountId = AccountId(ACCOUNT_ID),
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return not found when user is not a wallet member`() {
        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.founderAccount,
            targetAccountId = AccountId(ACCOUNT_ID_2),
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.NOT_FOUND
    }

    @Test
    fun `should return unprocessable entity when user has not permission to remove a wallet member`() {
        val memberWithoutPermission = walletFixture.founderAccount.toMember(
            type = MemberType.FOUNDER,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                founderContactsEnabled = false,
                manageMembers = false,
                viewBalance = false,
                notification = true,
            ),
            status = MemberStatus.ACTIVE,
        )

        walletRepository.save(wallet.copy(members = listOf(memberWithoutPermission)))

        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.founderAccount,
            targetAccountId = AccountId(ACCOUNT_ID_2),
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
    }

    @Test
    fun `deve falhar em remover um membro cotitular de uma carteira pj`() {
        val memberWithPermission = walletFixture.founderAccount.copy(document = "**************").toMember(
            type = MemberType.FOUNDER,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                founderContactsEnabled = false,
                manageMembers = true,
                viewBalance = false,
                notification = true,
            ),
            status = MemberStatus.ACTIVE,
        )

        val memberCoFounder = walletFixture.participantAccount.toMember(
            type = MemberType.COFOUNDER,
            permissions = MemberPermissions(
                viewBills = BillPermission.ALL_BILLS,
                scheduleBills = BillPermission.ALL_BILLS,
                founderContactsEnabled = false,
                manageMembers = false,
                viewBalance = false,
                notification = true,
            ),
            status = null,
        )

        walletRepository.save(wallet.copy(members = listOf(memberWithPermission, memberCoFounder)))

        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.founderAccount,
            targetAccountId = memberCoFounder.accountId,
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY
    }

    @Test
    fun `should remove member and return no content when user has permission to remove a wallet member`() {
        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.founderAccount,
            targetAccountId = walletFixture.participantAccount.accountId,
        )

        val response = client.toBlocking().exchange(request, Argument.of(Unit::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        walletRepository.findWallet(wallet.id).allMembers
            .first { it.accountId == walletFixture.participantAccount.accountId }.status shouldBe MemberStatus.REMOVED
        accountRepository.findById(walletFixture.participantAccount.accountId).configuration.defaultWalletId shouldBe participantWallet.id

        val slot = slot<WalletEvent>()
        verify(exactly = 1) {
            eventPublisher.publish(event = capture(slot))
        }

        slot.captured.shouldBeTypeOf<MemberRemoved>()
        (slot.captured as MemberRemoved).member shouldBe walletFixture.participantAccount.accountId
    }

    @Test
    fun `should return unprocessable entity when a founder tries to remove itself from wallet`() {
        val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant))
        walletRepository.save(wallet)

        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.founderAccount,
            targetAccountId = walletFixture.founderAccount.accountId,
        )

        val response =
            assertThrows<HttpClientResponseException> { client.toBlocking().exchange(request, Argument.VOID) }

        response.status shouldBe HttpStatus.UNPROCESSABLE_ENTITY

        walletRepository.findWallet(wallet.id).activeMembers
            .first { it.accountId == walletFixture.participantAccount.accountId }.status shouldBe MemberStatus.ACTIVE
    }

    @Test
    fun `should return no content when wallet participant leaves wallet`() {
        val wallet = walletFixture.buildWallet(otherMembers = listOf(walletFixture.participant))
        walletRepository.save(wallet)

        val request = buildRequest(
            walletId = wallet.id,
            currentAccount = walletFixture.participantAccount,
            targetAccountId = walletFixture.participantAccount.accountId,
        )

        val response = client.toBlocking().exchange(request, Argument.of(Unit::class.java))

        response.status shouldBe HttpStatus.NO_CONTENT

        walletRepository.findWallet(wallet.id).allMembers
            .first { it.accountId == walletFixture.participantAccount.accountId }.status shouldBe MemberStatus.REMOVED
    }

    private fun buildRequest(walletId: WalletId, currentAccount: Account, targetAccountId: AccountId) =
        HttpRequest.DELETE<Unit>("/wallet/${walletId.value}/member/${targetAccountId.value}")
            .cookie(buildCookie(account = currentAccount))
            .header("X-API-VERSION", "2")
}