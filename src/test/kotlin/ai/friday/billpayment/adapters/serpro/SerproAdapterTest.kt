/*
package ai.friday.billpayment.adapters.serpro

import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoObjectDbRepository
import ai.friday.billpayment.app.account.AccountId
import arrow.core.getOrElse
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.AmazonDynamoDBClientBuilder
import io.micronaut.http.client.DefaultHttpClientConfiguration
import io.micronaut.runtime.ApplicationConfiguration
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test

class SerproAdapterTest {

    private fun getAmazonDynamoDb(): AmazonDynamoDB = AmazonDynamoDBClientBuilder.standard()
        .withRegion("us-east-1")
        .build()

    private val dynamoDB = getAmazonDynamoDb()
    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val databaseObjectRepository = DynamoObjectDbRepository(dynamoDbDAO = dynamoDbDAO)
    private val accountRegisterRepository = AccountRegisterDbRepository(
        dynamoDbDAO = dynamoDbDAO,
        databaseObjectRepository = databaseObjectRepository,
    )

    private val configuration = SerproAdapterConfiguration(
        host = "https://gateway.apiserpro.serpro.gov.br",
        username = "FROM_AWS_SECRETS",
        password = "FROM_AWS_SECRETS",
    )

    private val serproAdapter = SerproAdapter(
        serproAuthenticationManager = SerproAuthenticationManager(configuration),
        configuration = configuration,
        serproHttpConfiguration = SerproHttpConfiguration(
            applicationConfiguration = ApplicationConfiguration(),
            configuration = DefaultHttpClientConfiguration(),
        ),
    )

    @Test
    @Disabled
    fun foo() {
        val accountRegisterData =
            accountRegisterRepository.findByAccountId(AccountId("ACCOUNT-78c75b51-d017-4c0b-9054-a248f4ef33db"))

        val selfieImageStream = accountRegisterRepository.getDocumentInputStream(accountRegisterData.uploadedSelfie!!)

        val result = serproAdapter.validateWithSelfie(accountRegisterData.documentInfo!!, selfieImageStream).map {
            it
        }.getOrElse {
            throw it
        }

        println("==========")
        println(result)
        println("==========")
    }
}*/