package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.micronaut.WalletConfigurationMicronaut
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.wallet.BillPermission
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.integration.DOCUMENT
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.NAME
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.shouldBe
import io.mockk.mockk
import java.time.Duration
import java.time.LocalDate
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.EnumSource

class ExpireInviteJobTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val walletRepository: WalletRepository = WalletDbRepository(
        dynamoDbDAO = dynamoDbDAO,
        accountRepository = mockk(),
    )
    private val configuration = WalletConfigurationMicronaut(
        maxOpenInvites = 10,
        inviteExpiration = Duration.ofDays(2),
    )
    private val expireInviteJob = ExpireInviteJob(walletRepository, configuration)

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
    }

    @Test
    fun `should not expire an invite when it is still valid`() {
        val pendingInvite = buildInvite(
            status = InviteStatus.PENDING,
            validUntil = getLocalDate().plusDays(1),
            created = getZonedDateTime(),
        )
        walletRepository.save(pendingInvite)

        expireInviteJob.execute()

        val savedInvite = walletRepository.findInvite(pendingInvite.walletId, pendingInvite.memberDocument)
        savedInvite.status shouldBe pendingInvite.status
    }

    @ParameterizedTest
    @EnumSource(InviteStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["PENDING"])
    fun `should not expire an invite when it is not pending`(enum: InviteStatus) {
        val pendingInvite = buildInvite(
            status = enum,
            validUntil = getLocalDate().minusDays(10),
            created = getZonedDateTime(),
        )
        walletRepository.save(pendingInvite)

        expireInviteJob.execute()

        val savedInvite = walletRepository.findInvite(pendingInvite.walletId, pendingInvite.memberDocument)
        savedInvite.status shouldBe pendingInvite.status
    }

    @Test
    fun `should expire an invite when it is not valid`() {
        val startZonedDate = ZonedDateTime.of(2021, 1, 30, 0, 0, 0, 0, brazilTimeZone)

        val invite = buildInvite(
            status = InviteStatus.PENDING,
            validUntil = startZonedDate.minusDays(1).toLocalDate(),
            created = getZonedDateTime(),
        )
        walletRepository.save(invite)

        withGivenDateTime(startZonedDate) {
            expireInviteJob.execute()

            val savedInvite = walletRepository.findInvite(invite.walletId, invite.memberDocument)
            savedInvite.status shouldBe InviteStatus.EXPIRED
            savedInvite.updatedAt shouldBe startZonedDate
            savedInvite.validUntil shouldBe invite.validUntil
        }
    }

    private fun buildInvite(status: InviteStatus, validUntil: LocalDate, created: ZonedDateTime) = Invite(
        walletId = WalletId(WALLET_ID),
        memberDocument = DOCUMENT,
        memberName = NAME,
        memberType = MemberType.COLLABORATOR,
        permissions = MemberPermissions(
            viewBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
            scheduleBills = BillPermission.ONLY_BILLS_ADDED_BY_USER,
            viewBalance = true,
            notification = true,
        ),
        status = status,
        validUntil = validUntil,
        walletName = "carteira",
        founderName = "fundador",
        founderDocument = "11111111111",
        created = created,
    )
}