package ai.friday.billpayment.adapters.jobs

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.SystemActivityDbRepository
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.right
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.ValueSource

internal class EnableAccountCreditCardQuotaJobTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val accountId = AccountId(ACCOUNT_ID)

    private val configurationWithCard = ACCOUNT.configuration.copy(
        groups = ACCOUNT.configuration.groups + listOf(AccountGroup.CREDIT_CARD_STANDARD_PLAN),
        creditCardConfiguration = CreditCardConfiguration(
            quota = 1000,
        ),
    )

    private val systemActivityRepository = SystemActivityDbRepository(dynamoDbDAO = dynamoDbDAO)

    private val systemActivityService = SystemActivityService(systemActivityRepository = systemActivityRepository)

    private val accountService = mockk<AccountService> {
        every {
            findAccountById(accountId)
        } returns ACCOUNT
        every {
            enableCreditCardUsage(accountId = any(), quota = any())
        } returns ACCOUNT.copy(
            configuration = configurationWithCard,
        ).right()
    }

    private val job: EnableAccountCreditCardQuotaJob = EnableAccountCreditCardQuotaJob(
        featuresRepository = mockk() {
            every {
                getAll().maintenanceMode
            } returns false
        },
        systemActivityService = systemActivityService,
        accountService = accountService,
    )

    @BeforeEach
    fun init() {
        createBillPaymentTable(amazonDynamoDB = dynamoDB)
    }

    @Test
    fun `não deve chamar a criacao de cartao se não encontrar ninguem`() {
        job.execute()

        verify(exactly = 0) {
            accountService.enableCreditCardUsage(accountId = any(), quota = any())
        }
    }

    @ParameterizedTest
    @ValueSource(longs = [30, 31, 32, 33, 34, 35, 36, 37])
    fun `deve chamar a criacao de cartao se tiver na janela e usuario ainda não tiver cartao`(pastDays: Long) {
        systemActivityService.setFirstOwnTrustedBillPaid(accountId, getZonedDateTime().minusDays(pastDays))

        job.execute()

        verify(exactly = 1) {
            accountService.enableCreditCardUsage(accountId = any(), quota = any())
        }
    }

    @ParameterizedTest
    @ValueSource(longs = [30, 31, 32, 33, 34, 35, 36, 37])
    fun `nao deve chamar a criacao de cartao se tiver na janela e usuario já tiver cartao`(pastDays: Long) {
        every {
            accountService.findAccountById(accountId)
        } returns ACCOUNT.copy(
            configuration = configurationWithCard,
        )

        systemActivityService.setFirstOwnTrustedBillPaid(accountId, getZonedDateTime().minusDays(pastDays))

        job.execute()

        verify(exactly = 0) {
            accountService.enableCreditCardUsage(accountId = any(), quota = any())
        }
    }

    @ParameterizedTest
    @ValueSource(longs = [29, 38])
    fun `nao deve chamar a criacao de cartao se tiver fora da janela`(pastDays: Long) {
        every {
            accountService.findAccountById(accountId)
        } returns ACCOUNT.copy(
            configuration = configurationWithCard,
        )

        systemActivityService.setFirstOwnTrustedBillPaid(accountId, getZonedDateTime().minusDays(pastDays))

        job.execute()

        verify(exactly = 0) {
            accountService.enableCreditCardUsage(accountId = any(), quota = any())
        }
    }
}