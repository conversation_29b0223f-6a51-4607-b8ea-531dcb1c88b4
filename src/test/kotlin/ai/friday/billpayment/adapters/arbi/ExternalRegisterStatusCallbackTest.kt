package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.MockUserPoolAdapter
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.accountRegisterDataMissingAcceptedAt
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DDADbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.LoginDbRepository
import ai.friday.billpayment.adapters.intercom.IntercomAdapterException
import ai.friday.billpayment.adapters.lock.updateAccountStatusLockProvider
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.NewAccountEmailConfiguration
import ai.friday.billpayment.app.account.RegisterNaturalPersonResponse
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.integrations.DatabaseObjectRepository
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.login.buildFederatedUsername
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.cnhDocumentInfo
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.createLockTable
import ai.friday.billpayment.integration.createPartialAccount
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.inspectors.forAll
import io.kotest.inspectors.forExactly
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.kotest.matchers.string.shouldContainIgnoringCase
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.context.annotation.Property
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import io.via1.communicationcentre.app.email.Attachment
import io.via1.communicationcentre.app.integrations.EmailSenderService
import jakarta.inject.Named
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.ValueSource

private const val emailDomain = "fake.mail.com"
private const val contractFileKey = "userContract.pdf"
private const val declarationOfResidencyKey = "declarationOfResidencyFile.pdf"
private const val kycFile = "kycreport.pdf"

@Property(name = "accountRegister.pixKey.emailDomain", value = emailDomain)
@MicronautTest(environments = [FRIDAY_ENV])
class ExternalRegisterStatusCallbackTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
    @Named("pending-internal-approve")
    val pendingInternalApprove: NewAccountEmailConfiguration,
    @Named("pending-internal-review")
    val pendingInternalReview: NewAccountEmailConfiguration,
    @Named("pending-activation")
    val pendingActivation: NewAccountEmailConfiguration,
    @Named(updateAccountStatusLockProvider) private val accountStatusLockProvider: InternalLock,
) {
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val loginRepository = LoginDbRepository(dynamoDbDAO)
    private val ddaRepository = DDADbRepository(dynamoDbDAO)

    @MockBean(MockUserPoolAdapter::class)
    fun userPoolMock(): UserPoolAdapter = userPoolMock
    private val userPoolMock: UserPoolAdapter = mockk(relaxed = true) {
        every { doesUserExist(any()) } returns true
    }

    @MockBean(PFMWalletCategoryService::class)
    fun pfmWalletCategoryService() = pfmWalletCategoryService
    private val pfmWalletCategoryService: PFMWalletCategoryService = mockk {
        every {
            findWalletCategories(any())
        } returns emptyList()

        every {
            createDefaultWalletCategories(any())
        } returns emptyList<WalletBillCategory>().right()
    }

    @MockBean(NotificationAdapter::class)
    fun mockDependency(): NotificationAdapter = notificationMock
    private val notificationMock: NotificationAdapter = mockk(relaxed = true)

    @MockBean(ArbiAccountAdapter::class)
    fun externalAccountRegister(): ExternalAccountRegister = externalAccountRegister
    private val externalAccountRegister: ExternalAccountRegister = mockk(relaxed = true)

    @MockBean(MessagePublisher::class)
    fun messagePublisherMock(): MessagePublisher = messagePublisherMock
    private val messagePublisherMock: MessagePublisher = mockk(relaxed = true)

    @MockBean(EmailSenderService::class)
    fun emailSenderService() = emailSenderService
    private val emailSenderService: EmailSenderService = mockk(relaxUnitFun = true)

    @MockBean(DatabaseObjectRepository::class)
    fun databaseObjectRepository(): DatabaseObjectRepository = databaseObjectRepository
    private val databaseObjectRepository: DatabaseObjectRepository = mockk(relaxed = true)

    @MockBean(RegisterInstrumentationService::class)
    fun registerInstrumentationService(): RegisterInstrumentationService = registerInstrumentationService
    private val registerInstrumentationService: RegisterInstrumentationService = mockk(relaxUnitFun = true)

    @field:Property(name = "celcoin-callback.identity")
    lateinit var celcoinIdentity: String

    @field:Property(name = "celcoin-callback.secret")
    lateinit var celcoinSecret: String

    @field:Property(name = "arbi-callback.identity")
    lateinit var arbiIdentity: String

    @field:Property(name = "arbi-callback.secret")
    lateinit var arbiSecret: String

    @field:Property(name = "lock.tableName")
    lateinit var lockTableName: String

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        createLockTable(dynamoDB, lockTableName)
    }

    private val registerNaturalPersonResponse =
        RegisterNaturalPersonResponse(success = true, accountNumber = 12345, accountDv = "6")

    private val externalRegisterStatusCallbackRequest = ExternalRegisterStatusCallbackRequest(
        document = accountRegisterDataMissingAcceptedAt.documentInfo!!.cpf,
        name = "Fulano de Tal",
        status = "APROVADO",
        externalId = "****************@*************",
        fullAccountNumber = "",
    )

    @Test
    fun `should return forbidden when role is invalid`() {
        val request = HttpRequest.POST("/arbi/registerStatus", externalRegisterStatusCallbackRequest)
            .basicAuth(celcoinIdentity, celcoinSecret)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }
        thrown.status shouldBe HttpStatus.FORBIDDEN
    }

    @Test
    fun `should return bad request when account is not found`() {
        val request = HttpRequest.POST("/arbi/registerStatus", externalRegisterStatusCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)
        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }
        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4001"
    }

    @ParameterizedTest
    @CsvSource(value = ["APPROVED,REPROVADO", "UNDER_REVIEW,APROVADO"])
    fun `should return conflict when account status does not match external status`(
        accountStatus: AccountStatus,
        externalStatus: String,
    ) {
        val request = HttpRequest.POST(
            "/arbi/registerStatus",
            externalRegisterStatusCallbackRequest.copy(status = externalStatus),
        )
            .basicAuth(arbiIdentity, arbiSecret)

        createPartialAccount(
            dynamoDbDAO,
            accountStatus,
            accountRegisterData = accountRegisterDataMissingAcceptedAt.copy(registrationType = RegistrationType.FULL),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.CONFLICT
    }

    @Test
    fun `should return ok when account status UNDER REVIEW matches external status REPROVADO`() {
        val request = HttpRequest.POST(
            "/arbi/registerStatus",
            externalRegisterStatusCallbackRequest.copy(status = "REPROVADO"),
        )
            .basicAuth(arbiIdentity, arbiSecret)

        createPartialAccount(
            dynamoDbDAO,
            AccountStatus.UNDER_REVIEW,
            accountRegisterData = accountRegisterDataMissingAcceptedAt.copy(registrationType = RegistrationType.FULL),
        )

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.getBody(ResponseTO::class.java).get().code shouldBe "2004"
    }

    @Test
    fun `should return internal server error on situacao APROVADO and error calling register natural person`() {
        val request = HttpRequest.POST("/arbi/registerStatus", externalRegisterStatusCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)

        createPartialAccount(
            dynamoDbDAO,
            AccountStatus.UNDER_EXTERNAL_REVIEW,
            accountRegisterData = accountRegisterDataMissingAcceptedAt.copy(registrationType = RegistrationType.FULL),
        )

        every {
            externalAccountRegister.registerNaturalPerson(any())
        } returns registerNaturalPersonResponse.copy(success = false)

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "5001"
    }

    @Test
    fun `should return success, create account, update status, register pix key and DDA on situacao APROVADO`() {
        val request = HttpRequest.POST("/arbi/registerStatus", externalRegisterStatusCallbackRequest)
            .basicAuth(arbiIdentity, arbiSecret)

        val (partialAccount, accountRegisterData) = createPartialAccount(
            dynamoDbDAO,
            AccountStatus.UNDER_EXTERNAL_REVIEW,
            accountRegisterData = accountRegisterDataMissingAcceptedAt.copy(registrationType = RegistrationType.FULL),
        )
        val providerUser =
            ProviderUser("MSISDN_XXXXX", ProviderName.MSISDN, "John doe", partialAccount.emailAddress)
        loginRepository.createLogin(providerUser, partialAccount.id, Role.GUEST)

        every {
            externalAccountRegister.registerNaturalPerson(any())
        } returns registerNaturalPersonResponse

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.getBody(ResponseTO::class.java).get().code shouldBe "2001"

        with(accountRepository.findPartialAccountById(partialAccount.id)) {
            status shouldBe AccountStatus.APPROVED
        }

        val account = accountRepository.findById(partialAccount.id)

        with(account) {
            status shouldBe AccountStatus.APPROVED
            document shouldBe accountRegisterData.documentInfo!!.cpf
            documentType shouldBe "CPF"
            name shouldBe accountRegisterData.documentInfo!!.name
            configuration.receiveDDANotification shouldBe false
            configuration.externalId shouldBe accountRegisterData.externalId
        }

        with(accountRepository.findAccountPaymentMethodsByAccountId(partialAccount.id)) {
            size shouldBe 1
            this[0].method.shouldBeTypeOf<InternalBankAccount>()
            val bankAccount = (this[0].method as InternalBankAccount)
            bankAccount.accountDv shouldBe registerNaturalPersonResponse.accountDv
            bankAccount.accountNo shouldBe registerNaturalPersonResponse.accountNumber
            bankAccount.accountType shouldBe AccountType.CHECKING
            bankAccount.bankNo shouldBe 213
            bankAccount.routingNo shouldBe 1
            bankAccount.document shouldBe account.document
        }

        with(ddaRepository.find(partialAccount.id)) {
            this.shouldNotBeNull()
            this.status shouldBe DDAStatus.PENDING
            this.provider shouldBe DDAProvider.ARBI
        }

        val logins = loginRepository.findUserLogin(account.emailAddress)
        logins shouldHaveSize 1
        logins.forAll {
            it.role shouldBe Role.GUEST
        }

        verify {
            messagePublisherMock.sendMessage(
                "register_pix_key_queue_name",
                RegisterPixKeyCommand(
                    AccountNumber(
                        "123456",
                    ),
                    PixKey(value = "<EMAIL>", type = PixKeyType.EMAIL),
                    "***********",
                    cnhDocumentInfo.name,
                    walletId = WalletId(account.accountId.value),
                ),
            )
        }

        verify {
            messagePublisherMock.sendMessage(
                "register_pix_key_queue_name",
                RegisterPixKeyCommand(
                    AccountNumber(
                        "123456",
                    ),
                    PixKey(value = "", type = PixKeyType.EVP),
                    "***********",
                    cnhDocumentInfo.name,
                    walletId = WalletId(account.accountId.value),
                ),
            )
        }

        verify(exactly = 0) {
            userPoolMock.removeUserFromGroup(providerUser.buildFederatedUsername()!!, Role.GUEST.name)
            userPoolMock.addUserToGroup(providerUser.buildFederatedUsername()!!, Role.OWNER.name)
            notificationMock.notifyUserActivated(account)
        }
    }

    @Test
    fun `should return success, update account status to UNDER_REVIEW and notify backoffice on situacao REPROVADO`() {
        val request =
            HttpRequest.POST("/arbi/registerStatus", externalRegisterStatusCallbackRequest.copy(status = "REPROVADO"))
                .basicAuth(arbiIdentity, arbiSecret)

        val (partialAccount, accountRegisterData) = createPartialAccount(
            dynamoDbDAO,
            AccountStatus.UNDER_EXTERNAL_REVIEW,
            accountRegisterData = accountRegisterCompleted.copy(registrationType = RegistrationType.FULL),
        )

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.getBody(ResponseTO::class.java).get().code shouldBe "2004"

        with(accountRepository.findPartialAccountById(partialAccount.id)) {
            status shouldBe AccountStatus.UNDER_REVIEW
        }

        val attachmentSlot = slot<List<Attachment>>()
        verify(exactly = 1) {
            emailSenderService.sendRawEmail(
                any(),
                match { it.contains(accountRegisterData.accountId.value) },
                any(),
                pendingInternalReview.recipients,
            )
            emailSenderService.sendRawEmail(
                any(),
                match { it.contains(accountRegisterData.accountId.value) },
                any(),
                pendingInternalReview.sensitiveRecipients,
                capture(attachmentSlot),
            )
        }

        attachmentSlot.captured.size shouldBe 5
        attachmentSlot.captured.forExactly(1) { attachment ->
            attachment.fileName shouldBe kycFile
            attachment.contentType shouldBe MediaType.APPLICATION_PDF
        }

        attachmentSlot.captured.forExactly(1) { attachment ->
            attachment.fileName shouldContainIgnoringCase declarationOfResidencyKey
            attachment.contentType shouldBe MediaType.APPLICATION_PDF
        }

        attachmentSlot.captured.forExactly(1) { attachment ->
            attachment.fileName shouldContainIgnoringCase contractFileKey
            attachment.contentType shouldBe MediaType.APPLICATION_PDF
        }

        verify {
            registerInstrumentationService.externallyRejected(partialAccount.id, partialAccount.registrationType)
        }
    }

    @Test
    fun `should return success on situacao REPROVADO when CRM event publish fails`() {
        val request =
            HttpRequest.POST("/arbi/registerStatus", externalRegisterStatusCallbackRequest.copy(status = "REPROVADO"))
                .basicAuth(arbiIdentity, arbiSecret)

        createPartialAccount(
            dynamoDbDAO,
            AccountStatus.UNDER_EXTERNAL_REVIEW,
            accountRegisterData = accountRegisterCompleted,
        )

        every {
            registerInstrumentationService.externallyRejected(any(), any())
        } throws IntercomAdapterException("mock exception")

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
    }

    @ParameterizedTest
    @ValueSource(strings = ["CANCELADO", "PROPOSTA NAO CHEGOU AO KYC. ENVIAR NOVAMENTE", "EM ANALISE"])
    fun `should return ok on ignorable situacao`(situacao: String) {
        val request =
            HttpRequest.POST("/arbi/registerStatus", externalRegisterStatusCallbackRequest.copy(status = situacao))
                .basicAuth(arbiIdentity, arbiSecret)

        createPartialAccount(dynamoDbDAO, AccountStatus.UNDER_EXTERNAL_REVIEW)

        val response = client.toBlocking().exchange(request, Argument.of(String::class.java))

        response.status shouldBe HttpStatus.OK
        response.getBody(ResponseTO::class.java).get().code shouldBe "2005"
    }

    @Test
    fun `should return bad request on unknown situacao`() {
        val request =
            HttpRequest.POST(
                "/arbi/registerStatus",
                externalRegisterStatusCallbackRequest.copy(status = "QUALQUER OUTRA SITUACAO"),
            )
                .basicAuth(arbiIdentity, arbiSecret)

        createPartialAccount(
            dynamoDbDAO,
            AccountStatus.UNDER_EXTERNAL_REVIEW,
            accountRegisterData = accountRegisterDataMissingAcceptedAt.copy(registrationType = RegistrationType.FULL),
        )

        val thrown = assertThrows<HttpClientResponseException> {
            client.toBlocking().exchange(request, Argument.of(String::class.java))
        }

        thrown.status shouldBe HttpStatus.BAD_REQUEST
        thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "4002"
    }

    @Test
    fun `should return internal server error when document is locked`() {
        val request =
            HttpRequest.POST("/arbi/registerStatus", externalRegisterStatusCallbackRequest.copy(status = "REPROVADO"))
                .basicAuth(arbiIdentity, arbiSecret)

        createPartialAccount(
            dynamoDbDAO,
            AccountStatus.UNDER_EXTERNAL_REVIEW,
            accountRegisterData = accountRegisterDataMissingAcceptedAt.copy(registrationType = RegistrationType.FULL),
        )

        val lock = accountStatusLockProvider.acquireLock(externalRegisterStatusCallbackRequest.document)
            ?: throw IllegalStateException("should have acquired lock")

        try {
            val thrown = assertThrows<HttpClientResponseException> {
                client.toBlocking().exchange(request, Argument.of(String::class.java))
            }

            thrown.status shouldBe HttpStatus.INTERNAL_SERVER_ERROR
            thrown.response.getBody(ResponseTO::class.java).get().code shouldBe "5003"
        } finally {
            lock.unlock()
        }
    }
}