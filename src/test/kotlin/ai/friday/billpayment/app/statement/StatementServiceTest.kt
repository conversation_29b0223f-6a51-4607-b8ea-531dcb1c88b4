package ai.friday.billpayment.app.statement

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.integrations.AccountStatementAdapter
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pfm.WalletBillCategory
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.balance
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.bankStatementItemInvestmentRedemption
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_2
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.getOrElse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.http.MediaType
import io.mockk.Called
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.Month
import java.time.Year
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.fail

internal class StatementServiceTest {
    private val internalBankRepository: InternalBankRepository = mockk()
    private val billRepository: BillRepository = mockk()
    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val walletService: WalletService = mockk()
    private val statementItemConverter: FridayStatementItemConverter = mockk()
    private val sqsMessagePublisher = mockk<MessagePublisher>(relaxUnitFun = true)
    private val notificationAdapter: NotificationAdapter = mockk(relaxed = true)
    private val accountService: AccountService = mockk(relaxed = true)
    private val accountStatementAdapter: AccountStatementAdapter = mockk(relaxed = true)
    private val cnpjFriday = "**************"
    private val contaLiquidacao = "0326310"
    private val walletBillCategoryService: PFMWalletCategoryService = mockk(relaxed = true)

    private val statementService = StatementService(
        internalBankRepository = internalBankRepository,
        billRepository = billRepository,
        walletService = walletService,
        statementItemConverter = statementItemConverter,
        accountService = accountService,
        notificationAdapter = notificationAdapter,
        accountStatementAdapter = accountStatementAdapter,
        sqsMessagePublisher = sqsMessagePublisher,
        queueName = "test-queue",
        selfCNPJArbiAccount = cnpjFriday,
        contaLiquidacao = contaLiquidacao,
        walletBillCategoryService = walletBillCategoryService,
        displayName = "Friday",
    )

    @Test
    fun `deve retornar erro ao não encontrar a carteira`() {
        every {
            walletService.findWalletOrNull(any())
        } returns null

        val result = statementService.findAllStatementsByDate(WalletId("errado"), getLocalDate(), getLocalDate())

        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<StatementError.WalletNotFound>() }
    }

    @Test
    fun `deve retornar os créditos e débitos em ordem cronológica`() {
        every {
            walletService.findWalletOrNull(any())
        } returns wallet
        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns balance
        every {
            accountStatementAdapter.getStatement(any(), any(), any(), any())
        } returns BankStatement(
            items = listOf(
                bankStatementItem.copy(date = getLocalDate().minusDays(2)), // 123
                bankStatementItem.copy(counterpartDocument = cnpjFriday, amount = 5000, counterpartAccountNo = contaLiquidacao),
                bankStatementItem.copy(operationNumber = "12345"), // 123
                bankStatementItem.copy(documentNumber = "***********", counterpartDocument = cnpjFriday, operationNumber = "12345", flow = BankStatementItemFlow.DEBIT, amount = 200),
            ),
            initialBalance = Balance(15591 * 2),
            finalBalance = Balance((123 * 2) + 5000 - 200),
        )

        every {
            billRepository.getPaidBills(any(), any(), any(), any())
        } returns listOf(
            getPaidBill().copy(paidDate = getZonedDateTime().toLocalDateTime().minusDays(1), categoryId = PFMCategoryId("CATEGORIA-1")), // 15591L
            getPaidBill().copy(paidDate = getZonedDateTime().toLocalDateTime().minusSeconds(2), categoryId = PFMCategoryId("CATEGORIA-2")), // 15591L
            getPaidBill().copy(
                paidDate = getZonedDateTime().toLocalDateTime(),
                recipient = Recipient("Boleto qualquer"),
                amount = 200L,
                amountPaid = 200L,
                amountTotal = 200L,
            ),
        )

        every { walletBillCategoryService.findWalletCategories(any()) } returns listOf(
            WalletBillCategory(
                walletId = WalletId(WALLET_ID),
                categoryId = PFMCategoryId("CATEGORIA-1"),
                name = "Categoria",
                default = false,
                enabled = true,
                icon = "SAUDE",
            ),
        )

        val result = statementService.findAllStatementsByDate(
            walletId = wallet.id,
            startDate = getLocalDate().minusDays(3),
            endDate = getLocalDate(),
        )

        result.isRight() shouldBe true
        result.map {
            it.statementItems.size shouldBe 5

            it.statementItems[0].date shouldBe getLocalDate().minusDays(2)
            it.statementItems[0].flow shouldBe BankStatementItemFlow.CREDIT
            it.statementItems[0].counterPartName shouldBe "fake counterpartName"

            it.statementItems[1].date shouldBe getLocalDate().minusDays(1)
            it.statementItems[1].flow shouldBe BankStatementItemFlow.DEBIT
            it.statementItems[1].category shouldBe "Categoria"

            it.statementItems[2].date shouldBe getLocalDate()
            it.statementItems[2].flow shouldBe BankStatementItemFlow.CREDIT

            it.statementItems[3].date shouldBe getLocalDate()
            it.statementItems[3].flow shouldBe BankStatementItemFlow.DEBIT
            it.statementItems[3].counterPartName shouldBe "Claro"
            it.statementItems[3].balance shouldBe Balance(123 * 2)
            it.statementItems[3].category shouldBe ""

            it.statementItems[4].date shouldBe getLocalDate()
            it.statementItems[4].flow shouldBe BankStatementItemFlow.DEBIT
            it.statementItems[4].counterPartName shouldBe "Boleto qualquer"
            it.statementItems[4].balance shouldBe Balance((123 * 2) - 200)
            it.statementItems[4].category shouldBe ""

            it.finalBalance shouldBe Balance((123 * 2) + 5000 - 200)
        }
    }

    @Test
    fun `não deve ignorar créditos da conta liquidação friday se o documento do dono da carteira for um cnpj friday`() {
        every {
            walletService.findWalletOrNull(any())
        } returns wallet.copy(members = wallet.allMembers.map { it.copy(document = cnpjFriday) })
        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns balance

        every {
            accountService.findAccountById(any())
        } returns walletFixture.founderAccount.copy(document = cnpjFriday)

        every {
            accountStatementAdapter.getStatement(any(), any(), any(), any())
        } returns BankStatement(
            items = listOf(
                bankStatementItem.copy(documentNumber = contaLiquidacao, counterpartDocument = cnpjFriday, date = getLocalDate().minusDays(2)),
                bankStatementItem.copy(documentNumber = contaLiquidacao, counterpartDocument = cnpjFriday, operationNumber = "12345"),
            ),
            initialBalance = Balance(15591 * 2),
            finalBalance = Balance(123 * 2),
        )

        every {
            billRepository.getPaidBills(any(), any(), any(), any())
        } returns listOf(
            getPaidBill().copy(paidDate = getZonedDateTime().toLocalDateTime().minusDays(1)),
            getPaidBill().copy(paidDate = getZonedDateTime().toLocalDateTime()),
        )

        val result = statementService.findAllStatementsByDate(
            walletId = wallet.id,
            startDate = getLocalDate().minusDays(3),
            endDate = getLocalDate(),
        )

        result.isRight() shouldBe true
        result.map {
            it.statementItems.size shouldBe 4

            it.statementItems[0].date shouldBe getLocalDate().minusDays(2)
            it.statementItems[0].flow shouldBe BankStatementItemFlow.CREDIT
            it.statementItems[0].counterPartName shouldBe "fake counterpartName"
            it.statementItems[1].date shouldBe getLocalDate().minusDays(1)
            it.statementItems[1].flow shouldBe BankStatementItemFlow.DEBIT
            it.statementItems[2].date shouldBe getLocalDate()
            it.statementItems[2].flow shouldBe BankStatementItemFlow.CREDIT
            it.statementItems[3].date shouldBe getLocalDate()
            it.statementItems[3].flow shouldBe BankStatementItemFlow.DEBIT
            it.statementItems[3].counterPartName shouldBe "Claro"
            it.statementItems[3].balance shouldBe Balance(123 * 2)
        }
    }

    @Test
    fun `não deve retornar os débitos via cartão de crédito`() {
        every {
            walletService.findWalletOrNull(any())
        } returns wallet
        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns balance
        every {
            accountStatementAdapter.getStatement(any(), any(), any(), any())
        } returns BankStatement(
            items = listOf(
                bankStatementItem.copy(date = getLocalDate().minusDays(2)),
                bankStatementItem.copy(operationNumber = "12345"),
            ),
            initialBalance = Balance(15591 * 2),
            finalBalance = Balance(123 * 2),
        )

        every {
            billRepository.getPaidBills(any(), any(), any(), any())
        } returns listOf(
            getPaidBill().copy(paidDate = getZonedDateTime().toLocalDateTime().minusDays(1)),
            getPaidBill().copy(
                paidDate = getZonedDateTime().toLocalDateTime(),
                paymentDetails = PaymentMethodsDetailWithCreditCard(
                    paymentMethodId = AccountPaymentMethodId(value = PAYMENT_METHOD_ID_2),
                    netAmount = 1,
                    feeAmount = 10,
                    installments = 1,
                    calculationId = null,
                    fee = 4.0,
                ),
            ),
        )

        val result = statementService.findAllStatementsByDate(
            walletId = wallet.id,
            startDate = getLocalDate().minusDays(3),
            endDate = getLocalDate(),
        )

        result.isRight() shouldBe true
        result.map {
            it.statementItems.size shouldBe 3

            it.statementItems[0].date shouldBe getLocalDate().minusDays(2)
            it.statementItems[0].flow shouldBe BankStatementItemFlow.CREDIT
            it.statementItems[0].counterPartName shouldBe "fake counterpartName"
            it.statementItems[1].date shouldBe getLocalDate().minusDays(1)
            it.statementItems[1].flow shouldBe BankStatementItemFlow.DEBIT
            it.statementItems[2].date shouldBe getLocalDate()
            it.statementItems[2].flow shouldBe BankStatementItemFlow.CREDIT
        }
    }

    @Test
    fun `deve enviar para a fila de processamento a solicitação de extrato`() {
        val accountId = walletFixture.founderAccount.accountId
        val wallet = walletFixture.buildWallet()
        every {
            walletService.findWallet(any())
        } returns wallet
        val startDate = getLocalDate().minusDays(3)
        val endDate = getLocalDate()

        val result = statementService.requestStatement(accountId, wallet.id, startDate, endDate)

        result.isRight().shouldBeTrue()

        verify {
            sqsMessagePublisher.sendMessage(
                queueName = "test-queue",
                body = RequestStatementMessageTO(
                    accountId = accountId.value,
                    walletId = wallet.id.value,
                    startDate = startDate.toString(),
                    endDate = endDate.toString(),
                ),
            )
        }
    }

    @Test
    fun `não deve conseguir solicitar extrato quando não tiver permissão na carteira`() {
        val accountId = walletFixture.ultraLimitedParticipantAccount.accountId
        val wallet = walletFixture.buildWallet(
            otherMembers = listOf(walletFixture.ultraLimitedParticipant),
        )
        every {
            walletService.findWallet(any())
        } returns wallet
        val startDate = getLocalDate().minusDays(3)
        val endDate = getLocalDate()

        val result = statementService.requestStatement(accountId, wallet.id, startDate, endDate)

        result.isLeft().shouldBeTrue()

        result.mapLeft {
            it shouldBe StatementError.NotAllowed
        }

        verify(exactly = 0) {
            sqsMessagePublisher.sendMessage(
                queueName = any(),
                body = any(),
            )
        }
    }

    @Test
    fun `não deve conseguir solicitar extrato quando não for membro da carteira`() {
        val accountId = walletFixture.ultraLimitedParticipantAccount.accountId
        val wallet = walletFixture.buildWallet()
        every {
            walletService.findWallet(any())
        } returns wallet
        val startDate = getLocalDate().minusDays(3)
        val endDate = getLocalDate()

        val result = statementService.requestStatement(accountId, wallet.id, startDate, endDate)

        result.isLeft().shouldBeTrue()

        result.mapLeft {
            it shouldBe StatementError.NotAllowed
        }

        verify(exactly = 0) {
            sqsMessagePublisher.sendMessage(
                queueName = any(),
                body = any(),
            )
        }
    }

    @Test
    fun `deve enviar email com csv e pdf do extrato do usuario com saldo anterior e posterior`() {
        val accountId = walletFixture.founderAccount.accountId
        val wallet = walletFixture.buildWallet()
        val startDate = getLocalDate().minusDays(3)
        val endDate = getLocalDate()
        every {
            accountService.findAccountById(any())
        } returns walletFixture.founderAccount
        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns balance
        every {
            walletService.findWalletOrNull(any())
        } returns wallet
        every {
            walletService.findWallet(any())
        } returns wallet
        every {
            internalBankRepository.findAllBankStatementCredits(any(), any(), any())
        } returns listOf(
            bankStatementItem.copy(date = getLocalDate().minusDays(2)),
            bankStatementItem.copy(operationNumber = "12345"),
        )
        every {
            billRepository.getPaidBills(any(), any(), any(), any())
        } returns emptyList()
        every {
            accountStatementAdapter.getStatement(any(), any(), any(), any())
        } returns BankStatement(items = listOf(), initialBalance = Balance(100), finalBalance = Balance(200))
        every {
            statementItemConverter.convertToPdf(any(), any(), any(), any(), any(), any(), any(), Balance(100), Balance(200), any(), any())
        } returns "PDF".toByteArray()
        every {
            statementItemConverter.convertToCsv(any())
        } returns "CSV"

        val result = statementService.sendStatementByEmail(accountId, wallet.id, startDate, endDate)
        result.isRight().shouldBeTrue()

        val fileName = "de_${startDate.year}_${startDate.month.value}_${startDate.dayOfMonth}_ate_${endDate.year}_${endDate.month.value}_${endDate.dayOfMonth}"

        verify {
            notificationAdapter.notifyAccountStatement(
                emailAddress = walletFixture.founderAccount.emailAddress,
                name = walletFixture.founderAccount.name,
                files = listOf(
                    ByteArrayWithNameAndType(
                        fileName = "Extrato_Friday_$fileName.csv",
                        mediaType = MediaType.TEXT_CSV,
                        data = "CSV".toByteArray(),
                    ),
                    ByteArrayWithNameAndType(
                        fileName = "Extrato_Friday_$fileName.pdf",
                        mediaType = MediaType.APPLICATION_PDF,
                        data = "PDF".toByteArray(),
                    ),
                ),
                periodMessage = any(),
            )
        }
    }

    @Test
    fun `deve retornar erro quando a geração do pdf falhar`() {
        val accountId = walletFixture.founderAccount.accountId
        val wallet = walletFixture.buildWallet()
        val startDate = getLocalDate().minusDays(3)
        val endDate = getLocalDate()
        every {
            accountService.findAccountById(any())
        } returns walletFixture.founderAccount
        every {
            walletService.findWalletOrNull(any())
        } returns wallet
        every {
            walletService.findWallet(any())
        } returns wallet
        every {
            internalBankRepository.findAllBankStatementCredits(any(), any(), any())
        } returns listOf(
            bankStatementItem.copy(date = getLocalDate().minusDays(2)),
            bankStatementItem.copy(operationNumber = "12345"),
        )
        every {
            billRepository.getPaidBills(any(), any(), any(), any())
        } returns emptyList()
        every {
            statementItemConverter.convertToPdf(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } throws Exception("erro ao gerar pdf")
        every {
            statementItemConverter.convertToCsv(any())
        } returns "CSV"

        val result = statementService.sendStatementByEmail(accountId, wallet.id, startDate, endDate)

        result.isLeft().shouldBeTrue()

        result.mapLeft {
            it shouldBe StatementError.ServerError
        }

        verify(exactly = 0) {
            notificationAdapter.notifyAccountStatement(
                emailAddress = any(),
                name = any(),
                files = any(),
                periodMessage = any(),
            )
        }
    }

    @Test
    fun `deve retornar erro quando a geração do csv falhar`() {
        val accountId = walletFixture.founderAccount.accountId
        val wallet = walletFixture.buildWallet()
        val startDate = getLocalDate().minusDays(3)
        val endDate = getLocalDate()
        every {
            accountService.findAccountById(any())
        } returns walletFixture.founderAccount
        every {
            walletService.findWalletOrNull(any())
        } returns wallet
        every {
            walletService.findWallet(any())
        } returns wallet
        every {
            internalBankRepository.findAllBankStatementCredits(any(), any(), any())
        } returns listOf(
            bankStatementItem.copy(date = getLocalDate().minusDays(2)),
            bankStatementItem.copy(operationNumber = "12345"),
        )
        every {
            billRepository.getPaidBills(any(), any(), any(), any())
        } returns emptyList()
        every {
            statementItemConverter.convertToPdf(any(), any(), any(), any(), any(), any(), any(), any(), any(), any(), any())
        } returns "PDF".toByteArray()
        every {
            statementItemConverter.convertToCsv(any())
        } throws Exception("erro ao gerar csv")

        val result = statementService.sendStatementByEmail(accountId, wallet.id, startDate, endDate)

        result.isLeft().shouldBeTrue()

        result.mapLeft {
            it shouldBe StatementError.ServerError
        }

        verify(exactly = 0) {
            notificationAdapter.notifyAccountStatement(
                emailAddress = any(),
                name = any(),
                files = any(),
                periodMessage = any(),
            )
        }
    }

    @Test
    fun `deve retornar a data de criacao de uma carteira`() {
        every {
            walletService.findWallet(any())
        } returns wallet
        every {
            accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
        } returns balance
        val result = statementService.getWalletPaymentMethodCreation(wallet.id)
        result.isRight().shouldBeTrue()
        result.map {
            it shouldBe balance.created!!.toLocalDate()
        }
    }

    @DisplayName("quando buscar os depósitos de uma data específica")
    @Nested
    inner class DepositsByMonthTest {
        @Test
        fun `deve retornar apenas depósitos`() {
            every {
                walletService.findWalletOrNull(any())
            } returns wallet
            every {
                accountService.findAccountPaymentMethodByIdAndAccountId(any(), any())
            } returns balance
            every {
                accountStatementAdapter.getStatement(any(), any(), any(), any())
            } returns BankStatement(
                items = listOf(
                    bankStatementItem.copy(amount = 2000L, date = LocalDate.of(2023, 10, 1)),
                    bankStatementItem.copy(operationNumber = "12345", date = LocalDate.of(2023, 10, 19)),
                ),
                initialBalance = Balance(15591 * 2),
                finalBalance = Balance(123 * 2),
            )

            every {
                billRepository.getPaidBills(any(), any(), any(), any())
            } returns listOf(
                getPaidBill().copy(paidDate = LocalDateTime.of(2023, 10, 1, 0, 0)),
                getPaidBill().copy(paidDate = LocalDateTime.of(2023, 10, 2, 0, 0)),
            )

            val result = statementService.findDepositsByDate(
                walletId = wallet.id,
                startDate = LocalDate.of(Year.of(2023).value, Month.OCTOBER, 1),
                endDate = LocalDate.of(Year.of(2023).value, Month.OCTOBER, 31),
            ).getOrElse { fail("should not occur") }

            result.size shouldBe 2
            result[0].amount shouldBe 2000L
            result[0].flow shouldBe BankStatementItemFlow.CREDIT
            result[1].amount shouldBe 123L
            result[1].flow shouldBe BankStatementItemFlow.CREDIT
        }
    }

    @DisplayName("quando buscar os créditos de uma carteira")
    @Nested
    inner class AllCredits {
        @Test
        fun `deve retornar todos os depósitos`() {
            every {
                walletService.findWalletOrNull(any())
            } returns wallet
            every {
                internalBankRepository.findAllBankStatementCredits(any(), any(), any())
            } returns listOf(
                bankStatementItem.copy(amount = 2000L, date = LocalDate.of(2023, 10, 1)),
                bankStatementItem.copy(operationNumber = "12345", date = LocalDate.of(2023, 10, 19)),
                bankStatementItemInvestmentRedemption.copy(amount = 1234L, date = LocalDate.of(2023, 10, 22)),
            )

            val result = statementService.findAllCredits(
                walletId = wallet.id,
                walletFixture.founder,
            ).getOrElse { fail("should not occur") }

            result.size shouldBe 3
            result[0].amount shouldBe 2000L
            result[0].flow shouldBe BankStatementItemFlow.CREDIT
            result[1].amount shouldBe 123L
            result[1].flow shouldBe BankStatementItemFlow.CREDIT
            result[2].amount shouldBe 1234L
            result[2].flow shouldBe BankStatementItemFlow.CREDIT
        }

        @Test
        fun `deve ignorar os depósitos se o usuário não puder ver saldo`() {
            every {
                walletService.findWalletOrNull(any())
            } returns wallet

            val result = statementService.findAllCredits(
                walletId = wallet.id,
                walletFixture.assistant,
            ).getOrElse { fail("should not occur") }

            verify {
                internalBankRepository wasNot Called
            }

            result.size shouldBe 0
        }

        @Test
        fun `deve ignorar depósitos feitos pela conta liquidação da Friday`() {
            every {
                walletService.findWalletOrNull(any())
            } returns wallet
            every {
                internalBankRepository.findAllBankStatementCredits(any(), any(), any())
            } returns listOf(
                bankStatementItem.copy(documentNumber = "1233455", amount = 2000L, date = LocalDate.of(2023, 10, 1)),
                bankStatementItem.copy(documentNumber = "1233455", operationNumber = "12345", date = LocalDate.of(2023, 10, 19)),
                bankStatementItem.copy(documentNumber = "1233455", counterpartDocument = cnpjFriday, counterpartAccountNo = contaLiquidacao),
                bankStatementItem.copy(documentNumber = "1233455", amount = 3000L, date = LocalDate.of(2023, 10, 22), counterpartDocument = cnpjFriday),
            )

            val result = statementService.findAllCredits(
                walletId = wallet.id,
                walletFixture.founder,
            ).getOrElse { fail("should not occur") }

            val balance = result.sumOf { it.amount }

            result.size shouldBe 3
            result[0].amount shouldBe 2000L
            result[0].flow shouldBe BankStatementItemFlow.CREDIT
            result[1].amount shouldBe 123L
            result[1].flow shouldBe BankStatementItemFlow.CREDIT
            result[2].amount shouldBe 3000L
            result[2].flow shouldBe BankStatementItemFlow.CREDIT

            balance shouldBe 5123L
        }

        @Test
        fun `não deve ignorar depósitos friday se o documento da conta for uma conta friday`() {
            every {
                walletService.findWalletOrNull(any())
            } returns wallet
            every {
                accountService.findAccountById(any())
            } returns walletFixture.founderAccount.copy(document = cnpjFriday)
            every {
                internalBankRepository.findAllBankStatementCredits(any(), any(), any())
            } returns listOf(
                bankStatementItem.copy(counterpartDocument = cnpjFriday, amount = 2000L, date = LocalDate.of(2023, 10, 1)),
                bankStatementItem.copy(documentNumber = contaLiquidacao, counterpartDocument = cnpjFriday, operationNumber = "12345", date = LocalDate.of(2023, 10, 19)),
            )

            val result = statementService.findAllCredits(
                walletId = wallet.id,
                walletFixture.founder,
            ).getOrElse { fail("should not occur") }

            val balance = result.sumOf { it.amount }

            result.size shouldBe 2
            result[0].amount shouldBe 2000L
            result[0].flow shouldBe BankStatementItemFlow.CREDIT
            result[1].amount shouldBe 123L
            result[1].flow shouldBe BankStatementItemFlow.CREDIT

            balance shouldBe 2123L
        }
    }
}