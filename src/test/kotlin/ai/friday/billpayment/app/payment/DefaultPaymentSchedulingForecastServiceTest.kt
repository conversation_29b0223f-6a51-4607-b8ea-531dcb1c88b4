package ai.friday.billpayment.app.payment

import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbBillRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.FORECAST_PREFIX
import ai.friday.billpayment.adapters.dynamodb.ScheduleForecastDbRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduleForecastEntity
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduledExternalPayment
import ai.friday.billpayment.app.bill.iso
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ScheduleForecastResult
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billScheduleCanceled
import ai.friday.billpayment.fichaCompensacaoCreditCardAdded
import ai.friday.billpayment.integration.FICHA_DE_COMPENSACAO_DIGITABLE_LINE
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.every
import io.mockk.mockk
import io.mockk.spyk
import io.mockk.verify
import java.time.LocalDate
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Test

class DefaultPaymentSchedulingForecastServiceTest {

    @Test
    fun `should provision bill amount for settlement`() {
        val scheduleDate = LocalDate.now().plusDays(10)

        forecastService.provision(paymentScheduledEvent.copy(scheduledDate = scheduleDate))
        scheduleForecastRepository.find(scheduleDate)
            .shouldBeTypeOf<ScheduleForecastResult.Item>().run {
                scheduledBills shouldBe 1
                paidAmount shouldBe 0
                paidBills shouldBe 0
                provisionedAmount shouldBe paymentScheduledEvent.amount
            }
    }

    @Test
    fun `should deprovision bill amount for internal settlement`() {
        val baseAmount = 500000L

        scheduleForecastRepository.save(
            scheduleForecast = ScheduleForecastEntity().apply {
                primaryKey = FORECAST_PREFIX
                scanKey = paymentScheduledEvent.scheduledDate.iso()
                provisionedAmount = baseAmount
                paidAmount = 200
                scheduledBills = 5
                paidBills = 1
            },
        )

        scheduleForecastRepository.find(paymentScheduledEvent.scheduledDate)
            .shouldBeTypeOf<ScheduleForecastResult.Item>().run {
                provisionedAmount shouldBe baseAmount
                paidAmount shouldBe 200
            }

        forecastService.deprovision(event = paymentScheduledCancelEvent)

        verify {
            scheduleForecastRepository.processPayment(
                paymentScheduledEvent.scheduledDate,
                paymentScheduledEvent.amount,
            )
        }

        scheduleForecastRepository.find(paymentScheduledEvent.scheduledDate)
            .shouldBeTypeOf<ScheduleForecastResult.Item>().run {
                provisionedAmount shouldBe baseAmount - BILL_AMOUNT
                paidAmount shouldBe 200 + BILL_AMOUNT
                paidBills shouldBe 2
                scheduledBills shouldBe 4
            }
    }

    @Test
    fun `should not provision bill amount for bills with external settlement`() {
        forecastService.provision(paymentScheduledEventWithExternalSettlement)

        scheduleForecastRepository
            .find(paymentScheduledEventWithExternalSettlement.scheduledDate)
            .shouldBeTypeOf<ScheduleForecastResult.NotFound>()
    }

    private companion object {
        const val BILL_AMOUNT: Long = 4000L
        val walletFixture = WalletFixture()

        val lockProvider = mockk<InternalLock>(relaxed = true)
        val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
        val dynamoDbDAO = DynamoDbDAO(amazonDynamoDB = dynamoDB)

        val billRepository = spyk(DynamoDbBillRepository(dynamoDbDAO))
        val billEventRepository = spyk(BillEventDBRepository(dynamoDbDAO, mockk(relaxed = true)))

        val billId = BillId("BILL-1")
        val barcode = BarCode.of(FICHA_DE_COMPENSACAO_DIGITABLE_LINE)

        val originWallet = walletFixture.buildWallet(
            "carteira origem",
            otherMembers = listOf(
                walletFixture.assistant,
                walletFixture.limitedParticipant,
                walletFixture.ultraLimitedParticipant,
                walletFixture.participant,
            ),
        )

        val paymentScheduledEvent = billPaymentScheduled.copy(
            walletId = originWallet.id,
            billId = billId,
            amount = BILL_AMOUNT,
            scheduledDate = LocalDate.now().plusDays(16),
        )

        val externalPaymentInfo = BillPaymentScheduledExternalPayment(AccountProviderName.ME_POUPE.name)

        val paymentScheduledEventWithExternalSettlement = paymentScheduledEvent.copy(
            amount = BILL_AMOUNT,
            infoData = externalPaymentInfo,
            scheduledDate = LocalDate.now().plusDays(15),
        )

        val paymentScheduledCancelEvent = billScheduleCanceled.copy(walletId = originWallet.id, billId = billId)

        val scheduleForecastRepository = spyk(ScheduleForecastDbRepository(db = DynamoDbDAO(amazonDynamoDB = dynamoDB)))

        val accountRepository = spyk(AccountDbRepository(dynamoDbDAO))
        val walletRepository = spyk(WalletDbRepository(dynamoDbDAO, accountRepository))

        val forecastService = DefaultPaymentSchedulingForecastService(
            billEventRepository = billEventRepository,
            schedulingForecastRepository = scheduleForecastRepository,
        )

        @BeforeAll
        @JvmStatic
        fun beforeAll() {
            createBillPaymentTable(LocalDbCreationRule.dynamoDB)
            createBillEventTable(LocalDbCreationRule.dynamoDB)

            every { lockProvider.acquireLock(any()) } returns mockk(relaxed = true)

            accountRepository.save(walletFixture.founderAccount)
            walletRepository.save(originWallet)

            listOf(
                fichaCompensacaoCreditCardAdded.copy(
                    walletId = originWallet.id,
                    billId = billId,
                    idNumber = "321",
                    barcode = barcode,
                    amount = BILL_AMOUNT,
                    amountTotal = BILL_AMOUNT,
                ),
                paymentScheduledEvent.copy(amount = BILL_AMOUNT, billId = billId),
                billPaid.copy(billId = billId),
                paymentScheduledCancelEvent.copy(billId = billId),
            ).also { billRepository.save(Bill.build(it.first(), it.last())) }
                .forEach { event -> billEventRepository.save(event) }
        }
    }
}