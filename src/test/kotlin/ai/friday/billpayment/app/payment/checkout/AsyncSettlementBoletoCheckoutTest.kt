package ai.friday.billpayment.app.payment.checkout

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.cashIn.FraudPreventionErrors
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.SettlementPaymentService
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.FraudPreventionPaymentOperationDenied
import ai.friday.billpayment.app.payment.PaymentOperation
import ai.friday.billpayment.app.payment.PaymentStatus
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.SettlementStatus
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.captureFunds.CaptureFundsLocator
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.buildCreditCardAuthorization
import ai.friday.billpayment.concessionariaBill
import ai.friday.billpayment.createBoletoSettlementResult
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.internalBankAccount
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.util.UUID
import java.util.stream.Stream
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource
import software.amazon.awssdk.services.sqs.model.InvalidMessageContentsException

class AsyncSettlementBoletoCheckoutTest {

    private val captureFundsLocator: CaptureFundsLocator = mockk {
        every {
            checkOnFraudPrevention(any())
        } returns null
    }

    private val messagePublisher: MessagePublisher = mockk {
        every {
            sendMessage(any(), any())
        } just Runs
    }

    private val transactionService = mockk<TransactionService>(relaxUnitFun = true)

    private val lock: SimpleLock = mockk {
        every {
            unlock()
        } just Runs
    }

    private val lockProvider: InternalLock = mockk {
        every {
            acquireLock(any())
        } returns lock
    }

    private val queueName = "settlementRequestQueueName"

    private val configuration: AsyncBoletoCheckoutConfiguration = mockk {
        every {
            settlementRequestQueueName
        } returns queueName
    }

    private val settlementPaymentService: SettlementPaymentService = mockk()

    private val checkout = AsyncSettlementBoletoCheckout(
        captureFundsLocator = captureFundsLocator,
        messagePublisher = messagePublisher,
        transactionService = transactionService,
        configuration = configuration,
        settlementPaymentService = settlementPaymentService,
        requestProtocol = RequestProtocol.MESSAGE,
    )

    private val transactionId = TransactionId.build()

    private val settlementOperation = BoletoSettlementResult(
        gateway = FinancialServiceGateway.FRIDAY,
        status = BoletoSettlementStatus.AUTHORIZED,
        bankTransactionId = transactionId.value,
        externalNsu = 0,
        externalTerminal = "",
        errorCode = "",
        errorDescription = null,
    )

    private val transaction = Transaction(
        id = transactionId,
        type = TransactionType.BOLETO_PAYMENT,
        payer = ACCOUNT.toPayer(),
        paymentData = paymentData(concessionariaBill.amountTotal),
        settlementData = SettlementData(
            settlementTarget = concessionariaBill,
            serviceAmountTax = 0,
            totalAmount = concessionariaBill.amountTotal,
            settlementOperation = settlementOperation,
        ),
        nsu = 1,
        actionSource = ActionSource.System,
        walletId = concessionariaBill.walletId,
    )

    fun paymentData(amount: Long) = createSinglePaymentDataWithBalance(
        AccountPaymentMethod(
            id = AccountPaymentMethodId(UUID.randomUUID().toString()),
            status = AccountPaymentMethodStatus.ACTIVE,
            method = internalBankAccount,
            accountId = AccountId(ACCOUNT_ID),
        ),
        amount,
    )

    @Nested
    @DisplayName("ao executar uma transação")
    inner class Execute {

        @Test
        fun `quando o requestProtocol é HTTP deve chamar o settlementPaymentService`() {
            val httpCheckout = AsyncSettlementBoletoCheckout(
                captureFundsLocator = captureFundsLocator,
                messagePublisher = messagePublisher,
                transactionService = transactionService,
                configuration = configuration,
                settlementPaymentService = settlementPaymentService,
                requestProtocol = RequestProtocol.HTTP,
            )

            val paymentOperation: PaymentOperation = mockk() {
                every {
                    status()
                } returns PaymentStatus.SUCCESS
            }

            every {
                captureFundsLocator.captureFunds(transaction)
            } returns paymentOperation

            every {
                settlementPaymentService.request(any())
            } returns Result.success(true)

            httpCheckout.execute(transaction)

            verify(exactly = 1) {
                settlementPaymentService.request(any())
            }

            verify(exactly = 0) {
                messagePublisher.sendMessage(any(), any())
            }

            transaction.status shouldBe TransactionStatus.PROCESSING
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.WAITING_CONFIRMATION
                bankTransactionId shouldBe transaction.id.value
            }
        }

        @Test
        fun `quando o requestProtocol é MESSAGE deve chamar o messagePublisher`() {
            val paymentOperation: PaymentOperation = mockk() {
                every {
                    status()
                } returns PaymentStatus.SUCCESS
            }

            every {
                captureFundsLocator.captureFunds(transaction)
            } returns paymentOperation

            checkout.execute(transaction)

            verify(exactly = 0) {
                settlementPaymentService.request(any())
            }

            verify(exactly = 1) {
                messagePublisher.sendMessage(
                    queueName = queueName,
                    body = any(),
                )
            }

            transaction.status shouldBe TransactionStatus.PROCESSING
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.WAITING_CONFIRMATION
                bankTransactionId shouldBe transaction.id.value
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#errorPaymentOperations")
        fun `quando não consegue capturar fundos não deve comandar a liquidacao (settlementStatus = AUTHORIZED)`(
            paymentOperation: PaymentOperation,
        ) {
            every {
                captureFundsLocator.captureFunds(transaction)
            } returns paymentOperation

            checkout.execute(transaction)

            verify(exactly = 0) {
                messagePublisher.sendMessage(any(), any())
            }

            verify {
                transactionService.save(transaction)
            }

            transaction.status shouldBe TransactionStatus.FAILED
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.AUTHORIZED
                bankTransactionId shouldBe transaction.id.value
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#successPaymentOperations")
        fun `quando consegue capturar fundos deve comandar a liquidacao (settlementStatus = WAITING_CONFIRMATION)`(
            paymentOperation: PaymentOperation,
        ) {
            every {
                captureFundsLocator.captureFunds(transaction)
            } returns paymentOperation

            checkout.execute(transaction)

            val slot = slot<SettlementClientRequestTO>()
            verify {
                messagePublisher.sendMessage(
                    queueName = queueName,
                    body = capture(slot),
                )
                transactionService.save(transaction)
            }

            val bill = transaction.settlementData.getTarget<Bill>()

            with(slot.captured) {
                transactionId shouldBe transaction.id.value
                amount shouldBe transaction.settlementData.totalAmount
                barcode shouldBe bill.barcode!!.number
                finalPayer.name shouldBe transaction.payer.name
                finalPayer.commercialName shouldBe null
                finalPayer.documentNumber shouldBe transaction.payer.document
            }

            transaction.status shouldBe TransactionStatus.PROCESSING
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.WAITING_CONFIRMATION
                bankTransactionId shouldBe transaction.id.value
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#errorSendMessageExceptions")
        fun `quando nao consegue comandar a liquidacao (settlementStatus = AUTHORIZED) deve cancelar a captura`(
            exception: Exception,
        ) {
            val paymentOperation: PaymentOperation = mockk() {
                every {
                    status()
                } returns PaymentStatus.SUCCESS
            }

            every {
                captureFundsLocator.captureFunds(transaction)
            } returns paymentOperation

            every {
                messagePublisher.sendMessage(any(), any())
            } throws exception

            val undoPaymentOperation: PaymentOperation = mockk() {
                every {
                    status()
                } returns PaymentStatus.SUCCESS
            }

            every {
                captureFundsLocator.undoCaptureFunds(transaction)
            } returns undoPaymentOperation

            checkout.execute(transaction)

            verify {
                messagePublisher.sendMessage(
                    queueName = queueName,
                    body = any(),
                )
                captureFundsLocator.undoCaptureFunds(transaction)
                transactionService.save(transaction)
            }

            transaction.status shouldBe TransactionStatus.FAILED
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.AUTHORIZED
                bankTransactionId shouldBe transaction.id.value
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#successPaymentOperations")
        fun `quando nao sabe se conseguiu comandar a liquidacao -settlementStatus = UNKNOWN- deve retentar`(
            paymentOperation: PaymentOperation,
        ) {
            every {
                captureFundsLocator.captureFunds(transaction)
            } returns paymentOperation

            every {
                messagePublisher.sendMessage(any(), any())
            } throws NoStackTraceException()

            checkout.execute(transaction)

            verify {
                messagePublisher.sendMessage(
                    queueName = queueName,
                    body = any(),
                )
                transactionService.save(transaction)
            }
            verify(exactly = 0) {
                captureFundsLocator.undoCaptureFunds(any())
            }

            transaction.status shouldBe TransactionStatus.PROCESSING

            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.UNKNOWN
                bankTransactionId shouldBe transaction.id.value
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#unknownPaymentOperations")
        fun `quando o resultado da captura eh desconhecido deve tentar cancelar`(paymentOperation: PaymentOperation) {
            every {
                captureFundsLocator.captureFunds(transaction)
            } returns paymentOperation

            val undoPaymentOperation: PaymentOperation = mockk() {
                every {
                    status()
                } returns PaymentStatus.SUCCESS
            }

            every {
                captureFundsLocator.undoCaptureFunds(transaction)
            } returns undoPaymentOperation

            checkout.execute(transaction)

            verify {
                messagePublisher wasNot Called
                captureFundsLocator.undoCaptureFunds(transaction)
                transactionService.save(transaction)
            }

            transaction.status shouldBe TransactionStatus.FAILED
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.AUTHORIZED
                bankTransactionId shouldBe transaction.id.value
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#unknownPaymentOperations")
        fun `quando nao consegue cancelar a captura deve manter a transacao processando`(paymentOperation: PaymentOperation) {
            every {
                captureFundsLocator.captureFunds(transaction)
            } returns paymentOperation

            val undoPaymentOperation: PaymentOperation = mockk {
                every {
                    status()
                } returns PaymentStatus.ERROR
            }

            every {
                captureFundsLocator.undoCaptureFunds(transaction)
            } returns undoPaymentOperation

            checkout.execute(transaction)

            verify {
                messagePublisher wasNot Called
                captureFundsLocator.undoCaptureFunds(transaction)
                transactionService.save(transaction)
            }

            transaction.status shouldBe TransactionStatus.PROCESSING
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.AUTHORIZED
                bankTransactionId shouldBe transaction.id.value
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#errorSendMessageExceptions")
        fun `quando captura, não consegue publicar a liquidação -settlementStatus = AUTHORIZED- e falha no cancelamento, deve manter a transação processando`(
            exception: Exception,
        ) {
            val paymentOperation: PaymentOperation = mockk() {
                every {
                    status()
                } returns PaymentStatus.SUCCESS
            }

            every {
                captureFundsLocator.captureFunds(transaction)
            } returns paymentOperation

            every {
                messagePublisher.sendMessage(any(), any())
            } throws exception

            val undoPaymentOperation: PaymentOperation = mockk {
                every {
                    status()
                } returns PaymentStatus.UNKNOWN
            }

            every {
                captureFundsLocator.undoCaptureFunds(transaction)
            } returns undoPaymentOperation

            checkout.execute(transaction)

            verify {
                messagePublisher.sendMessage(
                    queueName = queueName,
                    body = any(),
                )
                captureFundsLocator.undoCaptureFunds(transaction)
                transactionService.save(transaction)
            }

            transaction.status shouldBe TransactionStatus.PROCESSING
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.AUTHORIZED
                bankTransactionId shouldBe transaction.id.value
            }
        }

        @Test
        fun `quando nao tiver limite deve cancelar transacao`() {
            every {
                captureFundsLocator.checkOnFraudPrevention(transaction)
            } returns FraudPreventionPaymentOperationDenied(FraudPreventionErrors.NO_LIMIT_AVAILABLE)

            checkout.execute(transaction)

            verify {
                messagePublisher wasNot Called
                transactionService.save(transaction)
            }

            transaction.status shouldBe TransactionStatus.FAILED
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.AUTHORIZED
                bankTransactionId shouldBe transaction.id.value
            }
        }
    }

    @Nested
    @DisplayName("ao continuar uma transação")
    inner class ContinueSettlement {
        @Test
        fun `quando a liquidação terminar com sucesso, deve atualizar a transação para completa`() {
            val settlementOperation = createBoletoSettlementResult(
                status = BoletoSettlementStatus.CONFIRMED,
                transactionId = transaction.id.value,
            )

            val transactionResult = checkout.continueSettlement(transaction, settlementOperation)

            verify { transactionService.save(transaction) }

            transactionResult.status shouldBe TransactionStatus.COMPLETED
            transactionResult.settlementData.settlementOperation shouldBe settlementOperation
        }

        @ParameterizedTest
        @EnumSource(
            BoletoSettlementStatus::class,
            mode = EnumSource.Mode.INCLUDE,
            names = ["VOIDED", "UNAUTHORIZED"],
        )
        fun `quando a liquidação terminar com erro, deve atualizar a transação para falha`(status: BoletoSettlementStatus) {
            val settlementOperation = createBoletoSettlementResult(
                status = status,
                transactionId = transaction.id.value,
            )

            val transactionResult = checkout.continueSettlement(transaction, settlementOperation)

            verify { transactionService.save(transaction) }

            transactionResult.status shouldBe TransactionStatus.FAILED
            transactionResult.settlementData.settlementOperation shouldBe settlementOperation
        }

        @ParameterizedTest
        @EnumSource(
            BoletoSettlementStatus::class,
            mode = EnumSource.Mode.EXCLUDE,
            names = ["CONFIRMED", "UNAUTHORIZED", "VOIDED"],
        )
        fun `quando o settlement operation estiver num estado não suportado, deve lancar uma excecao`(status: BoletoSettlementStatus) {
            val settlementOperation = createBoletoSettlementResult(
                status = status,
                transactionId = transaction.id.value,
            )

            assertThrows<IllegalStateException> { checkout.continueSettlement(transaction, settlementOperation) }
        }
    }

    @Nested
    @DisplayName("ao desfazer uma transação")
    inner class RollbackTransaction {

        @Test
        fun `quando a transação está pendente e o paymentStatus é desconhecido, deve ser cancelada`() {
            val undoPaymentOperation: PaymentOperation = mockk<PaymentOperation> {
                every {
                    status()
                } returns PaymentStatus.SUCCESS
            }

            every {
                captureFundsLocator.undoCaptureFunds(transaction)
            } returns undoPaymentOperation

            transaction.settlementData.settlementOperation = null
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            val transactionResult = checkout.rollbackTransaction(transaction)

            verify {
                captureFundsLocator.undoCaptureFunds(transaction)
                transactionService.save(transaction)
            }

            transactionResult.status shouldBe TransactionStatus.FAILED
            transactionResult.settlementData.settlementOperation shouldBe null
        }

        @Test
        fun `quando a transação está pendente e comandou a liquidação -settlementStatus = WAITING_CONFIRMATION-, deve jogar uma exception`() {
            transaction.settlementData.settlementOperation = mockk<BoletoSettlementResult> {
                every { status } returns BoletoSettlementStatus.WAITING_CONFIRMATION
            }

            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            assertThrows<IllegalStateException> { checkout.rollbackTransaction(transaction) }

            verify(exactly = 0) {
                captureFundsLocator.undoCaptureFunds(transaction)
                transactionService.save(transaction)
            }
        }

        @Test
        fun `quando a transação está pendente e não sabe se comandou a liquidação -settlementStatus = UNKNOWN-, deve jogar uma exception`() {
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            transaction.settlementData.settlementOperation = mockk<BoletoSettlementResult> {
                every { status } returns BoletoSettlementStatus.UNKNOWN
            }

            assertThrows<IllegalStateException> { checkout.rollbackTransaction(transaction) }

            verify(exactly = 0) {
                captureFundsLocator.undoCaptureFunds(transaction)
                transactionService.save(transaction)
            }

            transaction.status shouldBe TransactionStatus.PROCESSING
        }

        @Test
        fun `quando a transação está pendente e não comandou a liquidação -settlementStatus = AUTHORIZED-, deve cancelar a transação`() {
            val undoPaymentOperation: PaymentOperation = mockk<PaymentOperation> {
                every {
                    status()
                } returns PaymentStatus.SUCCESS
            }

            every {
                captureFundsLocator.undoCaptureFunds(transaction)
            } returns undoPaymentOperation

            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            transaction.settlementData.settlementOperation = mockk<BoletoSettlementResult> {
                every { status } returns BoletoSettlementStatus.AUTHORIZED
            }

            val transactionResult = checkout.rollbackTransaction(transaction)

            verify {
                captureFundsLocator.undoCaptureFunds(transaction)
                transactionService.save(transaction)
            }

            transactionResult.status shouldBe TransactionStatus.FAILED
            with(transaction.settlementData.getOperation<BoletoSettlementResult>()) {
                status shouldBe BoletoSettlementStatus.AUTHORIZED
            }
        }

        @ParameterizedTest
        @EnumSource(
            PaymentStatus::class,
            mode = EnumSource.Mode.EXCLUDE,
            names = ["SUCCESS", "REFUNDED"],
        )
        fun `quando a transação estiver pendente, tiver que rodar o undoCaptureFunds e o mesmo falhar, deve jogar uma exception`(paymentStatus: PaymentStatus) {
            val undoPaymentOperation: PaymentOperation = mockk<PaymentOperation> {
                every {
                    status()
                } returns paymentStatus
            }

            every {
                captureFundsLocator.undoCaptureFunds(transaction)
            } returns undoPaymentOperation

            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            transaction.settlementData.settlementOperation = mockk<BoletoSettlementResult> {
                every { status } returns BoletoSettlementStatus.AUTHORIZED
            }

            assertThrows<IllegalStateException> { checkout.rollbackTransaction(transaction) }

            verify {
                captureFundsLocator.undoCaptureFunds(transaction)
            }

            verify(exactly = 0) {
                transactionService.save(transaction)
            }

            transaction.status shouldBe TransactionStatus.PROCESSING
        }
    }

    @Nested
    @DisplayName("ao verificar o estado de uma liquidação")
    inner class CheckSettlementStatus {

        @Test
        fun `quando não consegue o lock da transação deve retornar Processing`() {
            transaction.settlementData.settlementOperation = mockk<BoletoSettlementResult> {
                every { status } returns BoletoSettlementStatus.AUTHORIZED
            }

            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            every {
                lockProvider.acquireLock(any())
            } returns null

            val result = checkout.checkSettlementStatus(transaction)

            result shouldBe SettlementStatus.Processing
        }

        @Test
        fun `quando o pagamento terminou com erro deve retornar falha`() {
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.ERROR
            }

            val result = checkout.checkSettlementStatus(transaction)

            result.shouldBeTypeOf<SettlementStatus.Failure>()
            result.message shouldBe "Pagamento falhou."

            verify {
                messagePublisher wasNot Called
            }
        }

        @Test
        fun `quando o pagamento está autorizado deve retornar processando`() {
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.AUTHORIZED
            }

            val result = checkout.checkSettlementStatus(transaction)

            result shouldBe SettlementStatus.Processing

            verify {
                messagePublisher wasNot Called
            }
        }

        @Test
        fun `quando o pagamento está num estado desconhecido deve retornar processando`() {
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.UNKNOWN
            }

            val result = checkout.checkSettlementStatus(transaction)

            result shouldBe SettlementStatus.Processing

            verify {
                messagePublisher wasNot Called
            }
        }

        @Test
        fun `quando o pagamento foi estornado deve retornar falha`() {
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.REFUNDED
            }

            val result = checkout.checkSettlementStatus(transaction)

            result.shouldBeTypeOf<SettlementStatus.Failure>()
            result.message shouldBe "Pagamento estornado."

            verify {
                messagePublisher wasNot Called
            }
        }

        @Test
        fun `quando a transação está pendente aguardando confirmação da liquidação, não deve mudar a transacao`() {
            transaction.settlementData.settlementOperation = mockk<BoletoSettlementResult> {
                every { status } returns BoletoSettlementStatus.WAITING_CONFIRMATION
            }

            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            val result = checkout.checkSettlementStatus(transaction)

            result shouldBe SettlementStatus.Processing
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#shouldRetrySettlementOperations")
        fun `quando a transação está pendente e não conseguiu ou não sabe se conseguiu comandar a liquidação, deve comandar a liquidação`(
            settlementOperation: SettlementOperation?,
        ) {
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            transaction.settlementData.settlementOperation = settlementOperation

            val result = checkout.checkSettlementStatus(transaction)

            result shouldBe SettlementStatus.Processing

            transaction.settlementData.getOperation<BoletoSettlementResult>().status shouldBe BoletoSettlementStatus.WAITING_CONFIRMATION

            verify {
                messagePublisher.sendMessage(
                    queueName = queueName,
                    body = any(),
                )
                transactionService.save(transaction)
            }
        }

        @Test
        fun `quando a transação responder que está esperando a liquidação, deve responder que está procesando`() {
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            transaction.settlementData.settlementOperation = mockk<BoletoSettlementResult> {
                every { status } returns BoletoSettlementStatus.WAITING_CONFIRMATION
            }

            val result = checkout.checkSettlementStatus(transaction)

            result.shouldBeTypeOf<SettlementStatus.Processing>()

            verify(exactly = 0) {
                transactionService.save(transaction)
                messagePublisher.sendMessage(
                    queueName = queueName,
                    body = any(),
                )
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#shouldCompleteSettlementOperations")
        fun `quando a transação responder que foi finalizada com sucesso, deve responder que já completou`(
            settlementOperation: SettlementOperation,
        ) {
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            transaction.settlementData.settlementOperation = settlementOperation

            val result = checkout.checkSettlementStatus(transaction)

            result.shouldBeTypeOf<SettlementStatus.AlreadyCompleted>()

            verify(exactly = 0) {
                transactionService.save(transaction)
                messagePublisher.sendMessage(
                    queueName = queueName,
                    body = any(),
                )
            }
        }

        @ParameterizedTest
        @MethodSource("ai.friday.billpayment.app.payment.checkout.AsyncSettlementBoletoCheckoutTest#shouldFailSettlementOperations")
        fun `quando a transação responder que foi finalizada com erro, deve responder que falhou`(
            settlementOperation: SettlementOperation,
        ) {
            transaction.paymentData.toSingle().payment = mockk<PaymentOperation> {
                every { status() } returns PaymentStatus.SUCCESS
            }

            transaction.settlementData.settlementOperation = settlementOperation

            val result = checkout.checkSettlementStatus(transaction)

            result.shouldBeTypeOf<SettlementStatus.Failure>()

            verify(exactly = 0) {
                transactionService.save(transaction)
                messagePublisher.sendMessage(
                    queueName = queueName,
                    body = any(),
                )
            }
        }
    }

    companion object {
        @JvmStatic
        fun errorPaymentOperations(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    BalanceAuthorization(
                        status = BankOperationStatus.ERROR,
                        amount = concessionariaBill.amountTotal,
                        paymentGateway = FinancialServiceGateway.ARBI,
                        operationId = BankOperationId("1"),
                        errorDescription = "error",
                        timeout = false,
                    ),
                ),
                Arguments.arguments(
                    BalanceAuthorization(
                        operationId = BankOperationId("asd"),
                        status = BankOperationStatus.INSUFFICIENT_FUNDS,
                        amount = concessionariaBill.amountTotal,
                        paymentGateway = FinancialServiceGateway.ARBI,
                    ),
                ),
                Arguments.arguments(
                    buildCreditCardAuthorization(
                        status = CreditCardPaymentStatus.DENIED,
                        amount = 1L,
                    ),
                ),
            )
        }

        @JvmStatic
        fun successPaymentOperations(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    BalanceAuthorization(
                        status = BankOperationStatus.SUCCESS,
                        amount = concessionariaBill.amountTotal,
                        paymentGateway = FinancialServiceGateway.ARBI,
                    ),
                ),
                Arguments.arguments(
                    buildCreditCardAuthorization(
                        status = CreditCardPaymentStatus.PAYMENT_CONFIRMED,
                        amount = 1L,
                    ),
                ),
            )
        }

        @JvmStatic
        fun unknownPaymentOperations(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    BalanceAuthorization(
                        status = BankOperationStatus.UNKNOWN,
                        amount = concessionariaBill.amountTotal,
                        paymentGateway = FinancialServiceGateway.ARBI,
                        operationId = BankOperationId("1"),
                        errorDescription = "error",
                        timeout = true,
                    ),
                ),
                Arguments.arguments(
                    buildCreditCardAuthorization(
                        status = CreditCardPaymentStatus.ABORTED,
                        amount = 1L,
                    ),
                ),
            )
        }

        @JvmStatic
        fun errorSendMessageExceptions(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(InvalidMessageContentsException.builder().build()),
                Arguments.arguments(UnsupportedOperationException()),
            )
        }

        @JvmStatic
        fun shouldCompleteSettlementOperations(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    mockk<BoletoSettlementResult> {
                        every { status } returns BoletoSettlementStatus.CONFIRMED
                    },
                ),
            )
        }

        @JvmStatic
        fun shouldFailSettlementOperations(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    mockk<BoletoSettlementResult> {
                        every { status } returns BoletoSettlementStatus.UNAUTHORIZED
                    },
                ),
                Arguments.arguments(
                    mockk<BoletoSettlementResult> {
                        every { status } returns BoletoSettlementStatus.VOIDED
                    },
                ),
            )
        }

        @JvmStatic
        fun shouldRetrySettlementOperations(): Stream<Arguments> {
            return Stream.of(
                Arguments.arguments(
                    mockk<BoletoSettlementResult> {
                        every { status } returns BoletoSettlementStatus.AUTHORIZED
                    },
                ),
                Arguments.arguments(
                    mockk<BoletoSettlementResult> {
                        every { status } returns BoletoSettlementStatus.UNKNOWN
                    },
                ),
            )
        }
    }
}