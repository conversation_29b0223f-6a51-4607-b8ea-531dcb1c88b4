package ai.friday.billpayment.app.payment

import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.ScheduleCanceledReason
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.TEDService
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import ai.friday.billpayment.app.payment.transaction.CompleteTransaction
import ai.friday.billpayment.app.payment.transaction.FailTransaction
import ai.friday.billpayment.app.payment.transaction.PaymentFailedScheduleResolver
import ai.friday.billpayment.app.payment.transaction.PrepareTransaction
import ai.friday.billpayment.app.payment.transaction.StartTransaction
import ai.friday.billpayment.app.payment.transaction.TransactionInProcess
import ai.friday.billpayment.createSinglePaymentDataWithBalance
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillEventTable
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.internalBankAccount
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoiceBill
import ai.friday.billpayment.invoicePaymentScheduled
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.paymentMethodId2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.util.UUID
import net.javacrumbs.shedlock.core.SimpleLock
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

internal class InvoicePaymentServiceTest {

    private val updateBillService: UpdateBillService = mockk(relaxed = true)

    private val billEventPublisher: BillEventPublisher = mockk(relaxed = true)

    private val internalBankService: InternalBankService = mockk(relaxUnitFun = true)

    private val tedService: TEDService = mockk()

    private val mockMessagePublisher: MessagePublisher = mockk()

    private val balanceDirectInvoiceCheckout = BalanceDirectInvoiceCheckout(tedService)

    private val checkoutLocator = DefaultCheckoutLocator(
        balanceInvoiceCheckout = BalanceInvoiceCheckout(internalBankService, tedService),
        balancePixCheckout = mockk(),
        balanceDirectInvoiceCheckout = balanceDirectInvoiceCheckout,
        boletoCheckout = mockk(),
        asyncSettlementBoletoCheckout = mockk(),
        featureConfiguration = mockk(),
        investmentCheckout = mockk(),
    )

    private val bankAccount = internalBankAccount

    private val notificationMock: NotificationAdapter = mockk(relaxed = true)

    private val successBankTransfer = BankTransfer(
        status = BankOperationStatus.SUCCESS,
        amount = invoiceAdded.amountTotal,
        gateway = FinancialServiceGateway.ARBI,
    )

    private val errorDescription = "Generic error"

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val billEventRepository = ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository(
        dynamoDbDAO,
        allFalseFeatureConfiguration,
    )

    private val accountRepository = AccountDbRepository(dynamoDbDAO)

    private val transactionService = TransactionService(
        accountRepository = accountRepository,
        updateBillService = updateBillService,
        notificationAdapter = notificationMock,
        walletService = mockk(),
        transactionRepository = DynamoDbTransactionRepository(
            dynamoDbDAO = dynamoDbDAO,
            accountRepository = accountRepository,
            transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, mockk()),
        ),
    )

    private val notifyReceiptService = mockk<NotifyReceiptService>()
    private val completeTransaction = CompleteTransaction(
        billEventPublisher = billEventPublisher,
        billInstrumentationService = mockk(relaxUnitFun = true),
        balanceService = mockk(relaxed = true),
        notifyReceiptService = notifyReceiptService,
        onboardingInstrumentationService = mockk(relaxed = true),
    )

    private val paymentFailedScheduleResolver = PaymentFailedScheduleResolver(billEventPublisher = billEventPublisher)

    private val failTransaction = FailTransaction(
        transactionService = transactionService,
        billEventPublisher = billEventPublisher,
        paymentFailedScheduleResolver = paymentFailedScheduleResolver,
    )
    private val transactionInProcess = TransactionInProcess(
        messagePublisher = mockMessagePublisher,
        checkoutLocator = checkoutLocator,
    )

    private val prepareTransaction = PrepareTransaction(
        updateBillService = updateBillService,
        boletoSettlementService = mockk(),
    )

    private val startTransaction = StartTransaction(
        transactionService = transactionService,
        billEventPublisher = billEventPublisher,
    )

    private val simpleLock: SimpleLock = mockk(relaxed = true)

    private val lockProvider: InternalLock = mockk {
        every { acquireLock(any()) } returns simpleLock
    }

    private val service = BillPaymentService(
        transactionService = transactionService,
        checkoutLocator = checkoutLocator,
        prepareTransaction = prepareTransaction,
        startTransaction = startTransaction,
        completeTransaction = completeTransaction,
        failTransaction = failTransaction,
        transactionInProcess = transactionInProcess,
        lockProvider = lockProvider,
    )

    private val balance = AccountPaymentMethod(
        id = paymentMethodId2,
        accountId = AccountId(ACCOUNT_ID),
        status = AccountPaymentMethodStatus.ACTIVE,
        method = bankAccount,
        created = getZonedDateTime(),
    )

    private val invoiceTransaction = Transaction(
        type = TransactionType.INVOICE_PAYMENT,
        payer = ACCOUNT.toPayer(),
        paymentData = createSinglePaymentDataWithBalance(balance, invoiceBill.amountTotal),
        settlementData = SettlementData(invoiceBill, 0, invoiceBill.amountTotal),
        nsu = 1,
        actionSource = ActionSource.Scheduled,
        walletId = invoiceBill.walletId,
    )

    @BeforeEach
    fun setUp() {
        createBillPaymentTable(dynamoDB)
        createBillEventTable(dynamoDB)
        billEventRepository.save(invoiceAdded)
        billEventRepository.save(invoicePaymentStarted)
        with(bankAccount) { loadBalancePaymentMethod(accountRepository, bankNo, routingNo, accountNo.toBigInteger(), accountDv) }
    }

    @Test
    fun `should fail balance invoice transaction on INSUFFICIENT FUNDS`() {
        every {
            internalBankService.captureFunds(
                any(),
                any(),
                bankAccount,
                any(),
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.INSUFFICIENT_FUNDS,
                amount = invoiceBill.amountTotal,
                errorDescription = errorDescription,
                gateway = FinancialServiceGateway.ARBI,
            )
        }

        service.process(transaction = invoiceTransaction)

        val transactionDB = transactionService.findTransactionById(invoiceTransaction.id)
        invoiceTransaction.status shouldBe TransactionStatus.FAILED
        invoiceTransaction.paymentData.toSingle()
            .get<BalanceAuthorization>().status shouldBe BankOperationStatus.INSUFFICIENT_FUNDS
        invoiceTransaction.settlementData.settlementOperation shouldBe null
        transactionDB.id shouldBe invoiceTransaction.id
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(invoiceTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe errorDescription
    }

    @Test
    fun `should rollback balance invoice transaction on settlement transfer failure`() {
        every { internalBankService.captureFunds(any(), any(), bankAccount, any()) } answers { successBankTransfer }
        every {
            tedService.transfer(
                any(),
                any(),
                any(),
                any(),
            )
        } answers {
            TEDResult(
                status = TEDStatus.Failure(message = errorDescription),
                settleDate = getLocalDate(),
                authentication = "AUTH",
                bankTransactionId = 0,
                receipt = "receipt",
                gateway = FinancialServiceGateway.CELCOIN,
                amount = invoiceBill.amountTotal,
            )
        }
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                successBankTransfer.operationId,
                any(),
            )
        } answers { successBankTransfer }

        service.process(transaction = invoiceTransaction)

        val transactionDB = transactionService.findTransactionById(invoiceTransaction.id)
        assertTransaction(
            transactionDB,
            TransactionStatus.FAILED,
            BankOperationStatus.REFUNDED,
            BankOperationStatus.ERROR,
        )
        // transactionDB shouldBeEqualToComparingFields invoiceTransaction
        val slot = slot<PaymentFailed>()
        verify {
            billEventPublisher.publish(invoiceTransaction.settlementData.getTarget(), capture(slot))
        }
        slot.captured.errorDescription shouldBe TransactionError.GENERIC_EXCEPTION.description
        slot.captured.retryable shouldBe true
    }

    @Test
    fun `should rollback balance invoice transaction on not retryable settlement transfer failure`() {
        every { internalBankService.captureFunds(any(), any(), bankAccount, any()) } answers { successBankTransfer }
        every { tedService.transfer(any(), any(), any(), any()) } answers {
            TEDResult(
                status = TEDStatus.InvalidData(
                    message = errorDescription,
                ),
                settleDate = getLocalDate(),
                authentication = "AUTH",
                bankTransactionId = 0,
                receipt = "receipt",
                gateway = FinancialServiceGateway.CELCOIN,
                amount = invoiceBill.amountTotal,
            )
        }
        every {
            internalBankService.undoCaptureFunds(
                any(),
                any(),
                bankAccount,
                successBankTransfer.operationId,
                any(),
            )
        } answers { successBankTransfer }

        service.process(transaction = invoiceTransaction)

        val transactionDb = transactionService.findTransactionById(invoiceTransaction.id)
        assertTransaction(
            transactionDb,
            TransactionStatus.FAILED,
            BankOperationStatus.REFUNDED,
            BankOperationStatus.INVALID_DATA,
        )
        // transactionDb shouldBe invoiceTransaction
        val slot = mutableListOf<BillEvent>()
        verify {
            billEventPublisher.publish(invoiceTransaction.settlementData.getTarget(), capture(slot))
        }
        with(slot[0]) {
            shouldBeTypeOf<PaymentFailed>()
            errorDescription shouldBe errorDescription
            this.retryable shouldBe false
        }
    }

    @Test
    fun `should send to retry transaction when capture funds times out`() {
        every {
            internalBankService.captureFunds(
                any(),
                any(),
                bankAccount,
                any(),
            )
        } answers {
            BankTransfer(
                status = BankOperationStatus.TIMEOUT,
                gateway = FinancialServiceGateway.ARBI,
                amount = invoiceTransaction.settlementData.totalAmount,
            )
        }
        every { mockMessagePublisher.sendRetryTransactionMessage(ofType(TransactionId::class), any()) } just runs

        service.process(transaction = invoiceTransaction)

        val transactionDB = transactionService.findTransactionById(invoiceTransaction.id)
        transactionDB.status shouldBe TransactionStatus.PROCESSING
        transactionDB.paymentData.toSingle().get<BalanceAuthorization>().status shouldBe BankOperationStatus.TIMEOUT

        verify {
            mockMessagePublisher.sendRetryTransactionMessage(invoiceTransaction.id, null)
        }

        verify(exactly = 0) {
            billEventPublisher.publish(any(), any())
        }
    }

    @Test
    fun `should publish BillPaymentScheduleCanceled publication when bill is scheduled and transaction is complete`() {
        every { internalBankService.captureFunds(any(), any(), bankAccount, any()) } answers { successBankTransfer }
        every { tedService.transfer(any(), any(), any(), any()) } answers {
            TEDResult(
                status = TEDStatus.Success,
                settleDate = getLocalDate(),
                authentication = "AUTH",
                bankTransactionId = 0,
                receipt = "receipt",
                gateway = FinancialServiceGateway.CELCOIN,
                amount = invoiceBill.amountTotal,
            )
        }
        every {
            notifyReceiptService.notifyWithAsyncRetry(any())
        } just Runs

        invoiceTransaction.settlementData.getTarget<Bill>().apply(invoicePaymentScheduled, true)
        service.process(transaction = invoiceTransaction)

        val slot = mutableListOf<BillEvent>()
        verify {
            billEventPublisher.publish(any(), capture(slot))
            notifyReceiptService.notifyWithAsyncRetry(any())
        }
        slot[0].shouldBeTypeOf<BillPaid>()
        with(slot[1]) {
            shouldBeTypeOf<BillPaymentScheduleCanceled>()
            reason shouldBe ScheduleCanceledReason.EXECUTED
        }
    }

    @Test
    fun `should complete transaction on direct invoice type`() {
        every { tedService.transfer(any(), any(), any()) } answers {
            DirectTEDResult(
                status = DirectTEDStatus.Success,
                settleDate = getLocalDate(),
                authentication = "AUTH",
                gateway = FinancialServiceGateway.ARBI,
                amount = invoiceBill.amountTotal,
                operationId = BankOperationId(value = UUID.randomUUID().toString()),
            )
        }
        invoiceTransaction.settlementData.getTarget<Bill>().apply(invoicePaymentScheduled, true)

        every {
            notifyReceiptService.notifyWithAsyncRetry(any())
        } just Runs

        service.process(transaction = invoiceTransaction.copy(type = TransactionType.DIRECT_INVOICE))

        val slot = mutableListOf<BillEvent>()
        verify {
            billEventPublisher.publish(any(), capture(slot))
            notifyReceiptService.notifyWithAsyncRetry(any())
        }
        slot[0].shouldBeTypeOf<BillPaid>()
        with(slot[1]) {
            shouldBeTypeOf<BillPaymentScheduleCanceled>()
            reason shouldBe ScheduleCanceledReason.EXECUTED
        }

        with(transactionService.findTransactionById(invoiceTransaction.id)) {
            status shouldBe TransactionStatus.COMPLETED
            (settlementData.settlementOperation as BankTransfer).authentication shouldBe "AUTH"
        }
    }

    private fun assertTransaction(
        transaction: Transaction,
        transactionStatus: TransactionStatus,
        paymentBankOperationStatus: BankOperationStatus,
        settlementBankOperationStatus: BankOperationStatus,
    ) {
        transaction.status shouldBe transactionStatus
        transaction.paymentData.toSingle().get<BalanceAuthorization>().status shouldBe paymentBankOperationStatus
        transaction.settlementData.getOperation<BankTransfer>().status shouldBe settlementBankOperationStatus
    }
}