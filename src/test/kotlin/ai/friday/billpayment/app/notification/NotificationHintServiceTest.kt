package ai.friday.billpayment.app.notification

import ai.friday.billpayment.adapters.micronaut.NotificationHintConfigurationMicronaut
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.ScheduledBillRepository
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.createPaymentMethodsDetailWithBalance
import ai.friday.billpayment.getPaidBill
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.paymentMethodId2
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.withGivenDateTime
import io.kotest.matchers.nulls.shouldBeNull
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import java.time.ZoneId
import java.time.ZonedDateTime
import org.junit.jupiter.api.Test

class NotificationHintServiceTest {

    var dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val billRepository = mockk<BillRepository>()

    private val scheduledBillRepository = mockk<ScheduledBillRepository>()

    private val billCreatedHints = listOf("HINT 1", "HINT 2 - <EMAIL>")
    private val billComingDueHints = listOf("COMING DUE HINT 1", "COMING DUE HINT 2 - <EMAIL>")
    private val billComingDueLastWarnHints =
        listOf("COMING DUE LAST WARN HINT 1", "COMING DUE LAST WARN HINT 2 - <EMAIL>")
    private val billOverdueYesterdayHints = listOf("OVERDUE HINT 1", "OVERDUE HINT 2 - <EMAIL>")

    private val notificationHintService =
        DefaultNotificationHintService(
            billRepository = billRepository,
            scheduledBillRepository = scheduledBillRepository,
            notificationHintConfiguration = NotificationHintConfigurationMicronaut(
                billCreated = billCreatedHints,
                billComingDue = billComingDueHints,
                billComingDueLastWarn = billComingDueLastWarnHints,
                billOverdueYesterday = billOverdueYesterdayHints,
            ),
        )

    private val walletFixture = WalletFixture()

    private val wallet = walletFixture.buildWallet()

    private val scheduledBill = ScheduledBill(
        walletId = wallet.id,
        billId = BillId(BILL_ID),
        scheduledDate = getLocalDate(),
        billType = BillType.FICHA_COMPENSACAO,
        amount = 1L,
        scheduleTo = ScheduleTo.DUE_DATE,
        paymentLimitTime = null,
        paymentMethodsDetail = createPaymentMethodsDetailWithBalance(paymentMethodId2, billAdded.amountTotal),
        batchSchedulingId = BatchSchedulingId(),
    )

    @Test
    fun `should notify without hint when wallet has paid bills`() {
        every {
            billRepository.getPaidBills(walletId = wallet.id, any(), any(), any())
        } returns listOf(getPaidBill())

        val billCreatedHint = notificationHintService.getBillCreated(wallet.id, wallet.founder)
        val billComingDueHint = notificationHintService.getBillComingDue(wallet.id, wallet.founder)
        val billComingDueLastWarnHint = notificationHintService.getBillComingDueLastWarn(wallet.id, wallet.founder)
        val billOverdueYesterday = notificationHintService.getBillOverdueYesterday(wallet.id, wallet.founder)

        billCreatedHint.shouldBeNull()
        billComingDueHint.shouldBeNull()
        billComingDueLastWarnHint.shouldBeNull()
        billOverdueYesterday.shouldBeNull()
    }

    @Test
    fun `should notify without hint when wallet has scheduled bills`() {
        every {
            billRepository.getPaidBills(walletId = wallet.id, any(), any(), any())
        } returns listOf()

        every {
            scheduledBillRepository.findAllScheduledBillsByWalletId(walletId = wallet.id)
        } returns listOf(scheduledBill)

        val billCreatedHint = notificationHintService.getBillCreated(wallet.id, wallet.founder)
        val billComingDueHint = notificationHintService.getBillComingDue(wallet.id, wallet.founder)
        val billComingDueLastWarnHint = notificationHintService.getBillComingDueLastWarn(wallet.id, wallet.founder)
        val billOverdueYesterday = notificationHintService.getBillOverdueYesterday(wallet.id, wallet.founder)

        billCreatedHint.shouldBeNull()
        billComingDueHint.shouldBeNull()
        billComingDueLastWarnHint.shouldBeNull()
        billOverdueYesterday.shouldBeNull()
    }

    @Test
    fun `should notify with first hint when wallet no scheduled bills neither paid bills`() {
        every {
            billRepository.getPaidBills(walletId = wallet.id, any(), any(), any())
        } returns listOf()

        every {
            scheduledBillRepository.findAllScheduledBillsByWalletId(walletId = wallet.id)
        } returns listOf()

        withGivenDateTime(
            ZonedDateTime.of(
                2022,
                1,
                1,
                10,
                0,
                0,
                0,
                ZoneId.of("UTC"),
            ),
        ) {
            val billCreatedHint = notificationHintService.getBillCreated(wallet.id, wallet.founder)
            val billComingDueHint = notificationHintService.getBillComingDue(wallet.id, wallet.founder)
            val billComingDueLastWarnHint = notificationHintService.getBillComingDueLastWarn(wallet.id, wallet.founder)
            val billOverdueYesterday = notificationHintService.getBillOverdueYesterday(wallet.id, wallet.founder)

            billCreatedHint.shouldNotBeNull()
            billCreatedHint shouldBe billCreatedHints[0]

            billComingDueHint.shouldNotBeNull()
            billComingDueHint shouldBe billComingDueHints[0]

            billComingDueLastWarnHint.shouldNotBeNull()
            billComingDueLastWarnHint shouldBe billComingDueLastWarnHints[0]

            billOverdueYesterday.shouldNotBeNull()
            billOverdueYesterday shouldBe billOverdueYesterdayHints[0]
        }
    }

    @Test
    fun `should notify with second hint with founder CPF when wallet wallet hasnt scheduled bills neither paid bills`() {
        every {
            billRepository.getPaidBills(walletId = wallet.id, any(), any(), any())
        } returns listOf()

        every {
            scheduledBillRepository.findAllScheduledBillsByWalletId(walletId = wallet.id)
        } returns listOf()

        withGivenDateTime(
            ZonedDateTime.of(
                2022,
                1,
                1,
                10,
                0,
                1,
                0,
                ZoneId.of("UTC"),
            ),
        ) {
            val billCreatedHint = notificationHintService.getBillCreated(wallet.id, wallet.founder)
            val billComingDueHint = notificationHintService.getBillComingDue(wallet.id, wallet.founder)
            val billComingDueLastWarnHint = notificationHintService.getBillComingDueLastWarn(wallet.id, wallet.founder)
            val billOverdueYesterday = notificationHintService.getBillOverdueYesterday(wallet.id, wallet.founder)

            billCreatedHint.shouldNotBeNull()
            billCreatedHint shouldBe "HINT 2 - ${wallet.founder.document}@friday.ai"

            billComingDueHint.shouldNotBeNull()
            billComingDueHint shouldBe "COMING DUE HINT 2 - ${wallet.founder.document}@friday.ai"

            billComingDueLastWarnHint.shouldNotBeNull()
            billComingDueLastWarnHint shouldBe "COMING DUE LAST WARN HINT 2 - ${wallet.founder.document}@friday.ai"

            billOverdueYesterday.shouldNotBeNull()
            billOverdueYesterday shouldBe "OVERDUE HINT 2 - ${wallet.founder.document}@friday.ai"
        }
    }
}