package ai.friday.billpayment.app.contact

import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillEventEntity
import ai.friday.billpayment.adapters.dynamodb.ContactDbRepository
import ai.friday.billpayment.adapters.dynamodb.ContactEntity
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.accountDvKey
import ai.friday.billpayment.adapters.dynamodb.accountNoKey
import ai.friday.billpayment.adapters.dynamodb.bankISPBKey
import ai.friday.billpayment.adapters.dynamodb.bankNoKey
import ai.friday.billpayment.adapters.dynamodb.idKey
import ai.friday.billpayment.adapters.dynamodb.invalidatedKey
import ai.friday.billpayment.adapters.dynamodb.invalidationCodeKey
import ai.friday.billpayment.adapters.dynamodb.invalidationMessageKey
import ai.friday.billpayment.adapters.dynamodb.recipientPrefix
import ai.friday.billpayment.adapters.dynamodb.routingNoKey
import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.dynamodb.typeKey
import ai.friday.billpayment.adapters.micronaut.RecurrenceConfigurationMicronaut
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.allFalseFeatureConfiguration
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.UpdateBillService
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.ContactRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixTransactionError
import ai.friday.billpayment.app.recurrence.BillRecurrenceService
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.BILL_ID
import ai.friday.billpayment.integration.RECURRENCE_ID
import ai.friday.billpayment.invoiceAdded
import ai.friday.billpayment.invoicePaid
import ai.friday.billpayment.invoicePaymentFailedRetryable
import ai.friday.billpayment.invoicePaymentStarted
import ai.friday.billpayment.pixAdded
import ai.friday.billpayment.pixKeyAdded
import ai.friday.billpayment.weeklyRecurrenceNoEndDate
import ai.friday.morning.date.dateTimeFormat
import io.kotest.matchers.nulls.beNull
import io.kotest.matchers.should
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.mockk.Called
import io.mockk.clearMocks
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.math.BigInteger
import java.time.LocalDateTime
import java.util.UUID
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertDoesNotThrow
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource

val recurrentBillId = BillId(UUID.randomUUID().toString())

class ContactTest {

    private val mockDynamoDbDAO: DynamoDbDAO = mockk(relaxed = true)
    private val contactRepository: ContactRepository = ContactDbRepository(mockDynamoDbDAO)
    private val billEventRepository: BillEventRepository = BillEventDBRepository(
        mockDynamoDbDAO,
        allFalseFeatureConfiguration,
    )
    private val updateBillService: UpdateBillService = mockk(relaxed = true)

    private val recurrenceRepository: BillRecurrenceRepository = mockk(relaxed = true)
    private val recurrenceConfiguration = RecurrenceConfigurationMicronaut(
        lastLimitDate = "2020-01-01",
        limitDate = "2021-01-01",
    )

    private val recurrenceService =
        BillRecurrenceService(
            billRecurrenceRepository = recurrenceRepository,
            createBillService = mockk(),
            updateBillService = mockk(),
            billInstrumentationService = mockk(),
            recurrenceConfiguration = recurrenceConfiguration,
            possibleDuplicateBillService = mockk(),
            accountRepository = mockk(),
            walletRepository = mockk(),
            scheduleBillService = mockk(),
        )
    private val contactService = ContactService(
        contactRepository = contactRepository,
        billEventRepository = billEventRepository,
        updateBillService = updateBillService,
        recurrenceServiceProvider = { recurrenceService },
    )

    private val recurrenceId = RecurrenceId(UUID.randomUUID().toString())

    private val billEventEntities =
        listOf(invoiceAdded, invoicePaymentStarted, invoicePaid, invoicePaymentFailedRetryable).map {
            BillEventEntity().apply {
                billId = it.billId.value
                eventType = it.eventType
                created = it.created
                details = getObjectMapper().writeValueAsString(it.toBillEventDetailEntity())
            }
        }

    private val billEventEntitiesPix =
        listOf(pixAdded, invoicePaymentStarted, invoicePaid, invoicePaymentFailedRetryable).map {
            BillEventEntity().apply {
                billId = pixAdded.billId.value
                eventType = it.eventType
                created = it.created
                details = getObjectMapper().writeValueAsString(it.toBillEventDetailEntity())
            }
        }

    private val billEventEntitiesPixKey =
        listOf(pixKeyAdded, invoicePaymentStarted, invoicePaid, invoicePaymentFailedRetryable).map {
            BillEventEntity().apply {
                billId = pixKeyAdded.billId.value
                eventType = it.eventType
                created = it.created
                details = getObjectMapper().writeValueAsString(it.toBillEventDetailEntity())
            }
        }

    private val billEventEntitiesPixKeyRecurrence =
        listOf(
            pixKeyAdded.copy(
                actionSource = ActionSource.WalletRecurrence(recurrenceId = recurrenceId, accountId = AccountId(ACCOUNT_ID)),
                recurrenceRule = weeklyRecurrenceNoEndDate.rule,
            ),
            invoicePaymentStarted,
            invoicePaid,
            invoicePaymentFailedRetryable,
        ).map {
            BillEventEntity().apply {
                billId = recurrentBillId.value
                eventType = it.eventType
                created = it.created
                details = getObjectMapper().writeValueAsString(it.toBillEventDetailEntity())
            }
        }

    private val bankAccount = BankAccount(
        accountType = AccountType.CHECKING,
        bankNo = 111,
        routingNo = 4,
        accountNo = BigInteger("123555"),
        accountDv = "01",
        document = "***********",
    )
    private val recipient = Recipient(name = "Eduardo", document = "***********", bankAccount = bankAccount)
    private val outputList = listOf(
        ContactEntity().apply {
            contactId = "***********"
            accountId = ACCOUNT_ID
            name = "Eduardo"
            document = "***********"
            alias = "Antonio"
            bankAccounts = listOf()
            created = LocalDateTime.now().format(dateTimeFormat)
        },
        ContactEntity().apply {
            contactId = "***********"
            accountId = ACCOUNT_ID
            name = "Eduardo"
            document = "***********"
            alias = "Zander"
            bankAccounts = listOf()
            created = LocalDateTime.now().format(dateTimeFormat)
        },
        ContactEntity().apply {
            contactId = "***********"
            accountId = ACCOUNT_ID
            name = "Eduardo"
            document = "***********"
            bankAccounts = listOf()
            created = LocalDateTime.now().format(dateTimeFormat)
        },
        ContactEntity().apply {
            contactId = "***********"
            accountId = ACCOUNT_ID
            name = "Ana"
            document = "***********"
            bankAccounts = listOf()
            created = LocalDateTime.now().format(dateTimeFormat)
        },
        ContactEntity().apply {
            contactId = "***********"
            accountId = ACCOUNT_ID
            name = "Jairo"
            document = "***********"
            bankAccounts = listOf()
            created = LocalDateTime.now().format(dateTimeFormat)
        },
    ).shuffled()

    private val bankAccountId = "135"

    private val recipientEntity = ContactEntity().apply {
        contactId = "RECIPIENT-123"
        accountId = ACCOUNT_ID
        gSIndex1PrimaryKey = "test"
        gSIndex1ScanKey = "test"
        document = "**********"
        name = "test"
        alias = "test"
        bankAccounts = convertToMap(bankAccount)
        pixKeys = emptyList()
        created = LocalDateTime.now().format(dateTimeFormat)
    }

    @BeforeEach
    fun setup() {
        clearMocks(mockDynamoDbDAO)
        every {
            mockDynamoDbDAO.queryTableOnHashKey(
                invoiceAdded.billId.value,
                BillEventEntity::class.java,
            )
        } returns billEventEntities
        every {
            mockDynamoDbDAO.queryTableOnHashKey(
                pixAdded.billId.value,
                BillEventEntity::class.java,
            )
        } returns billEventEntitiesPix
        every {
            mockDynamoDbDAO.queryTableOnHashKey(
                pixKeyAdded.billId.value,
                BillEventEntity::class.java,
            )
        } returns billEventEntitiesPixKey
        every {
            mockDynamoDbDAO.queryTableOnHashKey(
                recurrentBillId.value,
                BillEventEntity::class.java,
            )
        } returns billEventEntitiesPixKeyRecurrence
    }

    @Test
    fun save() {
        every { mockDynamoDbDAO.save(any()) } throws Exception()
        assertDoesNotThrow { contactService.save(recipient, AccountId(ACCOUNT_ID), null) }
    }

    @Test
    fun `should save when recipient id exists and document match`() {
        val id = ContactId("RECIPIENT-123")
        val accountId = AccountId(ACCOUNT_ID)
        val recipientEntity = ContactEntity().apply {
            contactId = id.value
            this.accountId = accountId.value
            gSIndex1PrimaryKey = "test"
            gSIndex1ScanKey = "test"
            document = recipient.document!!
            name = "test"
            alias = "test"
            bankAccounts = emptyList()
            pixKeys = emptyList()
            created = LocalDateTime.now().format(dateTimeFormat)
        }
        every {
            mockDynamoDbDAO.queryTableOnHashKeyAndRangeKey(
                id.value,
                accountId.value,
                ContactEntity::class.java,
            )
        } returns listOf(recipientEntity)

        contactService.save(recipient, accountId, id)

        verify {
            mockDynamoDbDAO.updateItem(any())
        }
        val slot = slot<ContactEntity>()
        verify(exactly = 1) {
            mockDynamoDbDAO.save(capture(slot))
        }
        slot.captured.lastUsed?.bankAccountId shouldNotBe null
    }

    @Test
    fun `should save when recipient id does not exist`() {
        val accountId = AccountId(ACCOUNT_ID)
        val recipientEntity = ContactEntity().apply {
            contactId = "RECIPIENT-4657"
            this.accountId = accountId.value
            gSIndex1PrimaryKey = "test"
            gSIndex1ScanKey = "test"
            document = recipient.document!!
            name = "test"
            alias = "test"
            bankAccounts = emptyList()
            pixKeys = emptyList()
            created = LocalDateTime.now().format(dateTimeFormat)
        }
        every {
            mockDynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
                accountId.value,
                "${recipientPrefix}${recipient.document}",
                ContactEntity::class.java,
            )
        } returns listOf(recipientEntity)

        contactService.save(recipient, accountId, null)

        verify {
            mockDynamoDbDAO.updateItem(any())
        }
        verify(exactly = 0) {
            mockDynamoDbDAO.save(any())
        }
    }

    @Test
    fun `should order contacts by alias and then name`() {
        every {
            mockDynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
                ACCOUNT_ID,
                recipientPrefix,
                ContactEntity::class.java,
            )
        } answers { outputList }
        val recipients = contactService.getAll(AccountId(ACCOUNT_ID))
        recipients.size shouldBe outputList.size
        recipients[0].document shouldBe "***********"
        recipients[1].document shouldBe "***********"
        recipients[2].document shouldBe "***********"
        recipients[3].document shouldBe "***********"
        recipients[4].document shouldBe "***********"
    }

    @ParameterizedTest
    @MethodSource("billIds")
    fun `should do nothing on invalidate pix key with recipient not found`(billId: BillId) {
        every {
            mockDynamoDbDAO.queryTableOnHashKey(
                pixKeyAdded.contactId!!.value,
                ContactEntity::class.java,
            )
        } returns listOf()

        assertThrows<ItemNotFoundException> {
            contactService.invalidateSettlementInfo(pixKeyAdded.walletId, billId, "")
        }

        verify(exactly = 0) {
            mockDynamoDbDAO.save(any())
        }
    }

    @ParameterizedTest
    @MethodSource("billIds")
    fun `should do nothing on invalidate pix key with recipient is found but pix key is missing`(billId: BillId) {
        every {
            mockDynamoDbDAO.queryTableOnHashKey(
                pixKeyAdded.contactId!!.value,
                ContactEntity::class.java,
            )
        } returns listOf(
            buildContactEntity(pixKeyAdded).apply {
                pixKeys = listOf()
            },
        )

        contactService.invalidateSettlementInfo(pixKeyAdded.walletId, billId, "")

        verify(exactly = 0) {
            mockDynamoDbDAO.save(any())
        }
    }

    @ParameterizedTest
    @MethodSource("billIds")
    fun `should invalidate pix key when recipient and pix key are found`(billId: BillId) {
        val savedRecipient = buildContactEntity(pixKeyAdded)

        every {
            mockDynamoDbDAO.queryTableOnHashKey(
                pixKeyAdded.contactId!!.value,
                ContactEntity::class.java,
            )
        } returns listOf(
            savedRecipient,
        )

        every {
            recurrenceRepository.find(
                recurrenceId,
                pixKeyAdded.walletId,
            )
        } returns weeklyRecurrenceNoEndDate.copy(
            contactId = pixKeyAdded.contactId!!,
        )

        contactService.invalidateSettlementInfo(pixKeyAdded.walletId, billId, "")

        val slot = slot<ContactEntity>()
        verify {
            mockDynamoDbDAO.save(capture(slot))
        }

        with(slot.captured.pixKeys[0]) {
            getOrDefault(invalidatedKey, "false").toBoolean() shouldBe true
            getOrDefault(invalidationCodeKey, null) shouldBe InvalidationCode.INVALID_DATA.name
        }
    }

    @ParameterizedTest
    @MethodSource("billIds")
    fun `should invalidate pix key on right recipient when more than one recipient exists`(billId: BillId) {
        every {
            mockDynamoDbDAO.queryTableOnHashKey(
                pixKeyAdded.contactId!!.value,
                ContactEntity::class.java,
            )
        } returns listOf(
            buildContactEntity(pixKeyAdded),
        )
        every {
            recurrenceRepository.find(
                recurrenceId,
                pixKeyAdded.walletId,
            )
        } returns weeklyRecurrenceNoEndDate.copy(
            contactId = pixKeyAdded.contactId!!,
        )

        contactService.invalidateSettlementInfo(pixKeyAdded.walletId, billId, "")

        val slot = slot<ContactEntity>()
        verify(exactly = 1) {
            mockDynamoDbDAO.save(capture(slot))
        }

        with(slot.captured) {
            pixKeys[0].getOrDefault(invalidatedKey, "false").toBoolean() shouldBe true
            pixKeys[0].getOrDefault(invalidationCodeKey, null) shouldBe InvalidationCode.INVALID_DATA.name
            contactId shouldBe "123"
        }
    }

    @ParameterizedTest
    @MethodSource("generateInvoices")
    fun `should invalidate bank account`(bill: BillAdded) {
        val entity = buildContactEntity(bill)
        every { mockDynamoDbDAO.queryTableOnHashKey(bill.contactId!!.value, ContactEntity::class.java) } returns listOf(
            entity,
        )

        contactService.invalidateSettlementInfo(
            bill.walletId,
            bill.billId,
            invoicePaymentFailedRetryable.errorDescription,
        )

        val slot = slot<ContactEntity>()

        verify {
            mockDynamoDbDAO.save(capture(slot))
        }
        with(slot.captured.bankAccounts[0]) {
            get(invalidationCodeKey) shouldBe InvalidationCode.INVALID_DATA.name
            get(invalidationMessageKey) shouldBe invoicePaymentFailedRetryable.errorDescription
            get(invalidatedKey) shouldBe "true"
        }
    }

    @ParameterizedTest
    @MethodSource("generateInvoices")
    fun `should invalidate bank account when contact is from another wallet member`(bill: BillAdded) {
        val entity = buildContactEntity(bill, AccountId(ACCOUNT_ID_2))
        every { mockDynamoDbDAO.queryTableOnHashKey(bill.contactId!!.value, ContactEntity::class.java) } returns listOf(
            entity,
        )

        contactService.invalidateSettlementInfo(
            bill.walletId,
            bill.billId,
            invoicePaymentFailedRetryable.errorDescription,
        )

        val slot = slot<ContactEntity>()

        verify {
            mockDynamoDbDAO.save(capture(slot))
        }
        with(slot.captured.bankAccounts[0]) {
            get(invalidationCodeKey) shouldBe InvalidationCode.INVALID_DATA.name
            get(invalidationMessageKey) shouldBe invoicePaymentFailedRetryable.errorDescription
            get(invalidatedKey) shouldBe "true"
            slot.captured.accountId shouldBe ACCOUNT_ID_2
        }
    }

    @Test
    fun `should return invalidation code INVALID_TYPE on SettlementDestinationAccountTypeInvalid`() {
        val entity = buildContactEntity(pixAdded)
        every {
            mockDynamoDbDAO.queryTableOnHashKey(
                pixAdded.contactId!!.value,
                ContactEntity::class.java,
            )
        } returns listOf(entity)

        contactService.invalidateSettlementInfo(
            pixAdded.walletId,
            pixAdded.billId,
            PixTransactionError.SettlementDestinationAccountTypeInvalid.code,
        )

        val slot = slot<ContactEntity>()

        verify {
            mockDynamoDbDAO.save(capture(slot))
        }

        with(slot.captured.bankAccounts[0]) {
            get(invalidationCodeKey) shouldBe InvalidationCode.INVALID_TYPE.name
            get(invalidationMessageKey) should beNull()
            get(invalidatedKey) shouldBe "true"
        }
    }

    @Test
    fun `should not update bill when saved recipient bank account is updated with same data`() {
        every {
            mockDynamoDbDAO.queryTableOnHashKeyAndRangeKey(
                recipientEntity.contactId,
                recipientEntity.accountId,
                ContactEntity::class.java,
            )
        } returns listOf(recipientEntity)

        contactService.updateBankAccount(
            accountId = AccountId(recipientEntity.accountId),
            contactId = ContactId(recipientEntity.contactId),
            bankAccountId = BankAccountId(value = bankAccountId),
            bankAccount = bankAccount,
        )

        verify {
            updateBillService wasNot Called
        }
    }

    @Test
    fun `should not update bill when saved recipient alias is updated with same data`() {
        recipientEntity.apply {
            bankAccounts = emptyList()
            billIds = setOf(BILL_ID)
            recurrenceIds = setOf(RECURRENCE_ID)
        }
        every {
            mockDynamoDbDAO.queryTableOnHashKeyAndRangeKey(
                recipientEntity.contactId,
                recipientEntity.accountId,
                ContactEntity::class.java,
            )
        } returns listOf(recipientEntity)

        contactService.updateContact(
            accountId = AccountId(recipientEntity.accountId),
            contactId = ContactId(recipientEntity.contactId),
            alias = "test",
        )

        verify {
            updateBillService wasNot Called
        }
    }

    @Test
    fun `should not update IGNORED recurrence when saved recipient bank account is updated`() {
        every {
            mockDynamoDbDAO.queryTableOnHashKeyAndRangeKey(
                recipientEntity.contactId,
                recipientEntity.accountId,
                ContactEntity::class.java,
            )
        } returns listOf(recipientEntity)
        every { recurrenceRepository.findByContactId(ContactId(recipientEntity.contactId)) } returns listOf(
            weeklyRecurrenceNoEndDate.copy(status = RecurrenceStatus.IGNORED),
        )

        contactService.updateBankAccount(
            accountId = AccountId(recipientEntity.accountId),
            contactId = ContactId(recipientEntity.contactId),
            bankAccountId = BankAccountId(value = bankAccountId),
            bankAccount = bankAccount.copy(accountDv = "X"),
        )

        verify(exactly = 0) {
            recurrenceRepository.save(any())
        }
    }

    private fun buildContactEntity(bill: BillAdded, contactAccountId: AccountId? = null): ContactEntity {
        return ContactEntity().apply {
            contactId = "123"
            accountId = contactAccountId?.let { contactAccountId.value } ?: bill.walletId.value
            gSIndex1PrimaryKey = bill.walletId.value
            gSIndex1ScanKey = "$recipientPrefix${bill.recipient!!.document}"
            document = bill.recipient!!.document!!
            name = bill.recipient!!.name
            alias = bill.recipient!!.alias
            bankAccounts = convertToMap(bill.recipient!!.bankAccount)
            pixKeys = convertToMap(bill.recipient!!.pixKeyDetails)
            created = LocalDateTime.now().format(dateTimeFormat)
            billIds = setOf(bill.billId.value)
            recurrenceIds = setOf(recurrenceId.value)
        }
    }

    private fun convertToMap(bankAccount: BankAccount?): List<Map<String, String>> {
        return bankAccount?.let {
            listOf(
                mapOf(
                    idKey to bankAccountId,
                    typeKey to bankAccount.accountType.name,
                    bankNoKey to bankAccount.bankNo.toString(),
                    bankISPBKey to bankAccount.ispb.orEmpty(),
                    routingNoKey to bankAccount.routingNo.toString(),
                    accountNoKey to bankAccount.accountNo.toString(),
                    accountDvKey to bankAccount.accountDv,
                ).filterValues { it.isNotEmpty() },
            )
        } ?: listOf()
    }

    private fun convertToMap(pixKeyDetails: PixKeyDetails?): List<Map<String, String>> {
        return pixKeyDetails?.let {
            listOf(
                mapOf(
                    idKey to pixKeyDetails.key.value,
                    typeKey to pixKeyDetails.key.type.name,
                ),
            )
        } ?: listOf()
    }

    companion object {
        @JvmStatic
        fun generateInvoices(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(invoiceAdded),
                Arguments.of(pixAdded),
            )
        }

        @JvmStatic
        fun billIds(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(pixKeyAdded.billId),
                Arguments.of(recurrentBillId),
            )
        }
    }
}