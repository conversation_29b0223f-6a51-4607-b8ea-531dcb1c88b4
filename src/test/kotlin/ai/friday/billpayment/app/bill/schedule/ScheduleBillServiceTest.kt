package ai.friday.billpayment.app.bill.schedule

import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.calculateEffectiveDate
import ai.friday.billpayment.app.bill.schedule.security.ScheduleBillSecurityService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.ScheduledBillPaymentService
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.PaymentMethodsDetail
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.payment.ScheduleTo
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.payment.pinCode.PinCode
import ai.friday.billpayment.app.payment.pinCode.PinCodeService
import ai.friday.billpayment.app.payment.pinCode.ValidationResult
import ai.friday.billpayment.billAdded
import ai.friday.billpayment.billIgnored
import ai.friday.billpayment.billPaid
import ai.friday.billpayment.billPaymentScheduled
import ai.friday.billpayment.billPaymentStart
import ai.friday.billpayment.billRegisterUpdatedAlreadyPaid
import ai.friday.billpayment.billRegisterUpdatedNotPayable
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.invoiceAdded
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.withGivenDateTime
import arrow.core.Either
import arrow.core.right
import io.kotest.matchers.booleans.shouldBeFalse
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldContainExactly
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeInstanceOf
import io.mockk.called
import io.mockk.every
import io.mockk.mockk
import io.mockk.slot
import io.mockk.verify
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.junit.jupiter.params.provider.ValueSource
import software.amazon.awssdk.services.dynamodb.model.ResourceNotFoundException

class ScheduleBillServiceTest {

    private val billEventDbRepositoryMock: BillEventDBRepository = mockk(relaxed = true)
    private val billRepositoryMock: BillRepository = mockk(relaxed = true)
    private val billEventPublisherMock: BillEventPublisher = mockk(relaxed = true)
    private val accountServiceMock: AccountService = mockk()
    private val paymentSchedulingService: PaymentSchedulingService = mockk(relaxed = true)
    private val scheduledBillPaymentService: ScheduledBillPaymentService = mockk(relaxed = true)
    private val lockProvider: InternalLock = mockk(relaxed = true)

    private val walletRepository = mockk<WalletRepository>(relaxUnitFun = true)

    private val walletFixture = WalletFixture()

    private val wallet =
        walletFixture.buildWallet(id = billAdded.walletId, otherMembers = listOf(walletFixture.participant))

    private val participantWallet = walletFixture.buildPrimaryWallet(founderAccount = walletFixture.participantAccount)

    private val balance = ai.friday.billpayment.balance.copy(id = wallet.paymentMethodId)

    private lateinit var scheduleBillService: ScheduleBillService

    private val scheduleBillSecurityService = mockk<ScheduleBillSecurityService> {
        every { canSchedule(any()) } returns Unit.right()
    }

    private lateinit var bill: Bill

    private val mockedPinCodeService: PinCodeService = mockk() {
        every { validate(any(), any()) } returns Result.success(ValidationResult(valid = true, maxAttemptsReached = false))
    }

    @BeforeEach
    fun setup() {
        scheduleBillService = ScheduleBillService(
            billEventRepository = billEventDbRepositoryMock,
            accountService = accountServiceMock,
            paymentSchedulingService = paymentSchedulingService,
            scheduledBillPaymentServiceProvider = mockk {
                every { get() } returns scheduledBillPaymentService
            },
            lockProvider = lockProvider,
            billInstrumentationService = mockk(relaxUnitFun = true),
            billEventPublisher = billEventPublisherMock,
            scheduleBillSecurityService = scheduleBillSecurityService,
            batchSchedulePublisher = mockk(relaxUnitFun = true),
            walletRepository = walletRepository,
            isBatchScheduleNotificationEnabled = false,
            pinCodeService = mockedPinCodeService,
        )

        scheduleBillService.tedLimitTime = "17:00"

        bill = Bill.build(billAdded)

        every { billEventDbRepositoryMock.getBillById(any()) } answers {
            Either.Right(bill)
        }

        every {
            walletRepository.findWallet(any())
        } returns wallet

        every {
            billEventPublisherMock.publish(any(), any())
        } answers {
            bill.apply(secondArg())
        }
    }

    @Test
    fun `on payment schedule should return itemNotFound when bill not found`() {
        every { billEventDbRepositoryMock.getBillById(billAdded.billId) } answers {
            Either.Left(
                ResourceNotFoundException.builder().message("").build(),
            )
        }
        val response = scheduleBillService.schedulePayments(
            paymentWallet = wallet,
            billIdsWithMethods = listOf(billAdded.billId to null),
            accountId = wallet.founder.accountId,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            scheduleStrategy = ScheduleStrategy.ofDueDate(),
        )
        response.shouldBeInstanceOf<ScheduleResult.Success>()

        response.billIds.first().second.shouldBeFalse()
    }

    @ParameterizedTest
    @MethodSource("valuesProvider")
    fun `on payment schedule should return error in case of invalid status`(billEvent: BillEvent) {
        every { billEventDbRepositoryMock.getBillById(billAdded.billId) } answers {
            Either.Right(
                Bill.build(
                    billAdded,
                    billEvent,
                ),
            )
        }
        val response = scheduleBillService.schedulePayments(
            paymentWallet = wallet,
            billIdsWithMethods = listOf(billAdded.billId to null),
            accountId = wallet.founder.accountId,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            scheduleStrategy = ScheduleStrategy.ofDueDate(),
        )

        response.shouldBeInstanceOf<ScheduleResult.Success>()

        response.billIds.first().second.shouldBeFalse()
    }

    @Test
    fun `on payment schedule should set bill as scheduled for future effective Due date`() {
        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                wallet.paymentMethodId,
                wallet.founder.accountId,
            )
        } returns balance

        val response = scheduleBillService.schedulePayments(
            paymentWallet = wallet,
            billIdsWithMethods = listOf(billAdded.billId to null),
            accountId = wallet.founder.accountId,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            scheduleStrategy = ScheduleStrategy.ofDueDate(),
        )
        response.shouldBeInstanceOf<ScheduleResult.Success>()

        response.billIds.first().second.shouldBeTrue()

        val slot = slot<BillPaymentScheduled>()
        verify(exactly = 1) {
            billEventPublisherMock.publish(bill, capture(slot))
        }
        slot.captured.scheduledDate shouldBe billAdded.effectiveDueDate
        slot.captured.infoData.retrievePaymentMethodIds() shouldContainExactly listOf(balance.id)
    }

    @Test
    fun `should schedule for next workday when TED is after hours`() {
        val fixedDate = ZonedDateTime.of(LocalDate.of(2021, 9, 9), LocalTime.of(17, 0, 1), brazilTimeZone)

        bill = Bill.build(
            invoiceAdded.copy(
                dueDate = fixedDate.toLocalDate(),
                effectiveDueDate = fixedDate.toLocalDate(),
            ),
        )

        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                wallet.paymentMethodId,
                wallet.founder.accountId,
            )
        } returns balance

        val slot = slot<BillPaymentScheduled>()

        withGivenDateTime(fixedDate) {
            val response = scheduleBillService.schedulePayments(
                paymentWallet = wallet,
                billIdsWithMethods = listOf(invoiceAdded.billId to null),
                accountId = wallet.founder.accountId,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
            )
            response.shouldBeInstanceOf<ScheduleResult.Success>()

            response.billIds.first().second.shouldBeTrue()
        }

        verify(exactly = 1) {
            billEventPublisherMock.publish(bill, capture(slot))
        }
        slot.captured.scheduledDate shouldBe fixedDate.plusDays(1).toLocalDate()
        slot.captured.infoData.retrievePaymentMethodIds() shouldContainExactly listOf(balance.id)
    }

    @ParameterizedTest
    @ValueSource(ints = [12, 23])
    fun `should schedule TED for DUE_DATE when DUE_DATE is future`(hour: Int) {
        val fixedDate = ZonedDateTime.of(LocalDate.of(2021, 9, 9), LocalTime.of(hour, 0), brazilTimeZone)
        val dueDate = LocalDate.of(2021, 9, 16)

        bill = Bill.build(
            invoiceAdded.copy(
                dueDate = dueDate,
                effectiveDueDate = dueDate,
            ),
        )

        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                wallet.paymentMethodId,
                wallet.founder.accountId,
            )
        } returns balance

        val slot = slot<BillPaymentScheduled>()

        withGivenDateTime(fixedDate) {
            val response = scheduleBillService.schedulePayments(
                paymentWallet = wallet,
                billIdsWithMethods = listOf(invoiceAdded.billId to null),
                accountId = wallet.founder.accountId,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
            )
            response.shouldBeInstanceOf<ScheduleResult.Success>()

            response.billIds.first().second.shouldBeTrue()
        }

        verify(exactly = 1) {
            billEventPublisherMock.publish(bill, capture(slot))
        }
        slot.captured.scheduledDate shouldBe dueDate
        slot.captured.infoData.retrievePaymentMethodIds() shouldContainExactly listOf(balance.id)
    }

    @Test
    fun `should return unscheduled bill when payment method is not found`() {
        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                wallet.paymentMethodId,
                wallet.founder.accountId,
            )
        } throws PaymentMethodNotFound("")

        val response = scheduleBillService.schedulePayments(
            paymentWallet = wallet,
            billIdsWithMethods = listOf(billAdded.billId to null),
            accountId = wallet.founder.accountId,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            scheduleStrategy = ScheduleStrategy.ofDueDate(),
        )

        response.shouldBeInstanceOf<ScheduleResult.Success>()

        response.billIds.first().second.shouldBeFalse()
        verify {
            billRepositoryMock wasNot called
            billEventPublisherMock wasNot called
        }
        verify(exactly = 0) {
            billEventDbRepositoryMock.save(any())
        }
    }

    @Test
    fun `on payment schedule for overdue bill should be set as scheduled for today`() {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)
        bill = Bill.build(
            billAdded.copy(
                dueDate = now.minusDays(3).toLocalDate(),
                effectiveDueDate = calculateEffectiveDate(
                    dueDate = now.minusDays(3).toLocalDate(),
                    billType = billAdded.billType,
                    barcode = billAdded.barcode,
                ),
            ),
        )

        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                wallet.paymentMethodId,
                wallet.founder.accountId,
            )
        } returns balance

        withGivenDateTime(now.withHour(16)) {
            val response = scheduleBillService.schedulePayments(
                paymentWallet = wallet,
                billIdsWithMethods = listOf(billAdded.billId to null),
                accountId = wallet.founder.accountId,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
            )
            response.shouldBeInstanceOf<ScheduleResult.Success>()

            response.billIds.first().second.shouldBeTrue()

            val slot = slot<BillPaymentScheduled>()
            verify(exactly = 1) {
                billEventPublisherMock.publish(bill, capture(slot))
            }
            slot.captured.scheduledDate shouldBe getZonedDateTime().toLocalDate()
            slot.captured.infoData.retrievePaymentMethodIds() shouldContainExactly listOf(balance.id)
        }
    }

    @Test
    fun `on payment schedule for coming due should be set as scheduled for effectiveDueDate`() {
        bill = Bill.build(
            billAdded.copy(
                dueDate = billAdded.dueDate.minusDays(3),
                effectiveDueDate = calculateEffectiveDate(
                    dueDate = billAdded.dueDate.minusDays(3),
                    billType = billAdded.billType,
                    barcode = billAdded.barcode,
                ),
            ),
        )

        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                wallet.paymentMethodId,
                wallet.founder.accountId,
            )
        } returns balance

        withGivenDateTime(
            ZonedDateTime.of(
                LocalDateTime.of(LocalDate.of(2024, 1, 12), LocalTime.of(16, 0)),
                brazilTimeZone,
            ),
        ) {
            val response = scheduleBillService.schedulePayments(
                paymentWallet = wallet,
                billIdsWithMethods = listOf(billAdded.billId to null),
                accountId = wallet.founder.accountId,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
            )
            response.shouldBeInstanceOf<ScheduleResult.Success>()

            response.billIds.first().second.shouldBeTrue()

            val slot = slot<BillPaymentScheduled>()
            verify(exactly = 1) {
                billEventPublisherMock.publish(bill, capture(slot))
            }
            slot.captured.scheduledDate shouldBe bill.effectiveDueDate
            slot.captured.infoData.retrievePaymentMethodIds() shouldContainExactly listOf(balance.id)
        }
    }

    @Test
    fun `on payment schedule should set bill as scheduled for today`() {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)

        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                wallet.paymentMethodId,
                wallet.founder.accountId,
            )
        } returns balance

        withGivenDateTime(now.withHour(12)) {
            val response = scheduleBillService.schedulePayments(
                paymentWallet = wallet,
                billIdsWithMethods = listOf(billAdded.billId to null),
                accountId = wallet.founder.accountId,
                actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
                scheduleStrategy = ScheduleStrategy.ofAsap(),
            )
            response.shouldBeInstanceOf<ScheduleResult.Success>()

            response.billIds.first().second shouldBe true
        }

        val slot = slot<BillPaymentScheduled>()
        verify(exactly = 1) {
            billEventPublisherMock.publish(bill, capture(slot))
        }
        with(slot.captured) {
            scheduledDate shouldBe now.toLocalDate()
            slot.captured.infoData.retrievePaymentMethodIds() shouldContainExactly listOf(balance.id)
        }

        val slotScheduleBill = slot<ScheduledBill>()
        verify {
            paymentSchedulingService.schedule(capture(slotScheduleBill))
        }
        with(slotScheduleBill.captured) {
            scheduledDate shouldBe now.toLocalDate()
            scheduleTo shouldBe ScheduleTo.ASAP
        }
    }

    @Test
    fun `on payment schedule ensure that BillPaymentScheduled is idempotent`() {
        bill = Bill.build(billAdded, billPaymentScheduled)

        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                wallet.paymentMethodId,
                wallet.founder.accountId,
            )
        } returns balance

        val response = scheduleBillService.schedulePayments(
            paymentWallet = wallet,
            billIdsWithMethods = listOf(billAdded.billId to null),
            accountId = wallet.founder.accountId,
            actionSource = ActionSource.Api(accountId = wallet.founder.accountId),
            scheduleStrategy = ScheduleStrategy.ofDueDate(),
        )
        response.shouldBeInstanceOf<ScheduleResult.Success>()

        response.billIds.first().second.shouldBeTrue()
        verify(exactly = 0) {
            billRepositoryMock.save(ofType(Bill::class))
            billEventDbRepositoryMock.save(ofType(BillPaymentScheduled::class))
            billEventPublisherMock.publish(bill, ofType(BillPaymentScheduled::class))
        }
    }

    @Test
    fun `deve agendar o pagamento da bill com uma carteira diferente da carteira da bill`() {
        val now = ZonedDateTime.of(LocalDateTime.of(2021, 9, 9, 12, 0), brazilTimeZone)

        every {
            accountServiceMock.findAccountPaymentMethodByIdAndAccountId(
                participantWallet.paymentMethodId,
                participantWallet.founder.accountId,
            )
        } returns balance

        withGivenDateTime(now.withHour(12)) {
            val response = scheduleBillService.schedulePayments(
                paymentWallet = participantWallet,
                billIdsWithMethods = listOf(billAdded.billId to null),
                accountId = walletFixture.participantAccount.accountId,
                actionSource = ActionSource.Api(accountId = walletFixture.participantAccount.accountId),
                scheduleStrategy = ScheduleStrategy.ofAsap(),
            )
            response.shouldBeInstanceOf<ScheduleResult.Success>()

            response.billIds.first().second.shouldBeTrue()
        }

        verify {
            paymentSchedulingService.schedule(
                withArg {
                    it.walletId shouldBe participantWallet.id
                    it.scheduledDate shouldBe now.toLocalDate()
                    it.scheduleTo shouldBe ScheduleTo.ASAP
                },
            )
        }
    }

    @Nested
    @DisplayName("pinCode")
    inner class PinCodeTest {
        @Test
        fun `quando errar o pincode, não deve agendar`() {
            every { mockedPinCodeService.validate(any(), any()) } returns Result.success(ValidationResult(valid = false, maxAttemptsReached = false))

            val scheduleResult = scheduleBillService.schedulePayments(
                paymentWallet = wallet,
                billIdsWithMethods = listOf(
                    billAdded.billId to mockk<PaymentMethodsDetail>(),
                ),
                accountId = wallet.founder.accountId,
                actionSource = ActionSource.System,
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
                fingerprint = null,
                pinCode = PinCode("1234"),
            )

            scheduleResult.shouldBeInstanceOf<ScheduleResult.InvalidPinCode>()
        }

        @Test
        fun `quando atingir o número máximo de retentativas`() {
            every { mockedPinCodeService.validate(any(), any()) } returns Result.success(ValidationResult(valid = false, maxAttemptsReached = true))

            val scheduleResult = scheduleBillService.schedulePayments(
                paymentWallet = wallet,
                billIdsWithMethods = listOf(
                    billAdded.billId to mockk<PaymentMethodsDetail>(),
                ),
                accountId = wallet.founder.accountId,
                actionSource = ActionSource.System,
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
                fingerprint = null,
                pinCode = PinCode("1234"),
            )

            scheduleResult.shouldBeInstanceOf<ScheduleResult.PinCodeMaxAttemptsReached>()
        }

        @Test
        fun `quando acertar o pincode, deve agendar`() {
            val scheduleResult = scheduleBillService.schedulePayments(
                paymentWallet = wallet,
                billIdsWithMethods = listOf(
                    billAdded.billId to mockk<PaymentMethodsDetail>(),
                ),
                accountId = wallet.founder.accountId,
                actionSource = ActionSource.System,
                scheduleStrategy = ScheduleStrategy.ofDueDate(),
                fingerprint = null,
                pinCode = PinCode("1234"),
            )

            scheduleResult.shouldBeInstanceOf<ScheduleResult.Success>()
        }
    }

    companion object {
        @JvmStatic
        fun valuesProvider(): Stream<Arguments> {
            return Stream.of(
                Arguments.of(billIgnored),
                Arguments.of(billPaid),
                Arguments.of(billPaymentStart),
                Arguments.of(billRegisterUpdatedAlreadyPaid),
                Arguments.of(billRegisterUpdatedNotPayable),
            )
        }
    }
}