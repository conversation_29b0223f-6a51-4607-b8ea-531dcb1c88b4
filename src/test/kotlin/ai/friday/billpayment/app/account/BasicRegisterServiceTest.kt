package ai.friday.billpayment.app.account

import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.NotificationAdapterMock
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.serpro.BiometriaTO
import ai.friday.billpayment.adapters.serpro.SerproBiometriaFaceResponseTO
import ai.friday.billpayment.adapters.serpro.SerproBiometriaProbability
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.dda.DDAProvider
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.dda.DefaultFullDDAPostProcessor
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.DatabaseObjectRepository
import ai.friday.billpayment.app.integrations.DocumentValidationService
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.InternalLock
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.integrations.TokenData
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.notification.ChatBotMessagePublisher
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.balance
import ai.friday.billpayment.basicAccountRegisterData
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.equality.shouldBeEqualToIgnoringFields
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.verify
import java.io.InputStream
import java.time.Duration
import java.time.ZonedDateTime
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.CsvSource
import org.junit.jupiter.params.provider.EnumSource

class BasicRegisterServiceTest {

    private val documentValidationServiceMock: DocumentValidationService = mockk()

    private val dynamoDB: AmazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)

    private val databaseObjectRepositoryMock: DatabaseObjectRepository = mockk {
        every { getObjectInputStream(any()) } returns InputStream.nullInputStream()
    }

    private val registerService: RegisterService = mockk(relaxed = true)

    private val accountService: AccountService = mockk(relaxUnitFun = true)

    private val accountRegisterService: AccountRegisterService = mockk(relaxUnitFun = true)

    private val accountRegisterRepository = AccountRegisterDbRepository(
        dynamoDbDAO = dynamoDbDAO,
        databaseObjectRepository = databaseObjectRepositoryMock,
    )

    private val externalAccountRegister: ExternalAccountRegister = mockk()
    private val crmService: CrmService = mockk(relaxed = true)
    private val ddaService: DDAService = mockk(relaxed = true)
    private val loginRepository: LoginRepository = mockk()
    private val walletService: WalletService = mockk(relaxed = true)
    private val systemActivityServiceMock: SystemActivityService = mockk(relaxed = true)

    private val walletLimitsService: WalletLimitsService = mockk() {
        every { updateLimit(any(), any(), any(), any(), any()) } returns mockk(relaxed = true)
    }
    private val accountStatusLockProvider: InternalLock = mockk() {
        every { acquireLock(any()) } returns mockk(relaxed = true)
    }

    private val notificationAdapter: NotificationAdapterMock = mockk(relaxed = true)

    private val registerInstrumentationService: RegisterInstrumentationService = mockk(relaxed = true)

    private val tokenService: TokenService = mockk(relaxUnitFun = true)

    private val messagePublisherMock = mockk<MessagePublisher>(relaxed = true)

    private val adService: AdService = mockk(relaxed = true)
    val userPoolAdapter: UserPoolAdapter = mockk {
        every {
            doesUserExist(any())
        } returns true
        every { isUserEnabled(any()) } returns false
        every { enableUser(any()) } returns Unit.right()
    }
    private val chatBotMessagePublisher = mockk<ChatBotMessagePublisher>(relaxed = true)
    private val walletBillCategoryService = mockk<
        PFMWalletCategoryService,
        >(relaxed = true)
    private val basicRegisterService = BasicRegisterService(
        tokenService = tokenService,
        accountRegisterRepository = accountRegisterRepository,
        accountService = accountService,
        userFilesConfiguration = mockk(),
        userAddressConfiguration = mockk() {
            every { streetType } returns "Rua"
            every { streetName } returns "Rua"
            every { number } returns "123"
            every { neighborhood } returns "Bairro"
            every { complement } returns "Complemento"
            every { city } returns "Cidade"
            every { state } returns "Estado"
            every { zipCode } returns "********"
        },
        agreementFilesService = mockk(),
        notificationSenderService = mockk(),
        loginRepository = loginRepository,
        userPoolAdapter = userPoolAdapter,
        externalAccountRegister = externalAccountRegister,
        walletService = walletService,
        walletLimitsService = walletLimitsService,
        crmService = crmService,
        registerInstrumentationService = registerInstrumentationService,
        ddaService = ddaService,
        userJourneyService = mockk(relaxed = true),
        closeAccountService = mockk(),
        systemActivityService = systemActivityServiceMock,
        livenessService = mockk(),
        modattaProvider = mockk(),
        pendingInternalReviewConfiguration = mockk(),
        pendingActivationConfiguration = mockk(),
        accountStatusLockProvider = accountStatusLockProvider,
        messagePublisher = messagePublisherMock,
        documentValidationService = documentValidationServiceMock,
        accountRegisterService = accountRegisterService,
        fraudList = FraudListMicronautConfiguration(emptyList()),
        notificationAdapter = notificationAdapter,
        adService = adService,
        chatBotMessagePublisher = chatBotMessagePublisher,
        walletBillCategoryService = walletBillCategoryService,
    ).apply {
        registerPixKeyQueueName = ""
    }

    private val ddaProcessor = DefaultFullDDAPostProcessor(
        registerServiceProvider = mockk() {
            every { get() } returns registerService
        },
        basicRegisterServiceProvider = mockk() {
            every { get() } returns basicRegisterService
        },
        accountRepository = mockk(),
    )

    private val emailAddress = EmailAddress("<EMAIL>")

    private val partialAccount = PartialAccount(
        id = AccountId("test-account"),
        name = "fake name",
        emailAddress = EmailAddress("<EMAIL>"),
        status = AccountStatus.UNDER_REVIEW,
        statusUpdated = getZonedDateTime(),
        role = Role.GUEST,
        groups = emptyList(),
        registrationType = RegistrationType.BASIC,
        subscriptionType = SubscriptionType.PIX,
    )

    private val account = Account(
        name = "fake name",
        emailAddress = EmailAddress("<EMAIL>"),
        status = AccountStatus.UNDER_REVIEW,
        accountId = AccountId("test-account"),
        document = "",
        documentType = "",
        mobilePhone = "",
        created = getZonedDateTime(),
        updated = getZonedDateTime(),
        activated = null,
        configuration = LegacyAccountConfiguration(
            accountId = null,
            creditCardConfiguration = CreditCardConfiguration(
                quota = 0,
            ),
            defaultWalletId = null,
            receiveDDANotification = false,
            receiveNotification = false,
            accessToken = null,
            refreshToken = null,
            externalId = null,
            groups = listOf(),
            notificationGateway = NotificationGateways.WHATSAPP,
        ),
        firstLoginAsOwner = null,
        channel = null,
        imageUrlSmall = null,
        imageUrlLarge = null,
        subscriptionType = SubscriptionType.PIX,
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        basicRegisterService.pixKeyEmailDomain = "pixKeyEmailDomain"

        accountRegisterRepository.save(basicAccountRegisterData)
    }

    @Test
    fun `deve aprovar a conta com DDA e chamar o callback de atualização de estado do simple signup quando for usuario de conta simplificada`() {
        val currentRegister = accountRegisterCompleted.copy(
            externalId = ExternalId("basic", AccountProviderName.FRIDAY),
            politicallyExposed = PoliticallyExposed(selfDeclared = true, query = null),
            document = Document("***********"),
        )

        every { walletService.createPrimaryWallet(any(), any()) } returns mockk() {
            every { id } returns WalletId("walletId")
        }

        accountRegisterRepository.save(currentRegister)

        every {
            accountService.findAccountByDocumentOrNull("***********")
        } returns null

        val partialAccount = partialAccount.copy(
            id = currentRegister.accountId,
            name = currentRegister.documentInfo!!.name,
            emailAddress = currentRegister.emailAddress,
        )
        every {
            accountService.findPartialAccountById(currentRegister.accountId)
        } returns partialAccount andThen partialAccount.copy(status = AccountStatus.APPROVED)

        every {
            accountService.findPhysicalBankAccountByAccountId(currentRegister.accountId)
        } throws PaymentMethodNotFound(currentRegister.accountId) andThen listOf(balance)

        every {
            accountService.create(any())
        } answers {
            firstArg<Account>()
        }

        val account = account.copy(
            name = currentRegister.documentInfo!!.name,
            emailAddress = currentRegister.emailAddress,
            status = AccountStatus.UNDER_REVIEW,
            accountId = currentRegister.accountId,
        )

        every {
            accountService.findAccountById(currentRegister.accountId)
        } returns account

        every {
            accountService.createAccountPaymentMethod(any(), any(), any(), any())
        } returns balance

        every {
            accountService.save(any())
        } just runs

        every {
            externalAccountRegister.simpleRegisterNaturalPerson(any())
        } returns RegisterNaturalPersonResponse(true, 10000, "1")

        every {
            accountService.updatePartialAccountStatus(currentRegister.accountId, AccountStatus.APPROVED)
        } just Runs

        every {
            accountService.deletePartialAccount(currentRegister.accountId)
        } just runs

        every {
            crmService.contactExists(any())
        } returns true

        every {
            accountService.enableCreditCardUsage(
                accountId = any(),
                quota = 2_000_00,
                sendNotification = false,
            )
        } returns account.copy(status = AccountStatus.ACTIVE).right()

        val status = basicRegisterService.updateAccountStatus(
            document = "***********",
            status = ExternalRegisterStatus.APPROVED,
        )

        status.isRight() shouldBe true
        status.getOrElse { throw it } shouldBe SetupAccountResult.AccountApproved
        verify {
            externalAccountRegister.simpleRegisterNaturalPerson(any())
            ddaService.register(any(), any())
            accountService.updatePartialAccountStatus(currentRegister.accountId, AccountStatus.APPROVED)
            messagePublisherMock.sendMessage(
                any(),
                any(),
                any(),
            )

            walletService.createPrimaryWallet(any(), any())

            walletBillCategoryService.createDefaultWalletCategories(any())
        }
        verify(exactly = 0) {
            accountService.deletePartialAccount(any())
            loginRepository.findActiveUserLoginProvider(any())
        }
    }

    @Test
    fun `deve tentar reativar cognito ao ativar um usuario e ele já existe no cognito mas não está ativo`() {
        val currentRegister = accountRegisterCompleted.copy(
            externalId = ExternalId("basic", AccountProviderName.FRIDAY),
            politicallyExposed = PoliticallyExposed(selfDeclared = true, query = null),
            document = Document("***********"),
        )
        every {
            accountService.findPartialAccountById(currentRegister.accountId)
        } returns partialAccount.copy(status = AccountStatus.APPROVED)
        accountRegisterRepository.save(currentRegister)
        every { userPoolAdapter.enableUser(any()) } returns UserPoolEnableUserError.Unknown(IllegalStateException("erro")).left()
        basicRegisterService.activateAccount(currentRegister.accountId).isLeft() shouldBe true
        basicRegisterService.activateAccount(currentRegister.accountId).leftOrNull()!!.shouldBeTypeOf<IllegalStateException>()
    }

    @Test
    fun `deve salvar como system activity ao receber as infos do device`() {
        basicRegisterService.setDeviceInfo(DeviceAdIds("adjustid", "googleid", "appleid"), accountRegisterCompleted.accountId)

        val slot = slot<DeviceAdIds>()
        verify {
            systemActivityServiceMock.setDeviceInfo(capture(slot), any())
        }

        slot.captured.adAppleId shouldBe "appleid"
        slot.captured.adGoogleId shouldBe "googleid"
        slot.captured.adId shouldBe "adjustid"
    }

    @ParameterizedTest
    @CsvSource(
        "CHATBOT_AI_ENABLED",
        "SUBSCRIPTION_STANDARD_PLAN",
    )
    fun `ao ativar uma conta básica deve setar os limites de conta diário e mensal`(accountGroup: String) {
        val currentRegister = accountRegisterCompleted.copy(
            externalId = ExternalId("basic", AccountProviderName.FRIDAY),
            politicallyExposed = PoliticallyExposed(selfDeclared = true, query = null),
            document = Document("***********"),
            registrationType = RegistrationType.BASIC,
        )
        val account = Account(
            name = currentRegister.documentInfo!!.name,
            emailAddress = currentRegister.emailAddress,
            status = AccountStatus.UNDER_REVIEW,
            accountId = currentRegister.accountId,
            document = "",
            documentType = "",
            mobilePhone = "",
            created = getZonedDateTime(),
            updated = getZonedDateTime(),
            activated = null,
            configuration = LegacyAccountConfiguration(
                accountId = null,
                creditCardConfiguration = CreditCardConfiguration(
                    quota = 0,
                ),
                defaultWalletId = null,
                receiveDDANotification = false,
                receiveNotification = false,
                accessToken = null,
                refreshToken = null,
                externalId = null,
                groups = listOf(AccountGroup.valueOf(accountGroup)),
                notificationGateway = NotificationGateways.WHATSAPP,
            ),
            firstLoginAsOwner = null,
            channel = null,
            type = UserAccountType.BASIC_ACCOUNT,
            imageUrlSmall = null,
            imageUrlLarge = null,
            subscriptionType = SubscriptionType.PIX,
        )

        every { accountService.getChatbotType(any<Account>()) } returns when (accountGroup) {
            "CHATBOT_AI_ENABLED" -> ChatbotType.CHABOT_AI
            else -> ChatbotType.CHATBOT_LEGACY
        }

        every { crmService.contactExists(any()) } returns true

        every { registerService.findByAccountId(any()) } returns currentRegister.right()

        every { loginRepository.findActiveUserLoginProvider(any()) } returns listOf()

        every { accountService.addGroupsToAccount(any<AccountId>(), any()) } returns account

        every { accountService.findPartialAccountById(any()) } returns PartialAccount(
            id = currentRegister.accountId,
            name = currentRegister.documentInfo!!.name,
            emailAddress = currentRegister.emailAddress,
            status = AccountStatus.APPROVED,
            statusUpdated = getZonedDateTime(),
            role = Role.GUEST,
            groups = emptyList(),
            registrationType = RegistrationType.BASIC,
            subscriptionType = SubscriptionType.PIX,
        )
        every { ddaService.register(any(), any()) } returns DDAStatus.ACTIVE
        every { ddaService.findByAccount(any()) } returns DDARegister(accountId = account.accountId, document = account.document, created = ZonedDateTime.now(), status = DDAStatus.ACTIVE, lastUpdated = ZonedDateTime.now(), lastSuccessfullExecution = null, provider = DDAProvider.ARBI, migrated = null)

        every { accountService.findAccountById(any()) } returns account

        accountRegisterRepository.save(currentRegister)
        ddaProcessor.process(
            ddaRegister = DDARegister(
                accountId = currentRegister.accountId,
                document = "123145",
                created = getZonedDateTime(),
                status = DDAStatus.ACTIVE,
                lastUpdated = getZonedDateTime(),
                lastSuccessfullExecution = null,
                provider = DDAProvider.ARBI,
                migrated = null,
            ),
        )

        verify {
            walletLimitsService.updateLimit(
                accountId = any(),
                walletId = any(),
                type = DailyPaymentLimitType.MONTHLY,
                amount = 10_000_00,
                source = ActionSource.System,
            )

            walletLimitsService.updateLimit(
                accountId = any(),
                walletId = any(),
                type = DailyPaymentLimitType.DAILY,
                amount = 500_00,
                source = ActionSource.System,
            )

            if (accountGroup == "CHATBOT_AI_ENABLED") {
                chatBotMessagePublisher.publishWelcomeMessage(any(), any())
            } else {
                notificationAdapter.notifyUserActivated(any())
            }
            adService.publishAccountActivated(accountId = currentRegister.accountId)
        }
    }

    @Test
    fun `quando o usuário não possui as informacoes necessarias, deve dar erro`() {
        accountRegisterRepository.save(basicAccountRegisterData.copy(document = null))

        val result = basicRegisterService.validateIdentity(accountId = basicAccountRegisterData.accountId)

        result.isLeft() shouldBe true

        result.mapLeft { it: ValidateIdentityError ->
            it shouldBe ValidateIdentityError.UnableToValidate
        }

        verify(exactly = 0) { documentValidationServiceMock.validateWithSelfie(ofType<Document>(), any(), any()) }
    }

    @Test
    fun `deve retornar a identidade validada quando a validacao na serpro for com sucesso`() {
        every {
            documentValidationServiceMock.validateWithSelfie(
                document = basicAccountRegisterData.document!!,
                name = basicAccountRegisterData.getName(),
                selfieImageStream = ofType(InputStream::class),
            )
        } returns SerproBiometriaFaceResponseTO(
            cpfDisponivel = true,
            cnhDisponivel = true,
            biometriaFace = BiometriaTO(
                disponivel = true,
                probabilidade = SerproBiometriaProbability.HIGH,
                similaridade = 0.9,
            ),
        ).right()

        val result = basicRegisterService.validateIdentity(accountId = basicAccountRegisterData.accountId)

        result.isRight() shouldBe true
        result.map { it: AccountRegisterData ->
            it.identityValidationStatus!! shouldBe IdentityValidationStatus.CHECKED
        }

        accountRegisterRepository.findByAccountId(basicAccountRegisterData.accountId).identityValidationStatus shouldBe IdentityValidationStatus.CHECKED
    }

    @Test
    fun `deve retornar a identidade não validada quando a validacao na serpro retornar que não bateu a selfie`() {
        every {
            documentValidationServiceMock.validateWithSelfie(
                document = basicAccountRegisterData.document!!,
                name = basicAccountRegisterData.getName(),
                selfieImageStream = ofType(InputStream::class),
            )
        } returns SerproBiometriaFaceResponseTO(
            cpfDisponivel = true,
            cnhDisponivel = true,
            biometriaFace = BiometriaTO(
                disponivel = true,
                probabilidade = SerproBiometriaProbability.LOW,
                similaridade = 0.1,
            ),
        ).right()

        val result = basicRegisterService.validateIdentity(accountId = basicAccountRegisterData.accountId)

        result.isRight() shouldBe true
        result.map { it: AccountRegisterData ->
            it.identityValidationStatus!! shouldBe IdentityValidationStatus.REJECTED
        }

        verify {
            registerInstrumentationService.upgraded(
                accountId = basicAccountRegisterData.accountId,
                registrationType = RegistrationType.BASIC,
                reason = listOf(RiskAnalysisFailedReason.DOCUMENT_VALIDATION_REJECTED),
            )
        }
        accountRegisterRepository.findByAccountId(basicAccountRegisterData.accountId).identityValidationStatus shouldBe IdentityValidationStatus.REJECTED
    }

    @Test
    fun `deve retornar a identidade não validada quando a validacao na serpro der erro`() {
        every {
            documentValidationServiceMock.validateWithSelfie(
                document = basicAccountRegisterData.document!!,
                name = basicAccountRegisterData.getName(),
                selfieImageStream = ofType(InputStream::class),
            )
        } returns NoStackTraceException().left()

        val result = basicRegisterService.validateIdentity(accountId = basicAccountRegisterData.accountId)

        result.isLeft() shouldBe true
        result.mapLeft { it: ValidateIdentityError ->
            it.shouldBeTypeOf<ValidateIdentityError.InvalidResponse>()
        }

        accountRegisterRepository.findByAccountId(basicAccountRegisterData.accountId).identityValidationStatus shouldBe null
    }

    @Test
    fun `quando a probabilidade retornar nula, deve retornar um status de unknown`() {
        every {
            documentValidationServiceMock.validateWithSelfie(
                document = basicAccountRegisterData.document!!,
                name = basicAccountRegisterData.getName(),
                selfieImageStream = ofType(InputStream::class),
            )
        } returns SerproBiometriaFaceResponseTO(
            cpfDisponivel = true,
            cnhDisponivel = true,
            biometriaFace = BiometriaTO(
                disponivel = false,
                probabilidade = null,
                similaridade = null,
            ),
        ).right()

        val result = basicRegisterService.validateIdentity(accountId = basicAccountRegisterData.accountId)

        result.isRight() shouldBe true
        result.map { it: AccountRegisterData ->
            it.identityValidationStatus!! shouldBe IdentityValidationStatus.UNKNOWN
        }
        verify {
            registerInstrumentationService.upgraded(
                accountId = basicAccountRegisterData.accountId,
                registrationType = RegistrationType.BASIC,
                reason = listOf(RiskAnalysisFailedReason.UNABLE_TO_VALIDATE_DOCUMENT),
            )
        }

        accountRegisterRepository.findByAccountId(basicAccountRegisterData.accountId).identityValidationStatus shouldBe IdentityValidationStatus.UNKNOWN
    }

    @Test
    fun `deve salvar email não verificado quando o token é emitido`() {
        accountRegisterRepository.save(basicAccountRegisterData.copy(accountId = account.accountId))

        every {
            tokenService.issueToken(any<EmailAddress>(), any())
        } answers { IssuedToken(duration = Duration.ofSeconds(30), accountId = secondArg(), cooldown = Duration.ofSeconds(10)).right() }

        every { accountService.findPartialAccountById(any()) }.returns(
            partialAccount.copy(
                status = AccountStatus.REGISTER_INCOMPLETE,
                id = account.accountId,
            ),
        )

        every {
            accountRegisterService.checkIsTestAccount(accountId = any())
        } returns false

        val token =
            basicRegisterService.issueToken(email = emailAddress, accountId = account.accountId, "clientIp")
        token.isRight() shouldBe true

        val accountRegister = accountRegisterRepository.findByAccountId(account.accountId)
        accountRegister.emailAddress shouldBe emailAddress
        accountRegister.emailVerified shouldBe false
        accountRegister.clientIP shouldBe "clientIp"

        val slot = slot<AccountRegisterData>()
        verify {
            crmService.upsertContact(capture(slot))
        }
        slot.captured.emailAddress shouldBe emailAddress
    }

    @Test
    fun `deve salvar email do usuário quando o token for válido e atualizar usuário no crm`() {
        accountRegisterRepository.save(basicAccountRegisterData.copy(accountId = account.accountId))

        val validTokenKey = TokenKey(account.accountId, "123456")
        every { tokenService.validateToken(any(), TokenType.EMAIL) } returns Either.Right(
            TokenData.of(
                emailAddress,
            ),
        )

        every { accountService.findPartialAccountById(any()) }.returns(
            partialAccount.copy(
                status = AccountStatus.REGISTER_INCOMPLETE,
                id = account.accountId,
            ),
        )

        val result = basicRegisterService.updateMsisdnEmail(validTokenKey)
        result.isRight() shouldBe true
        result.map {
            val register = accountRegisterRepository.findByAccountId(account.accountId)
            register.emailAddress shouldBe emailAddress
            register.emailVerified shouldBe true
            register.lastUpdated.format(dateTimeFormat) shouldBe it.lastUpdated.format(
                dateTimeFormat,
            )

            val slot = slot<AccountRegisterData>()
            verify {
                crmService.upsertContact(capture(slot))
            }
            slot.captured.shouldBeEqualToIgnoringFields(register, AccountRegisterData::lastUpdated)
        }

        verify { accountService.updatePartialAccountEmail(eq(account.accountId), eq(emailAddress)) }
    }

    @ParameterizedTest
    @EnumSource(InvalidTokenReason::class)
    fun `não deve salvar email do usuário quando o token for inválido`(reason: InvalidTokenReason) {
        accountRegisterRepository.save(
            basicAccountRegisterData.copy(
                accountId = account.accountId,
                emailVerified = false,
            ),
        )

        val validTokenKey = TokenKey(account.accountId, "123456")
        every { tokenService.validateToken(any(), TokenType.EMAIL) } returns Either.Left(
            InvalidTokenException(reason),
        )

        every { accountService.findPartialAccountById(any()) }.returns(
            partialAccount.copy(
                status = AccountStatus.REGISTER_INCOMPLETE,
                id = account.accountId,
            ),
        )

        val result = basicRegisterService.updateMsisdnEmail(validTokenKey)
        result.isLeft() shouldBe true
        result.mapLeft {
            it.shouldBeTypeOf<InvalidTokenException>()
            it.reason shouldBe reason
        }

        val register = accountRegisterRepository.findByAccountId(account.accountId)
        register.emailAddress shouldBe basicAccountRegisterData.emailAddress
        register.emailVerified shouldBe false
    }

    @Test
    fun `deve retornar AccountIsBlockedForEdition quando o cadastro esta em revisao e o usuario tenta atualizar o email`() {
        every { accountService.findPartialAccountById(eq(account.accountId)) }.returns(partialAccount.copy(status = AccountStatus.UNDER_REVIEW))

        val result = basicRegisterService.updateMsisdnEmail(TokenKey(account.accountId, "adasd"))
        result.isLeft() shouldBe true
        result.mapLeft { it.shouldBeTypeOf<AccountIsBlockedForEdition>() }
    }
}