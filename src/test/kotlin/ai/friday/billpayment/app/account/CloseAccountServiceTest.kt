package ai.friday.billpayment.app.account

import DynamoDBUtils
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.NoStackTraceException
import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import ai.friday.billpayment.adapters.arbi.ArbiInvalidBalanceException
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterDbRepository
import ai.friday.billpayment.adapters.dynamodb.AccountRegisterEntity
import ai.friday.billpayment.adapters.dynamodb.BillEventDBRepository
import ai.friday.billpayment.adapters.dynamodb.BillRecurrenceDBRepository
import ai.friday.billpayment.adapters.dynamodb.DDADbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.DynamoDbTransactionRepository
import ai.friday.billpayment.adapters.dynamodb.PixKeyDbRepository
import ai.friday.billpayment.adapters.dynamodb.PixKeyDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.ScheduledBillDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDBRepository
import ai.friday.billpayment.adapters.dynamodb.ScheduledWalletDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.UtilityAccountDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.adapters.dynamodb.accountRegisterPrefix
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.backoffice.FraudPreventionService
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.ExternalBankAccount
import ai.friday.billpayment.app.banking.ExternalBankAccountService
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.dda.CreateDDARegister
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.DDAProviderService
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.RegisterInstrumentationService
import ai.friday.billpayment.app.integrations.UserPoolAdapter
import ai.friday.billpayment.app.liveness.LivenessService
import ai.friday.billpayment.app.login.LoginService
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyService
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.BackOfficeRecurrenceService
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.statement.StatementItem
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.app.statement.UserStatement
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.subscription.UnsubscribeError
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionDetails
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountId
import ai.friday.billpayment.app.utilityaccount.UtilityConnectionMethod
import ai.friday.billpayment.app.wallet.BackOfficeWalletService
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.app.wallet.WalletType
import ai.friday.billpayment.app.wallet.simpleName
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.createDDAConfig
import ai.friday.billpayment.creditCardCashInTransaction
import ai.friday.billpayment.defaultTransactionEntityConverter
import ai.friday.billpayment.externalBankAccount
import ai.friday.billpayment.integration.ACCOUNT_ID_2
import ai.friday.billpayment.integration.DOCUMENT_2
import ai.friday.billpayment.integration.EMAIL
import ai.friday.billpayment.integration.EMAIL_2
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_2
import ai.friday.billpayment.integration.PAYMENT_METHOD_ID_3
import ai.friday.billpayment.integration.WALLET_ID
import ai.friday.billpayment.integration.WALLET_ID_2
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.createBillPaymentTable
import ai.friday.billpayment.integration.loadAccountIntoDb
import ai.friday.billpayment.integration.loadBalancePaymentMethod
import ai.friday.billpayment.scheduledBill
import ai.friday.billpayment.weeklyWalletRecurrenceNoEndDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.kotest.assertions.failure
import io.kotest.inspectors.forAll
import io.kotest.inspectors.forNone
import io.kotest.matchers.booleans.shouldBeTrue
import io.kotest.matchers.collections.shouldBeEmpty
import io.kotest.matchers.collections.shouldBeSingleton
import io.kotest.matchers.collections.shouldHaveSize
import io.kotest.matchers.shouldBe
import io.kotest.matchers.shouldNotBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.mockk.Called
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.slot
import io.mockk.spyk
import io.mockk.verify
import io.mockk.verifyOrder
import java.time.ZonedDateTime
import java.util.stream.Stream
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.DisplayName
import org.junit.jupiter.api.Nested
import org.junit.jupiter.api.Test
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.EnumSource
import org.junit.jupiter.params.provider.MethodSource

internal class CloseAccountServiceTest {
    private val accountId = AccountId("ACCOUNT-TO-CLOSE")
    private val account = ACCOUNT.copy(accountId = accountId)
    private val documentPF = "***********"
    private val documentPJ = "***********111"

    private val walletFixture = WalletFixture(founderAccountId = accountId)
    private val wallet =
        walletFixture.buildWallet(
            accountPaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
        )
    private val secondaryWallet =
        walletFixture.buildWallet(
            accountPaymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_3),
            type = WalletType.SECONDARY,
        )
    private val wallets = listOf(wallet, secondaryWallet)
    private val walletAccountNumber = AccountNumber("123456")
    private val secondaryWalletAccountNumber = AccountNumber("678906")

    private val enhancedClient = DynamoDBUtils.setupDynamoDB()
    private val utilityAccountRepository =
        UtilityAccountDbRepository(client = UtilityAccountDynamoDAO(cli = enhancedClient))

    private val dynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
    private val dynamoDbDAO = DynamoDbDAO(amazonDynamoDB = dynamoDB)

    private val scheduledBillRepository = ScheduledBillDBRepository(dynamoDbDAO = dynamoDbDAO)

    private val dynamoDbEnhancedAsyncClient = DynamoDBUtils.getDynamoDBAsync()
    private val scheduledWalletDynamoDAO = ScheduledWalletDynamoDAO(dynamoDbEnhancedAsyncClient)
    private val scheduledWalletRepository = ScheduledWalletDBRepository(scheduledWalletDynamoDAO)

    private val accountRepository = AccountDbRepository(dynamoDbDAO = dynamoDbDAO)
    private val walletRepository = spyk(WalletDbRepository(dynamoDbDAO = dynamoDbDAO, accountRepository = mockk()))

    private val billRecurrenceRepository =
        BillRecurrenceDBRepository(
            dynamoDbDAO = dynamoDbDAO,
            lastLimitDateString = getLocalDate().plusWeeks(2).format(dateFormat),
        )
    private val backOfficeRecurrenceService = BackOfficeRecurrenceService(billRecurrenceRepository)
    private val accountService =
        spyk(
            AccountService(
                accountConfigurationService = mockk(),
                accountRepository = accountRepository,
                crmService = mockk(),
                chatBotMessagePublisher = mockk(),
                notificationAdapter = mockk(),
                walletRepository = walletRepository,
            ),
        )
    private val accountRegisterService = mockk<AccountRegisterService>()
    private val pixKeyDomain = "fake.io"
    private val walletService =
        WalletService(
            accountService = accountService,
            walletRepository = walletRepository,
            configuration = mockk(),
            notificationAdapter = mockk(),
            eventPublisher = mockk(relaxUnitFun = true),
            crmService = mockk(),
            pixKeyRepository = mockk(),
        ).apply {
            defaultPixKeyDomain = pixKeyDomain
        }
    private val backOfficeAccountService =
        spyk(BackOfficeAccountService(backOfficeAccountRepository = accountRepository))
    private val userPoolAdapter: UserPoolAdapter =
        mockk {
            every { signOutUser(any()) } just runs
            every { disableUser(any()) } returns Unit.right()
        }

    private val subscription: Subscription =
        mockk {
            every { walletId } returns wallet.id
            every { paymentStatus } returns SubscriptionPaymentStatus.PAID
        }

    private val subscriptionService: SubscriptionService =
        mockk {
            every { unsubscribe(any()) } returns Unit.right()
            every { findOrNull(accountId) } returns subscription
        }

    private val pixKeyManagement: PixKeyManagement =
        mockk {
            every { deleteKey(any(), any(), any()) } returns Unit.right()
        }
    private val ddaProviderService: DDAProviderService =
        mockk {
            every {
                remove(any())
            } just runs
        }
    private val ddaRepository =
        DDADbRepository(
            dynamoDbDAO = dynamoDbDAO,
        )

    private val ddaConfig = createDDAConfig()

    private val createDDARegister =
        CreateDDARegister(
            ddaRepository = ddaRepository,
            ddaConfig = ddaConfig,
        )

    private val ddaService =
        DDAService(
            ddaProviderService = ddaProviderService,
            accountRepository = mockk(),
            billValidationService = mockk(),
            updateBillService = mockk(),
            findBillService = mockk(),
            featureConfiguration = mockk(),
            ddaRepository = ddaRepository,
            messagePublisher = mockk(),
            fullDDAPostProcessor = mockk(),
            ddaConfig = ddaConfig,
            createDDARegister = createDDARegister,
            fichaCompensacaoService = mockk(),
            walletService = mockk(),
            openFinanceIncentiveService = mockk(relaxed = true),
        )
    private val externalAccountRegister: ExternalAccountRegister =
        mockk {
            every { close(any()) } returns Unit.right()
        }
    private val loginService: LoginService = mockk(relaxUnitFun = true)
    private val schedulingService =
        spyk(
            PaymentSchedulingService(
                scheduledBillRepository = scheduledBillRepository,
                scheduledWalletRepository = scheduledWalletRepository,
            ),
        )
    private val balanceService: BalanceService =
        mockk {
            every { getBalanceFrom(any(), any(), any()) } returns Balance(0)
        }

    private val backOfficeWalletService =
        spyk(
            BackOfficeWalletService(
                accountService = accountService,
                walletRepository = walletRepository,
                eventPublisher = mockk(relaxUnitFun = true),
                supportEmail = "<EMAIL>",
                allowedOnboardingAssistantIds = listOf(),
                chatbotName = "Fred",
            ),
        )
    private val billEventRepository =
        BillEventDBRepository(
            dynamoDbDAO = dynamoDbDAO,
            featureConfiguration = mockk(),
        )
    private val transactionRepository =
        spyk(
            DynamoDbTransactionRepository(
                dynamoDbDAO = dynamoDbDAO,
                accountRepository = accountRepository,
                transactionEntityConverter = defaultTransactionEntityConverter(billEventRepository, walletRepository),
            ),
        )
    private val accountRegisterRepository =
        spyk(
            AccountRegisterDbRepository(
                dynamoDbDAO = dynamoDbDAO,
                databaseObjectRepository = mockk(),
            ),
        )

    private val pixKeyRepository = PixKeyDbRepository(PixKeyDynamoDAO(enhancedClient))

    private val registerInstrumentationService = mockk<RegisterInstrumentationService>(relaxed = true)
    private val livenessService = mockk<LivenessService>(relaxed = true) {
        every {
            markAsFraud(any())
        } returns Unit.right()
    }

    private val crmService = mockk<CrmService>(relaxed = true)

    private val mockedDeviceFingerprint = mockk<DeviceFingerprintService> {
        every { withRealOrTemporaryDeviceId(any(), any(), ofType<(DeviceId) -> Any>()) } answers {
            thirdArg<(DeviceId) -> Any>().invoke(deviceId)
        }
    }

    private val deviceId = DeviceId("device-id")

    private val pixKeyService =
        PixKeyService(
            pixKeyRepository = pixKeyRepository,
            pixKeyManagementAdapter = pixKeyManagement,
            accountService = accountService,
            walletService = walletService,
            deviceFingerprintService = mockedDeviceFingerprint,
        )

    private val fraudPreventionService = mockk<FraudPreventionService>(relaxed = true)

    private val messagePublisher = mockk<MessagePublisher>(relaxed = true)

    private val statementService = mockk<StatementService> {
        every { findAllStatementsByDate(any(), any(), any()) } returns mockk<UserStatement> {
            every { statementItems } returns emptyList()
        }.right()

        every { findAllCredits(any(), any()) } returns emptyList<BankStatementItem>().right()
    }

    private val onboardingTestPixService = mockk<OnboardingTestPixService>()

    private val externalBankAccountService = mockk<ExternalBankAccountService>()

    private val closeWalletStepExecutors = listOf(
        DeleteWalletPixKeyStepExecutor(pixKeyManagement, walletService, mockedDeviceFingerprint),
        DeleteOldPixKeyStepExecutor(pixKeyManagement, mockedDeviceFingerprint, walletService),
        DeleteCustomWalletPixKeysStepExecutor(pixKeyService),
        DisconnectUtilityAccountsStepExecutor(utilityAccountRepository),
        IgnoreRecurrencesStepExecutor(backOfficeRecurrenceService),
        TransferAllFundsFromAccountStepExecutor(fraudPreventionService),
        CancelAllSchedulesStepExecutor(schedulingService),
        CloseExternalAccountNowStepExecutor(
            accountService,
            externalAccountRegister,
            backOfficeAccountService,
            CloseExternalAccountLaterStepExecutor(
                backOfficeAccountService,
                messagePublisher,
                "close_wallet",
            ),
        ),
        CloseExternalAccountLaterStepExecutor(
            backOfficeAccountService,
            messagePublisher,
            "close_wallet",
        ),
        RefundOnboardingTestPixStepExecutor(
            onboardingTestPixService,
            balanceService,
            accountService,
        ),
        CloseAndRemoveMembersStepExecutor(backOfficeWalletService),
    )

    private val closeAccountStepExecutors = listOf(
        UnsubscribeStepExecutor(subscriptionService),
        RemoveFromDDAStepExecutor(ddaService),
        SignOutUserStepExecutor(userPoolAdapter),
        DisableNotificationStepExecutor(accountService),
        RemoveFromUserPoolStepExecutor(userPoolAdapter),
        RemoveLoginStepExecutor(loginService),
        UpdateStatusClosedStepExecutor(backOfficeAccountService),
        RemoveFromWalletsStepExecutor(backOfficeWalletService),
        CloseCreditCardsStepExecutor(backOfficeAccountService),
        CloseCrmContactStepExecutor(crmService),
        DeactivateAccountRegisterStepExecutor(
            accountRegisterRepository,
            registerInstrumentationService,
        ),
        MarkAsFraudStepExecutor(
            livenessService,
        ),
        CloseFounderWalletStepExecutor(
            walletService,
            closeWalletStepExecutors,
        ),
    )

    private val closeAccountQueueName = "close_account"

    private val closeAccountStepDiscovery = mockk<CloseAccountStepDiscovery>() {
        every {
            prepareClose(
                accountId = any(),
                closureDetails = any(),
            )
        } returns emptyList<CloseAccountStep>().right()
    }
    private val closeAccountService =
        CloseAccountService(
            accountService = accountService,
            accountRegisterService = accountRegisterService,
            accountRegisterRepository = accountRegisterRepository,
            walletService = walletService,
            loginService = loginService,
            transactionRepository = transactionRepository,
            balanceService = balanceService,
            backOfficeAccountService = backOfficeAccountService,
            crmService = crmService,
            subscriptionService = subscriptionService,
            livenessService = livenessService,
            statementService = statementService,
            closeAccountStepExecutors = closeAccountStepExecutors,
            messagePublisher = messagePublisher,
            closeAccountQueueName = closeAccountQueueName,
            onboardingTestPixService = onboardingTestPixService,
            externalAccountService = externalBankAccountService,
            closeAccountStepDiscovery = listOf(closeAccountStepDiscovery),
            closeWalletStepDiscovery = listOf(),
        )

    private val withoutReasonClosureDetails =
        AccountClosureDetails.create(
            reason = null,
            description = "Conta encerrada por solicitação interna",
            at = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone),
        )
    private val userRequestClosureDetails =
        AccountClosureDetails.create(
            reason = AccountClosureReason.USER_REQUEST,
            description = "Conta encerrada por solicitação do usuário",
            at = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone),
        )

    private val possibleFraudClosureDetails = AccountClosureDetails.create(
        reason = AccountClosureReason.POSSIBLE_FRAUD,
        description = "Conta encerrada por possível fraude",
        at = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone),
    )

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        setupAccount(documentPF)
    }

    private fun setupAccount(document: String) {
        loadAccountIntoDb(
            amazonDynamoDB = dynamoDB,
            accountId = accountId,
            document = document,
        )
        accountRegisterRepository.save(
            accountRegisterCompleted.copy(
                accountId = accountId,
                documentInfo = accountRegisterCompleted.documentInfo?.copy(cpf = document),
            ),
        )
        walletRepository.save(wallet)
        loadBalancePaymentMethod(
            accountRepository = accountRepository,
            bankNo = 213L,
            bankAccountNo = walletAccountNumber.number,
            bankAccountDv = walletAccountNumber.dv,
            document = document,
            accountId = accountId.value,
            position = 1,
            paymentMethodId = wallet.paymentMethodId.value,
        )
        walletRepository.save(secondaryWallet)
        loadBalancePaymentMethod(
            accountRepository = accountRepository,
            bankNo = 213L,
            bankAccountNo = secondaryWalletAccountNumber.number,
            bankAccountDv = secondaryWalletAccountNumber.dv,
            document = document,
            accountId = accountId.value,
            position = 2,
            paymentMethodId = secondaryWallet.paymentMethodId.value,
        )
        ddaRepository.save(
            DDARegister(
                accountId = accountId,
                document = document,
                created = getZonedDateTime(),
                status = DDAStatus.ACTIVE,
                lastUpdated = getZonedDateTime(),
            ),
        )

        pixKeyRepository.create(wallet.id, walletAccountNumber, PixKey(document, PixKeyType.CPF))
    }

    @Test
    fun `should remove pix key`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        verify {
            pixKeyManagement.deleteKey(
                key = "$documentPF@$pixKeyDomain",
                document = Document(documentPF),
                deviceId = deviceId,
            )
            pixKeyManagement.deleteKey(
                key = "$<EMAIL>",
                document = Document(documentPF),
                deviceId = deviceId,
            )
            pixKeyManagement.deleteKey(
                key = documentPF,
                document = Document(documentPF),
                deviceId = deviceId,
            )
        }
        verify(exactly = 0) {
            messagePublisher.sendMessage(closeAccountQueueName, any(), any())
        }
    }

    @Test
    fun `deve enviar para fila quando o delay for maior do que zero`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails, delaySeconds = 1).isRight() shouldBe true

        verify {
            messagePublisher.sendMessage(closeAccountQueueName, any(), 1)
        }
    }

    @Test
    fun `should not fail when user does not have subscription`() {
        every { subscriptionService.findOrNull(accountId) } returns null

        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true
    }

    @Test
    fun `should remove user from user pool`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        verify { userPoolAdapter.disableUser(documentPF) }
    }

    @Test
    fun `should not remove user from user pool when account has balance`() {
        val amount = 10L
        every {
            balanceService.getBalanceFrom(any(), wallet.paymentMethodId, any())
        } returns Balance(amount)

        every {
            balanceService.getBalanceFrom(any(), secondaryWallet.paymentMethodId, any())
        } returns Balance(0L)

        every {
            statementService.findAllCredits(any(), any())
        } returns
            listOf(
                bankStatementItem,
            ).right()

        every { externalBankAccountService.findLastUsed(any()) } returns null
        every { onboardingTestPixService.findPixKey(any()) } returns Result.success(null)

        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)

        result.isLeft() shouldBe true
        result.mapLeft {
            it shouldBe CloseAccountError.CloseFounderWalletError(CloseWalletError.BalanceDifferentThanZero(wallet.id, 10L, externalBankAccount = null, pixKey = null))
        }

        verify { userPoolAdapter wasNot Called }
    }

    @Test
    fun `deve buscar a última conta usada como cash-in e a chave pix do onboarding test quando tiver saldo`() {
        val amount = 10L
        val document = Document(wallet.founder.document)
        val externalBankAccount = ExternalBankAccount(document = document, accountNo = 123L, accountDv = "4", routingNo = 567L, accountType = null, bankNo = null, lastUsed = null, bankISPB = null)
        val pixKey = PixKey(type = PixKeyType.EMAIL, value = "<EMAIL>")

        every { balanceService.getBalanceFrom(any(), wallet.paymentMethodId, any()) } returns Balance(amount)
        every { balanceService.getBalanceFrom(any(), secondaryWallet.paymentMethodId, any()) } returns Balance(0L)
        every { statementService.findAllCredits(any(), any()) } returns listOf(bankStatementItem).right()
        every { externalBankAccountService.findLastUsed(any()) } returns externalBankAccount
        every { onboardingTestPixService.findPixKey(any()) } returns Result.success(pixKey)

        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).fold(
            { left ->
                verify(exactly = 1) { externalBankAccountService.findLastUsed(document) }
                verify(exactly = 1) { onboardingTestPixService.findPixKey(accountId) }

                left shouldBe CloseAccountError.CloseFounderWalletError(
                    CloseWalletError.BalanceDifferentThanZero(
                        walletId = wallet.id,
                        balance = 10L,
                        externalBankAccount = externalBankAccount,
                        pixKey = pixKey,
                    ),
                )
            },
            { _ ->
                failure("Should be left")
            },
        )
    }

    @Test
    fun `nao deve remover o usuário se algum dos discovery falhar`() {
        every {
            closeAccountStepDiscovery.prepareClose(
                accountId = any(),
                closureDetails = any(),
            )
        } returns CloseAccountError.CloseAccountErrorWithMessageAndAmount("key", 10L).left()
        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)

        result.isLeft() shouldBe true
        result.mapLeft {
            it shouldBe CloseAccountError.CloseAccountErrorWithMessageAndAmount("key", 10L)
        }

        verify { userPoolAdapter wasNot Called }
    }

    @Test
    fun `no fechamento de carteira secundária deve remover somente a carteira secundária do usuário`() {
        every {
            balanceService.getBalanceFrom(any(), secondaryWallet.paymentMethodId, any())
        } returns Balance(0L)

        val result = closeAccountService.closeSecondaryWallet(secondaryWallet)

        result.isRight() shouldBe true

        accountService.findAccountPaymentMethodByIdAndAccountId(secondaryWallet.paymentMethodId, accountId).status shouldBe AccountPaymentMethodStatus.CLOSED
        walletService.findWallet(secondaryWallet.id).status shouldBe WalletStatus.CLOSED
    }

    @Test
    fun `no fechamento de carteira secundária deve retornar erro se a carteira for primária`() {
        every {
            balanceService.getBalanceFrom(any(), wallet.paymentMethodId, any())
        } returns Balance(0L)

        val result = closeAccountService.closeSecondaryWallet(wallet)

        result.shouldBeTypeOf<Either.Left<CloseAccountStepError>>()
    }

    @Test
    fun `deve remover o usuario e transferir o saldo da conta do usuario para o tripix quando o usuario so tiver isso de saldo`() {
        val amount = 10L
        every {
            balanceService.getBalanceFrom(any(), wallet.paymentMethodId, any())
        } returns Balance(amount)

        every {
            balanceService.getBalanceFrom(any(), secondaryWallet.paymentMethodId, any())
        } returns Balance(0L)

        every {
            onboardingTestPixService.refundFriday(any(), any())
        } returns Unit.right()

        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)

        verify {
            onboardingTestPixService.refundFriday(any(), 10L)
            userPoolAdapter.signOutUser("***********")
            userPoolAdapter.disableUser("***********")
        }

        result.isRight() shouldBe true
        result.map { it: CloseAccountResult ->
            it.steps shouldHaveAllDefaultPFStepsWithStatus CloseAccountStepStatus.Success

            wallets.forEach { wallet ->
                val closeWalletStep = it.steps.closeFounderWalletStep(wallet.id)

                if (wallet.type == WalletType.PRIMARY) {
                    closeWalletStep shouldHaveAllDefaultStepsWithStatus CloseWalletStepStatus.Success
                } else {
                    closeWalletStep shouldHaveAllDefaultStepsExceptDeleteOldPixWithStatus CloseWalletStepStatus.Success
                }
            }

            // carteira com saldo do tripix e nao pode ser fechada agora
            with(it.steps.closeFounderWalletStep(wallet.id)) {
                this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                this.steps shouldHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                this.shouldCloseExternalAccountLater()
            }

            // carteira que pode ser fechada agora
            with(it.steps.closeFounderWalletStep(secondaryWallet.id)) {
                this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                this.shouldCloseExternalAccountNow()
            }
        }
    }

    @Test
    fun `should cancel subscription`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        verify { subscriptionService.unsubscribe(accountId) }
    }

    @ParameterizedTest
    @MethodSource("unsubscribeErrors")
    fun `deve enviar para fila de retentativa se algum passo falha`(unsubscribeError: UnsubscribeError, expectedStatus: CloseAccountStepStatus) {
        every {
            subscriptionService.unsubscribe(accountId)
        } returns unsubscribeError.left()

        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)

        result.isRight() shouldBe true
        result.map { it: CloseAccountResult ->
            it.step(CloseAccountStepTypeUnsubscribe) shouldHaveStatus expectedStatus
        }

        if (expectedStatus == CloseAccountStepStatus.Warning) {
            verify(exactly = 0) { messagePublisher.sendMessage(any(), any(), any()) }
        } else {
            val slot = slot<CloseAccountMessage>()
            verify { messagePublisher.sendMessage(closeAccountQueueName, capture(slot), any()) }
            with(slot.captured) {
                this.accountId shouldBe accountId
            }
        }
    }

    @Test
    fun `should turn off notifications`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        accountRepository.findById(accountId).configuration.receiveNotification shouldBe false
    }

    @Test
    fun `should remove account from DDA`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true
        ddaRepository.find(accountId)?.status shouldBe DDAStatus.PENDING_CLOSE
    }

    @Test
    fun `should close external account`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        verify {
            externalAccountRegister.close(AccountNumber("123456"))
            externalAccountRegister.close(AccountNumber("678906"))
        }
    }

    @Test
    fun `should update account status to CLOSED`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        accountRepository.findById(accountId).status shouldBe AccountStatus.CLOSED
    }

    @Test
    fun `should remove user logins`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        verify {
            loginService.remove(any<AccountId>())
        }
    }

    @Test
    fun `should update payment methods status to CLOSED`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        accountRepository.findAccountPaymentMethodsByAccountId(accountId)
            .forAll { it.status shouldBe AccountPaymentMethodStatus.CLOSED }
    }

    @Test
    fun `should remove all pix keys`() {
        val activePixKeys = pixKeyRepository.list(wallet.id)
        activePixKeys.size shouldBe 1

        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        val response = pixKeyRepository.list(wallet.id)

        response.size shouldBe 0
    }

    @Test
    fun `should unschedule bills`() {
        schedulingService.schedule(scheduledBill.copy(walletId = wallet.id))

        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        scheduledBillRepository.findAllScheduledBillsByWalletId(wallet.id).shouldBeEmpty()
        scheduledWalletRepository.findAllWalletsWithScheduledBills().toIterable().toList()
            .forNone { it shouldBe wallet.id }
    }

    @Test
    fun `should remove user from CRM`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        verify(exactly = 1) {
            crmService.removeContactAsync(accountId)
        }
    }

    @Test
    fun `deve publicar evento no Intercom`() {
        closeAccountService.closeAccount(accountId, userRequestClosureDetails).isRight() shouldBe true

        verify(exactly = 1) {
            registerInstrumentationService.closed(accountId, any(), userRequestClosureDetails)
        }
    }

    @Test
    fun `should not remove user from user pool when account has any ongoing transaction`() {
        transactionRepository.save(
            creditCardCashInTransaction.copy(
                payer = creditCardCashInTransaction.payer.copy(accountId = accountId),
                status = TransactionStatus.PROCESSING,
                walletId = wallet.id,
            ),
        )

        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)

        result.isLeft() shouldBe true
        result.mapLeft {
            it shouldBe CloseAccountError.CloseFounderWalletError(CloseWalletError.TransactionStillProcessing(wallet.id))
        }
    }

    @Test
    fun `should remove user from shared wallets`() {
        loadPrimaryWallet()
        loadSecondaryWallet()

        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        walletRepository.findWallets(accountId).filter { it.hasActiveMember(accountId) }.shouldBeEmpty()
    }

    @Test
    fun `should not have any member with an wallet that is being deactivated as default`() {
        val anotherMemberAccountId = AccountId(ACCOUNT_ID_2)
        val anotherMember =
            Member(
                accountId = anotherMemberAccountId,
                document = DOCUMENT_2,
                name = "Ciclano de Tal",
                emailAddress = EmailAddress(EMAIL_2),
                type = MemberType.COLLABORATOR,
                status = MemberStatus.ACTIVE,
                permissions = MemberPermissions.of(MemberType.COLLABORATOR),
                created = getZonedDateTime(),
                updated = getZonedDateTime(),
            )
        loadPrimaryWallet(listOf(anotherMember))
        loadSecondaryWallet()

        loadAccountIntoDb(accountId = anotherMemberAccountId, amazonDynamoDB = dynamoDB)
        accountService.updateAccountConfig(
            accountId = anotherMemberAccountId,
            name = AccountConfigurationName.DEFAULT_WALLET_ID,
            value = accountId.value,
        )

        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        walletRepository.findWallets(accountId, MemberStatus.REMOVED).filter { it.founder.accountId == accountId }
            .forAll { wallet ->
                wallet.activeMembers.forAll { activeMember ->
                    accountRepository.findById(activeMember.accountId).configuration.defaultWalletId shouldNotBe wallet.id
                }
            }
    }

    @Test
    fun `should close all wallets from founder and remove members`() {
        val walletFixture = WalletFixture()
        loadPrimaryWallet(listOf(walletFixture.participant))
        loadSecondaryWallet()

        loadAccountIntoDb(accountId = walletFixture.participant.accountId, amazonDynamoDB = dynamoDB)

        walletRepository.findWallet(WalletId(WALLET_ID)).activeMembers.shouldHaveSize(2)
        walletRepository.findWallet(WalletId(WALLET_ID_2)).activeMembers.shouldHaveSize(2)

        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        walletRepository.findWallets(accountId, MemberStatus.REMOVED).filter { it.founder.accountId == accountId }
            .forAll { wallet ->
                wallet.status shouldBe WalletStatus.CLOSED
            }
        walletRepository.findWallets(accountId, MemberStatus.REMOVED).filter { it.founder.accountId != accountId }
            .forAll { wallet ->
                wallet.status shouldBe WalletStatus.ACTIVE
            }

        walletRepository.findWallet(WalletId(WALLET_ID)).activeMembers.shouldHaveSize(0)
        walletRepository.findWallet(WalletId(WALLET_ID_2)).activeMembers.shouldHaveSize(1)
    }

    @Test
    fun `should add CLOSED at account register index`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        dynamoDbDAO.queryIndexOnHashKeyValue(
            "$accountRegisterPrefix$documentPF#CLOSED",
            AccountRegisterEntity::class.java,
        )
            .shouldBeSingleton()
    }

    @Test
    fun `should invalidate refresh token from user pool`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight() shouldBe true

        verify { userPoolAdapter.signOutUser(documentPF) }
    }

    @Test
    fun `verify order`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)

        verifyOrder {
            balanceService.getBalanceFrom(any(), any(), any())
            transactionRepository.hasTransactionByWalletAndStatus(any(), any())
            pixKeyManagement.deleteKey(any(), any(), any())
            schedulingService.cancelAllSchedules(any())
            externalAccountRegister.close(any())
            userPoolAdapter.signOutUser(any())
            userPoolAdapter.disableUser(any())
            loginService.remove(any<AccountId>())
            accountService.updateAccountConfig(any(), any(), any())
            backOfficeAccountService.updateStatusClosed(any())
            backOfficeWalletService.removeFromWallets(any())
            backOfficeAccountService.closeCreditCards(any())
            accountRegisterRepository.deactivate(any(), any())
        }
    }

    @Test
    fun `should retry steps when external account is closed`() {
        accountRepository.closeAccountPaymentMethod(
            accountPaymentMethodId = wallet.paymentMethodId,
            accountId = wallet.founder.accountId,
        )
        accountRepository.closeAccountPaymentMethod(
            accountPaymentMethodId = secondaryWallet.paymentMethodId,
            accountId = secondaryWallet.founder.accountId,
        )

        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)
        result.isRight() shouldBe true
        result.map { it: CloseAccountResult ->
            it.steps shouldHaveAllDefaultPFStepsWithStatus CloseAccountStepStatus.Success

            wallets.forEach { wallet ->
                val closeWalletStep = it.steps.closeFounderWalletStep(wallet.id)

                if (wallet.type == WalletType.PRIMARY) {
                    closeWalletStep shouldHaveAllDefaultStepsWithStatus CloseWalletStepStatus.Success
                } else {
                    closeWalletStep shouldHaveAllDefaultStepsExceptDeleteOldPixWithStatus CloseWalletStepStatus.Success
                }

                closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeCloseExternalAccountNow
                closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeCloseExternalAccountLater
            }
        }

        verifyOrder {
            pixKeyManagement.deleteKey(any(), any(), any())
            userPoolAdapter.signOutUser(any())
            userPoolAdapter.disableUser(any())
            loginService.remove(any<AccountId>())
            accountService.updateAccountConfig(any(), any(), any())
            backOfficeAccountService.updateStatusClosed(any())
            backOfficeWalletService.removeFromWallets(any())
            backOfficeAccountService.closeCreditCards(any())
            accountRegisterRepository.deactivate(any(), any())
        }

        verify(exactly = 0) {
            externalAccountRegister.close(any())
        }
    }

    @Test
    fun `should not fail on error disabling notifications`() {
        every {
            accountService.updateAccountConfig(any(), any(), any())
        } returns UpdateAccountConfigError.WalletNotFound.left()

        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)
        result.isRight() shouldBe true
        result.map { it: CloseAccountResult ->
            it.step(CloseAccountStepTypeRemoveFromUserPool) shouldHaveStatus CloseAccountStepStatus.Success
            it.step(CloseAccountStepTypeRemoveFromDDA) shouldHaveStatus CloseAccountStepStatus.Success
            it.step(CloseAccountStepTypeDisableNotification) shouldHaveStatus CloseAccountStepStatus.Warning
        }
    }

    @Test
    fun `should not fail on error removing user from user pool`() {
        every {
            userPoolAdapter.disableUser(any())
        } returns UserPoolRemoveUserError.UserNotFound.left()

        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)
        result.isRight() shouldBe true
        result.map { it: CloseAccountResult ->
            it.steps.forEach {
                it.status shouldNotBe CloseAccountStepStatus.Pending
            }

            val stepResult = it.steps.singleOrNull { it.type == CloseAccountStepTypeRemoveFromUserPool }

            stepResult?.status shouldBe CloseAccountStepStatus.Warning
        }
    }

    @Test
    fun `should not fail on error closing external account`() {
        every {
            externalAccountRegister.close(any())
        } returns NoStackTraceException().left()

        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)
        result.isRight() shouldBe true
        result.map { it: CloseAccountResult ->
            it.steps.forEach {
                it.status shouldNotBe CloseAccountStepStatus.Pending
            }

            it.steps.filterIsInstance<CloseFounderWalletCloseAccountStep>().forEach { closeWalletStep ->
                closeWalletStep.steps.forEach {
                    it.status shouldNotBe CloseWalletStepStatus.Pending
                }

                val stepResult = closeWalletStep.steps.singleOrNull { it.type == CloseWalletStepTypeCloseExternalAccountNow }

                stepResult?.status shouldBe CloseWalletStepStatus.Warning
            }
        }
    }

    @Test
    fun `nao deve dar erro ao falhar encerrando AccountRegisterData`() {
        every {
            accountRegisterRepository.deactivate(any(), any())
        } throws ItemNotFoundException("")

        val result = closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)
        result.isRight() shouldBe true
    }

    @Test
    fun `deve remover as recorrencias ao fechar conta`() {
        billRecurrenceRepository.save(weeklyWalletRecurrenceNoEndDate.copy(walletId = wallet.id))

        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)

        with(billRecurrenceRepository.findByWalletId(wallet.id)) {
            this shouldHaveSize 1
            this.forAll { it.status shouldBe RecurrenceStatus.IGNORED }
        }
    }

    @Test
    fun `deve adicionar o motivo de fechamento da conta, a descricao e a data no AccountRegisterData`() {
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails)

        val accountRegisterData = accountRegisterRepository.findByAccountId(accountId, false)
        accountRegisterData.accountClosureDetails shouldBe withoutReasonClosureDetails
    }

    @Test
    fun `quando encerramento for por fraude confirmada deve marcar o rosto do usuario como fraudador no LivenessService`() {
        val fraudConfirmedClosedAccountData =
            AccountClosureDetails.create(
                reason = AccountClosureReason.FRAUD,
                description = "Fraude confirmada",
                at = getZonedDateTime(),
            )
        closeAccountService.closeAccount(accountId, fraudConfirmedClosedAccountData)

        verify { livenessService.markAsFraud(accountId) }
    }

    @Test
    fun `quando encerramento for por possivel fraude nao deve marcar o rosto do usuario como fraudador no LivenessService`() {
        val fraudConfirmedClosedAccountData =
            AccountClosureDetails.create(
                reason = AccountClosureReason.POSSIBLE_FRAUD,
                description = "Possível fraude",
                at = getZonedDateTime(),
            )
        closeAccountService.closeAccount(accountId, fraudConfirmedClosedAccountData)

        verify(exactly = 0) { livenessService.markAsFraud(accountId) }
    }

    @Test
    fun `deve encerrar uma partialAccount, salvar o motivo e remover do CRM`() {
        val partialAccount = loadPartialAccount()
        val partialAccountClosureDetails =
            AccountClosureDetails.create(
                reason = null,
                description = "Encerramento de cadastro",
                at = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone),
            )

        closeAccountService.closePartialAccount(partialAccount.id, partialAccountClosureDetails)

        val partialAccountData = accountRepository.findPartialAccountById(partialAccount.id)
        partialAccountData.status shouldBe AccountStatus.CLOSED

        val partialAccountRegisterData = accountRegisterRepository.findByAccountId(partialAccount.id, false)
        partialAccountRegisterData.accountClosureDetails shouldBe partialAccountClosureDetails

        verify {
            crmService.removeContactAsync(partialAccount.id)
        }
    }

    @Test
    fun `em caso de fraude confirmada deve encerrar a partialAccount e marcar o rosto do usuario como fraudador no LivenessService`() {
        val partialAccount = loadPartialAccount()
        val fraudConfirmedClosedAccountData =
            AccountClosureDetails.create(
                reason = AccountClosureReason.FRAUD,
                description = "Fraude confirmada",
                at = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone),
            )

        closeAccountService.closePartialAccount(partialAccount.id, fraudConfirmedClosedAccountData)

        val partialAccountData = accountRepository.findPartialAccountById(partialAccount.id)
        partialAccountData.status shouldBe AccountStatus.CLOSED

        val partialAccountRegisterData = accountRegisterRepository.findByAccountId(partialAccount.id, false)
        partialAccountRegisterData.accountClosureDetails shouldBe fraudConfirmedClosedAccountData

        verify { livenessService.markAsFraud(partialAccount.id) }
    }

    @Test
    fun `deve desconectar as contas de consumo`() {
        val utilityAccount =
            UtilityAccount(
                id = UtilityAccountId(value = "closed-account"),
                status = UtilityAccountConnectionStatus.CONNECTED,
                walletId = wallet.id,
                accountEmail = "<EMAIL>",
                attempts = 0,
                utility = Utility.VIVO_MOBILE,
                updatedAt = ZonedDateTime.now().minusDays(1),
                createdAt = ZonedDateTime.now().minusDays(1),
                notificatedAt = null,
                lastBillIdFound = null,
                lastDueDateFound = null,
                lastScannedAt = null,
                connectionMethod = UtilityConnectionMethod.SCRAPING,
                connectionDetails = UtilityAccountConnectionDetails.create(Utility.VIVO_MOBILE, mapOf("login" to "test", "password" to "test")),
                addedBy = accountId,
            )
        utilityAccountRepository.save(utilityAccount)
        closeAccountService.closeAccount(accountId, withoutReasonClosureDetails).isRight().shouldBeTrue()

        utilityAccountRepository.findById(
            utilityAccount.id,
            utilityAccount.walletId,
        )!!.status shouldBe UtilityAccountConnectionStatus.DISCONNECTED
    }

    private fun loadPartialAccount(): PartialAccount {
        val partialAccount =
            accountRepository.create(
                "partial account",
                EmailAddress("<EMAIL>"),
                registrationType = RegistrationType.BASIC,
            )
        accountRegisterRepository.save(
            accountRegisterCompleted.copy(
                accountId = partialAccount.id,
                nickname = partialAccount.name,
            ),
        )

        return partialAccount
    }

    private fun CloseAccountResult.step(type: CloseAccountStepType) = steps.step(type)
    private fun List<CloseAccountStep>.step(type: CloseAccountStepType) = this.singleOrNull { it.type == type }
    private fun List<CloseWalletStep>.step(type: CloseWalletStepType) = this.singleOrNull { it.type == type }
    private infix fun CloseAccountStep?.shouldHaveStatus(status: CloseAccountStepStatus) {
        this?.status shouldBe status
    }

    private infix fun CloseWalletStep?.shouldHaveStatus(status: CloseWalletStepStatus) {
        this?.status shouldBe status
    }

    @ParameterizedTest
    @MethodSource("closureDetails")
    fun `quando for indicacao de fraude deve remover do user pool, transferir o saldo para a conta friday e mandar para a fila de fechamento de conta`(closureDetails: AccountClosureDetails) {
        val amount = 10L
        every {
            balanceService.getBalanceFrom(any(), any(), any())
        } returns Balance(amount)

        every { fraudPreventionService.transferAllFundsFromAccount(any(), any()) } returns BankTransfer(operationId = BankOperationId(value = ""), gateway = FinancialServiceGateway.ARBI, status = BankOperationStatus.SUCCESS, amount = 10, authentication = "", errorDescription = "", debitOperationNumber = null, creditOperationNumber = null, pixKeyDetails = null).right()

        val result = closeAccountService.closeAccount(accountId, closureDetails)

        result.isRight() shouldBe true
        result.map { it: CloseAccountResult ->
            it.steps shouldHaveAllDefaultPFStepsWithStatus CloseAccountStepStatus.Success

            // carteiras com saldo capturados por fraude e nao podem ser fechadas agora
            wallets.forEach { wallet ->
                val closeWalletStep = it.steps.closeFounderWalletStep(wallet.id)

                if (wallet.type == WalletType.PRIMARY) {
                    closeWalletStep shouldHaveAllDefaultStepsWithStatus CloseWalletStepStatus.Success
                } else {
                    closeWalletStep shouldHaveAllDefaultStepsExceptDeleteOldPixWithStatus CloseWalletStepStatus.Success
                }

                closeWalletStep.steps shouldHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                closeWalletStep.shouldCloseExternalAccountLater()
            }
        }

        if (closureDetails.isFraud()) {
            verify { livenessService.markAsFraud(accountId) }
        }
        if (closureDetails.isPossibleFraud()) {
            verify(exactly = 0) { livenessService.markAsFraud(accountId) }
        }
        verify { userPoolAdapter.disableUser(any()) }
        verify { fraudPreventionService.transferAllFundsFromAccount(any(), any()) }
        verify { messagePublisher.sendMessage(any(), any<CloseExternalAccountEvent>(), 900) }
    }

    @Test
    fun `quando confirmar uma fraude de uma conta ja encerrada, deve mudar o closure details e marcar rosto como fraudador`() {
        val amount = 10L
        every {
            balanceService.getBalanceFrom(any(), any(), any())
        } returns Balance(amount)

        every { fraudPreventionService.transferAllFundsFromAccount(any(), any()) } returns BankTransfer(operationId = BankOperationId(value = ""), gateway = FinancialServiceGateway.ARBI, status = BankOperationStatus.SUCCESS, amount = 10, authentication = "", errorDescription = "", debitOperationNumber = null, creditOperationNumber = null, pixKeyDetails = null).right()
        every { livenessService.markAsFraud(any()) } returns Unit.right()

        val closeResult = closeAccountService.closeAccount(accountId, possibleFraudClosureDetails)

        closeResult.isRight() shouldBe true

        val accountRegister = accountRegisterRepository.findByAccountId(accountId, false)

        val result = closeAccountService.confirmFraud(accountRegister)

        result.isRight() shouldBe true

        verify(exactly = 1) { livenessService.markAsFraud(accountId) }
    }

    @Test
    fun `deve fechar a conta do usuario caso saldo for invalido`() {
        every {
            balanceService.getBalanceFrom(any(), any(), any())
        } throws ArbiInvalidBalanceException("invalid balance")
        every { livenessService.markAsFraud(any()) } returns Unit.right()

        val closeResult = closeAccountService.closeAccount(accountId, possibleFraudClosureDetails)

        closeResult.isRight() shouldBe true

        val accountRegister = accountRegisterRepository.findByAccountId(accountId, false)

        val result = closeAccountService.confirmFraud(accountRegister)

        result.isRight() shouldBe true

        verify(exactly = 0) {
            fraudPreventionService.transferAllFundsFromAccount(any(), any())
            statementService.findAllCredits(any(), any())
            onboardingTestPixService.refundFriday(any(), any())
        }
    }

    @Test
    fun `deve fechar a conta do usuario caso tenha um erro ao buscar o saldo`() {
        every {
            balanceService.getBalanceFrom(any(), any(), any())
        } throws ArbiAdapterException()
        every { livenessService.markAsFraud(any()) } returns Unit.right()

        val closeResult = closeAccountService.closeAccount(accountId, possibleFraudClosureDetails)

        closeResult.isRight() shouldBe true
    }

    @Nested
    @DisplayName("deve poder encerrar conta PF")
    inner class CanClosePFAccountTest {
        @ParameterizedTest
        @EnumSource(AccountClosureReason::class, mode = EnumSource.Mode.EXCLUDE, names = ["FRAUD"])
        fun `quando usuario nao possui saldo nem transacao processando e nao eh encerramento por fraude confirmada`(reason: AccountClosureReason) {
            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.WithReason(reason = reason, description = "", at = getZonedDateTime()))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPFSteps()
                steps shouldNotHaveStep CloseAccountStepTypeMarkAsFraud

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    closeWalletStep.shouldCloseExternalAccountNow()
                }
            }
        }

        @Test
        fun `mas nao deve encerrar a conta no arbi se a carteira for virtual`() {
            loadBalancePaymentMethod(
                accountRepository = accountRepository,
                bankNo = 213L,
                bankAccountNo = walletAccountNumber.number,
                bankAccountDv = walletAccountNumber.dv,
                document = documentPF,
                accountId = accountId.value,
                position = 1,
                paymentMethodId = wallet.paymentMethodId.value,
                mode = BankAccountMode.VIRTUAL,
            )
            loadBalancePaymentMethod(
                accountRepository = accountRepository,
                bankNo = 213L,
                bankAccountNo = secondaryWalletAccountNumber.number,
                bankAccountDv = secondaryWalletAccountNumber.dv,
                document = documentPF,
                accountId = accountId.value,
                position = 2,
                paymentMethodId = secondaryWallet.paymentMethodId.value,
                mode = BankAccountMode.VIRTUAL,
            )

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPFSteps()
                steps shouldNotHaveStep CloseAccountStepTypeMarkAsFraud

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeCloseExternalAccountNow
                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeCloseExternalAccountLater
                }
            }
        }

        @ParameterizedTest
        @EnumSource(AccountClosureReason::class, mode = EnumSource.Mode.INCLUDE, names = ["FRAUD"])
        fun `quando usuario nao possui saldo nem transacao processando e eh encerramento por fraude confirmada`(reason: AccountClosureReason) {
            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.WithReason(reason = reason, description = "", at = getZonedDateTime()))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPFSteps()
                steps shouldHaveStep CloseAccountStepTypeMarkAsFraud

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    closeWalletStep.shouldCloseExternalAccountNow()
                }
            }
        }

        @ParameterizedTest
        @EnumSource(AccountClosureReason::class, mode = EnumSource.Mode.INCLUDE, names = ["FRAUD", "POSSIBLE_FRAUD"])
        fun `quando usuario possui saldo e o motivo de encerramento eh fraude`(reason: AccountClosureReason) {
            every { balanceService.getBalanceFrom(accountId, secondaryWallet.paymentMethodId, any()) } returns Balance(10L)

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.WithReason(reason = reason, description = "", at = getZonedDateTime()))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPFSteps()

                if (reason == AccountClosureReason.FRAUD) {
                    steps shouldHaveStep CloseAccountStepTypeMarkAsFraud
                } else {
                    steps shouldNotHaveStep CloseAccountStepTypeMarkAsFraud
                }

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()
                }

                // carteira sem saldo, fecha agora
                with(steps.closeFounderWalletStep(wallet.id)) {
                    this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountNow()
                }

                // carteira com saldo, captura e fecha depois
                with(steps.closeFounderWalletStep(secondaryWallet.id)) {
                    this.steps shouldHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountLater()
                }
            }
        }

        @Test
        fun `quando usuario tem saldo mas nao fez nenhum deposito`() {
            every { balanceService.getBalanceFrom(any(), any(), any()) } returns Balance(10L)

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.WithoutReason(description = "", at = getZonedDateTime()))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPFSteps()
                steps shouldNotHaveStep CloseAccountStepTypeMarkAsFraud

                // tem saldo nas duas carteiras, captura e encerra depois
                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    closeWalletStep.steps shouldHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    closeWalletStep.shouldCloseExternalAccountLater()
                }
            }
        }

        @Test
        fun `quando usuario tem saldo e esta inadimplente`() {
            every { balanceService.getBalanceFrom(accountId, wallet.paymentMethodId, any()) } returns Balance(10L)

            every { subscription.paymentStatus } returns SubscriptionPaymentStatus.OVERDUE
            accountService.save(account.copy(paymentStatus = AccountPaymentStatus.Overdue))

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPFSteps()
                steps shouldNotHaveStep CloseAccountStepTypeMarkAsFraud

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()
                }

                // carteira onde a assinatura é cobrada e com saldo, deve capturar o saldo restante
                with(steps.closeFounderWalletStep(wallet.id)) {
                    this.steps shouldHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountLater()
                }

                // carteira onde a assinatura não é cobrada e não tem saldo, não deve tentar capturar saldo
                with(steps.closeFounderWalletStep(secondaryWallet.id)) {
                    this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountNow()
                }
            }
        }

        @Test
        fun `quando usuario tem movimentacoes feitas no dia`() {
            every { statementService.findAllStatementsByDate(secondaryWallet.id, any(), any()) } returns mockk<UserStatement> {
                every {
                    statementItems
                } returns listOf(
                    mockk<StatementItem>(relaxed = true) {
                        every { flow } returns BankStatementItemFlow.DEBIT
                    },
                )
            }.right()

            every { subscription.paymentStatus } returns SubscriptionPaymentStatus.OVERDUE
            accountService.save(account.copy(paymentStatus = AccountPaymentStatus.Overdue))

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPFSteps()
                steps shouldNotHaveStep CloseAccountStepTypeMarkAsFraud

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()
                }

                // carteira sem movimentacao no dia
                with(steps.closeFounderWalletStep(wallet.id)) {
                    this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountNow()
                }

                // carteira com movimentacao no dia
                with(steps.closeFounderWalletStep(secondaryWallet.id)) {
                    this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountLater()
                }
            }
        }
    }

    @Nested
    @DisplayName("deve poder encerrar conta PJ")
    inner class CanClosePJAccountTest {

        @BeforeEach
        fun setup() {
            setupAccount(documentPJ)
        }

        @ParameterizedTest
        @EnumSource(AccountClosureReason::class, mode = EnumSource.Mode.EXCLUDE, names = ["FRAUD"])
        fun `quando usuario nao possui saldo nem transacao processando e nao eh encerramento por fraude confirmada`(reason: AccountClosureReason) {
            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.WithReason(reason = reason, description = "", at = getZonedDateTime()))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPJSteps()
                steps shouldNotHaveStep CloseAccountStepTypeMarkAsFraud

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    closeWalletStep.shouldCloseExternalAccountNow()
                }
            }
        }

        @Test
        fun `mas nao deve encerrar a conta no arbi se a carteira for virtual`() {
            loadBalancePaymentMethod(
                accountRepository = accountRepository,
                bankNo = 213L,
                bankAccountNo = walletAccountNumber.number,
                bankAccountDv = walletAccountNumber.dv,
                document = documentPF,
                accountId = accountId.value,
                position = 1,
                paymentMethodId = wallet.paymentMethodId.value,
                mode = BankAccountMode.VIRTUAL,
            )
            loadBalancePaymentMethod(
                accountRepository = accountRepository,
                bankNo = 213L,
                bankAccountNo = secondaryWalletAccountNumber.number,
                bankAccountDv = secondaryWalletAccountNumber.dv,
                document = documentPF,
                accountId = accountId.value,
                position = 2,
                paymentMethodId = secondaryWallet.paymentMethodId.value,
                mode = BankAccountMode.VIRTUAL,
            )

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPJSteps()

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeCloseExternalAccountNow
                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeCloseExternalAccountLater
                }
            }
        }

        @ParameterizedTest
        @EnumSource(AccountClosureReason::class, mode = EnumSource.Mode.INCLUDE, names = ["FRAUD"])
        fun `quando usuario nao possui saldo nem transacao processando e eh encerramento por fraude confirmada`(reason: AccountClosureReason) {
            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.WithReason(reason = reason, description = "", at = getZonedDateTime()))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPJSteps()

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    closeWalletStep.shouldCloseExternalAccountNow()
                }
            }
        }

        @ParameterizedTest
        @EnumSource(AccountClosureReason::class, mode = EnumSource.Mode.INCLUDE, names = ["FRAUD", "POSSIBLE_FRAUD"])
        fun `quando usuario possui saldo e o motivo de encerramento eh fraude`(reason: AccountClosureReason) {
            every { balanceService.getBalanceFrom(accountId, secondaryWallet.paymentMethodId, any()) } returns Balance(10L)

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.WithReason(reason = reason, description = "", at = getZonedDateTime()))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPJSteps()

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()
                }

                // carteira sem saldo, fecha agora
                with(steps.closeFounderWalletStep(wallet.id)) {
                    this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountNow()
                }

                // carteira com saldo, captura e fecha depois
                with(steps.closeFounderWalletStep(secondaryWallet.id)) {
                    this.steps shouldHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountLater()
                }
            }
        }

        @Test
        fun `quando usuario tem saldo mas nao fez nenhum deposito`() {
            every { balanceService.getBalanceFrom(any(), any(), any()) } returns Balance(10L)

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.WithoutReason(description = "", at = getZonedDateTime()))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPJSteps()

                // tem saldo nas duas carteiras, captura e encerra depois
                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()

                    closeWalletStep.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    closeWalletStep.steps shouldHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    closeWalletStep.shouldCloseExternalAccountLater()
                }
            }
        }

        @Test
        fun `quando usuario tem saldo e esta inadimplente`() {
            every { balanceService.getBalanceFrom(accountId, wallet.paymentMethodId, any()) } returns Balance(10L)

            every { subscription.paymentStatus } returns SubscriptionPaymentStatus.OVERDUE
            accountService.save(account.copy(paymentStatus = AccountPaymentStatus.Overdue, document = documentPJ))

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPJSteps()

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()
                }

                // carteira onde a assinatura é cobrada e com saldo, deve capturar o saldo restante
                with(steps.closeFounderWalletStep(wallet.id)) {
                    this.steps shouldHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountLater()
                }

                // carteira onde a assinatura não é cobrada e não tem saldo, não deve tentar capturar saldo
                with(steps.closeFounderWalletStep(secondaryWallet.id)) {
                    this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountNow()
                }
            }
        }

        @Test
        fun `quando usuario tem movimentacoes feitas no dia`() {
            every { statementService.findAllStatementsByDate(secondaryWallet.id, any(), any()) } returns mockk<UserStatement> {
                every {
                    statementItems
                } returns listOf(
                    mockk<StatementItem>(relaxed = true) {
                        every { flow } returns BankStatementItemFlow.DEBIT
                    },
                )
            }.right()

            every { subscription.paymentStatus } returns SubscriptionPaymentStatus.OVERDUE
            accountService.save(account.copy(paymentStatus = AccountPaymentStatus.Overdue, document = documentPJ))

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isRight() shouldBe true
            result.map { steps ->
                steps.shouldHaveAllDefaultPJSteps()

                wallets.forEach { wallet ->
                    val closeWalletStep = steps.closeFounderWalletStep(wallet.id)

                    closeWalletStep.shouldHaveAllDefaultSteps()
                }

                // carteira sem movimentacao no dia
                with(steps.closeFounderWalletStep(wallet.id)) {
                    this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountNow()
                }

                // carteira com movimentacao no dia
                with(steps.closeFounderWalletStep(secondaryWallet.id)) {
                    this.steps shouldNotHaveStep CloseWalletStepTypeTransferAllFundsFromAccount
                    this.steps shouldNotHaveStep CloseWalletStepTypeRefundOnboardingTestPix

                    this.shouldCloseExternalAccountLater()
                }
            }
        }
    }

    @Nested
    @DisplayName("não deve poder encerrar conta")
    inner class CannotCloseAccountTest {
        @ParameterizedTest
        @EnumSource(value = AccountStatus::class, mode = EnumSource.Mode.EXCLUDE, names = ["ACTIVE", "PENDING_CLOSE", "BLOCKED"])
        fun `quando usuario esta com status diferente de ACTIVE ou PENDING_CLOSE ou BLOCKED`(status: AccountStatus) {
            accountRepository.save(account.copy(status = status))

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe CloseAccountError.InvalidAccountStatus(status)
            }
        }

        @Test
        fun `quando usuario tem transacao processando`() {
            transactionRepository.save(
                creditCardCashInTransaction.copy(
                    payer = creditCardCashInTransaction.payer.copy(accountId = accountId),
                    status = TransactionStatus.PROCESSING,
                    walletId = wallet.id,
                ),
            )
            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe CloseAccountError.CloseFounderWalletError(CloseWalletError.TransactionStillProcessing(wallet.id))
            }
        }

        @Test
        fun `quando usuario tem saldo em conta e nao eh do triPix e o motivo de encerramento nao eh fraude nem inadimplencia`() {
            every { balanceService.getBalanceFrom(any(), any(), any()) } returns Balance(10L)
            every { statementService.findAllCredits(any(), any()) } returns listOf(bankStatementItem).right()
            every { externalBankAccountService.findLastUsed(any()) } returns null
            every { onboardingTestPixService.findPixKey(any()) } returns Result.success(null)

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isLeft() shouldBe true
            result.mapLeft {
                it.shouldBeTypeOf<CloseAccountError.CloseFounderWalletError>()
                it.error.shouldBeTypeOf<CloseWalletError.BalanceDifferentThanZero>()
            }
        }

        @Test
        fun `quando usuario tem saldo e esta inadimplente mas o saldo nao esta na carteira da assinatura`() {
            every { balanceService.getBalanceFrom(accountId, secondaryWallet.paymentMethodId, any()) } returns Balance(10L)
            every { statementService.findAllCredits(secondaryWallet.id, any()) } returns listOf(bankStatementItem).right()

            every { subscription.paymentStatus } returns SubscriptionPaymentStatus.OVERDUE

            every { externalBankAccountService.findLastUsed(any()) } returns null
            every { onboardingTestPixService.findPixKey(any()) } returns Result.success(null)

            accountService.save(account.copy(paymentStatus = AccountPaymentStatus.Overdue))

            val result = closeAccountService.prepareClose(accountId, AccountClosureDetails.create(reason = null, description = ""))

            result.isLeft() shouldBe true
            result.mapLeft {
                it shouldBe CloseAccountError.CloseFounderWalletError(CloseWalletError.BalanceDifferentThanZero(secondaryWallet.id, 10L, externalBankAccount = null, pixKey = null))
            }
        }
    }
/*
    @ParameterizedTest
    @NullSource
    @EnumSource(value = AccountClosureReason::class)
    fun `deve conseguir serializar a mensagem`(reason: AccountClosureReason?) {
        val message = CloseAccountMessage(
            accountId = AccountId(value = "accountId"),
            steps = CloseAccountStepType.entries.map { it.toSimpleStep() } + listOf(
                CloseFounderWalletCloseAccountStep(
                    walletId = WalletId("walletId"),
                    steps = CloseWalletStepTypeentries.map { it.toStep() },
                ),
                DeactivateAccountRegisterStep(closureDetails = AccountClosureDetails.create(reason, "description")),
            ),
        )

        val json = getObjectMapper().writeValueAsString(message)

        val unmarshalled = parseObjectFrom<CloseAccountMessage>(json)

        unmarshalled shouldBe message
    }*/

    private fun loadPrimaryWallet(otherMembers: List<Member> = listOf()) {
        val founder =
            Member(
                accountId = accountId,
                document = documentPF,
                name = "Fulano de Tal",
                emailAddress = EmailAddress(EMAIL),
                type = MemberType.FOUNDER,
                status = MemberStatus.ACTIVE,
                permissions = MemberPermissions.of(MemberType.FOUNDER),
                created = getZonedDateTime(),
                updated = getZonedDateTime(),
            )

        val wallet =
            Wallet(
                id = WalletId(WALLET_ID),
                name = founder.simpleName(),
                members = listOf(founder) + otherMembers,
                maxOpenInvitations = 10,
                status = WalletStatus.ACTIVE,
                type = WalletType.PRIMARY,
                paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
            )

        walletRepository.save(wallet)
    }

    private fun loadSecondaryWallet() {
        val member =
            Member(
                accountId = accountId,
                document = documentPF,
                name = "Fulano de Tal",
                emailAddress = EmailAddress(EMAIL),
                type = MemberType.COLLABORATOR,
                status = MemberStatus.ACTIVE,
                permissions = MemberPermissions.of(MemberType.COLLABORATOR),
                created = getZonedDateTime(),
                updated = getZonedDateTime(),
            )

        val anotherFounder =
            Member(
                accountId = AccountId(ACCOUNT_ID_2),
                document = DOCUMENT_2,
                name = "Ciclano de Tal",
                emailAddress = EmailAddress(EMAIL_2),
                type = MemberType.FOUNDER,
                status = MemberStatus.ACTIVE,
                permissions = MemberPermissions.of(MemberType.FOUNDER),
                created = getZonedDateTime(),
                updated = getZonedDateTime(),
            )

        val sharedWallet =
            Wallet(
                id = WalletId(WALLET_ID_2),
                name = anotherFounder.simpleName(),
                members = listOf(anotherFounder, member),
                maxOpenInvitations = 10,
                status = WalletStatus.ACTIVE,
                type = WalletType.PRIMARY,
                paymentMethodId = AccountPaymentMethodId(PAYMENT_METHOD_ID_2),
            )

        walletRepository.save(sharedWallet)
    }

    private fun List<CloseAccountStep>.closeFounderWalletStep(walletId: WalletId): CloseFounderWalletCloseAccountStep {
        val steps = this.filterIsInstance<CloseFounderWalletCloseAccountStep>().filter {
            it.walletId == walletId
        }
        return when (steps.size) {
            0 -> throw failure("passo para encerramento da carteira ${walletId.value} não encontrado")
            1 -> steps.single()
            else -> throw failure("mais de um para encerramento da carteira ${walletId.value} encontrado")
        }
    }

    private fun List<CloseAccountStep>.shouldHaveAllDefaultPFSteps() {
        this shouldHaveStep CloseAccountStepTypeUnsubscribe
        this shouldHaveStep CloseAccountStepTypeRemoveFromDDA
        this shouldHaveStep CloseAccountStepTypeSignOutUser
        this shouldHaveStep CloseAccountStepTypeDisableNotification
        this shouldHaveStep CloseAccountStepTypeRemoveFromUserPool
        this shouldHaveStep CloseAccountStepTypeRemoveLogin
        this shouldHaveStep CloseAccountStepTypeUpdateStatusClosed
        this shouldHaveStep CloseAccountStepTypeRemoveFromWallets
        this shouldHaveStep CloseAccountStepTypeCloseCreditCards
        this shouldHaveStep CloseAccountStepTypeCloseCrmContact
        this shouldHaveStep CloseAccountStepTypeDeactivateAccountRegister
    }

    private fun List<CloseAccountStep>.shouldHaveAllDefaultPJSteps() {
        this shouldHaveStep CloseAccountStepTypeUnsubscribe
        this shouldHaveStep CloseAccountStepTypeRemoveFromDDA
        this shouldNotHaveStep CloseAccountStepTypeSignOutUser
        this shouldHaveStep CloseAccountStepTypeDisableNotification
        this shouldNotHaveStep CloseAccountStepTypeRemoveFromUserPool
        this shouldNotHaveStep CloseAccountStepTypeRemoveLogin
        this shouldHaveStep CloseAccountStepTypeUpdateStatusClosed
        this shouldHaveStep CloseAccountStepTypeRemoveFromWallets
        this shouldHaveStep CloseAccountStepTypeCloseCreditCards
        this shouldHaveStep CloseAccountStepTypeCloseCrmContact
        this shouldHaveStep CloseAccountStepTypeDeactivateAccountRegister
        this shouldNotHaveStep CloseAccountStepTypeMarkAsFraud
    }

    private infix fun List<CloseAccountStep>.shouldHaveAllDefaultPFStepsWithStatus(status: CloseAccountStepStatus) {
        this.step(CloseAccountStepTypeUnsubscribe) shouldHaveStatus status
        this.step(CloseAccountStepTypeRemoveFromDDA) shouldHaveStatus status
        this.step(CloseAccountStepTypeSignOutUser) shouldHaveStatus status
        this.step(CloseAccountStepTypeDisableNotification) shouldHaveStatus status
        this.step(CloseAccountStepTypeRemoveFromUserPool) shouldHaveStatus status
        this.step(CloseAccountStepTypeRemoveLogin) shouldHaveStatus status
        this.step(CloseAccountStepTypeUpdateStatusClosed) shouldHaveStatus status
        this.step(CloseAccountStepTypeRemoveFromWallets) shouldHaveStatus status
        this.step(CloseAccountStepTypeCloseCreditCards) shouldHaveStatus status
        this.step(CloseAccountStepTypeCloseCrmContact) shouldHaveStatus status
        this.step(CloseAccountStepTypeDeactivateAccountRegister) shouldHaveStatus status
    }

    private fun CloseFounderWalletCloseAccountStep.shouldHaveAllDefaultSteps() {
        steps shouldHaveStep CloseWalletStepTypeIgnoreRecurrences
        steps shouldHaveStep CloseWalletStepTypeCancelAllSchedules
        steps shouldHaveStep CloseWalletStepTypeDeleteWalletPixKey
        steps shouldHaveStep CloseWalletStepTypeDeleteOldPixKey
        steps shouldHaveStep CloseWalletStepTypeDeleteCustomPixKeys
        steps shouldHaveStep CloseWalletStepTypeDisconnectUtilityAccounts
        steps shouldHaveStep CloseWalletStepTypeCloseAndRemoveMembers
    }

    private infix fun CloseFounderWalletCloseAccountStep.shouldHaveAllDefaultStepsWithStatus(status: CloseWalletStepStatus) {
        steps.step(CloseWalletStepTypeIgnoreRecurrences) shouldHaveStatus status
        steps.step(CloseWalletStepTypeCancelAllSchedules) shouldHaveStatus status
        steps.step(CloseWalletStepTypeDeleteWalletPixKey) shouldHaveStatus status
        steps.step(CloseWalletStepTypeDeleteOldPixKey) shouldHaveStatus status
        steps.step(CloseWalletStepTypeDeleteCustomPixKeys) shouldHaveStatus status
        steps.step(CloseWalletStepTypeDisconnectUtilityAccounts) shouldHaveStatus status
        steps.step(CloseWalletStepTypeCloseAndRemoveMembers) shouldHaveStatus status
    }

    private infix fun CloseFounderWalletCloseAccountStep.shouldHaveAllDefaultStepsExceptDeleteOldPixWithStatus(status: CloseWalletStepStatus) {
        steps.step(CloseWalletStepTypeIgnoreRecurrences) shouldHaveStatus status
        steps.step(CloseWalletStepTypeCancelAllSchedules) shouldHaveStatus status
        steps.step(CloseWalletStepTypeDeleteOldPixKey) shouldHaveStatus CloseWalletStepStatus.Warning
        steps.step(CloseWalletStepTypeDeleteCustomPixKeys) shouldHaveStatus status
        steps.step(CloseWalletStepTypeDisconnectUtilityAccounts) shouldHaveStatus status
        steps.step(CloseWalletStepTypeCloseAndRemoveMembers) shouldHaveStatus status
    }

    private fun CloseFounderWalletCloseAccountStep.shouldCloseExternalAccountNow() {
        steps shouldHaveStep CloseWalletStepTypeCloseExternalAccountNow
        steps shouldNotHaveStep CloseWalletStepTypeCloseExternalAccountLater
    }

    private fun CloseFounderWalletCloseAccountStep.shouldCloseExternalAccountLater() {
        steps shouldHaveStep CloseWalletStepTypeCloseExternalAccountLater
        steps shouldNotHaveStep CloseWalletStepTypeCloseExternalAccountNow
    }

    private infix fun List<CloseAccountStep>.shouldHaveStep(stepType: CloseAccountStepType) {
        when (this.filter { it.type == stepType }.size) {
            0 -> throw failure("passo $stepType não encontrado")
            1 -> {}
            else -> throw failure("mais de um passo $stepType encontrado")
        }
    }

    private infix fun List<CloseWalletStep>.shouldHaveStep(stepType: CloseWalletStepType) {
        when (this.filter { it.type == stepType }.size) {
            0 -> throw failure("passo $stepType não encontrado")
            1 -> {}
            else -> throw failure("mais de um passo $stepType encontrado")
        }
    }

    private infix fun List<CloseWalletStep>.shouldNotHaveStep(stepType: CloseWalletStepType) {
        when (this.filter { it.type == stepType }.size) {
            0 -> {}
            1 -> throw failure("passo $stepType encontrado")
            else -> throw failure("mais de um passo $stepType encontrado")
        }
    }

    private infix fun List<CloseAccountStep>.shouldNotHaveStep(stepType: CloseAccountStepType) {
        when (this.filter { it.type == stepType }.size) {
            0 -> {}
            1 -> throw failure("passo $stepType encontrado")
            else -> throw failure("mais de um passo $stepType encontrado")
        }
    }

    companion object {
        @JvmStatic
        fun unsubscribeErrors() = listOf(
            Arguments.of(UnsubscribeError.AccountNotFound, CloseAccountStepStatus.Warning),
            Arguments.of(UnsubscribeError.BillProcessing, CloseAccountStepStatus.Error),
            Arguments.of(UnsubscribeError.ServerError(""), CloseAccountStepStatus.Error),
        )

        @JvmStatic
        fun closureDetails(): Stream<AccountClosureDetails> {
            return Stream.of(
                AccountClosureDetails.create(
                    reason = AccountClosureReason.FRAUD,
                    description = "Conta encerrada por fraude",
                    at = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone),
                ),
                AccountClosureDetails.create(
                    reason = AccountClosureReason.POSSIBLE_FRAUD,
                    description = "Conta encerrada por possível fraude",
                    at = ZonedDateTime.of(2023, 1, 1, 0, 0, 0, 0, brazilTimeZone),
                ),
            )
        }
    }
}