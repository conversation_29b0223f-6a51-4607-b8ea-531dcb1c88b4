package ai.friday.billpayment.app.account

import ai.friday.billpayment.accountRegisterCompleted
import ai.friday.billpayment.app.dda.DDAService
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ModattaProvider
import ai.friday.billpayment.app.modatta.register.SimpleSignUpApprover
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.usage.WalletLimitsService
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.balance
import ai.friday.billpayment.integration.ACCOUNT_ID
import ai.friday.billpayment.integration.AccountFixture
import arrow.core.right
import io.mockk.every
import io.mockk.just
import io.mockk.mockk
import io.mockk.runs
import io.mockk.verify
import org.junit.jupiter.api.Test

class RegisterServiceModattaTest {
    val accountId = AccountId(ACCOUNT_ID)

    val account = AccountFixture.createAccount().copy(accountId = accountId)

    val partialAccount = PartialAccount(
        id = accountId,
        name = account.name,
        emailAddress = account.emailAddress,
        status = AccountStatus.UNDER_EXTERNAL_REVIEW,
        role = Role.GUEST,
        statusUpdated = null,
        groups = listOf(),
        registrationType = RegistrationType.FULL,
        subscriptionType = SubscriptionType.PIX,
    )

    val accountRegisterData = accountRegisterCompleted.copy(
        externalId = ExternalId(
            "123",
            AccountProviderName.MODATTA,
        ),
    )

    private val accountRegisterService = mockk<AccountRegisterService>()

    private val accountService = mockk<AccountService> {
        every { findPartialAccountById(any()) } returns partialAccount andThen partialAccount.copy(status = AccountStatus.APPROVED)
        every {
            addGroupsToAccount(any<AccountId>(), any())
        } returns account

        every {
            findPhysicalBankAccountByAccountId(any())
        } returns listOf(balance)

        every {
            updatePartialAccountStatus(any(), any())
        } just runs

        every {
            findAccountById(any())
        } returns account

        every {
            findAccountByIdOrNull(any())
        } returns account

        every {
            deletePartialAccount(any())
        } just runs

        every {
            save(any())
        } just runs

        every {
            enableCreditCardUsage(any(), any(), false)
        } returns account.right()
    }

    private val modattaProvider = mockk<ModattaProvider>(relaxed = true)

    private val accountRegisterRepository = mockk<AccountRegisterRepository> {
        every { findByDocument(any()) } returns accountRegisterData

        every {
            findByAccountId(any())
        } returns accountRegisterData
    }

    private val ddaService = mockk<DDAService> {
        every {
            register(any(), any())
        } returns DDAStatus.ACTIVE
    }

    private val crmService: CrmService = mockk {
        every {
            contactExists(any())
        } returns true

        every {
            upsertContact(any<Account>())
        } returns mockk()
    }

    private val walletService: WalletService = mockk(relaxed = true)
    private val walletLimitsService: WalletLimitsService = mockk(relaxed = true)

    private val registerServiceWithMocks =
        RegisterService(
            tokenService = mockk(),
            accountRegisterRepository = accountRegisterRepository,
            accountService = accountService,
            accountRegisterService = accountRegisterService,
            documentOCRParser = mockk(),
            userFilesConfiguration = mockk(),
            userAddressConfiguration = mockk(relaxed = true),
            agreementFilesService = mockk(),
            notificationSenderService = mockk(),
            ddaService = ddaService,
            notificationAdapter = mockk(),
            userPoolAdapter = mockk(relaxed = true),
            kycService = mockk(),
            externalAccountRegister = mockk(),
            ecmProvider = mockk(),
            walletService = walletService,
            walletLimitsService = walletLimitsService,
            crmService = crmService,
            registerInstrumentationService = mockk(relaxed = true),
            pdfFileParser = mockk(),
            pendingInternalApproveConfiguration = mockk(),
            pendingInternalReviewConfiguration = mockk(),
            pendingActivationConfiguration = mockk(),
            accountStatusLockProvider = mockk(relaxed = true),
            bigDataService = mockk(),
            userJourneyService = mockk(relaxUnitFun = true),
            closeAccountService = mockk(relaxed = true),
            systemActivityService = mockk(relaxed = true),
            livenessService = mockk(),
            modattaProvider = modattaProvider,
            eventPublisher = mockk(relaxed = true),
            fraudList = mockk(relaxed = true),
            pendingUpgradeInternalApproveConfiguration = mockk(relaxed = true),
            messagePublisher = mockk(relaxed = true),
            adService = mockk(relaxed = true),
            chatBotMessagePublisher = mockk(relaxed = true),
            walletBillCategoryService = mockk(relaxed = true),
            documentScanService = mockk(relaxed = true),
            activityService = mockk(relaxed = true),
            loginRepository = mockk(relaxed = true),
        ).apply {
            registerPixKeyQueueName = ""
            pixKeyEmailDomain = ""
        }

    @Test
    fun `deve notificar o provedor externo quando o status da conta mudar para APPROVED`() {
        registerServiceWithMocks.updateAccountStatus("document", ExternalRegisterStatus.APPROVED)

        verify(exactly = 1) {
            modattaProvider.updateStatus(
                any(),
                any(),
                AccountStatus.ACTIVE,
                null,
                SimpleSignUpApprover.EXTERNAL_ACCOUNT_REGISTER,
            )
        }
    }

    @Test
    fun `não deve notificar o provedor externo quando a conta for rejeitada`() {
        registerServiceWithMocks.updateAccountStatus("document", ExternalRegisterStatus.REJECTED)

        verify(exactly = 0) {
            modattaProvider.updateStatus(
                any(),
                any(),
                any(),
                any(),
            )
        }
    }

    @Test
    fun `deve notificar o provedor externo quando uma conta for ativada`() {
        registerServiceWithMocks.updateAccountStatus("document", ExternalRegisterStatus.APPROVED)

        verify(exactly = 1) {
            modattaProvider.updateStatus(
                any(),
                any(),
                AccountStatus.ACTIVE,
                null,
                SimpleSignUpApprover.EXTERNAL_ACCOUNT_REGISTER,
            )
        }
    }
}