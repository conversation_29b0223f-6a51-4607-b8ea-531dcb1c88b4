package ai.friday.billpayment.app.integrations

import ai.friday.billpayment.Err
import ai.friday.billpayment.PrintableSealedClass
import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.CompiledHtml
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.IntegrationError
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.ZipCode
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountConfiguration
import ai.friday.billpayment.app.account.AccountEvent
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.Address
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBinDetails
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardChallenge
import ai.friday.billpayment.app.account.CreditCardChallengeStatus
import ai.friday.billpayment.app.account.CreditCardToken
import ai.friday.billpayment.app.account.DeleteCreditCardResult
import ai.friday.billpayment.app.account.DeviceAdIds
import ai.friday.billpayment.app.account.DocumentInfo
import ai.friday.billpayment.app.account.DocumentQuality
import ai.friday.billpayment.app.account.ExternalAccount
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.MonthlyIncome
import ai.friday.billpayment.app.account.NotifyUpgradeStatusRequest
import ai.friday.billpayment.app.account.NotifyUpgradeStatusResponse
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PaymentMethod
import ai.friday.billpayment.app.account.PoliticallyExposed
import ai.friday.billpayment.app.account.RegisterNaturalPersonResponse
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.SendAccountRegisterDocumentsRequest
import ai.friday.billpayment.app.account.SendDocumentsResponse
import ai.friday.billpayment.app.account.SendSimpleSignUpDocumentsRequest
import ai.friday.billpayment.app.account.SendUpgradeDocumentsRequest
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.SystemActivity
import ai.friday.billpayment.app.account.SystemActivityKey
import ai.friday.billpayment.app.account.SystemActivityKeyType
import ai.friday.billpayment.app.account.SystemActivityType
import ai.friday.billpayment.app.account.TokenChannel
import ai.friday.billpayment.app.account.TokenKey
import ai.friday.billpayment.app.account.UpdateCreditCardMethodRiskResult
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.UserPoolEnableUserError
import ai.friday.billpayment.app.account.UserPoolRemoveUserError
import ai.friday.billpayment.app.account.UserPoolSetMfaPreferenceError
import ai.friday.billpayment.app.balance.Balance
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.Bank
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankOperationExecuted
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.banking.ExternalBankAccount
import ai.friday.billpayment.app.banking.FinancialInstitution
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.InternalBankStatementItem
import ai.friday.billpayment.app.banking.OmnibusBankStatement
import ai.friday.billpayment.app.banking.OmnibusBankStatementItem
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillComingDue
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.CreateBillResult
import ai.friday.billpayment.app.bill.CreateBoletoRequest
import ai.friday.billpayment.app.bill.EmailBill
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.InvalidInstallmentCalculation
import ai.friday.billpayment.app.bill.InvalidSchedulePaymentFee
import ai.friday.billpayment.app.bill.MailboxAddBillError
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.RefundedBill
import ai.friday.billpayment.app.bill.TrackableBill
import ai.friday.billpayment.app.campaigns.RedeemCodeResult
import ai.friday.billpayment.app.conciliation.BoletoOccurrence
import ai.friday.billpayment.app.contact.BankAccountId
import ai.friday.billpayment.app.contact.Contact
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.contact.LastUsed
import ai.friday.billpayment.app.contact.SavedBankAccount
import ai.friday.billpayment.app.contact.SavedPixKey
import ai.friday.billpayment.app.dda.BatchAddOrder
import ai.friday.billpayment.app.dda.BatchMigrationOrder
import ai.friday.billpayment.app.dda.DDABill
import ai.friday.billpayment.app.dda.DDAItem
import ai.friday.billpayment.app.dda.DDARegister
import ai.friday.billpayment.app.dda.DDARegisterMessage
import ai.friday.billpayment.app.dda.DDAStatus
import ai.friday.billpayment.app.fee.FeePaymentCreditCardInstallment
import ai.friday.billpayment.app.fee.FeesWithSummary
import ai.friday.billpayment.app.fee.ListFeesCommand
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.job.Job
import ai.friday.billpayment.app.limit.Limit
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.login.Login
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.message.QueueMessage
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.app.modatta.ModattaDepositCallback
import ai.friday.billpayment.app.modatta.register.SimpleSignUpApprover
import ai.friday.billpayment.app.msisdnauth.MsisdnAuth
import ai.friday.billpayment.app.msisdnauth.MsisdnAuthId
import ai.friday.billpayment.app.notification.BillPaymentNotification
import ai.friday.billpayment.app.notification.BillsComingDueLists
import ai.friday.billpayment.app.notification.ByteArrayWithNameAndType
import ai.friday.billpayment.app.notification.NotificationMedia
import ai.friday.billpayment.app.notification.NotificationType
import ai.friday.billpayment.app.notification.NotifyRequest
import ai.friday.billpayment.app.notification.WhatsappNotification
import ai.friday.billpayment.app.onboarding.OnboardingTestPix
import ai.friday.billpayment.app.onboarding.OnboardingTestPixStatus
import ai.friday.billpayment.app.onepixpay.EndToEndOnePixPayId
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.OnePixPayId
import ai.friday.billpayment.app.onepixpay.OnePixPayIdSource
import ai.friday.billpayment.app.onepixpay.ProcessScheduleError
import ai.friday.billpayment.app.onepixpay.QrCodeTransactionId
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.BillPaymentResponse
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BoletoReceiptData
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.DirectTEDResult
import ai.friday.billpayment.app.payment.DirectTEDUndoneResult
import ai.friday.billpayment.app.payment.InvoiceReceiptData
import ai.friday.billpayment.app.payment.PixPaymentResult
import ai.friday.billpayment.app.payment.PixReceiptData
import ai.friday.billpayment.app.payment.ReceiptData
import ai.friday.billpayment.app.payment.ReceiptFiles
import ai.friday.billpayment.app.payment.ReceiptFilesData
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.payment.ScheduledBill
import ai.friday.billpayment.app.payment.SettlementFundsTransfer
import ai.friday.billpayment.app.payment.SettlementStatus
import ai.friday.billpayment.app.payment.TEDResult
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.checkout.SettlementClientRequestTO
import ai.friday.billpayment.app.pix.CreatePixKeyError
import ai.friday.billpayment.app.pix.DeletePixKeyError
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixStatement
import ai.friday.billpayment.app.recurrence.BaseRecurrence
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.referrer.ReferrerRegister
import ai.friday.billpayment.app.register.RiskAnalysisFailedReason
import ai.friday.billpayment.app.register.kyc.FaceMatchResult
import ai.friday.billpayment.app.register.kyc.KycDossier
import ai.friday.billpayment.app.statement.StatementItem
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionStatus
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.usage.TransactionAmount
import ai.friday.billpayment.app.utilityaccount.Utility
import ai.friday.billpayment.app.utilityaccount.UtilityAccount
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.utilityaccount.UtilityAccountId
import ai.friday.billpayment.app.utilityaccount.UtilityAccountUpdateInfo
import ai.friday.billpayment.app.wallet.DailyPaymentLimit
import ai.friday.billpayment.app.wallet.DailyPaymentLimitType
import ai.friday.billpayment.app.wallet.Invite
import ai.friday.billpayment.app.wallet.InviteStatus
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletEvent
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletPaymentLimits
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import arrow.core.Either
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.EachProperty
import io.micronaut.http.MediaType
import io.reactivex.Flowable
import io.via1.communicationcentre.app.email.IncomingEmail
import io.via1.communicationcentre.app.parser.ParsedBill
import io.via1.communicationcentre.app.receipt.Receipt
import io.via1.communicationcentre.app.receipt.SQSEvent
import java.io.InputStream
import java.time.Duration
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.Month
import java.time.Year
import java.time.YearMonth
import java.time.ZonedDateTime
import java.util.UUID
import net.javacrumbs.shedlock.core.SimpleLock
import reactor.core.publisher.Flux
import reactor.core.publisher.Mono

interface BankDataService {
    fun getPixParticipants(): List<FinancialInstitution>

    fun getHolidays(): List<LocalDate>

    fun getBankCodes(): List<Bank>

    fun getIncomeReportPDF(document: Document, year: Year): ByteArray
}

interface PixPaymentService {
    @Deprecated("Utilize o transfer")
    fun initPayment(
        bill: Bill,
        paymentMethod: AccountPaymentMethod,
        bankOperationId: BankOperationId,
    ): PixPaymentResult

    fun initPayment(
        command: PixCommand,
        bankOperationId: BankOperationId,
        deviceId: DeviceId?,
    ): PixPaymentResult

    fun checkPaymentStatus(
        bankAccount: InternalBankAccount?,
        bankOperationId: BankOperationId,
    ): PixPaymentResult

    fun getStatement(accountNumber: AccountNumber): List<PixStatement>

    fun checkEndToEndStatus(endToEndCode: String): String

    fun transfer(command: NewPixCommand, deviceId: DeviceId?): PixPaymentResult
}

interface PixKeyManagement {
    fun registerKey(
        accountNo: AccountNumber,
        key: PixKey,
        document: String,
        name: String,
        deviceId: DeviceId?,
    ): PixKey

    fun deleteKey(
        key: String,
        document: Document,
        deviceId: DeviceId?,
    ): Either<PixKeyError, Unit>

    fun findKeyDetails(
        key: PixKey,
        document: String,
    ): Either<PixKeyError, PixKeyDetailsResult>

    fun findKeyDetailsCacheable(
        key: PixKey,
        document: String,
    ): PixKeyDetailsResult

    fun findInternalKeys(document: String)

    fun startClaim(
        key: String,
        keyType: String,
        type: String,
    ): String

    fun confirmClaim(
        claimId: String,
        reason: String,
    )

    fun completeClaim(claimId: String)

    fun cancelClaim(
        claimId: String,
        reason: String,
    )

    fun listClaims()
}

interface TEDService : CheckableSettlementStatus {
    fun transfer(
        originAccountNo: String,
        recipient: Recipient,
        totalAmount: Long,
    ): DirectTEDResult

    fun transfer(
        recipient: Recipient,
        totalAmount: Long,
        externalTerminal: String,
        externalNsu: Long,
    ): TEDResult

    fun checkTEDUndone(
        amount: Long,
        startDate: LocalDate,
        endDate: LocalDate,
        originAccountNo: String,
    ): List<DirectTEDUndoneResult>
}

interface CheckableSettlementStatus {
    fun checkSettlementStatus(transaction: Transaction): SettlementStatus
}

interface BankAccountService {
    fun getBalance(accountNo: String): Long

    fun getStatement(
        accountNo: String,
        document: String,
        initialDate: ZonedDateTime,
        endDate: ZonedDateTime,
    ): BankStatement

    fun transfer(

        originAccountNo: String,
        targetAccountNo: String,
        amount: Long,
        operationId: BankOperationId = BankOperationId.build(),
    ): BankTransfer

    fun checkTransferStatus(
        idRequisicaoParceiroOriginal: String,
        startDate: LocalDate,
        endDate: LocalDate,
        originAccountNo: String,
    ): Boolean
}

interface UpdateablePaymentStatus {
    fun updatePaymentStatus(transaction: Transaction)
}

interface FundProvider : UpdateablePaymentStatus {
    fun captureFunds(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
        paymentMethod: PaymentMethod,
        amount: Long,
    ): BankTransfer

    fun undoCaptureFunds(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
        paymentMethod: PaymentMethod,
        operationId: BankOperationId,
        amount: Long,
        ongoingRefundOperationId: BankOperationId? = null,
    ): BankTransfer

    fun checkCaptureFunds(
        accountId: AccountId,
        paymentMethod: PaymentMethod,
        operationId: BankOperationId,
        operationDate: LocalDate,
    ): Boolean

    @Deprecated("Apenas para não replicar códigos nos checkouts legados, que utilizam apenas saldo")
    override fun updatePaymentStatus(transaction: Transaction) {
        val balanceAuthorization = transaction.paymentData.toSingle().payment as BalanceAuthorization
        val captured =
            checkCaptureFunds(
                transaction.paymentData
                    .toSingle()
                    .accountPaymentMethod.accountId,
                transaction.paymentData
                    .toSingle()
                    .accountPaymentMethod.method,
                balanceAuthorization.operationId,
                transaction.created.toLocalDate(),
            )
        transaction.paymentData.toSingle().payment =
            balanceAuthorization.apply {
                status = if (captured) BankOperationStatus.SUCCESS else BankOperationStatus.ERROR
            }
    }
}

interface CreditCardFeeCalculatorService {
    fun calculateFeeAmount(
        accountId: AccountId,
        netAmount: Long,
        installments: Int,
        calculationId: String? = null,
    ): Either<InvalidSchedulePaymentFee, Long>

    fun calculateInstallment(
        accountId: AccountId,
        netAmount: Long,
        installments: Int,
        calculationId: String? = null,
    ): Either<InvalidInstallmentCalculation, FeePaymentCreditCardInstallment>

    fun getFee(
        installments: Int,
    ): Double?
}

interface DDAProviderService {
    fun getBills(
        dueDate: LocalDate,
        document: String,
    ): List<DDAItem>

    // TODO: temporary implementation
    fun getBillsWithoutMicronautParser(
        dueDate: LocalDate,
        document: String,
    ): Boolean

    fun add(
        documents: List<Document>,
        documentType: DocumentType,
    ): List<Document>

    fun batchAdd(documents: List<Document>): DDABatchResult

    fun queryBatchAdd(idRequisicao: String): DDAQueryBatchAddResult

    fun queryBatchRemove(idRequisicao: String): DDAQueryBatchRemoveResult

    fun remove(document: Document)

    fun batchRemove(documents: List<Document>): DDABatchResult
}

sealed class DDABatchResult : PrintableSealedClass() {
    data class Accepted(
        val requestId: String,
    ) : DDABatchResult()

    data object UnknownResponse : DDABatchResult()
}

sealed class DDAQueryBatchRemoveResult : PrintableSealedClass() {
    data class PartialSuccess(
        val failed: List<Document>,
        val success: List<Document>,
    ) : DDAQueryBatchRemoveResult()

    data class Failed(
        val failed: List<Document>,
    ) : DDAQueryBatchRemoveResult()

    data class PayerNotFound(
        val document: String,
    ) : DDAQueryBatchRemoveResult()

    data object PendingRequestError : DDAQueryBatchRemoveResult()

    data object ProcessingRequest : DDAQueryBatchRemoveResult()

    data object Success : DDAQueryBatchRemoveResult()

    data object UnknownResponse : DDAQueryBatchRemoveResult()
}

sealed class DDAQueryBatchAddResult : PrintableSealedClass() {
    data class PartialSuccess(
        val failed: List<Document>,
        val success: List<Document>,
    ) : DDAQueryBatchAddResult()

    data class Failed(
        val failed: List<Document>,
    ) : DDAQueryBatchAddResult()

    data object PendingRequestError : DDAQueryBatchAddResult()

    data object ProcessingRequest : DDAQueryBatchAddResult()

    data object Success : DDAQueryBatchAddResult()

    data object UnknownResponse : DDAQueryBatchAddResult()
}

interface BillValidationService {
    fun validate(request: CreateBoletoRequest): BillValidationResponse

    fun validate(bill: Bill): BillValidationResponse

    fun validate(barCode: BarCode): BillValidationResponse
}

interface NotificationService {
    fun notifyMembers(
        members: List<Member>,
        type: NotificationType,
        buildNotification: (account: Account) -> BillPaymentNotification?,
    )

    fun notifyMembersViaChatbot(
        members: List<Member>,
        type: NotificationType,
        notificationFn: (Account) -> WhatsappNotification?,
    )

    fun notifyAccount(
        accountId: AccountId,
        type: NotificationType,
        buildNotification: (account: Account) -> BillPaymentNotification?,
    )

    fun notifyAccount(
        account: Account,
        type: NotificationType,
        buildNotification: (account: Account) -> BillPaymentNotification?,
    )

    fun notifyAccountViaChatbot(
        account: Account,
        type: NotificationType,
        buildNotification: (account: Account) -> WhatsappNotification?,
    )

    fun sendNotification(
        account: Account?,
        type: NotificationType,
        billPaymentNotification: BillPaymentNotification,
    )
}

interface NotificationPublisher {
    fun sendNotification(
        account: Account?,
        type: NotificationType,
        billPaymentNotification: BillPaymentNotification,
    )
}

interface NotificationAdapter {
    fun notifyBillCreated(
        members: List<Member>,
        payee: String,
        amount: Long,
        dueDate: LocalDate,
        billType: BillType,
        billId: BillId,
        billStatus: BillStatus,
        walletId: WalletId,
        walletName: String,
        author: Member?,
        description: String?,
        actionSource: ActionSource,
        hint: String? = null,
    )

    fun notifyAddFailure(
        members: List<Member>,
        source: ActionSource,
        from: EmailAddress,
        subject: String,
        walletName: String,
        billId: BillId?,
        walletId: WalletId,
        result: CreateBillResult,
    )

    fun notifyBillComingDue(
        members: List<Member>,
        author: Member?,
        walletName: String,
        walletId: WalletId,
        billView: BillView,
        hint: String? = null,
    )

    fun notifyOnePixPay(
        members: List<Member>,
        walletName: String,
        onePixPayId: OnePixPayId<*>,
        bills: List<BillView>,
        delay: Duration,
        hasBasicAccountLimitExceeded: Boolean = false,
    )

    fun notifyBillComingDueSecondaryWallet(
        members: List<Member>,
        wallet: Wallet,
        bills: List<BillView>,
    )

    fun notifyInvoicePaymentUndone(
        members: List<Member>,
        walletName: String,
        author: Member,
        recipient: Recipient,
        amount: Long,
        settleDate: LocalDate,
    )

    fun notifyBoletoReceipt(
        members: List<Member>,
        receiptData: BoletoReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    )

    fun notifyInvoiceReceipt(
        members: List<Member>,
        receiptData: InvoiceReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    )

    fun notifyPixReceipt(
        members: List<Member>,
        receiptData: PixReceiptData,
        receiptFilesData: ReceiptFilesData,
        mailReceiptHtml: CompiledHtml,
    )

    fun notifyCashIn(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        senderName: String,
        senderDocument: String,
        amount: Long,
    )

    fun notifyCashInPaymentInsufficientBalanceToday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        pendingAmountToday: Long,
        senderName: String,
        senderDocument: String,
        amount: Long,
    )

    fun notifyCashInPaymentSufficientBalanceToday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        senderName: String,
        senderDocument: String,
        amount: Long,
    )

    fun notifyInsufficientBalanceToday(
        members: List<Member>,
        pendingScheduledAmountToday: Long,
        pendingAmountTotal7Days: Long,
        pendingAmountTotal15Days: Long,
        walletId: WalletId,
        walletName: String,
        isAfterHours: Boolean,
    )

    fun notifyInsufficientBalanceTodaySecondaryWallet(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        pendingScheduledAmountToday: Long,
    )

    fun notifyScheduledBillNotPayable(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        author: Member,
        payee: String,
        dueDate: LocalDate,
        totalAmount: Long,
        description: String,
        billId: BillId,
    )

    fun notifyCashInFailure(
        members: List<Member>,
        amount: Long,
        errorMessage: String,
        walletId: WalletId,
        walletName: String,
    )

    fun notifyTransferNotPayable(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        billId: BillId,
        author: Member,
        recipientName: String,
        dueDate: LocalDate,
        amount: Long,
        errorMessage: String,
    )

    fun notifyBillComingDueLastWarn(
        members: List<Member>,
        wallet: Wallet,
        dueDate: LocalDate,
        paymentLimitTime: LocalTime,
        bills: List<BillView>,
        hint: String?,
    )

    fun notifyBillOverdueYesterday(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        hint: String?,
    )

    fun notifyUserActivated(account: Account)

    fun notifyUpgradeCompleted(account: Account)

    fun notifyWalletMemberJoined(
        founder: Member,
        inviteeFullName: String,
        walletName: String,
    )

    fun notifyUserAuthenticationRequired(accountId: AccountId)

    fun notifyRegisterUpdated(
        accountId: AccountId,
        mobilePhone: MobilePhone,
        name: String,
    )

    fun notifyBillSchedulePostponedDueLimitReached(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        payee: String,
        totalAmount: Long,
        type: BillType,
        author: Member,
    )

    fun notifyBillScheduleCanceledDueAmountHigherThanDailyLimit(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        payee: String,
        totalAmount: Long,
        type: BillType,
        author: Member,
    )

    fun notifyScheduleBillCanceledDueCreditCardDenied(
        members: List<Member>,
        walletId: WalletId,
        billId: BillId,
        payee: String,
        amount: Long,
    )

    fun notifyNotVisibleBillAlreadyExists(
        member: Member,
        walletId: WalletId,
        billId: BillId,
        payee: String,
        dueDate: LocalDate,
        totalAmount: Long,
        walletName: String,
    )

    fun notifyToken(
        accountId: AccountId,
        mobilePhone: MobilePhone,
        token: String,
    )

    fun notifyForgotPasswordToken(
        accountId: AccountId,
        document: String,
        emailAddress: EmailAddress,
        token: String,
        duration: Duration,
    )

    fun notifyEmailVerificationToken(
        accountId: AccountId,
        emailAddress: EmailAddress,
        token: String,
        duration: Duration,
    )

    fun notifyInvitedMember(
        invite: Invite,
        receiver: EmailAddress,
    )

    fun notifyInviteReminder(
        invite: Invite,
        account: Account?,
    )

    fun notifyFirstBillScheduled(account: Account)

    fun notifyOptOutNotifications(account: Account)

    fun notifyOnePixPayFailure(accountId: AccountId)

    fun notifyPaymentIntentFailed(
        accountId: AccountId,
        paymentIntentId: PaymentIntentId,
    )

    fun notifyBasicSignUpReopened(
        accountId: AccountId,
        mobilePhone: MobilePhone,
    )

    fun notifySubscriptionGrantedByInvestment(
        accountId: AccountId,
        dueDate: LocalDate,
    )

    fun notifySubscriptionCreated(
        accountId: AccountId,
        dueDate: LocalDate,
        amount: Long,
    )

    fun notifySubscriptionOverdue(
        accountId: AccountId,
        effectiveDueDate: LocalDate,
        amount: Long,
        alternativeVersion: Boolean,
    )

    fun notifyInAppSubscriptionOverdue(
        accountId: AccountId,
    )

    fun notifySubscriptionOverdueWarningDetailedNotificationsShutdown(
        accountId: AccountId,
        effectiveDueDate: LocalDate,
    )

    fun notifySubscriptionOverdueWarningBillNotificationsAndDDAShutdown(
        accountId: AccountId,
        accountClosureDate: LocalDate,
    )

    fun notifySubscriptionOverdueWarningAccountClosure(
        accountId: AccountId,
        daysUntilClosure: Long,
        closeDate: LocalDate,
    )

    fun notifySubscriptionOverdueCloseAccount(accountId: AccountId)

    fun notifyLegacySubscriptionOverdue(
        accountId: AccountId,
        days: Long,
    )

    fun notifyUtilityAccountUpdatedStatus(utilityAccount: UtilityAccount)

    fun notifyUtilityAccountConnected(utilityAccount: UtilityAccount, info: UtilityAccountUpdateInfo?)

    fun notifyUtilityAccountRequestReconnection(utilityAccount: UtilityAccount)

    fun notifyDisconnectLegacyUtilityAccount(utilityAccount: UtilityAccount)

    fun notifyInvoicesNotFoundUtilityAccount(utilityAccount: UtilityAccount)

    fun notifyInvoicesScanErrorUtilityAccount(utilityAccount: UtilityAccount)

    fun notifyCreditCardEnabled(
        accountId: AccountId,
        quota: Long,
    )

    fun notifySubscriptionInsufficientBalance(
        members: List<Member>,
        walletId: WalletId,
        walletName: String,
        amount: Long,
    )

    fun notifyPixNotReceivedFailure(
        accountId: AccountId,
        wallet: Wallet,
        amount: Long,
        senderName: String,
        senderDocument: String,
    )

    fun notifyRegisterDenied(
        accountId: AccountId,
        mobilePhone: MobilePhone,
    )

    fun notifyRegisterUpgraded(
        accountId: AccountId,
        mobilePhone: MobilePhone,
    )

    fun notifyAccountStatement(
        periodMessage: String,
        emailAddress: EmailAddress,
        name: String,
        files: List<ByteArrayWithNameAndType>,
    )

    fun notifyWalletSummary(
        periodMessage: String,
        emailAddress: EmailAddress,
        name: String,
        files: List<ByteArrayWithNameAndType>,
    )

    fun notifyHumanChatWelcome(
        accountId: AccountId,
        mobilePhone: MobilePhone,
    )

    fun notifyTriPixNextDayReminder(
        account: Account,
    )

    fun notifyTriPixLastDayReminder(
        account: Account,
    )

    fun notifyTriPixExpired(
        account: Account,
    )

    fun notifyCustom(
        accountId: AccountId,
        template: String,
        parameters: List<String>,
        quickReplyButtonsWhatsAppParameter: List<String>,
        buttonWhatsAppParameter: String?,
        media: NotificationMedia?,
    )

    fun notifyCustomEmail(accountId: AccountId, template: String, parameters: Map<String, String>)
}

data class PersonBasicInfo(
    val name: String? = null,
    val birthDate: LocalDate? = null,
    val motherName: String? = null,
    val fatherName: String? = null,
    val gender: String? = null,
)

data class PersonReceitaFederalInfo(
    val name: String,
    val birthDate: LocalDate,
    val socialName: String? = null,
    val hasObitIndication: Boolean,
    val obitYear: String? = null,
)

interface BigDataService {
    fun getPersonName(cpf: String): Either<Exception, String>

    fun getPersonBasicInfo(cpf: String): Either<Exception, PersonBasicInfo>

    fun getKycDossier(cpf: String): Either<Exception, KycDossier>

    fun getCompanyName(cnpj: String): Either<Exception, String>

    fun getAddress(zipCode: String): Either<Exception, Address>
}

interface BillEventRepository {
    fun save(billEvent: BillEvent)

    fun findLastBill(
        barCode: BarCode,
        walletId: WalletId,
    ): Either<Exception, Bill>

    fun findLastBill(
        idNumber: String,
        walletId: WalletId,
    ): Either<Exception, Bill>

    fun findLastBill(
        barCode: BarCode,
        walletId: WalletId,
        dueDate: LocalDate,
    ): Either<Exception, Bill>

    fun getBillById(billId: BillId): Either<Exception, Bill>

    fun getBillByExternalId(externalId: ExternalBillId): Either<Exception, Bill>

    fun getBill(
        walletId: WalletId,
        billId: BillId,
    ): Either<Exception, Bill>

    fun listBills(
        barCode: BarCode,
        dueDate: LocalDate?,
    ): List<Bill>

    fun deleteUniqueConstraint(barCode: BarCode, walletId: WalletId, dueDate: LocalDate)
}

interface UserEventRepository {
    fun save(userEvent: UserEvent)
}

interface FinancialInstitutionGlobalDataRepository {
    fun saveHolidays(holidays: List<LocalDate>)

    fun savePixParticipants(pixParticipants: List<FinancialInstitution>)

    fun saveBankList(bankList: List<Bank>)

    fun loadHolidays(): List<LocalDate>

    fun loadPixParticipants(): List<FinancialInstitution>

    fun loadBankList(): List<Bank>
}

interface MailboxGlobalDataRepository {
    fun saveAllowedList(list: List<String>)
    fun loadAllowedList(): List<String>

    fun saveBlockList(list: List<String>)
    fun loadBlockList(): List<String>

    fun saveDoNotDisturbList(list: List<String>)
    fun loadDoNotDisturbList(): List<String>
}

interface MailboxWalletDataRepository {
    fun add(walletId: WalletId, type: MailboxListType, email: EmailAddress)
    fun remove(walletId: WalletId, type: MailboxListType, email: EmailAddress)
    fun load(walletId: WalletId, type: MailboxListType): List<EmailAddress>
    fun has(walletId: WalletId, type: MailboxListType, email: EmailAddress): Boolean
}

interface ScheduledWalletRepository {
    fun save(
        walletId: WalletId,
        minScheduleDate: LocalDate,
    )

    fun delete(walletId: WalletId)

    fun findAllWalletsWithScheduledBills(): Flux<WalletId>

    fun findWalletsWithScheduledBillsUntil(scheduledDate: LocalDate): Flux<WalletId>

    fun findAllWalletsWithScheduledBillsToDate(scheduledDate: LocalDate): Flux<WalletId>

    fun findWalletsWithScheduledBillsBetween(startDate: LocalDate, endDate: LocalDate): Flux<WalletId>
}

interface ScheduledBillRepository {
    fun save(scheduledBill: ScheduledBill)

    fun findScheduledBillById(billId: BillId): List<ScheduledBill>

    fun delete(scheduledBill: ScheduledBill)

    fun findAllScheduledBillsByWalletId(walletId: WalletId): List<ScheduledBill>

    fun findScheduledBillsByWalletIdAndScheduledDate(
        walletId: WalletId,
        scheduledDate: LocalDate,
    ): List<ScheduledBill>

    fun findScheduledBillsByWalletIdAndUntilScheduledDate(
        walletId: WalletId,
        scheduledDate: LocalDate,
    ): List<ScheduledBill>

    fun findScheduledBillsByWalletIdBetween(
        walletId: WalletId,
        beginDate: LocalDate,
        endDate: LocalDate,
    ): List<ScheduledBill>
}

interface AcquirerService {
    fun authorizeAndCapture(
        accountId: AccountId,
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        installments: Int = 1,
    ): CreditCardAuthorization

    fun authorize(
        accountId: AccountId,
        orderId: String,
        amount: Long,
        creditCard: CreditCard,
        softDescriptor: String,
        installments: Int = 1,
    ): CreditCardAuthorization

    fun capture(acquirerTid: String)

    fun cancel(orderId: String)

    fun checkStatus(orderId: String): CreditCardAuthorization

    fun tokenize(
        cardNumber: String,
        expirationDate: String,
        brand: CreditCardBrand,
        holderName: String,
        uid: String,
    ): Either<IntegrationError, CreditCardToken>

    fun validate(
        token: CreditCardToken,
        cvv: String,
    ): Either<IntegrationError, Boolean>
}

interface CreditCardInformationService {
    fun retrieveBinDetails(bin: String): CreditCardBinDetails?
}

interface TransactionRepository {
    fun findById(id: TransactionId): Transaction

    fun findByIdAndWallet(
        id: TransactionId,
        walletId: WalletId,
        transactionType: TransactionType?,
    ): Transaction

    fun findByWalletAndStatusAndBankOperationId(
        walletId: WalletId,
        status: TransactionStatus,
        bankOperationId: BankOperationId,
    ): Transaction

    fun findByWalletAndStatusAndType(
        walletId: WalletId,
        transactionStatus: TransactionStatus,
        transactionType: TransactionType,
    ): List<Transaction>

    fun findCreditCardUsage(
        accountId: AccountId,
        walletId: WalletId,
        datePattern: String = "",
    ): TransactionAmount

    fun getAuthentication(id: TransactionId): String

    fun getBankTransactionId(id: TransactionId): Long

    fun findTransactionTypeByWalletAndNSU(
        walletId: WalletId,
        nsu: Long,
    ): Pair<String, String>

    fun findTransactionWalletId(id: TransactionId): WalletId?

    fun hasTransactionByWalletAndStatus(
        walletId: WalletId,
        transactionStatus: TransactionStatus,
    ): Boolean

    fun save(transaction: Transaction)

    fun findTransactions(walletId: WalletId): List<Transaction>

    fun findTransactions(
        billId: BillId,
        status: TransactionStatus?,
    ): List<Transaction>

    fun findTransactionIdByWalletAndNSU(
        walletId: WalletId,
        nsu: Long,
    ): TransactionId?
}

interface AccountConfigurationRepository {
    fun save(accountConfiguration: AccountConfiguration)

    fun find(accountId: AccountId): AccountConfiguration
}

interface AccountRepository {
    fun findAccountByDocument(document: String): Account

    fun findAccountByDocumentOrNull(document: String): Account?

    fun findById(accountId: AccountId): Account

    fun findByIdOrNull(accountId: AccountId): Account?

    fun findByGroup(
        group: AccountGroup,
    ): List<Account>

    fun listAccountsByDocument(document: String): List<Account>

    fun findAccountPaymentMethodsByAccountId(accountId: AccountId): List<AccountPaymentMethod>

    fun findAccountPaymentMethodsByHMac(hmac: String): List<AccountPaymentMethod>

    fun findAccountPaymentMethodsByAccountIdAndStatus(
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): List<AccountPaymentMethod>

    fun findCreditCardsByAccountIdAndStatus(
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): List<AccountPaymentMethod>

    fun incrementNSU(accountId: AccountId): Int

    fun findAccountPaymentMethodByIdAndAccountId(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
        status: AccountPaymentMethodStatus? = null,
    ): AccountPaymentMethod

    fun findAllPhysicalBalanceAccount(): List<AccountPaymentMethod>

    fun findVirtualBankAccountByDocument(document: String): Either<FindError, AccountPaymentMethod>

    fun findAllVirtualBankAccount(): List<AccountPaymentMethod>

    fun create(
        username: String,
        emailAddress: EmailAddress,
        accountId: AccountId = AccountId("ACCOUNT-${UUID.randomUUID()}"),
        registrationType: RegistrationType,
        groups: List<AccountGroup> = emptyList(),
        subscriptionType: SubscriptionType = SubscriptionType.PIX,
    ): PartialAccount

    fun create(userAccount: Account): Account

    fun save(account: Account)

    fun updateAccountStatus(accountId: AccountId, newStatus: AccountStatus)

    fun findPartialAccountById(accountId: AccountId): PartialAccount

    fun findPartialAccountByIdOrNull(accountId: AccountId): PartialAccount?

    fun updatePartialAccountStatus(
        accountId: AccountId,
        newStatus: AccountStatus,
    )

    fun updatePartialAccountEmail(
        accountId: AccountId,
        newEmail: EmailAddress,
    )

    fun updatePartialAccountName(
        accountId: AccountId,
        newName: String,
    )

    fun updatePartialAccountGroups(
        accountId: AccountId,
        groups: List<AccountGroup>,
    ): PartialAccount

    fun deletePartialAccount(accountId: AccountId)

    fun findAllAccountsActivatedSince(
        date: LocalDate,
        filterAccountsThatReceiveNotification: Boolean = false,
    ): List<Account>

    fun findAccountByUpgradeStatus(status: UpgradeStatus): List<Account>

    fun createAccountPaymentMethod(
        accountId: AccountId,
        bankAccount: BankAccount,
        position: Int,
        mode: BankAccountMode = BankAccountMode.PHYSICAL,
    ): AccountPaymentMethod

    fun createAccountPaymentMethod(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
        bankAccount: BankAccount,
        position: Int,
        status: AccountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
        mode: BankAccountMode = BankAccountMode.PHYSICAL,
    ): AccountPaymentMethod

    fun createAccountPaymentMethod(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId = AccountPaymentMethodId(UUID.randomUUID().toString()),
        creditCard: CreditCard,
        position: Int,
        status: AccountPaymentMethodStatus = AccountPaymentMethodStatus.ACTIVE,
    ): AccountPaymentMethod

    fun createExternalAccountPaymentMethod(
        accountId: AccountId,
        providerName: AccountProviderName,
        position: Int,
    ): AccountPaymentMethod

    fun activateAccountPaymentMethod(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    )

    fun findPhysicalBankAccountByAccountId(accountId: AccountId): List<AccountPaymentMethod>

    fun findPhysicalAccountPaymentMethod(
        bankNo: Long,
        routingNo: Long,
        accountNo: AccountNumber,
    ): Either<FindError, AccountPaymentMethod>

    fun deleteAccountPaymentMethod(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): DeleteCreditCardResult

    fun updateCreditCardPaymentMethodRisk(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
        riskLevel: RiskLevel,
    ): UpdateCreditCardMethodRiskResult

    suspend fun findNameAndEmail(
        accountId: AccountId,
        role: Role,
    ): Either<Exception, Pair<String, EmailAddress>>

    fun checkAccountRole(
        accountId: AccountId,
        role: Role,
    ): Role?

    fun findPartialAccountByStatus(accountStatus: AccountStatus): List<PartialAccount>

    fun findByExternalId(externalId: ExternalId): List<Account>

    fun saveCreditCardBin(
        accountId: AccountId,
        id: AccountPaymentMethodId,
        bin: String,
    )

    fun updatePartialAccountRegistrationType(
        accountId: AccountId,
        registrationType: RegistrationType,
    )

    fun findAccountIdByPhysicalBankAccountNo(
        bankNo: Long,
        routingNo: Long,
        accountNo: AccountNumber,
    ): AccountId
}

interface SubscriptionRepository {
    fun save(subscription: Subscription)

    fun find(accountId: AccountId): Subscription?

    fun findAll(): List<Subscription>

    fun find(status: SubscriptionStatus): List<Subscription>

    fun find(
        subscriptionStatus: SubscriptionStatus,
        paymentStatus: SubscriptionPaymentStatus,
    ): List<Subscription>
}

interface BackOfficeAccountRepository {
    fun findById(accountId: AccountId): Account

    fun findByIdOrNull(accountId: AccountId): Account?

    fun closeCreditCards(accountId: AccountId)

    fun closeAccountPaymentMethod(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
    )

    fun setAccountPaymentMethodStatus(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
        status: AccountPaymentMethodStatus,
    )

    fun updatePartialAccountStatus(
        accountId: AccountId,
        newStatus: AccountStatus,
    )

    fun save(account: Account)
}

fun interface BoletoOccurrenceProcessor {
    fun process(occurrence: BoletoOccurrence): OccurrenceProcessorResult
}

sealed class OccurrenceProcessorResult : PrintableSealedClassV2() {
    data object Success : OccurrenceProcessorResult()

    data object TransactionNotfound : OccurrenceProcessorResult()

    data class UnknownError(
        val ex: Throwable? = null,
    ) : OccurrenceProcessorResult()
}

interface MessagePublisher {
    fun sendMessage(
        queueName: String,
        body: Any,
        delaySeconds: Int? = null,
    )

    fun sendGenerateBillReceipt(billPaid: BillPaid)

    fun sendMessage(queueMessage: QueueMessage)

    fun sendMessageBatch(messageBatch: QueueMessageBatch)

    fun sendRetryTransactionMessage(
        transactionId: TransactionId,
        delaySeconds: Int?,
    )

    fun sendRetryCashinMessage(transactionId: TransactionId, delaySeconds: Int? = null)

    fun sendBatchAddOrderMessage(batchAddOrder: BatchAddOrder)

    fun sendBatchDDARequestedMessage(batch: List<DDARegisterMessage>)

    fun sendBatchMigrationOrderMessage(batchMigrationOrder: BatchMigrationOrder)
}

interface EventPublisher {
    fun publish(topic: String, message: Any, attributes: Map<String, String> = emptyMap())

    fun publish(
        event: BillEvent,
        billType: BillType,
    )

    fun publish(event: WalletEvent)

    fun publish(event: AccountEvent)
}

interface LoginRepository {
    fun findUserLogin(provider: ProviderUser): Login?

    fun findUserLogin(emailAddress: EmailAddress): List<Login>

    fun createLogin(
        providerUser: ProviderUser,
        id: AccountId,
        role: Role,
    )

    fun updateRole(
        accountId: AccountId,
        emailAddress: EmailAddress,
        role: Role,
    )

    fun findActiveUserLoginProvider(emailAddress: EmailAddress): List<ProviderUser>

    fun remove(providerUser: ProviderUser)

    fun remove(accountId: AccountId)

    fun updateEmail(
        accountId: AccountId,
        currentEmailAddress: EmailAddress,
        newEmailAddress: EmailAddress,
    )
}

interface ContactRepository {
    fun save(recipient: Contact)

    fun delete(
        accountId: AccountId,
        contactId: ContactId,
    )

    fun deleteBankAccount(
        accountId: AccountId,
        contactId: ContactId,
        bankAccountId: BankAccountId,
    )

    fun deletePixKey(
        accountId: AccountId,
        contactId: ContactId,
        value: String,
    )

    fun findByAccountId(accountId: AccountId): List<Contact>

    fun findById(contactId: ContactId): Contact

    fun findByIdOrNull(contactId: ContactId): Contact?

    fun findByIdAndAccountId(
        contactId: ContactId,
        accountId: AccountId,
    ): Contact

    fun findRecipientByAccountIDAndDocument(
        accountId: AccountId,
        document: String,
    ): Either<Exception, Contact>

    fun append(
        accountId: AccountId,
        contactId: ContactId,
        savedBankAccount: SavedBankAccount,
    )

    fun append(
        accountId: AccountId,
        contactId: ContactId,
        savedPixKey: SavedPixKey,
    )

    fun updateLastUsed(
        accountId: AccountId,
        contactId: ContactId,
        lastUsed: LastUsed?,
    )
}

interface BillRepository {
    fun findBill(
        billId: BillId,
        walletId: WalletId,
    ): BillView

    fun findByWalletAndStatus(
        walletId: WalletId,
        status: BillStatus,
    ): List<BillView>

    fun findByWallet(walletId: WalletId): List<BillView>

    fun findByWallet(walletId: WalletId, criteria: FindBillsCriteria): List<BillView>

    fun findByWalletAndEffectiveDueDate(
        walletId: WalletId,
        dueDate: LocalDate,
    ): List<BillView>

    fun findByContactId(contactId: ContactId): List<BillView>

    fun findRecurrenceBillByContactId(contactId: ContactId): List<BillView>

    fun save(bill: Bill)

    fun findOverdueBills(walletId: WalletId): List<BillView>

    fun findBillsWaitingFunds(walletId: WalletId): List<BillView>

    fun findBillsComingDue(walletId: WalletId): List<BillView>

    fun findBillsComingDueWithin(
        walletId: WalletId,
        periodInDays: Long,
    ): List<BillView>

    fun remove(
        billId: BillId,
        walletId: WalletId,
    )

    fun setBillComingDueMessageSent(
        billId: BillId,
        walletId: WalletId,
        billComingDueMessageSent: Boolean,
    )

    fun saveSettlementFundsTransfer(settlementFundsTransfer: SettlementFundsTransfer)

    fun findSettlementFundsTransfers(billId: BillId): List<SettlementFundsTransfer>

    fun getPaidBills(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        types: List<BillType>? = null,
    ): List<BillView>

    fun refund(
        walletId: WalletId,
        billId: BillId,
        transactionId: TransactionId,
        type: BillType,
        amount: Long,
        originalPaidDate: ZonedDateTime,
    )

    fun getRefundedBills(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
    ): List<RefundedBill>

    fun getTotalPaid(
        walletId: WalletId,
        date: LocalDate,
        types: List<BillType>?,
    ): Long =
        getTotalPaid(
            walletId,
            date.atStartOfDay(brazilTimeZone),
            date.atTime(LocalTime.MAX).atZone(brazilTimeZone),
            types,
        )

    fun getTotalPaidBillsByScheduleSourceType(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        actionSource: ActionSource,
    ): Long

    fun getTotalPaid(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        types: List<BillType>?,
    ): Long

    fun getStuckedProcessingBills(seconds: Long): List<BillView>

    fun findByWalletAndEffectiveDueDateMonth(
        walletId: WalletId,
        year: Year,
        month: Month,
    ): List<BillView>
}

data class FindBillsCriteria(
    val from: YearMonth? = null,
    val to: YearMonth? = null,
) {
    companion object {
        fun create(from: String?, to: String?): FindBillsCriteria {
            return FindBillsCriteria(from = from?.let { YearMonth.parse(it) }, to = to?.let { YearMonth.parse(it) })
        }
    }
}

interface SettlementPaymentService {
    fun request(request: SettlementClientRequestTO): Result<Boolean>
}

interface BoletoSettlementService {
    fun validateBill(bill: BillView): BillValidationResponse

    fun validateBill(boleto: CreateBoletoRequest): BillValidationResponse

    fun validateBill(bill: Bill): BillValidationResponse

    @Deprecated(message = "use specific interface for settlement BoletoSettlementValidationService")
    fun settlementValidation(bill: Bill): BillValidationResponse

    fun initPayment(
        bill: Bill,
        nsu: Int,
        transactionId: String,
        payerDocument: String,
        payerName: String,
    ): BillPaymentResponse

    fun confirmPayment(
        externalTerminal: String,
        externalNsu: Int,
        transactionId: String,
    )

    fun cancelPayment(
        externalTerminal: String,
        externalNsu: Int,
        transactionId: String,
    )

    fun getBankByInstitutionCode(institutionCode: Int): String

    fun queryPayment(transactionId: String): BoletoSettlementStatus

    fun getBalanceAmount(): Either<Exception, Long>
}

interface EmailService {
    fun process(event: SQSEvent)
}

interface ParserService {
    fun parseBillFrom(incomingEmail: IncomingEmail): List<ParsedBill>
}

interface TransactionalValidationResponse {
    var transactionId: Long
}

interface FindBillService {
    fun findByWalletId(walletId: WalletId): List<BillView>

    fun findActiveAndWaitingApprovalBills(walletId: WalletId): List<BillView>

    fun find(
        walletId: WalletId,
        billId: BillId,
    ): BillView

    fun findOverdueBills(walletId: WalletId): List<BillView>

    fun findBillsWaitingFunds(walletId: WalletId): List<BillView>

    fun findBillsComingDue(walletId: WalletId): List<BillView>

    fun findBillsByDueDateAsync(dueDate: LocalDate): Flowable<BillComingDue>

    fun findAllWalletsThatReceiveNotification(): List<Wallet>

    fun findAllWallets(
        filterAccountsThatReceiveNotification: Boolean,
        filterActiveAccounts: Boolean,
    ): List<Wallet>

    fun findOpenBills(
        dueDate: LocalDate,
        paymentLimitTime: LocalTime,
    ): Flowable<BillComingDue>
}

interface MailBoxService {
    fun addBill(
        walletId: WalletId,
        barCode: BarCode,
        dueDate: LocalDate?,
        from: EmailAddress,
        subject: String,
        receipt: Receipt? = null,
        shouldNotifyOnRetryableError: Boolean,
    ): Either<MailboxAddBillError, Unit>
}

interface BillService {
    fun markBillComingDueMessageSent(
        billId: BillId,
        walletId: WalletId,
    )

    fun addBillsFromBackoffice(
        userEmail: EmailAddress,
        from: EmailAddress,
        subject: String,
        bills: List<EmailBill>,
    ): List<Pair<BarCode, MailboxAddBillError?>>

    fun sumAmountUntilEachDate(
        walletId: WalletId,
        dates: List<LocalDate>,
        filter: ((BillView) -> Boolean)? = null,
    ): List<Long>

    fun getPaidBills(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        types: List<BillType>? = null,
    ): List<BillView>
}

interface InternalBankRepository {
    fun findBankStatementItem(
        accountPaymentMethodId: AccountPaymentMethodId,
        date: LocalDate,
        operationNumber: String,
    ): Either<FindError, BankStatementItem>

    fun findAllOmnibusBankStatementItem(accountPaymentMethodId: AccountPaymentMethodId): OmnibusBankStatement

    fun save(omnibusBankStatementItem: OmnibusBankStatementItem)

    fun create(internalBankStatementItem: InternalBankStatementItem)

    fun update(internalBankStatementItem: InternalBankStatementItem)

    fun findAllBankStatementItem(accountPaymentMethodId: AccountPaymentMethodId): BankStatement

    fun findAllBankStatementItem(
        accountPaymentMethodId: AccountPaymentMethodId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): BankStatement

    fun findAllBankStatementCredits(
        accountPaymentMethodId: AccountPaymentMethodId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<BankStatementItem>
}

interface SmsSender {
    fun send(
        phoneNumber: MobilePhone,
        message: String,
    )
}

open class TokenData protected constructor(
    val value: String,
    val type: TokenType,
) {
    companion object {
        fun of(
            value: String,
            type: TokenType,
        ) =
            when (type) {
                TokenType.EMAIL -> of(EmailAddress(value))
                TokenType.MOBILE_PHONE -> of(MobilePhone(value), TokenChannel.SMS)
                TokenType.LIVENESS_ID -> of(LivenessId(value))
                TokenType.WHATSAPP -> of(MobilePhone(value), TokenChannel.WHATSAPP)
            }

        fun of(
            mobilePhone: MobilePhone,
            channel: TokenChannel,
        ) =
            when (channel) {
                TokenChannel.SMS -> TokenData(mobilePhone.msisdn, TokenType.MOBILE_PHONE)
                TokenChannel.WHATSAPP -> TokenData(mobilePhone.msisdn, TokenType.WHATSAPP)
            }

        fun of(emailAddress: EmailAddress) = TokenData(emailAddress.value, TokenType.EMAIL)

        fun of(livenessId: LivenessId) = TokenData(livenessId.value, TokenType.LIVENESS_ID)
    }
}

class TokenDataWithExpiration(
    tokenData: TokenData,
    val expiration: Long,
) : TokenData(tokenData.value, tokenData.type)

enum class TokenType {
    EMAIL,
    MOBILE_PHONE,
    LIVENESS_ID,
    WHATSAPP,
}

interface TokenRepository {
    fun save(
        tokenKey: TokenKey,
        tokenData: TokenDataWithExpiration,
    )

    fun existsToken(
        accountId: AccountId,
        tokenType: TokenType,
    ): Boolean

    fun retrieveValidated(
        tokenKey: TokenKey,
        tokenType: TokenType,
        errorLimit: Int,
    ): Either<InvalidTokenException, TokenDataWithExpiration>

    fun retrieveNotExpired(
        accountId: AccountId,
        tokenType: TokenType,
    ): Either<InvalidTokenException, TokenDataWithExpiration>

    fun retrieveNotExpiredTokenKey(
        accountId: AccountId,
        tokenType: TokenType,
    ): Either<InvalidTokenException, TokenKey>

    fun incrementErrorCount(accountId: AccountId): Int

    fun delete(
        tokenKey: TokenKey,
        tokenData: TokenData,
    )
}

interface AccountRegisterRepository {
    fun findByAccountId(
        accountId: AccountId,
        checkOpenAccount: Boolean = true,
    ): AccountRegisterData

    fun findByDocument(document: String): AccountRegisterData

    fun findByDocumentOrNull(document: String): AccountRegisterData?

    fun save(accountRegister: AccountRegisterData): AccountRegisterData

    fun saveOriginalOcrAndPersonData(
        accountId: AccountId,
        originalDocumentInfo: DocumentInfo,
        personBasicInfo: PersonBasicInfo?,
        validatedDocumentInfo: DocumentInfo,
    )

    fun findValidatedDocumentInfo(accountId: AccountId): DocumentInfo?

    fun create(
        accountId: AccountId,
        emailAddress: EmailAddress,
        emailAddressVerified: Boolean = false,
        username: String,
        mobilePhone: MobilePhone? = null,
        mobilePhoneVerified: Boolean = false,
        livenessId: LivenessId? = null,
        externalId: ExternalId? = null,
        documentInfo: DocumentInfo? = null,
        monthlyIncome: MonthlyIncome? = null,
        registrationType: RegistrationType,
        politicallyExposed: PoliticallyExposed? = null,
    ): AccountRegisterData

    fun putDocument(
        storedObject: StoredObject,
        fileData: InputStream,
    )

    fun getDocumentInputStream(storedObject: StoredObject): InputStream

    fun deactivate(
        accountId: AccountId,
        closureDetails: AccountClosureDetails,
    )

    fun findByVerifiedMobilePhone(mobilePhone: MobilePhone): List<AccountRegisterData>
}

@Deprecated("use interface ObjectRepository")
interface DatabaseObjectRepository {
    fun putObject(
        storedObject: StoredObject,
        fileData: InputStream,
    )

    fun getObjectInputStream(storedObject: StoredObject): InputStream
}

interface ObjectRepository {
    fun listObjectKeys(
        bucketName: String,
        directoryKey: String,
    ): List<String>

    fun region(): String

    fun loadObject(
        bucketName: String,
        key: String,
    ): InputStream

    fun loadObjectWithMetadata(
        bucketName: String,
        key: String,
    ): ObjectWithMetadata

    fun moveObject(
        fromBucket: String,
        fromKey: String,
        toBucket: String = fromBucket,
        toKey: String,
    )

    fun copyObject(
        fromBucket: String,
        fromKey: String,
        toBucket: String = fromBucket,
        toKey: String,
    )

    fun putObject(storedObject: StoredObject, fileData: ByteArray, mediaType: MediaType)
    fun findKeysByPrefix(storedObject: StoredObject): List<StoredObject>
    fun hasKeyPrefix(storedObject: StoredObject): Boolean
}

interface DocumentOCRParser {
    fun parseDocument(imageBase64: String): Either<Exception, DocumentInfo>

    fun parseDocument(
        frontImageBase64: String,
        backImageBase64: String,
        documentType: DocumentType,
    ): Either<Exception, DocumentInfo>

    fun fetchDocumentQuality(imageBase64: String): Either<Exception, DocumentQuality>
}

sealed class DocumentType(
    val value: String,
) {
    data object RG : DocumentType("RG")

    data object NEWRG : DocumentType("NEWRG")

    data object CNH : DocumentType("CNH")

    data object CNHV2 : DocumentType("CNHV2")

    data object CPF : DocumentType("CPF")

    data object CNPJ : DocumentType("CNPJ")

    class OTHER(
        other: String,
    ) : DocumentType(other)

    companion object {
        fun of(value: String): DocumentType =
            when (value) {
                "CNH" -> CNH
                "RG" -> RG
                "NEWRG" -> NEWRG
                "CNHV2" -> CNHV2
                "CPF" -> CPF
                "CNPJ" -> CNPJ
                else -> OTHER(value)
            }
    }
}

interface FaceMatcher {
    fun match(
        documentImage: InputStream,
        selfieImage: InputStream,
    ): Either<Exception, FaceMatchResult>
}

interface RecurenceRepository<Recurrence : BaseRecurrence> {
    fun find(
        recurrenceId: RecurrenceId,
        walletId: WalletId,
    ): Recurrence

    fun findOrNull(
        recurrenceId: RecurrenceId,
        walletId: WalletId,
    ): Recurrence?

    fun findByWalletId(walletId: WalletId): List<Recurrence>

    fun findAll(status: RecurrenceStatus = RecurrenceStatus.ACTIVE): List<Recurrence>

    fun save(recurrence: Recurrence)
}

interface BillRecurrenceRepository : RecurenceRepository<BillRecurrence> {
    fun findByContactId(contactId: ContactId): List<BillRecurrence>
}

interface ReceiptFileRepository {
    fun generateReceiptFiles(
        receiptData: ReceiptData,
        receiptHtml: CompiledHtml,
    ): ReceiptFilesData

    fun generateLinks(receiptData: ReceiptData): ReceiptFiles

    fun hasReceiptFilesStored(receiptData: ReceiptData): Boolean
}

interface ReceiptTemplateCompiler {
    fun canBuildFor(receiptData: ReceiptData): Boolean

    fun buildReceiptHtml(receiptData: ReceiptData, wallet: Wallet): CompiledHtml

    fun buildReceiptMailHtml(receiptData: ReceiptData): CompiledHtml
}

interface StatementTemplateCompiler {
    fun buildStatementHtml(form: StatementTemplateForm): CompiledHtml

    fun toPDF(html: CompiledHtml): ByteArray
}

interface StatementItemConverter {
    fun convertToCsv(list: List<StatementItem>): String

    fun convertToPdf(
        name: String,
        walletName: String,
        bankAccount: InternalBankAccount,
        startDate: LocalDate,
        endDate: LocalDate,
        created: LocalDateTime,
        statementItems: List<StatementItem>,
        initialBalance: Balance,
        finalBalance: Balance,
        initialBalanceDate: LocalDate,
        finalBalanceDate: LocalDate,
    ): ByteArray
}

interface TemplateForm

data class StatementTemplateForm(
    val name: String,
    val walletName: String,
    val document: String,
    val bankAccount: InternalBankAccount,
    val startDate: LocalDate,
    val endDate: LocalDate,
    val created: LocalDateTime,
    val statementItems: List<StatementItem>,
    val initialBalance: Balance,
    val finalBalance: Balance,
    val initialBalanceDate: LocalDate,
    val finalBalanceDate: LocalDate,
) : TemplateForm

interface TemplateCompiler {
    fun buildHtml(form: TemplateForm): CompiledHtml
}

interface ReceiptRepository {
    fun save(receiptData: ReceiptData)

    fun findById(
        walletId: WalletId,
        billId: BillId,
    ): ReceiptData
}

interface PDFConverter {
    fun convert(html: String, cropToFit: Boolean): ByteArray
}

interface UserPoolAdapter {
    fun configureUser(
        username: String,
        accountId: AccountId,
        groupNames: List<String>,
    )

    fun createUser(
        username: String,
        password: String,
        accountId: AccountId,
        emailAddress: EmailAddress,
        phoneNumber: MobilePhone,
        role: Role,
        name: String,
        setMFA: Boolean = true,
    )

    fun doesUserExist(username: String): Boolean

    fun isUserEnabled(username: String): Boolean

    fun addUserToGroup(
        username: String,
        groupName: String,
    )

    fun removeAccountId(username: String): Either<UserPoolRemoveUserError, Unit>

    fun disableUser(username: String): Either<UserPoolRemoveUserError, Unit>

    fun removeUserFromGroup(
        username: String,
        groupName: String,
    )

    fun signOutUser(username: String)

    fun resetPassword(username: String)

    fun setUserPassword(
        username: String,
        password: String,
    )

    fun updatePhoneNumber(
        username: String,
        phoneNumber: MobilePhone,
    )

    fun updateEmailAddress(
        username: String,
        emailAddress: EmailAddress,
    )

    fun setAccountId(
        username: String,
        accountId: AccountId,
    )

    fun getAccountId(username: String): AccountId?

    fun enableUser(
        username: String,
        accountId: AccountId? = null,
    ): Either<UserPoolEnableUserError, Unit>

    fun setMfaPreference(
        username: String,
        enabled: Boolean,
    ): Either<UserPoolSetMfaPreferenceError, Unit>

    fun listUserEvents(username: String): Either<UserPoolListAuthEventsException, List<String>>
}

interface WalletRepository {
    fun save(invite: Invite): Invite

    fun save(wallet: Wallet): Wallet

    fun findInvite(
        walletId: WalletId,
        memberDocument: String,
    ): Invite

    fun findInvitesFrom(
        walletId: WalletId,
        from: Duration,
    ): List<Invite>

    fun findInvites(
        document: String,
        inviteStatus: InviteStatus,
    ): List<Invite>

    fun findExpiringInvites(): List<Invite>

    fun markInviteReminderAsSent(invite: Invite)

    fun isInviteReminderSent(invite: Invite): Boolean

    fun findWallet(walletId: WalletId): Wallet

    fun findWalletOrNull(walletId: WalletId): Wallet?

    fun findWallets(accountId: AccountId): List<Wallet>

    fun findWallets(
        accountId: AccountId,
        memberStatus: MemberStatus? = MemberStatus.ACTIVE,
    ): List<Wallet>

    fun upsertMember(
        walletId: WalletId,
        member: Member,
    )

    fun createMember(
        walletId: WalletId,
        member: Member,
    )

    fun saveLimit(
        walletId: WalletId,
        type: DailyPaymentLimitType,
        currentAmount: Long,
        lastAmount: Long?,
        withTimestamp: Boolean = true,
    ): DailyPaymentLimit

    fun deleteMonthlyLimit(
        walletId: WalletId,
    )

    fun findLimitOrNull(
        walletId: WalletId,
        type: DailyPaymentLimitType,
    ): DailyPaymentLimit?

    fun findAllLimits(
        walletId: WalletId,
        isBasicAccount: Boolean?,
    ): WalletPaymentLimits

    fun findAccountPaymentMethod(walletId: WalletId): AccountPaymentMethod
}

interface DDARepository {
    fun save(ddaRegister: DDARegister): DDARegister

    fun find(accountId: AccountId): DDARegister?

    fun find(document: Document): DDARegister?

    fun findOlderRegister(
        document: Document,
        newerAccountId: AccountId,
    ): DDARegister?

    fun findByStatus(status: DDAStatus): List<DDARegister>

    fun find(
        barCode: BarCode,
        document: String,
        dueDate: LocalDate,
    ): DDABill?

    fun save(ddaBill: DDABill)
}

interface ReferrerRepository {
    fun save(referrerRegister: ReferrerRegister)

    fun find(accountId: AccountId): ReferrerRegister?
}

interface InternalLock {
    fun waitForAcquireLock(lockName: String): SimpleLock

    fun acquireLock(
        lockName: String,
        minDuration: Duration? = null,
        maxDuration: Duration? = null,
        simultaneousLock: Int = 1,
    ): SimpleLock?
}

interface LockReleaser {
    fun release(lockName: String): Result<Unit>
}

interface ECMProvider {
    fun sendAccountRegisterDocuments(request: SendAccountRegisterDocumentsRequest): SendDocumentsResponse

    fun sendSimpleSignUpDocuments(request: SendSimpleSignUpDocumentsRequest): SendDocumentsResponse

    fun sendUpgradeDocuments(request: SendUpgradeDocumentsRequest): SendDocumentsResponse

    fun notifyUpgradeStatus(request: NotifyUpgradeStatusRequest): NotifyUpgradeStatusResponse
}

interface ExternalAccountRegister {
    fun notifyAccountRegisterDocumentsSent(
        name: String,
        document: String,
    ): Boolean

    fun registerNaturalPerson(customer: ExternalAccount): RegisterNaturalPersonResponse

    fun simpleRegisterNaturalPerson(customer: ExternalAccount): RegisterNaturalPersonResponse

    fun queryExternalRegisterStatus(
        name: String,
        document: String,
    ): ExternalRegisterStatus?

    fun close(accountNo: AccountNumber): Either<Exception, Unit>

    fun getNaturalPersonStatus(accountNo: AccountNumber): Either<Exception, NaturalPersonStatus>
    fun getNaturalPersonsStatus(accountNos: List<AccountNumber>): Result<List<Pair<AccountNumber, NaturalPersonStatus>>>

    fun setNaturalPersonStatus(
        accountNo: AccountNumber,
        status: NaturalPersonStatus,
        reason: NaturalPersonStatusChangeReason,
    ): Either<Exception, Unit>
}

enum class NaturalPersonStatus(
    description: String,
) {
    ATI("Ativa"),
    ATM("Ativa Monitorada"),
    BLQ("Bloqueada"),
    ENC("Encerrada"),
    INT("Inativa"),
    AT2("Ativa 2"),
    SCR("Aceita Somente Créditos"),
    PLR("Paralisada"),
}

enum class NaturalPersonStatusChangeReason(
    description: String,
) {
    CadRenewalOrLivenessOrKYC("Renovação cadastral/Liveness/KYC"),
    BlockForPrevention("Bloqueio prevenção"),
    BlockForFraud("Bloqueio fraude"),
    BlockForLoss("Bloqueio perda"),
    BlockForTheft("Bloqueio roubo"),
    UnlockForPrevention("Desbloqueio prevenção"),
    UnlockForFraud("Desbloqueio fraude"),
    UnlockForLoss("Desbloqueio perda"),
    UnlockForTheft("Desbloqueio roubo"),
}

interface TedConfiguration {
    val limitTime: String
}

enum class MaintenanceServicesEnum {
    BOLETO,
}

interface FeatureConfiguration {
    val asyncSettlement: Boolean
    val forwardEmailToManualWorkflow: Boolean
    val automaticUserRegister: Boolean
    val zeroAuthEnabled: Boolean
    val creditCardChallenge: Boolean
    val blockDuplicateByIdNumber: Boolean
    val userPilotEnabled: Boolean
    val ddaBatchAddAsync: Boolean
    val silenceNotifications: Boolean
    val skipFullImportWhenAccountHasBills: Boolean
    val optOutNotification: Boolean
    val creditCardQuota: Boolean
    val updateScheduleOnAmountLowered: Boolean
    val updateAmountAfterPaymentWindow: Boolean
    val imageReceipt: Boolean
}

interface FraudListConfiguration {
    val documents: List<String>
}

interface GeneralCreditCardConfiguration {
    val periodLimit: Duration
    val maxPeriodLimit: Int
    val maxLimit: Int
    val challenge: CreditCardChallengeConfiguration
}

interface CreditCardChallengeConfiguration {
    val softDescriptor: String
    val expiration: Duration
    val maxAttempts: Int
    val minAmount: Long
    val maxAmount: Long
}

interface WalletConfiguration {
    val maxOpenInvites: Int
    val inviteExpiration: Duration
}

interface NotificationHintConfiguration {
    val billCreated: List<String>
    val billComingDue: List<String>
    val billComingDueLastWarn: List<String>
    val billOverdueYesterday: List<String>
}

interface BlipConfiguration {
    val allowedIps: List<String>
}

interface ModattaBassConfiguration {
    val name: String
    val document: String
    val bankNo: Long
    val routingNo: Long
    val allowedIps: List<String>
}

interface CrmService {
    fun findContact(accountId: AccountId): CrmContact?

    fun findContact(document: Document): CrmContact?

    fun upsertContact(account: Account): CrmContact?

    fun upsertContact(account: PartialAccount): CrmContact?

    fun upsertContact(account: AccountRegisterData): CrmContact?

    fun removeContactAsync(accountId: AccountId)

    fun contactExists(accountId: AccountId): Boolean

    fun createContact(contact: TemporaryCrmContact)

    fun createContact(contact: TemporaryCrmContactMinimal)
}

interface CrmRepository {
    fun upsertContact(contact: CrmContact)

    fun createContact(contact: TemporaryCrmContact)

    fun findContact(accountId: AccountId): CrmContact?

    fun findContact(document: Document): CrmContact?

    fun publishEvent(
        accountId: AccountId,
        eventName: String,
        customParams: Map<String, Any?> = mapOf(),
    )

    fun publishEvent(
        emailAddress: EmailAddress,
        eventName: String,
        customParams: Map<String, Any?> = mapOf(),
        retries: Int = 5,
    )

    fun createContact(contact: TemporaryCrmContactMinimal)
}

data class CrmContact(
    val accountId: AccountId,
    val emailAddress: EmailAddress,
    val role: Role? = null,
    val name: String? = null,
    val mobilePhone: MobilePhone? = null,
    val document: String? = null,
    val removed: Boolean = false,
    val groups: List<AccountGroup> = listOf(),
    val bankAccount: List<String>? = emptyList(),
    val accountType: UserAccountType,
    val isCNPJAccount: Boolean? = null,
    val cnpjWallets: List<WalletId>? = null,
    val cpfWallets: List<WalletId>? = null,
    val otherMembersOnWallets: List<AccountId>? = null,
    val subscriptionType: SubscriptionType,
    val subscriptionAccessConcessionReason: InAppSubscriptionReason? = null,
    val subscriptionEndsAt: String? = null,
    val subscriptionAutoRenew: Boolean? = null,
    val cupomPromo: String? = null,
)

data class TemporaryCrmContactMinimal(
    val emailAddress: EmailAddress,
    val mobilePhone: MobilePhone? = null,
    val cupomPromo: String? = null,
)

data class TemporaryCrmContact(
    val accountId: AccountId,
    val mobilePhone: MobilePhone,
    val emailAddress: EmailAddress,
)

data class PixCommand(
    val payerName: String,
    val recipientName: String,
    val recipientDocument: String,
    val recipientBankAccount: BankAccount?,
    val recipientPixKey: PixKey?,
    val originBankAccount: InternalBankAccount,
    val description: String,
    val amount: Long,
    val pixQrCodeId: String?,
)

sealed class NewPixCommand {
    abstract val payerName: String
    abstract val recipientName: String
    abstract val recipientDocument: String
    abstract val originBankAccount: InternalBankAccount
    abstract val description: String
    abstract val amount: Long
    abstract val bankOperationId: BankOperationId

    data class BankAccountCommand(
        override val payerName: String,
        override val recipientName: String,
        override val recipientDocument: String,
        override val originBankAccount: InternalBankAccount,
        override val description: String,
        override val amount: Long,
        override val bankOperationId: BankOperationId,
        val recipientBankAccount: BankAccount,
    ) : NewPixCommand()

    data class PixKeyCommand(
        override val payerName: String,
        override val recipientName: String,
        override val recipientDocument: String,
        override val originBankAccount: InternalBankAccount,
        override val description: String,
        override val amount: Long,
        override val bankOperationId: BankOperationId,
        val pixQrCodeId: String?,
        val pixKeyDetails: PixKeyDetails,
        val endToEnd: String,
    ) : NewPixCommand()
}

data class ObjectWithMetadata(
    val data: InputStream,
    val contentLength: Long,
)

enum class CashInAmountTier {
    BELOW_FIVE_HUNDRED,
    ABOVE_FIVE_HUNDRED, ;

    companion object {
        fun find(amount: Long): CashInAmountTier =
            if (amount < 500_00) {
                BELOW_FIVE_HUNDRED
            } else {
                ABOVE_FIVE_HUNDRED
            }
    }
}

interface BillInstrumentationService {
    fun scheduled(
        wallet: Wallet,
        accountId: AccountId,
        billsCount: Int,
    )

    fun createdRecurring(recurrence: BillRecurrence)

    fun createdMailbox(bill: Bill)

    fun paid(bill: Bill)
}

interface CashInInstrumentationService {
    fun creditCardSucceeded(transaction: Transaction)

    fun creditCardFailed(transaction: Transaction)

    fun depositReceived(bankOperationExecuted: BankOperationExecuted)
}

interface RegisterInstrumentationService {
    fun completed(
        accountId: AccountId,
        registrationType: RegistrationType,
    )

    fun rejected(
        accountId: AccountId,
        registrationType: RegistrationType,
        deniedReason: List<RiskAnalysisFailedReason>,
    )

    fun upgraded(
        accountId: AccountId,
        registrationType: RegistrationType,
        reason: List<RiskAnalysisFailedReason>,
    )

    fun reopened(
        accountId: AccountId,
        registrationType: RegistrationType,
        deniedReason: List<RiskAnalysisFailedReason>,
    )

    fun externallyRejected(
        accountId: AccountId,
        registrationType: RegistrationType,
    )

    fun activated(
        accountId: AccountId,
        registrationType: RegistrationType,
    )

    fun closed(
        accountId: AccountId,
        accountType: UserAccountType,
        closureDetails: AccountClosureDetails,
    )

    fun accountUpgraded(accountId: AccountId)
}

interface OnePixPayInstrumentation {
    fun requestedToVerifyBillsComingDueForWallet(wallet: Wallet)

    fun accountIsNotSupported(
        wallet: Wallet,
        founder: Account,
        chatbotType: ChatbotType,
    )

    fun decidedToNotifyBillsComingDue(
        wallet: Wallet,
        account: Account,
        bills: List<BillView>,
    )

    fun decidedNotToNotifyBillsComingDue(
        wallet: Wallet,
        account: Account,
        bills: BillsComingDueLists,
    )

    fun decidedNotToNotifyBillsComingDue(
        wallet: Wallet,
        account: Account,
        bills: List<BillView>,
    )

    fun userReceivedADeposit(
        accountId: AccountId,
        deposit: BankStatementItem,
    )

    fun onePixPayFailedToUnscheduleBills(
        wallet: Wallet,
        account: Account,
        bills: List<Bill>,
    )

    fun onePixPayFailed(
        wallet: Wallet,
        account: Account,
        reason: ProcessScheduleError,
    )

    fun onePixPayFullyProcessed(
        wallet: Wallet,
        account: Account,
        onePixPay: OnePixPay,
        bills: List<Bill>,
    )

    fun onePixPayPartiallyProcessed(
        wallet: Wallet,
        account: Account,
        onePixPay: OnePixPay,
    )

    enum class DepositSource {
        CALLBACK,
        QUEUE,
    }
}

interface ScheduledBillPaymentService {
    fun process(
        walletId: WalletId,
        scheduledDate: LocalDate = getZonedDateTime().toLocalDate(),
        includeSubscription: Boolean = true,
    )
}

interface PaymentSchedulingForecastService {
    fun provision(event: BillPaymentScheduled)

    fun deprovision(event: BillPaymentScheduleCanceled)

    fun find(refDate: LocalDate): ScheduleForecastResult
}

interface ZipCodeService {
    fun register(zipCode: ZipCode)
}

interface SystemActivityRepository {
    fun save(
        key: SystemActivityKey,
        activityType: SystemActivityType,
        activityValue: String,
    )

    fun find(
        key: SystemActivityKey,
        activityType: SystemActivityType,
    ): SystemActivity?

    fun find(
        keyType: SystemActivityKeyType,
        activityType: SystemActivityType,
        activityValue: String,
    ): List<String>

    fun findLowerThan(
        keyType: SystemActivityKeyType,
        activityType: SystemActivityType,
        activityValue: String,
    ): List<String>

    fun findBetween(
        keyType: SystemActivityKeyType,
        activityType: SystemActivityType,
        minValue: String,
        maxValue: String,
    ): List<String>
}

interface PixQRCodeCreatorService {
    fun createQRCode(
        key: PixKey,
        document: String,
        amount: Long,
        recipientName: String,
        transactionId: QrCodeTransactionId,
        aditionalInfo: String = "",
    ): String
}

interface PixQRCodeParserService {
    fun parseQRCodeCacheable(
        qrCodeValue: String,
        document: String,
    ): Either<PixKeyError, PixKeyDetailsResult>

    fun parseQRCode(
        qrCodeValue: String,
        document: String,
    ): Either<PixKeyError, PixKeyDetailsResult>
}

interface UserJourneyAdapter {
    fun register(
        accountId: AccountId,
        name: String,
        email: EmailAddress,
        accountType: UserAccountType,
        createdAt: ZonedDateTime,
    )

    fun trackEvent(
        accountId: AccountId,
        eventName: String,
    )
}

interface CreditCardService {
    fun findActiveAndPendingCreditCards(accountId: AccountId): List<AccountPaymentMethod>
}

sealed class CreditCardRetrieveResult : PrintableSealedClassV2() {
    data class Success(
        val cards: List<CreditCard>,
    ) : CreditCardRetrieveResult()

    data class UnknownError(
        val ex: Exception? = null,
    ) : CreditCardRetrieveResult()
}

interface ExternalBankAccountRespository {
    fun saveLastUsed(externalBankAccount: ExternalBankAccount)

    fun findLastUsed(document: Document): ExternalBankAccount?

    fun findAll(document: Document): List<ExternalBankAccount>
}

interface BillComingDueRepository {
    fun save(billComingDue: BillComingDue)

    fun findById(billId: BillId): BillComingDue?

    fun findByEffectiveDueDate(effectiveDueDate: LocalDate): List<BillComingDue>

    fun findByEffectiveDueDateAsync(effectiveDueDate: LocalDate): Flowable<BillComingDue>

    fun findOpenBills(
        effectiveDueDate: LocalDate,
        paymentLimitTime: LocalTime,
    ): Flowable<BillComingDue>
}

interface BillTrackingRepository {
    fun create(
        trackableBill: TrackableBill,
        processingDate: LocalDate,
    )

    fun save(
        trackableBill: TrackableBill,
        billTrackingType: BillTrackingCalculateOptions,
        processingDate: LocalDate,
        partition: Int? = null,
    )

    fun update(
        trackableBill: TrackableBill,
        processingDate: LocalDate,
    )

    fun update(
        trackableBill: TrackableBill,
        billTrackingType: BillTrackingCalculateOptions,
        processingDate: LocalDate,
    )

    fun findById(billId: BillId): Mono<TrackableBill>

    fun remove(billId: BillId)

    fun findBillsByPartition(
        calculateOptions: BillTrackingCalculateOptions,
        date: LocalDate,
        partition: Int,
    ): Flux<TrackableBill>
}

interface UserDDANotificationTrackingRepository {
    fun save(
        accountId: AccountId,
        date: LocalDate,
    )

    fun remove(accountId: AccountId)

    fun findUsersToActivateNotification(date: LocalDate = getLocalDate()): Flux<String>
}

interface BoletoOccurrenceAdapter {
    fun getOccurrences(
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<BoletoOccurrence>
}

interface BoletoOccurrenceRepository {
    fun save(occurrence: BoletoOccurrence)

    fun exists(occurrence: BoletoOccurrence): Boolean
}

interface CreditCardChallengeRepository {
    fun find(
        paymentMethodId: AccountPaymentMethodId,
        status: CreditCardChallengeStatus = CreditCardChallengeStatus.ACTIVE,
    ): CreditCardChallenge?

    fun save(creditCardChallenge: CreditCardChallenge)

    fun findAll(paymentMethodId: AccountPaymentMethodId): List<CreditCardChallenge>
}

interface JobRepository {
    fun findAll(): List<Job>

    fun save(job: Job)
}

interface UtilityAccountRepository {
    fun save(register: UtilityAccount)

    fun findAll(
        walletId: WalletId,
        status: UtilityAccountConnectionStatus,
    ): List<UtilityAccount>

    fun findAllManual(): List<UtilityAccount>

    fun find(
        connectUtilityId: UtilityAccountId,
        status: UtilityAccountConnectionStatus,
    ): UtilityAccount?

    fun find(connectUtilityId: UtilityAccountId): UtilityAccount?

    fun findByIndex(walletId: WalletId): List<UtilityAccount>

    fun findById(
        utilityAccountId: UtilityAccountId,
        walletId: WalletId,
    ): UtilityAccount?

    fun findByIndex(status: UtilityAccountConnectionStatus): List<UtilityAccount>

    fun findByIndex(utility: Utility): List<UtilityAccount>
}

interface OnboardingTestPixRepository {
    fun save(onboardingTestPix: OnboardingTestPix)

    fun findByStatus(onboardingTestPixStatus: OnboardingTestPixStatus): List<OnboardingTestPix>

    fun findByAccountId(accountId: AccountId): OnboardingTestPix?
}

interface PixKeyRepository {
    fun create(
        walletId: WalletId,
        accountNo: AccountNumber,
        pixKey: PixKey,
    )

    fun list(walletId: WalletId): List<PixKey>

    fun delete(
        walletId: WalletId,
        pixKey: PixKey,
    )

    fun pixKeyExists(pixKey: PixKey): Boolean
}

interface PixKeyServiceInterface {
    fun createPixKey(walletId: WalletId, pixKey: PixKey): Either<CreatePixKeyError, Unit>
    fun deletePixKey(walletId: WalletId, pixKey: PixKey): Either<DeletePixKeyError, Unit>
    fun listPixKeys(walletId: WalletId): List<PixKey>
}

interface MsisdnAuthRepository {
    fun save(msisdnAuth: MsisdnAuth)

    fun find(msisdnAuthId: MsisdnAuthId): MsisdnAuth?

    fun findByAccountId(accountId: AccountId): MsisdnAuth?
}

interface KmsService {
    suspend fun encryptData(text: String): String?

    suspend fun decryptData(encryptedDataVal: String): String

    suspend fun decryptData(
        encryptedData: String,
        keyId: String,
    ): String
}

interface ModattaProvider {
    fun updateStatus(
        accountId: AccountId,
        externalId: ExternalId,
        status: AccountStatus,
        riskAnalysisFailedReasons: List<RiskAnalysisFailedReason>? = null,
        approvedBy: SimpleSignUpApprover? = null,
    )

    fun depositPixCallback(bankStatementItem: BankStatementItem)

    fun depositCallback(depositCallback: ModattaDepositCallback)
}

interface MailboxListsService {
    fun findGlobal(type: MailboxListType): List<String>

    fun putGlobal(type: MailboxListType, items: List<String>)

    fun deleteGlobal(type: MailboxListType, email: String)
}

interface NotifyService<T : NotifyRequest> {
    fun notify(request: T)
}

interface BillEventPublisher {
    fun publish(
        bill: Bill,
        event: BillEvent,
    )

    fun storePastBill(vararg events: BillEvent)
}

interface BatchScheduledPublisher {
    fun publish(
        id: BatchSchedulingId,
        results: List<Pair<BillId, Bill?>>,
        scheduleStrategy: ScheduleStrategy,
    )
}

interface DocumentValidationService {
    fun validateWithSelfie(
        document: Document,
        name: String,
        selfieImageStream: InputStream,
    ): Either<Exception, DocumentValidationResponse>

    fun validateWithSelfie(
        documentInfo: DocumentInfo,
        selfieImageStream: InputStream,
    ): Either<Exception, DocumentValidationResponse>
}

interface DocumentValidationResponse {
    fun selfieMatchesDocument(): Boolean?

    fun getPercentage(): Double?
}

interface CreditCardValidationService {
    fun validateOwnership(
        validationRequest: CreditCardOwnershipValidationRequest,
    ): Either<Exception, CreditCardOwnership>
}

interface CreditCardScoreService {
    fun creditcardScore(
        scoreRequest: CreditCardScoreValidationRequest,
    ): Either<Exception, CreditCardScore>
}

data class CreditCardOwnershipValidationRequest(
    val bin: String,
    val lastFourDigits: String,
    val cpf: String,
)

data class CreditCardScoreValidationRequest(
    val bin: String,
    val lastFourDigits: String,
    val cpf: String,
    val totalValue: String,
)

enum class CreditCardOwnership {
    IS_OWNER,
    NOT_OWNER,
    UNKNOWN,
}

enum class CreditCardScore {
    MATCH,
    NO_MATCH,
    MANUAL_CHECK,
}

interface ListFeesService {
    fun listFees(request: ListFeesCommand): Either<Err, FeesWithSummary>
}

data class UndoCaptureFundsCommand(
    val transactionId: TransactionId,
    val source: Source,
) {
    data class Source(
        val provider: FinancialServiceGateway,
        val reason: Reason,
        val details: String?,
    )

    enum class Reason {
        SETTLEMENT_REFUND,
        CAPTURE_FAIL,
    }
}

interface IModattaCampaignAdapter {
    fun redeemCode(code: String): Either<Exception, RedeemCodeResult>
}

interface ProcessScheduledBillsPublisher {
    fun publish(
        walletId: WalletId,
        scheduleDate: LocalDate,
        includeSubscription: Boolean,
    )

    fun publish(
        walletIdList: List<WalletId>,
        scheduleDate: LocalDate,
        includeSubscription: Boolean,
    )
}

interface DDACallbackService {
    fun notifyBillAdded(bankSlipId: String)

    fun notifyBillUpdated(bankSlipId: String)

    fun notifyEventIgnored(bankSlipId: String)

    fun notifyError(bankSlipId: String)
}

interface ScheduleForecastRepository {
    fun processSchedule(
        date: LocalDate,
        amount: Long,
    ): ScheduleForecastResult.Item

    fun processCancelment(
        date: LocalDate,
        amount: Long,
    ): ScheduleForecastResult.Item

    fun processPayment(
        date: LocalDate,
        amount: Long,
    ): ScheduleForecastResult.Item

    fun find(date: LocalDate): ScheduleForecastResult
}

enum class SettlementProvider { CELCOIN }

sealed class ScheduleForecastResult : PrintableSealedClassV2() {
    data class Item(
        val provisionedAmount: Long,
        val scheduledBills: Long,
        val paidBills: Long,
        val paidAmount: Long,
        val settlementProvider: SettlementProvider = SettlementProvider.CELCOIN,
    ) : ScheduleForecastResult()

    data object NotFound : ScheduleForecastResult()
}

interface LimitRepository {
    fun get(
        accountId: AccountId,
        key: String,
    ): Limit?

    fun create(
        accountId: AccountId,
        key: String,
        maxCount: Int,
        expiration: Duration,
    )

    fun increment(
        accountId: AccountId,
        key: String,
    )
}

interface AccountStatementAdapter {
    fun getStatement(
        accountNo: String,
        document: Document,
        initialDate: LocalDate,
        endDate: LocalDate,
        sortType: String = "Movement",
        logBody: Boolean = false,
    ): BankStatement
}

interface AccountBalanceAdapter {
    fun getBalance(
        accountNo: String,
        document: Document,
        logBody: Boolean = false,
    ): String
}

data class AdEventMetadata(
    val accountId: AccountId,
    val trackingIds: DeviceAdIds,
    val clientIP: String,
    val phoneNumber: MobilePhone?,
    val emailAddress: EmailAddress?,
    val state: String?,
)

interface AdAdapter {
    fun trackEvent(
        name: String,
        metadata: AdEventMetadata,
    )
}

interface HMacService {
    fun sign(
        creditCard: String,
        expirationDate: String,
    ): String
}

enum class WelcomeMessageType {
    WELCOME_CHATBOT_ONBOARDING,
    WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX,
}

enum class TestPixReminderType {
    NEXT_DAY,
    LAST_DAY,
    EXPIRED,
}

enum class OpenFinanceIncentiveType {
    DDA,
    CASH_IN,
}

data class ChatBotTransactionId(
    val value: String,
)

@EachProperty("integrations.openfinance.participants")
data class SweepingParticipant @ConfigurationInject constructor(
    val id: String,
    val name: String,
    val compe: String,
    val ispb: String,
    val temporarilyUnavailable: Boolean? = false,
    val restrictedTo: List<String>? = null,
)

data class OpenFinanceParticipantId(
    val value: String = UUID.randomUUID().toString(),
)

data class OFSweepingConsent(
    val participantId: OpenFinanceParticipantId,
    val participantName: String,
    val transactionLimit: Long,
)

data class OFSweepingCashIn(
    val id: SweepingCashInId,
    val participant: SweepingParticipant,
    val amount: Long,
    val status: SweepingCashInStatus,
    val updatedAt: ZonedDateTime,
)

data class ExternalTransactionId(
    val value: String = UUID.randomUUID().toString(),
)

data class SweepingRequest(
    val transactionId: ExternalTransactionId,
    val amount: Long,
)

interface SweepingAccountServiceInterface {
    fun requestSweepingCashIn(command: RequestSweepingCashInCommand): Either<RequestSweepingCashInError, EndToEnd?>
    fun confirmSweepingCashIn(endToEnd: EndToEnd): Either<ConfirmSweepingCashInError, SweepingCashInId>
    fun getLastActiveCashIns(walletId: WalletId): List<OFSweepingCashIn>
}

interface OpenFinanceConsentService {
    fun getParticipants(): List<SweepingParticipant>
    fun getAuthorizedSweepingConsents(walletId: WalletId): List<OFSweepingConsent>
}

data class EndToEnd(override val value: String = UUID.randomUUID().toString()) : OnePixPayIdSource {
    override fun toOnePixPayId() = EndToEndOnePixPayId(this)
}

data class SweepingCashInId(val value: String = UUID.randomUUID().toString())

enum class SweepingCashInSource {
    WEB_APP,
    CHATBOT,
}

data class RequestSweepingCashInCommand(
    val consentId: String?,
    val requestSource: SweepingCashInSource = SweepingCashInSource.WEB_APP,
    val approvalSource: SweepingCashInSource = SweepingCashInSource.WEB_APP,
    val externalTransactionId: ExternalTransactionId? = null,
    val sweepingCashInId: SweepingCashInId,
    val walletId: WalletId,
    val amount: Long,
    val description: String,
)

sealed class RequestSweepingCashInError : PrintableSealedClassV2() {
    data object PaymentNotCreated : RequestSweepingCashInError()
    data class LimitError(val limitType: LimitType) : RequestSweepingCashInError()
    data class GenericError(val message: String) : RequestSweepingCashInError()
    data class ItemNotFound(val message: String) : RequestSweepingCashInError()
    data class ActiveConsentNotFound(val walletId: WalletId) : RequestSweepingCashInError()
    data object Conflict : RequestSweepingCashInError()
}

sealed class ConfirmSweepingCashInError : PrintableSealedClassV2() {
    data class ItemNotFound(val endToEnd: EndToEnd) : ConfirmSweepingCashInError()
    data class GenericError(val message: String) : ConfirmSweepingCashInError()
}

enum class LimitType { DAILY, WEEKLY, MONTHLY, YEARLY, GLOBAL, TRANSACTION, UNKNOWN }

enum class UserEventType { ACCOUNT, UTILITY_ACCOUNT }

enum class UserEventSource { BILL_PAYMENT, API, CHATBOT }

data class UserEvent(
    val entityId: String,
    val entityType: UserEventType,
    val event: String,
    val metadata: Map<String, String> = emptyMap(),
    val timestamp: ZonedDateTime = getZonedDateTime(),
    val source: UserEventSource = UserEventSource.BILL_PAYMENT,
    val correlationId: String = UUID.randomUUID().toString(),
) {
    constructor(accountId: AccountId, event: String, metadata: Map<String, String> = emptyMap(), timestamp: ZonedDateTime = getZonedDateTime(), source: UserEventSource = UserEventSource.BILL_PAYMENT, correlationId: String = UUID.randomUUID().toString()) :
        this(accountId.value, UserEventType.ACCOUNT, event, metadata, timestamp, source, correlationId)

    constructor(utilityAccountId: UtilityAccountId, event: String, metadata: Map<String, String> = emptyMap(), timestamp: ZonedDateTime = getZonedDateTime(), source: UserEventSource = UserEventSource.BILL_PAYMENT, correlationId: String = UUID.randomUUID().toString()) :
        this(utilityAccountId.value, UserEventType.UTILITY_ACCOUNT, event, metadata, timestamp, source, correlationId)
}

interface UserEventService {
    fun save(event: UserEvent)
}

interface Html2ImageConverter {
    fun convert(compiledHtml: CompiledHtml): ByteArray
}

enum class SweepingCashInStatus(val order: Int, val finalExternalStatus: Boolean) {
    CREATED(order = 0, finalExternalStatus = false),
    UNKNOWN(order = 1, finalExternalStatus = false),
    WAITING_SETTLEMENT(order = 2, finalExternalStatus = true),
    FAILED(order = 3, finalExternalStatus = true),
    SUCCESS(order = 4, finalExternalStatus = true),
}