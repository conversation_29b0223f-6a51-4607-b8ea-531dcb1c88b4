package ai.friday.billpayment.app.bill

import ai.friday.billpayment.Err
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityService
import ai.friday.billpayment.app.bill.mailbox.MailboxSecurityValidationErrorLevel
import ai.friday.billpayment.app.bill.mailbox.MailboxValidateRequest
import ai.friday.billpayment.app.integrations.BillInstrumentationService
import ai.friday.billpayment.app.integrations.MailBoxService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import io.via1.communicationcentre.app.receipt.Receipt
import java.time.LocalDate
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
class DefaultMailBoxService(
    private val createBillService: CreateBillService,
    private val fichaCompensacaoService: FichaCompensacaoService,
    private val notificationAdapter: NotificationAdapter,
    private val walletService: WalletService,
    private val billInstrumentationService: BillInstrumentationService,
    private val mailboxSecurityService: MailboxSecurityService,
    private val accountService: AccountService,
) : MailBoxService {

    private val logger = LoggerFactory.getLogger(this::class.java)

    override fun addBill(
        walletId: WalletId,
        barCode: BarCode,
        dueDate: LocalDate?,
        from: EmailAddress,
        subject: String,
        receipt: Receipt?,
        shouldNotifyOnRetryableError: Boolean,
    ): Either<MailboxAddBillError, Unit> {
        val wallet = walletService.findWallet(walletId)
        val member = wallet.allMembers.firstOrNull { it.emailAddress == from }
        val accountId = member?.accountId
        val actionSource = ActionSource.WalletMailBox(from = from.value, accountId = accountId)

        val request = MailboxValidateRequest(from = from, walletId = walletId, barCode = barCode, dueDate = dueDate, receipt = receipt, fromAccountId = accountId)

        val walletFounderAccount = accountService.findAccountById(wallet.founder.accountId)

        val securityValidationResult = mailboxSecurityService.execute(request)

        securityValidationResult.firstOrNull { it.level == MailboxSecurityValidationErrorLevel.BLOCK }?.let {
            logger.warn(append("mailboxValidateRequest", request).andAppend("validationError", it).andAppend("walletFounderAccountId", walletFounderAccount.accountId.value).andAppend("walletId", wallet.id.value), "MailboxSecurityValidationError")
            return it.left()
        }

        if (request.barCode.checkIsConcessionaria() && request.dueDate == null) {
            return MailboxAddBillError.MailBoxAddInvalidRequest("Concessionaria bills must have dueDate").left()
        }

        val fn = if (request.barCode.checkIsConcessionaria()) ::addConcessionaria else ::addFichaCompensacao

        val result = fn(
            request.barCode,
            walletId,
            request.dueDate,
            this.sanitizeSubject(subject),
            member,
            actionSource,
            securityValidationResult,
        )

        val isBillOverdue = request.dueDate?.isBefore(getLocalDate())

        notifyAddBoletoResult(
            result,
            wallet,
            request.from,
            subject,
            actionSource,
            isBillOverdue == true || shouldNotifyOnRetryableError,
        )

        if (result is CreateBillResult.FAILURE) {
            return when (result) {
                is CreateBillResult.FAILURE.BillUnableToValidate -> MailboxAddBillError.MailboxAddInvalidBill(
                    result,
                    isRetryable = if (isBillOverdue != null) !isBillOverdue else result.isRetryable,
                ).left()

                else -> MailboxAddBillError.MailboxAddInvalidBill(result).left()
            }
        }
        if (result is CreateBillResult.SUCCESS && result.warningCode in listOf(null, WarningCode.NONE)) {
            billInstrumentationService.createdMailbox(result.bill)
        }

        return Unit.right()
    }

    private fun addConcessionaria(
        barCode: BarCode,
        walletId: WalletId,
        dueDate: LocalDate?,
        subject: String,
        member: Member?,
        actionSource: ActionSource.WalletMailBox,
        securityValidationErrors: List<MailboxAddBillError.MailboxSecurityValidationError>,
    ) = createBillService.createConcessionaria(
        request = CreateConcessionariaRequest(
            description = subject,
            dueDate = dueDate ?: throw IllegalStateException("Concessionaria bills must have dueDate"),
            walletId = walletId,
            barcode = barCode,
            source = actionSource,
            member = member,
            securityValidationErrors = securityValidationErrors,
        ),
        dryRun = false,
    )

    private fun addFichaCompensacao(
        barCode: BarCode,
        walletId: WalletId,
        dueDate: LocalDate?,
        subject: String,
        member: Member?,
        actionSource: ActionSource.WalletMailBox,
        securityValidationErrors: List<MailboxAddBillError.MailboxSecurityValidationError>,
    ) = fichaCompensacaoService.createFichaDeCompensacao(
        request = CreateFichaDeCompensacaoRequest(
            description = subject,
            walletId = walletId,
            barcode = barCode,
            source = actionSource,
            member = member,
            securityValidationErrors = securityValidationErrors,
        ),
        dryRun = false,
    )

    private fun sanitizeSubject(subject: String) = subject.take(descriptionMaxLength).replace(Regex("[|']"), "_")

    private fun notifyAddBoletoResult(
        addBoletoResult: CreateBillResult,
        wallet: Wallet,
        from: EmailAddress,
        subject: String,
        actionSource: ActionSource.WalletMailBox,
        shouldNotifyOnRetryableError: Boolean,
    ) {
        when (addBoletoResult) {
            is CreateBillResult.SUCCESS -> return
            is CreateBillResult.FAILURE -> {
                val billId = when (addBoletoResult) {
                    is CreateBillResult.FAILURE.BillAlreadyExists -> addBoletoResult.bill.billId
                    else -> null
                }
                val sourceMember = actionSource.accountId?.let { wallet.getActiveMemberOrNull(it) }

                val isRetryableError = addBoletoResult is CreateBillResult.FAILURE.BillUnableToValidate && addBoletoResult.isRetryable
                if (addBoletoResult !is CreateBillResult.FAILURE.ServerError && (!isRetryableError || shouldNotifyOnRetryableError)) {
                    notificationAdapter.notifyAddFailure(
                        members = listOfNotNull(sourceMember).ifEmpty { wallet.getMembersCanViewAll() },
                        billId = billId,
                        source = actionSource,
                        from = from,
                        subject = subject.trim().ifEmpty { "_(sem assunto)_" },
                        walletName = wallet.name,
                        walletId = wallet.id,
                        result = addBoletoResult,
                    )
                }
            }
        }
    }
}

sealed class MailboxAddBillError(message: String = "Mailbox Add Bill Error") : Err(message) {
    open class MailboxAddInvalidBill(val result: CreateBillResult.FAILURE, val isRetryable: Boolean = false) : MailboxAddBillError()

    open class MailboxAccountNotFoundError : MailboxAddBillError()

    open class MailboxSecurityValidationError(val reason: String, val level: MailboxSecurityValidationErrorLevel) : MailboxAddBillError()

    open class MailBoxAddInvalidRequest(message: String) : MailboxAddBillError(message)
}