package ai.friday.billpayment.app.bill

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.hasEarlyAccess
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicateBillService
import ai.friday.billpayment.app.bill.schedule.ScheduleBillService
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.BillValidationService
import ai.friday.billpayment.app.integrations.FeatureConfiguration
import ai.friday.billpayment.app.payment.BillValidationResponse
import ai.friday.billpayment.app.payment.BillValidationStatus
import ai.friday.billpayment.app.payment.ScheduleStrategy
import ai.friday.billpayment.app.payment.UnableToValidateReason
import ai.friday.billpayment.app.sanitizeDocumentNumber
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.canView
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.timeFormat
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.handleErrorWith
import java.time.LocalDate
import java.time.LocalTime
import kotlin.reflect.KClass
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class FichaCompensacaoService(
    private val validationService: BillValidationService,
    private val billEventRepository: BillEventRepository,
    private val updateBillService: UpdateBillService,
    private val billEventPublisher: BillEventPublisher,
    private val featureConfiguration: FeatureConfiguration,
    private val possibleDuplicateBillService: PossibleDuplicateBillService,
    private val walletService: WalletService,
    private val scheduleBillService: ScheduleBillService,
    private val blockEmptyAmountCreationService: BlockEmptyAmountCreationService,
    private val enrichmentRules: List<FichaCompensacaoCreationEnrichmentRule>,
) {
    open fun createFichaDeCompensacao(
        request: CreateFichaDeCompensacaoRequest,
        preemptValidation: BillValidationResponse? = null,
        dueDateToOverride: LocalDate? = null,
        dryRun: Boolean = false,
    ): CreateBillResult {
        val markers = log("dryRun" to dryRun, "request" to request, "preemptValidationStatus" to preemptValidation?.getStatus())

        val validation = preemptValidation ?: validationService.validate(request.barcode)
        markers.and("validationStatus" to validation.getStatus(), "validation" to validation)

        var ignoreAmountPaid: Boolean = false

        if (validation.billRegisterData != null) {
            billEventRepository.findLastBill(request.barcode, request.walletId).handleErrorWith {
                val idNumber = validation.billRegisterData?.idNumber
                if (featureConfiguration.blockDuplicateByIdNumber && idNumber != null) {
                    val lastBill = billEventRepository.findLastBill(
                        idNumber = idNumber,
                        walletId = request.walletId,
                    )

                    if (lastBill.isRight()) {
                        LOG.warn(markers.and("idNumber" to idNumber), "DuplicatedByIdNumber")
                    }

                    lastBill
                } else {
                    Either.Left(it)
                }
            }.map { existingBill ->
                val shouldCreateNewFicha = shouldCreateNewFicha(existingBill, validation)
                markers.and(
                    "existingBill" to existingBill.billId.value,
                    "shouldCreateNewFicha" to shouldCreateNewFicha,
                )
                if (shouldCreateNewFicha) {
                    if (!dryRun) {
                        updateBillService.updateBillNotPayable(existingBill)
                    }
                    ignoreAmountPaid = validation.isCreditCard()
                } else {
                    val memberCanView = request.member.canView(existingBill)

                    markers.and(
                        "shouldCreateNewFicha" to false,
                        "existingBillStatus" to existingBill.status.name,
                        "memberCanView" to memberCanView,
                    )

                    // extracted to a method to avoid nested statements
                    return handleExistingBill(
                        existingBill,
                        request,
                        validation,
                        memberCanView,
                        dryRun,
                    ).also { result ->
                        LOG.info(markers.andAppend("result", result), "createFichaDeCompensacao")
                    }
                }
            }
        }

        return when (validation.getStatus(ignoreAmountPaid = ignoreAmountPaid)) {
            BillValidationStatus.Payable -> processPayableFicha(validation, request, dryRun, dueDateToOverride)

            BillValidationStatus.NotPayable -> CreateBillResult.FAILURE.BillNotPayable(
                description = validation.errorDescription.orEmpty(),
                billRegisterData = validation.billRegisterData,
            )

            BillValidationStatus.PaymentNotAuthorized -> CreateBillResult.FAILURE.BillNotPayable(
                code = AddBillError.PAYMENT_NOT_AUTHORIZED.code,
                description = AddBillError.PAYMENT_NOT_AUTHORIZED.description,
                billRegisterData = validation.billRegisterData,
            )

            is BillValidationStatus.UnableToValidate -> {
                val validationStatus = validation.getStatus() as BillValidationStatus.UnableToValidate

                LOG.info(markers, "createFichaDeCompensacao")

                if (validationStatus.reason == UnableToValidateReason.BARCODE_NOT_FOUND || !validationStatus.shouldRetryValidation) {
                    return CreateBillResult.FAILURE.BillUnableToValidate(
                        validation.errorDescription.orEmpty(),
                        isRetryable = validationStatus.shouldRetryValidation,
                    )
                }

                return CreateBillResult.FAILURE.ServerError(BillValidationException(validation.errorDescription.orEmpty()))
            }

            BillValidationStatus.AlreadyPaid -> processAlreadyPaidFicha(validation, request, dryRun, dueDateToOverride)
        }.also { result ->
            LOG.info(markers.andAppend("result", result), "createFichaDeCompensacao")
        }
    }

    // TODO: what about DDA?
    private val nonReactivableBillSources: Set<KClass<out ActionSource>> = setOf(
        ActionSource.VehicleDebts::class,
    )

    private fun handleExistingBill(
        existingBill: Bill,
        request: CreateFichaDeCompensacaoRequest,
        validation: BillValidationResponse,
        memberCanView: Boolean,
        dryRun: Boolean,
    ): CreateBillResult {
        val billWasIgnored = existingBill.isIgnored()

        // if member could not visualize and bill is being added by them, they get permission
        if (!memberCanView) {
            val billPermissionUpdated = request.toBillPermissionUpdate(existingBill.billId)
            if (dryRun) {
                existingBill.apply(billPermissionUpdated)
            } else {
                billEventPublisher.publish(existingBill, billPermissionUpdated)
            }
        }

        // if existing bill is not ignored
        if (!billWasIgnored) {
            return when (memberCanView) {
                // and member can view return bill already exists
                true -> CreateBillResult.FAILURE.BillAlreadyExists(existingBill)
                // and member could not visualize return not visible bill already exists
                false -> CreateBillResult.SUCCESS(
                    bill = existingBill,
                    warningCode = WarningCode.NOT_VISIBLE_BILL_ALREADY_EXISTS,
                )
            }
        }

        // if bill is ignored it should be reactivated if all conditions are satisfied
        if (request.source::class in nonReactivableBillSources) {
            return CreateBillResult.SUCCESS(bill = existingBill)
        }

        val reactivatedBill = reactivateFichaDeCompensacao(existingBill, request, dryRun)
        if (!dryRun) {
            updateBillService.synchronizeBill(existingBill, validation)
        }
        return CreateBillResult.SUCCESS(bill = reactivatedBill)
    }

    private fun reactivateFichaDeCompensacao(
        existingBill: Bill,
        request: CreateFichaDeCompensacaoRequest,
        dryRun: Boolean,
    ): Bill {
        val billReactivated = BillReactivated(
            billId = existingBill.billId,
            walletId = existingBill.walletId,
            actionSource = request.source,
        )

        if (dryRun) {
            existingBill.apply(billReactivated)
        } else {
            billEventPublisher.publish(existingBill, billReactivated)
        }

        return existingBill
    }

    private fun shouldCreateNewFicha(
        existingBill: Bill,
        validation: BillValidationResponse,
    ): Boolean {
        if (existingBill.dueDate == validation.billRegisterData?.dueDate) {
            return false
        }

        if (validation.getStatus(ignoreAmountPaid = validation.isCreditCard()) != BillValidationStatus.Payable) {
            return false
        }

        if (existingBill.isProcessing()) {
            return false
        }

        if (!AccountId(existingBill.walletId.value).hasEarlyAccess() && validation.isEmptyAmount()) {
            return false
        }

        if (existingBill.registrationUpdateNumber != null && validation.isRegistrationResponseOlderThan(existingBill.registrationUpdateNumber!!)) {
            return false
        }

        if (featureConfiguration.blockDuplicateByIdNumber && existingBill.idNumber != null && existingBill.idNumber == validation.billRegisterData?.idNumber) {
            return validation.isCreditCard()
        }

        return true
    }

    private fun processAlreadyPaidFicha(
        billValidationResponse: BillValidationResponse,
        addFichaDeCompensacaoRequest: CreateFichaDeCompensacaoRequest,
        dryRun: Boolean,
        overriddenDueDate: LocalDate? = null,
    ): CreateBillResult {
        if (dryRun || addFichaDeCompensacaoRequest.source !is ActionSource.DDA) {
            return CreateBillResult.FAILURE.AlreadyPaid.WithData(
                description = billValidationResponse.errorDescription.orEmpty(),
                billRegisterData = billValidationResponse.billRegisterData!!,
            )
        }
        val fichaCompensacaoAdded = applyEnrichmentRules(billValidationResponse.toFichaCompensacaoAdded(addFichaDeCompensacaoRequest, overriddenDueDate))
        updateBillService.storeEvent(Bill.build(), fichaCompensacaoAdded)
        val bill = Bill.build(fichaCompensacaoAdded)
        val synchronizeBillResponse = updateBillService.synchronizeBill(bill, billValidationResponse)

        if (synchronizeBillResponse.status !is BillSynchronizationStatus.BillStatusUpdated || synchronizeBillResponse.status.billStatus != BillStatus.ALREADY_PAID) {
            LOG.error(
                Markers.empty()
                    .andAppend("walletId", addFichaDeCompensacaoRequest.walletId)
                    .andAppend("billId", fichaCompensacaoAdded.billId.value)
                    .andAppend("synchronizeBillResponseStatus", synchronizeBillResponse.status)
                    .andAppend("error", "Already paid bill imported through DDA should have ALREADY_PAID status"),
                "CreateBillService",
            )
        }

        return CreateBillResult.SUCCESS(bill)
    }

    private fun processPayableFicha(
        billValidationResponse: BillValidationResponse,
        addFichaDeCompensacaoRequest: CreateFichaDeCompensacaoRequest,
        dryRun: Boolean,
        dueDateToOverride: LocalDate? = null,
    ): CreateBillResult {
        val markers = Markers.append("billValidationResponseStatus", billValidationResponse.getStatus())
            .andAppend("request", addFichaDeCompensacaoRequest)
            .andAppend("dryRun", dryRun)

        return when {
            addFichaDeCompensacaoRequest.source is ActionSource.DDA && billValidationResponse.isCreditCardOverdue() ->
                CreateBillResult.FAILURE.BillNotPayable(
                    description = AddBillError.CREDIT_CARD_OVERDUE.description,
                    code = AddBillError.CREDIT_CARD_OVERDUE.code,
                    billRegisterData = billValidationResponse.billRegisterData,
                )

            billValidationResponse.isEmptyAmount() && blockEmptyAmountCreationService.check(billValidationResponse, addFichaDeCompensacaoRequest) ->
                CreateBillResult.FAILURE.BillNotPayable(
                    description = AddBillError.AMOUNT_ZERO.description,
                    code = AddBillError.AMOUNT_ZERO.code,
                    billRegisterData = billValidationResponse.billRegisterData,
                )

            billValidationResponse.hasExceededAmountLimit() && (addFichaDeCompensacaoRequest.source is ActionSource.Api || !AccountId(addFichaDeCompensacaoRequest.walletId.value).hasEarlyAccess()) ->
                CreateBillResult.FAILURE.BillNotPayable(
                    description = AddBillError.AMOUNT_EXCEEDED.description,
                    code = AddBillError.AMOUNT_EXCEEDED.code,
                    billRegisterData = billValidationResponse.billRegisterData,
                )

            else -> {
                val fichaCompensacaoAdded = applyEnrichmentRules(billValidationResponse.toFichaCompensacaoAdded(addFichaDeCompensacaoRequest, dueDateToOverride))
                if (!dryRun) {
                    billEventPublisher.publish(Bill.build(), fichaCompensacaoAdded)
                }

                val bill = Bill.build(fichaCompensacaoAdded)
                val possibleDuplicateBills = possibleDuplicateBillService.check(bill)

                if (!dryRun && bill.subscriptionFee) {
                    val wallet = walletService.findWallet(bill.walletId)
                    scheduleBillService.schedulePayment(
                        paymentWallet = wallet,
                        bill = bill,
                        member = wallet.founder,
                        actionSource = ActionSource.System,
                        scheduleStrategy = ScheduleStrategy.ofDueDate(),
                    ).map { scheduledBill ->
                        CreateBillResult.SUCCESS(
                            bill = scheduledBill,
                            warningCode = bill.getWarningCode(),
                            possibleDuplicateBills = possibleDuplicateBills,
                        )
                    }.getOrElse { throw it }
                } else {
                    if (fichaCompensacaoAdded.needsBeneficiaryUpdate()) {
                        CreateBillResult.SUCCESS(
                            bill = bill,
                            possibleDuplicateBills = possibleDuplicateBills,
                            warningCode = WarningCode.BENEFICIARY_UPDATE_REQUIRED,
                        )
                    } else {
                        CreateBillResult.SUCCESS(
                            bill = bill,
                            warningCode = bill.getWarningCode(),
                            possibleDuplicateBills = possibleDuplicateBills,
                        )
                    }
                }
            }
        }.also { result -> LOG.info(markers.and("result" to result), "processPayableFicha") }
    }

    private fun BillValidationResponse.isCreditCardOverdue() = isCreditCard() && billRegisterData!!.dueDate!!.isBefore(getZonedDateTime().toLocalDate())

    private fun CreateBillRequest.toSourceAccountId(): AccountId {
        return source.accountId ?: throw IllegalStateException("invalid action source $source")
    }

    private fun CreateBoletoRequest.toBillPermissionUpdate(billId: BillId): BillPermissionUpdated = BillPermissionUpdated(
        billId = billId,
        created = createdOn.toInstant().toEpochMilli(),
        walletId = walletId,
        actionSource = source,
        permissionUpdated = PermissionUpdated.VisibilityAdded(toSourceAccountId()),
    )

    private fun BillValidationResponse.toFichaCompensacaoAdded(createFichaDeCompensacaoRequest: CreateFichaDeCompensacaoRequest, overriddenDueDate: LocalDate? = null): FichaCompensacaoAdded {
        return FichaCompensacaoAdded(
            billId = createFichaDeCompensacaoRequest.id,
            created = createFichaDeCompensacaoRequest.createdOn.toInstant().toEpochMilli(),
            walletId = createFichaDeCompensacaoRequest.walletId,
            description = createFichaDeCompensacaoRequest.description,
            dueDate = overriddenDueDate ?: billRegisterData!!.dueDate!!,
            amount = billRegisterData!!.amount,
            amountTotal = billRegisterData!!.amountTotal,
            discount = billRegisterData!!.discount,
            fine = billRegisterData!!.fine,
            interest = billRegisterData!!.interest,
            barcode = createFichaDeCompensacaoRequest.barcode,
            recipient = billRegisterData!!.recipient!!.copy(
                document = billRegisterData?.recipient?.document?.sanitizeDocumentNumber(),
            ),
            recipientChain = billRegisterData!!.recipientChain,
            assignor = billRegisterData!!.assignor,
            document = billRegisterData!!.payerDocument!!,
            payerName = billRegisterData?.payerName,
            payerAlias = createFichaDeCompensacaoRequest.payerAlias,
            paymentLimitTime = LocalTime.parse(billRegisterData!!.paymentLimitTime!!, timeFormat),
            lastSettleDate = billRegisterData!!.settleDate!!.format(dateFormat),
            expirationDate = billRegisterData!!.expirationDate!!.format(dateFormat),
            amountCalculationModel = billRegisterData!!.amountCalculationModel,
            actionSource = createFichaDeCompensacaoRequest.source,
            effectiveDueDate =
            calculateEffectiveDate(
                barcode = createFichaDeCompensacaoRequest.barcode,
                billType = BillType.FICHA_COMPENSACAO,
                dueDate = billRegisterData!!.dueDate!!,
            ),
            fichaCompensacaoType = billRegisterData!!.fichaCompensacaoType!!,
            idNumber = billRegisterData!!.idNumber,
            registrationUpdateNumber = billRegisterData!!.registrationUpdateNumber,
            subscriptionFee = false,
            interestData = billRegisterData!!.interestData,
            fineData = billRegisterData!!.fineData,
            discountData = billRegisterData!!.discountData,
            abatement = billRegisterData?.abatement?.toBigDecimal(),
            securityValidationResult = createFichaDeCompensacaoRequest.securityValidationErrors.map { it.reason },
            divergentPayment = billRegisterData!!.divergentPayment,
            partialPayment = billRegisterData!!.partialPayment,
            externalId = createFichaDeCompensacaoRequest.externalBillId,
        )
    }

    private fun applyEnrichmentRules(billAdded: FichaCompensacaoAdded): FichaCompensacaoAdded =
        enrichmentRules.fold(billAdded) { event, rule ->
            runCatching {
                rule.execute(event)
            }.getOrElse { e ->
                LOG.error(log("billId" to billAdded.billId.value, "rule" to rule.javaClass.simpleName), "Error applying enrichment rule", e)
                event
            }
        }

    companion object {
        private val LOG = LoggerFactory.getLogger(FichaCompensacaoService::class.java)
    }
}