package ai.friday.billpayment.app.bill.tracking

import ai.friday.billpayment.Err
import ai.friday.billpayment.ServerError
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.BillTrackingRepository
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface RemoveTrackableBillService {
    fun remove(event: BillEvent): Either<Err, Unit>
}

@FridayMePoupe
class DefaultRemoveTrackableBillService(
    private val repository: BillTrackingRepository,
) : RemoveTrackableBillService {
    private val logger = LoggerFactory.getLogger(DefaultRemoveTrackableBillService::class.java)

    override fun remove(event: BillEvent): Either<Err, Unit> = try {
        val markers = Markers.append("eventType", event.eventType.name)
            .andAppend("billId", event.billId.value)
        logger.info(markers, "DefaultRemoveTrackableBillService#remove")
        repository.remove(event.billId)
        Unit.right()
    } catch (ex: Exception) {
        ServerError(ex).left()
    }
}