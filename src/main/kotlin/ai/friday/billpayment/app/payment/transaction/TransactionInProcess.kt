package ai.friday.billpayment.app.payment.transaction

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.payment.CheckoutLocator
import ai.friday.billpayment.app.payment.Transaction

const val PIX_DELAY_TO_RETRY = 16
const val BOLETO_DELAY_TO_RETRY = 18
const val INVESTMENT_DELAY_TO_RETRY = 5

@FridayMePoupe
class TransactionInProcess(
    private val messagePublisher: MessagePublisher,
    private val checkoutLocator: CheckoutLocator,
) {

    fun execute(transaction: Transaction) {
        val checkout = checkoutLocator.getCheckout(transaction)
        if (checkout.supportsRetryTransaction()) {
            sendToRetryQueue(transaction)
        }
    }

    private fun sendToRetryQueue(transaction: Transaction) {
        with(transaction) {
            val delayToRetry =
                when (transaction.settlementData.getTarget<Bill>().billType) {
                    BillType.PIX -> PIX_DELAY_TO_RETRY
                    BillType.CONCESSIONARIA, BillType.FICHA_COMPENSACAO -> BOLETO_DELAY_TO_RETRY
                    BillType.INVOICE, BillType.OTHERS -> null
                    BillType.INVESTMENT -> INVESTMENT_DELAY_TO_RETRY
                }
            messagePublisher.sendRetryTransactionMessage(id, delayToRetry)
        }
    }
}