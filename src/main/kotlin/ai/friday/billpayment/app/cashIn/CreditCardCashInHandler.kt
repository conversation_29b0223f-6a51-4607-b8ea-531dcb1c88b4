package ai.friday.billpayment.app.cashIn

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.account.DefaultCreditCardService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.AcquirerService
import ai.friday.billpayment.app.integrations.CashInInstrumentationService
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.wallet.WalletService
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
class CreditCardCashInHandler(
    private val acquirerService: AcquirerService,
    private val internalBankService: InternalBankService,
    private val transactionRepository: TransactionRepository,
    private val balanceService: BalanceService,
    private val cashInInstrumentationService: CashInInstrumentationService,
    private val systemActivityService: SystemActivityService,
    private val creditCardService: DefaultCreditCardService,
    private val walletService: WalletService,
    private val notificationAdapter: NotificationAdapter,
) : CashInHandler {
    @field:Property(name = "cashIn.softDescriptor")
    lateinit var softDescriptor: String

    override val supportedTransactionType = TransactionType.CASH_IN

    override fun execute(transaction: Transaction): Transaction {
        try {
            val ccAuthorization = acquirerService.authorizeAndCapture(
                accountId = transaction.payer.accountId,
                orderId = transaction.id.value,
                amount = transaction.settlementData.totalAmount,
                creditCard = transaction.paymentData.toSingle().accountPaymentMethod.method as CreditCard,
                softDescriptor = softDescriptor,
            )

            transaction.apply {
                paymentData.toSingle().payment = ccAuthorization
            }

            when (ccAuthorization.status) {
                CreditCardPaymentStatus.DENIED -> return transaction.apply { status = TransactionStatus.FAILED }
                CreditCardPaymentStatus.ABORTED -> return transaction
                CreditCardPaymentStatus.AUTHORIZED, CreditCardPaymentStatus.PAYMENT_CONFIRMED, CreditCardPaymentStatus.VOIDED, CreditCardPaymentStatus.REFUNDED -> {
                    LOG.warn(
                        Markers.append("whenUnknownBranch", ccAuthorization.status)
                            .andAppend("ccAuthorization", ccAuthorization),
                        "WHENUNKNOWNBRANCH#CreditCardCashinCheckout#execute",
                    )
                }
            }

            return doCashIn(transaction).also {
                balanceService.invalidate(it.settlementData.getTarget<CreditCardCashIn>().bankAccount.id)
            }
        } catch (e: Exception) {
            LOG.error(Markers.append("transactionId", transaction.id.value), "CreditCardCashinCheckoutExecute", e)
            return transaction
        }
    }

    override fun retry(transaction: Transaction): Transaction {
        val currentCreditCardAuthorization = acquirerService.checkStatus(transaction.id.value)
        transaction.apply {
            paymentData.toSingle().payment = currentCreditCardAuthorization
        }

        return when (currentCreditCardAuthorization.status) {
            CreditCardPaymentStatus.ABORTED -> transaction
            CreditCardPaymentStatus.DENIED -> transaction.apply {
                status = TransactionStatus.FAILED
            }

            CreditCardPaymentStatus.PAYMENT_CONFIRMED -> resolveCashInSettlement(transaction)
            else -> {
                LOG.error(
                    Markers.append("transactionId", transaction.id.value).and(
                        Markers.append(
                            "acquirerStatus",
                            currentCreditCardAuthorization,
                        ),
                    ),
                    "CreditCardCashinCheckoutRetry",
                )
                transaction.apply {
                    status = TransactionStatus.FAILED
                }
            }
        }
    }

    private fun resolveCashInSettlement(transaction: Transaction): Transaction {
        if (checkCashInFunds(transaction)) {
            transaction.apply {
                status = TransactionStatus.COMPLETED
                settlementData.settlementOperation =
                    settlementData.getOperation<BankTransfer>().copy(status = BankOperationStatus.SUCCESS)
            }
            return transaction
        }
        return doCashIn(transaction)
    }

    private fun checkCashInFunds(transaction: Transaction): Boolean {
        return transaction.settlementData.settlementOperation?.let {
            internalBankService.checkCashInFunds((it as BankTransfer).operationId, transaction.created.toLocalDate())
        } ?: false
    }

    private fun doCashIn(transaction: Transaction): Transaction {
        val target = transaction.settlementData.getTarget<CreditCardCashIn>()
        val destinationAccountNo = (target.bankAccount.method as InternalBankAccount).buildFullAccountNumber()

        transaction.apply {
            settlementData.settlementOperation =
                internalBankService.prepareCashInFunds(settlementData.getTarget<CreditCardCashIn>().amount)
        }
        transactionRepository.save(transaction)

        val bankTransfer = internalBankService.cashInFunds(
            transaction.settlementData.getOperation<BankTransfer>().operationId,
            destinationAccountNo,
            target.amount,
        )

        transaction.apply {
            settlementData.settlementOperation = bankTransfer
        }

        if (bankTransfer.status != BankOperationStatus.SUCCESS) {
            val markers = Markers.append("transactionId", transaction.id.value)
            if (bankTransfer.status == BankOperationStatus.INSUFFICIENT_FUNDS) {
                markers.andAppend("ACTION", "VERIFY")
            }
            LOG.error(markers, "CreditCardCashinCheckoutExecute")
            return transaction
        }

        return transaction.apply {
            status = TransactionStatus.COMPLETED
        }
    }

    override fun notifyStarted(transaction: Transaction) {
        // nothing here
    }

    override fun notifyCompleted(transaction: Transaction) {
        systemActivityService.setHasCashedIn(transaction.walletId)
        cashInInstrumentationService.creditCardSucceeded(transaction)
    }

    override fun notifyFailed(transaction: Transaction) {
        val cashin = transaction.settlementData.settlementTarget as CreditCardCashIn
        val accountPaymentMethodId = cashin.bankAccount.id
        val accountId = cashin.bankAccount.accountId
        val wallet =
            walletService.findWallets(accountId).single { it.paymentMethodId == accountPaymentMethodId }

        cashInInstrumentationService.creditCardFailed(transaction)

        notificationAdapter.notifyCashInFailure(
            members = listOf(wallet.getActiveMember(transaction.payer.accountId)),
            amount = transaction.settlementData.totalAmount,
            errorMessage = transaction.paymentData.toSingle()
                .get<CreditCardAuthorization>().acquirerReturnMessage,
            walletId = wallet.id,
            walletName = wallet.name,
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(CreditCardCashInHandler::class.java)
    }
}