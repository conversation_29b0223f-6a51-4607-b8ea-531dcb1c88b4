package ai.friday.billpayment.app.job

import ai.friday.billpayment.app.bill.andAppend
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.discovery.event.ServiceReadyEvent
import io.micronaut.discovery.event.ServiceStoppedEvent
import io.micronaut.runtime.event.ApplicationShutdownEvent
import io.micronaut.runtime.event.annotation.EventListener
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import java.time.Duration
import java.time.Instant
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Requires(notEnv = ["test"])
@Singleton
open class JobManager(val jobs: List<AbstractJob>) {

    @field:Property(name = "shedlock.defaults.lock-at-least-for", defaultValue = "PT0S")
    private lateinit var defaultLockAtLeastFor: String

    @field:Property(name = "shedlock.defaults.lock-at-most-for")
    private lateinit var defaultLockAtMostFor: String

    private val shutdownPoolingInterval = 10000L

    fun listJobs(): List<AbstractJob> {
        return jobs.sortedBy { it.jobName }
    }

    @NewSpan
    @EventListener
    open fun onServiceReady(event: ServiceReadyEvent) {
        val markers = Markers.append("jobs", jobs.size)
            .andAppend("defaultLockAtLeastFor", defaultLockAtLeastFor)
            .andAppend("defaultLockAtMostFor", defaultLockAtMostFor)
        LOG.info(markers, "JobManager#onServiceReady")

        jobs.forEach {
            it.initialize(
                defaultLockAtLeastFor = defaultLockAtLeastFor,
                defaultLockAtMostFor = defaultLockAtMostFor,
            )
        }
    }

    @NewSpan
    @EventListener
    open fun onBeginShutdown(event: ApplicationShutdownEvent) {
        LOG.info("JobManager#onBeginShutdown")

        jobs.beginShutdown()
    }

    @NewSpan
    @EventListener
    open fun onServiceStopped(event: ServiceStoppedEvent) {
        LOG.info("JobManager#onServiceStopped")

        val start = getZonedDateTime().toInstant()

        jobs.beginShutdown()

        var waitTime = calcShutdownElapsedTime(start)

        jobs.forEach {
            while (it.running && it.shutdownGracefully && it.shutdownGracefullyMaxWaitTime > waitTime) {
                LOG.warn(Markers.append("jobName", it.jobName), "JobManager#onServiceStopped#waitForRunningJob")
                sleep(shutdownPoolingInterval)

                waitTime = calcShutdownElapsedTime(start)
            }
        }

        jobs.forEach {
            val markers = Markers.append("jobName", it.jobName)
            if (it.running && it.shutdownGracefully && waitTime >= it.shutdownGracefullyMaxWaitTime) {
                LOG.error(markers, "JobManager#onServiceStopped")
            } else {
                LOG.info(markers, "JobManager#onServiceStopped")
            }
            it.unlock()
        }
    }

    private fun List<AbstractJob>.beginShutdown() {
        forEach {
            it.beginShutdown()
        }
    }

    private fun sleep(millis: Long) {
        try {
            Thread.sleep(millis)
        } catch (e: InterruptedException) {
            LOG.warn("JobManager#interrupted", e)
        }
    }

    private fun calcShutdownElapsedTime(start: Instant): Long {
        val now = getZonedDateTime().toInstant()
        return Duration.between(start, now).toMinutes()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(JobManager::class.java)
    }
}

data class Job(
    val name: String,
    val crons: List<String>,
    val fixedDelay: Duration?,
    val running: Boolean,
    val lastStartTime: ZonedDateTime?,
    val lastElapsedMinutes: Long?,
    val shouldLock: Boolean,
    val lockAtLeastFor: Duration,
    val lockAtMostFor: Duration,
    val shutdownGracefully: Boolean,
    val shutdownGracefullyMaxWaitTime: Int,
)