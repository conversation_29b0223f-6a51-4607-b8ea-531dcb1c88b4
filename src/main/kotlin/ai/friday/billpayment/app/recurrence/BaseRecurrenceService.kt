package ai.friday.billpayment.app.recurrence

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.AsyncUtils.callAsync
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.bill.duplication.PossibleDuplicate
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.RecurenceRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.getOrElse
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import java.util.concurrent.CompletionStage
import net.logstash.logback.marker.Markers
import org.slf4j.Logger

abstract class BaseRecurrenceService<Recurrence : BaseRecurrence> (
    protected val recurrenceRepository: RecurenceRepository<Recurrence>,
    protected val accountRepository: AccountRepository,
    protected val walletRepository: WalletRepository,
    recurrenceConfiguration: RecurrenceConfiguration,
) {
    abstract val LOG: Logger
    abstract val canStartInThePast: Boolean

    protected val limitDate = LocalDate.parse(recurrenceConfiguration.limitDate, DateTimeFormatter.ISO_DATE)
    protected val lastLimitDate = LocalDate.parse(recurrenceConfiguration.lastLimitDate, DateTimeFormatter.ISO_DATE)
    fun find(recurrenceId: RecurrenceId, walletId: WalletId): Recurrence {
        return recurrenceRepository.find(recurrenceId, walletId)
    }

    open fun create(recurrence: Recurrence, dryRun: Boolean): Either<RecurrenceCreationError, RecurrenceResult<Recurrence>> {
        recurrence.validate().getOrElse {
            return Either.Left(it)
        }
        val sequence = recurrence.rule.generateDateSequence(limitDate = limitDate)
        val firstDueDate = sequence.first()
        if (!dryRun) {
            return try {
                val updatedRecurrence = generateRecurringInstance(recurrence, firstDueDate, true)
                recurrenceRepository.save(updatedRecurrence)
                callAsync {
                    doCreate(updatedRecurrence, sequence.drop(1))
                }
                Either.Right(
                    RecurrenceResult(
                        updatedRecurrence,
                    ),
                )
            } catch (e: Exception) {
                Either.Left(RecurrenceCreationError.ServerError(e))
            }
        }
        val warningCode = calculateWarningCode(recurrence, firstDueDate)

        val possibleDuplicateBills = getPossibleDuplicates(recurrence)

        return Either.Right(
            RecurrenceResult(
                recurrence = recurrence,
                warningCode = warningCode,
                possibleDuplicateBills = possibleDuplicateBills,
            ),
        )
    }

    fun continueRecurrences() {
        val activeRecurrences = recurrenceRepository.findAll(RecurrenceStatus.ACTIVE)
        val markers = Markers.append("activeRecurrences", activeRecurrences.size)

        val recurrencesToContinue =
            activeRecurrences.filter { recurrence ->
                recurrence.endDateIsBeforeLastLimitDate() && recurrence.founderAccountIsActive()
            }
        markers.andAppend("recurrencesToContinue", recurrencesToContinue.size)

        LOG.info(markers, "ContinueRecurrences")

        recurrencesToContinue.forEach {
            doContinueRecurrence(it)
        }
    }

    private fun doContinueRecurrence(recurrence: Recurrence) {
        val markers = Markers.append("recurrence", recurrence)
        try {
            val sequence =
                recurrence.rule.generateDateSequence(startDate = recurrence.lastDueDate!!, limitDate = limitDate)

            if (sequence.any()) {
                LOG.info(markers, "UpdateRecurrences")
            } else {
                LOG.warn(markers, "UpdateRecurrences")
            }

            sequence.fold(recurrence) { aggregate, current -> generateRecurringInstance(aggregate, current, false) }
        } catch (e: Exception) {
            LOG.error(markers.andAppend("ACTION", "VERIFY"), "UpdateRecurrences", e)
        }
    }

    protected abstract fun generateRecurringInstance(
        recurrence: Recurrence,
        dueDate: LocalDate,
        isFirstRecurrenceInstance: Boolean,
    ): Recurrence

    abstract fun doCreate(recurrence: Recurrence, sequence: List<LocalDate>): CompletionStage<Recurrence>

    protected abstract fun calculateWarningCode(recurrence: Recurrence, firstDueDate: LocalDate): WarningCode?

    protected abstract fun getPossibleDuplicates(recurrence: Recurrence): List<PossibleDuplicate>

    private fun Recurrence.validate(): Either<RecurrenceCreationError, Recurrence> {
        return when {
            this.rule.startDate.isAfter(limitDate) -> Either.Left(RecurrenceCreationError.DueDateAfterLimit)
            !canStartInThePast && this.rule.startDate.isBefore(getZonedDateTime().toLocalDate()) -> Either.Left(
                RecurrenceCreationError.DueDateInThePast,
            )

            else -> Either.Right(this)
        }
    }

    private fun Recurrence.founderAccountIsActive(): Boolean {
        val wallet = walletRepository.findWallet(walletId)
        val founderAccount = accountRepository.findById(wallet.founder.accountId)
        return founderAccount.status == AccountStatus.ACTIVE
    }

    private fun Recurrence.endDateIsBeforeLastLimitDate() =
        (rule.endDate == null || rule.endDate!!.isAfter(lastLimitDate))
}

interface RecurrenceConfiguration {
    val lastLimitDate: String
    val limitDate: String
}

data class RecurrenceResult<Recurrence : BaseRecurrence>(
    val recurrence: Recurrence,
    val warningCode: WarningCode? = null,
    val possibleDuplicateBills: List<PossibleDuplicate> = listOf(),
)

private fun RecurrenceRule.calculateLimitDate(limitDate: LocalDate) = this.endDate?.let {
    if (it.isBefore(limitDate)) {
        it
    } else {
        limitDate
    }
} ?: limitDate

fun RecurrenceRule.generateDateSequence(startDate: LocalDate? = null, limitDate: LocalDate): List<LocalDate> {
    val endDate = this.calculateLimitDate(limitDate)
    var count = 0L

    return generateSequence { this.nextDate(this.startDate, count++) }
        .dropWhile { startDate != null && (it.isBefore(startDate) || it == startDate) }
        .takeWhile { it.isBefore(endDate) || it == endDate }.toList()
}

sealed class RecurrenceCreationError(val message: String) : PrintableSealedClassV2() {
    data object DueDateAfterLimit : RecurrenceCreationError("Duedate must not be after limit date")
    data object DueDateInThePast : RecurrenceCreationError("Duedate must not be in the past")
    class ServerError(val exception: Throwable) : RecurrenceCreationError(exception.localizedMessage)
}