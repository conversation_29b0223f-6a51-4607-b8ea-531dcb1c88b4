package ai.friday.billpayment.app.wallet

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountRegisterData
import ai.friday.billpayment.app.account.CloseAccountResult
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.CloseAccountStepError
import ai.friday.billpayment.app.account.ExternalAccount
import ai.friday.billpayment.app.account.Gender
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.WalletConfiguration
import ai.friday.billpayment.app.pfm.PFMWalletCategoryService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.RegisterPixKeyCommand
import ai.friday.billpayment.app.reports.arbiBankNo
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
class SecondaryWalletService(
    private val walletService: WalletService,
    private val configuration: WalletConfiguration,
    private val messagePublisher: MessagePublisher,
    private val accountRepository: AccountRepository,
    private val externalAccountRegister: ExternalAccountRegister,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val walletBillCategoryService: PFMWalletCategoryService,
    private val eventPublisher: EventPublisher,
    private val closeAccountService: CloseAccountService,
    @Property(name = "sqs.registerPixKeyQueueName") private val registerPixKeyQueueName: String,
) {
    fun create(
        account: Account,
        name: String,
        existingBankAccountNumber: AccountNumber? = null,
    ): Either<CreateWalletErrors, Wallet> {
        val markers =
            Markers
                .append("accountId", account.accountId.value)
                .andAppend("existingBankAccountNumber", existingBankAccountNumber)

        if (account.type == UserAccountType.BASIC_ACCOUNT) {
            return CreateWalletErrors.BasicAccountNotAllowed.left()
        }

        val accountPaymentMethods = accountRepository.findAccountPaymentMethodsByAccountId(account.accountId)

        val wallets = walletService.findWallets(account.accountId)

        if (wallets.filter { it.founder.accountId == account.accountId }.size >= 5) {
            return CreateWalletErrors.MaxWalletsAllowed.left()
        } else if (wallets.any { it.name.equals(name, ignoreCase = true) }) {
            return CreateWalletErrors.WalletNameAlreadyExists.left()
        }

        val accountRegister = accountRegisterRepository.findByAccountId(account.accountId)
        val bankAccountNumber =
            existingBankAccountNumber ?: accountRegister.createExternalBankAccount().getOrElse {
                LOG.error(markers, "BackofficeRegisterService#createSecondaryBankAccount")
                return it.left()
            }
        markers.andAppend("bankAccountNumber", bankAccountNumber)

        val accountPaymentMethod =
            accountRepository.createAccountPaymentMethod(
                accountId = account.accountId,
                bankAccount =
                BankAccount(
                    accountType = AccountType.CHECKING,
                    bankNo = arbiBankNo,
                    routingNo = 1,
                    accountNo = bankAccountNumber.number,
                    accountDv = bankAccountNumber.dv,
                    document = accountRegister.documentInfo!!.cpf,
                    ispb = null,
                ),
                position = accountPaymentMethods.size + 1,
                mode = BankAccountMode.PHYSICAL,
            )

        val founder = createFounder(account)

        val wallet =
            Wallet(
                name = name,
                members = listOf(founder),
                maxOpenInvitations = configuration.maxOpenInvites,
                status = WalletStatus.ACTIVE,
                type = WalletType.SECONDARY,
                paymentMethodId = accountPaymentMethod.id,
            )

        walletService.save(wallet)

        eventPublisher.publish(
            event = MemberAdded(
                walletId = wallet.id,
                member = account.accountId,
                actionSource = ActionSource.Api(wallet.founder.accountId),
            ),
        )

        walletBillCategoryService.createDefaultWalletCategories(wallet.id)

        messagePublisher.sendMessage(
            registerPixKeyQueueName,
            RegisterPixKeyCommand(
                accountNo = bankAccountNumber,
                key = walletService.walletPixKey(wallet = wallet),
                document = founder.document,
                name = founder.name,
                walletId = wallet.id,
            ),
        )

        try {
            messagePublisher.sendMessage(
                registerPixKeyQueueName,
                RegisterPixKeyCommand(
                    accountNo = bankAccountNumber,
                    key = PixKey(value = "", type = PixKeyType.EVP),
                    document = founder.document,
                    name = founder.name,
                    walletId = wallet.id,
                ),
            )
        } catch (e: Exception) {
            LOG.error(markers, "BackofficeRegisterService#createSecondaryBankAccount", e)
            return CreateWalletErrors.RegisterEvpPixKey.left()
        }

        return wallet.right()
    }

    fun close(
        walletId: WalletId,
    ): Either<CloseAccountStepError, CloseAccountResult> {
        val wallet = try {
            walletService.findWallet(walletId)
        } catch (e: Exception) {
            return CloseAccountStepError.GenericError("Wallet not found").left()
        }

        if (wallet.status == WalletStatus.CLOSED) {
            return CloseAccountStepError.GenericError("Wallet already closed").left()
        }

        val closeMethod = if (wallet.type == WalletType.SECONDARY) {
            closeAccountService.closeSecondaryWallet(wallet)
        } else {
            closeAccountService.closeWalletPJ(wallet)
        }

        return closeMethod.fold(
            ifLeft = {
                it.left()
            },
            ifRight = {
                it.right()
            },
        )
    }

    fun createExternalAccount(accountId: AccountId): Either<CreateWalletErrors, AccountNumber> {
        val accountRegister = accountRegisterRepository.findByAccountId(accountId)
        return accountRegister.createExternalBankAccount()
    }

    private fun AccountRegisterData.createExternalBankAccount(): Either<CreateWalletErrors.RegisterNaturalPersonError, AccountNumber> {
        val markers = Markers.append("accountId", accountId.value)

        val response =
            externalAccountRegister.registerNaturalPerson(
                customer =
                ExternalAccount(
                    documentInfo = documentInfo!!,
                    address = address!!,
                    email = emailAddress.value,
                    mobilePhone = mobilePhone!!,
                    politicallyExposed = politicallyExposed!!,
                    monthlyIncome = monthlyIncome!!,
                    calculatedGender = calculatedGender ?: Gender.M,
                ),
            )

        markers.andAppend("response", response)

        return if (response.success) {
            LOG.info(markers, "BackofficeRegisterService#createExternalBankAccount")
            AccountNumber(response.accountNumber!!.toBigInteger(), response.accountDv!!).right()
        } else {
            LOG.error(markers, "BackofficeRegisterService#createExternalBankAccount")
            CreateWalletErrors.RegisterNaturalPersonError.left()
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SecondaryWalletService::class.java)
    }
}

private fun createFounder(account: Account) =
    Member(
        accountId = account.accountId,
        document = account.document,
        name = account.name,
        emailAddress = account.emailAddress,
        type = MemberType.FOUNDER,
        status = MemberStatus.ACTIVE,
        permissions = MemberPermissions.of(MemberType.FOUNDER),
        created = getZonedDateTime(),
        updated = getZonedDateTime(),
    )