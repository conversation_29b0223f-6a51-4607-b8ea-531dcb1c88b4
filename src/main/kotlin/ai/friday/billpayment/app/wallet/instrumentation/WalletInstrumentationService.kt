package ai.friday.billpayment.app.wallet.instrumentation

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.instrumentation.BaseInstrumentationService
import ai.friday.billpayment.app.instrumentation.InstrumentationRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.wallet.MemberRemoved
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
open class WalletInstrumentationService(
    instrumentationRepository: InstrumentationRepository<WalletInstrumentationEvent>,
    val walletRepository: WalletRepository,
) : BaseInstrumentationService<WalletInstrumentationEvent>(instrumentationRepository) {
    fun memberRemoved(memberRemoved: MemberRemoved) {
        val wallet = walletRepository.findWallet(memberRemoved.walletId)
        try {
            publishEventAsync(
                WalletInstrumentationEvent.MemberRemoved.create(
                    wallet = wallet,
                    removedMember = wallet.getMember(memberRemoved.member),
                    removedBy = wallet.getMember((memberRemoved.actionSource as ActionSource.Api).accountId),
                ),
            )
        } catch (exception: Exception) {
            LOG.error(
                Markers.append("walletId", wallet.id).andAppend("member.accountId", memberRemoved.member),
                "memberRemoved",
                exception,
            )
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletInstrumentationService::class.java)
    }
}