package ai.friday.billpayment.app

import io.micronaut.context.annotation.DefaultScope
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

const val FRIDAY_ENV = "friday"

const val ME_POUPE_ENV = "me-poupe"

@Deprecated("Optar por usar o modulo do serviço ou anotar como uma feature que possa ligar e desligar. Dessa forma não escala bem")
@DefaultScope(Singleton::class)
@Requires(env = [FRIDAY_ENV, ME_POUPE_ENV])
@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.CLASS)
annotation class FridayMePoupe