package ai.friday.billpayment.app.email

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.EmailService
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.ParserService
import ai.friday.billpayment.app.mailbox.MailboxGlobalData
import ai.friday.billpayment.app.notification.EmailNotProcessedNotificationRequest
import ai.friday.billpayment.app.notification.NotificationRouterService
import ai.friday.billpayment.app.stripSpacesAndPunctuation
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletStatus
import ai.friday.billpayment.log
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import io.via1.communicationcentre.app.email.EmailParseException
import io.via1.communicationcentre.app.email.IncomingEmail
import io.via1.communicationcentre.app.integrations.EmailSenderService
import io.via1.communicationcentre.app.receipt.Receipt
import io.via1.communicationcentre.app.receipt.SQSEvent
import io.via1.communicationcentre.app.receipt.Status
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

data class AddBoletoFromEmailMessage(
    val accountId: AccountId,
    val walletId: WalletId,
    val barCode: BarCode,
    val dueDate: LocalDate?,
    val from: String,
    val subject: String,
    val receipt: Receipt,
    val emailDestinationPath: String,
)

const val SUCCESS_PATH = "success"
const val FAILURE_PATH = "failure"
const val LOOP_DETECTED_PATH = "loop_detected"
const val ACCOUNT_CLOSED_PATH = "account_closed"
const val DOCUMENT_NOT_FOUND = "document_not_found"
const val NO_BILLS_FOUND_PATH = "no_bills_found"

@FridayMePoupe
open class DefaultEmailService(
    private val billParseService: ParserService,
    private val accountService: AccountService,
    private val emailSenderService: EmailSenderService,
    private val walletService: WalletService,
    private val mailObjectService: MailObjectService,
    private val notificationRouterService: NotificationRouterService,
    @Property(name = "integrations.manual-workflow.emails") private val manualWorkflowEmail: String,
    @Property(name = "features.forwardEmailToManualWorkflow") private val forwardEmailToManualWorkflow: Boolean,
    @Property(name = "communication-centre.forward.sender") private val forwardEmailAddress: String,
    @Property(name = "sqs.addBillFromEmailQueueName") private val addBillFromEmailQueueName: String,
    private val messagePublisher: MessagePublisher,
) : EmailService {

    private val logger = LoggerFactory.getLogger(DefaultEmailService::class.java)

    @NewSpan("process")
    override fun process(event: SQSEvent) {
        val markers = log("mail" to event.mail)

        val receipt = event.receipt
        markers.and("spf_status" to receipt.spfVerdict.status, "virus_status" to receipt.virusVerdict.status, "S3Action" to receipt.action)
        markers.andAppend("veredict_spam_status", receipt.spamVerdict.status)
        markers.andAppend("veredict_virus_status", receipt.virusVerdict.status)
        markers.andAppend("veredict_spf_status", receipt.spfVerdict.status)
        markers.andAppend("veredict_dmarc_status", receipt.dmarcVerdict.status)
        markers.andAppend("veredict_dkim_status", receipt.dkimVerdict.status)

        try {
            val incomingEmailWithMetadata = mailObjectService.retrieveEmailFromS3(receipt.action)
            val incomingEmail = incomingEmailWithMetadata.incomingEmail
            val account = accountService.findLastAccountByDocument(incomingEmail.extractDocument())

            addLogFieldsToMarker(markers, incomingEmailWithMetadata, account.accountId)

            if (Status.FAIL == receipt.virusVerdict.status) {
                markers.and("result" to "email movido para quarentena")
                logger.warn(markers, "IncomingEmailProcessor")
                mailObjectService.moveToQuarantine(receipt.action, account.accountId)
                return
            }

            if (incomingEmail.sender.address == forwardEmailAddress) {
                logger.info(markers.andAppend("result", "loop detected"), "IncomingEmailProcessor")

                mailObjectService.moveToUnprocessed(receipt.action, account.accountId, LOOP_DETECTED_PATH)
                return
            }

            if (account.emailAddress != EmailAddress(incomingEmail.sender.address)) {
                markers.andAppend("status", "email forwarded to owner")
                if (account.closed) {
                    logger.warn(markers.andAppend("closedAccount", true), "IncomingEmailProcessor")

                    val lastUpdate = getLastAccountUpdate(account)

                    if (lastUpdate != null && lastUpdate.plusDays(30) > getZonedDateTime()) {
                        tryForwardMessage(incomingEmail, account.emailAddress, markers)
                    }
                } else {
                    logger.info(markers, "IncomingEmailProcessor")
                    tryForwardMessage(incomingEmail, account.emailAddress, markers)
                }
            }

            if (account.status == AccountStatus.CLOSED) {
                logger.info(markers.andAppend("result", "ignored: account closed"), "IncomingEmailProcessor")

                mailObjectService.moveToUnprocessed(receipt.action, account.accountId, ACCOUNT_CLOSED_PATH)
                return
            }

            return tryToAddBillFromMailBox(markers, incomingEmailWithMetadata.incomingEmail, account, receipt)
        } catch (e: AccountNotFoundException) {
            mailObjectService.moveToUnprocessed(receipt.action, AccountId("ACCOUNT-DOCUMENT_NOT_FOUND"), DOCUMENT_NOT_FOUND)
            logger.error(markers, "IncomingEmailProcessor", e)
            return
        } catch (e: Exception) {
            logger.error(markers, "IncomingEmailProcessor", e)
            throw e
        }
    }

    private fun getLastAccountUpdate(account: Account): ZonedDateTime? {
        val lastUpdate = walletService.findWallets(account.accountId, MemberStatus.REMOVED)
            .firstOrNull { it.checkPrimary(account.accountId, WalletStatus.CLOSED) }
            ?.founder
            ?.updated
        return lastUpdate
    }

    private fun tryToAddBillFromMailBox(
        markers: LogstashMarker,
        incomingEmail: IncomingEmail,
        account: Account,
        receipt: Receipt,
    ) {
        val logName = "IncomingEmailProcessor"
        try {
            val parsedBillList = billParseService.parseBillFrom(incomingEmail)

            if (parsedBillList.isEmpty()) {
                val emailDestinationPath = handleBillNotParsed(incomingEmail, receipt, account, NO_BILLS_FOUND_PATH, markers)

                logger.warn(
                    markers
                        .andAppend("result", "no bill found")
                        .andAppend("emailDestinationPath", emailDestinationPath),
                    logName,
                    EmailParseException("No bill was found on incoming email", incomingEmail.sender.address),
                )
                return
            }

            val processPath = mailObjectService.buildProcessedPath(receipt.action, account.accountId, SUCCESS_PATH)

            parsedBillList.forEach { parsedBill ->
                markers.andAppend("parsedBill", parsedBill)

                messagePublisher.sendMessage(
                    addBillFromEmailQueueName,
                    AddBoletoFromEmailMessage(
                        walletId = account.defaultWalletId(),
                        accountId = account.accountId,
                        barCode = BarCode.ofDigitable(parsedBill.digitableLine),
                        dueDate = getDueDate(parsedBill.dueDate),
                        from = incomingEmail.sender.address,
                        subject = incomingEmail.subject,
                        receipt = receipt,
                        emailDestinationPath = processPath,
                    ),
                )

                logger.info(
                    markers.andAppend("result", "bill added"),
                    logName,
                )
            }

            mailObjectService.moveTo(receipt.action, account.accountId, processPath)
            logger.info(markers.andAppend("emailDestinationPath", processPath), logName)
        } catch (e: Exception) {
            val emailDestinationPath = handleBillNotParsed(incomingEmail, receipt, account, FAILURE_PATH, markers)
            logger.error(markers.andAppend("emailDestinationPath", emailDestinationPath), logName, e)
        }
    }

    private fun tryForwardMessage(incomingEmail: IncomingEmail, emailAddress: EmailAddress, markers: LogstashMarker) {
        try {
            emailSenderService.forward(incomingEmail, emailAddress.toString())
        } catch (e: Exception) {
            logger.warn(markers, "IncomingEmailProcessor", e)
        }
    }

    private fun addLogFieldsToMarker(
        markers: LogstashMarker,
        incomingEmailWithMetadata: IncomingEmailWithMetadata,
        accountId: AccountId,
    ) =
        markers.andAppend("accountId", accountId.value)
            .andAppend("sender", incomingEmailWithMetadata.incomingEmail.sender.address)
            .andAppend("subject", incomingEmailWithMetadata.incomingEmail.subject)
            .andAppend("allAttachmentsSize", incomingEmailWithMetadata.incomingEmail.attachments.size)
            .andAppend("attachmentsSize", incomingEmailWithMetadata.incomingEmail.attachments.filter { it.isPDF }.size)
            .andAppend("emailTotalSize", incomingEmailWithMetadata.contentLength)
            .andAppend(
                "attachmentsName",
                incomingEmailWithMetadata.incomingEmail.attachments.joinToString(separator = ",") { it.fileName },
            )

    private fun handleBillNotParsed(
        incomingEmail: IncomingEmail,
        receipt: Receipt,
        account: Account,
        reason: String,
        markers: LogstashMarker,
    ): String {
        val emailDestinationPath = mailObjectService.moveToUnprocessed(receipt.action, account.accountId, reason)
        if (!MailboxGlobalData.doNotDisturbList.contains(incomingEmail.sender.address)) {
            sendToManualWorkflow(incomingEmail, receipt, markers)
            notifyEmailNotProcessed(account, incomingEmail)
        }
        return emailDestinationPath
    }

    private fun notifyEmailNotProcessed(account: Account, incomingEmail: IncomingEmail) {
        val wallet = walletService.findWallet(account.configuration.defaultWalletId!!)
        notificationRouterService.routeNotification(
            EmailNotProcessedNotificationRequest(
                members = wallet.activeMembers,
                walletName = wallet.name,
                sender = EmailAddress(incomingEmail.sender.address),
                subject = incomingEmail.subject,
                dateTime = getZonedDateTime(),
            ),
        )
    }

    private fun sendToManualWorkflow(incomingEmail: IncomingEmail, receipt: Receipt, markers: LogstashMarker) {
        if (forwardEmailToManualWorkflow) {
            try {
                val document = incomingEmail.recipient.substringBefore("@")
                incomingEmail.subject = "$document : ${incomingEmail.subject}"
                tryForwardMessage(incomingEmail, EmailAddress(manualWorkflowEmail), markers)
            } catch (e: Exception) {
                logger.error(
                    append("S3Action", receipt.action).andAppend("status", "failed to notify manual workflow"),
                    "IncomingEmailProcessor",
                    e,
                )
            }
        }
    }

    private fun getDueDate(dueDate: String?): LocalDate? {
        return if (!dueDate.isNullOrEmpty()) {
            LocalDate.parse(dueDate, DateTimeFormatter.ofPattern("dd/MM/yyyy"))
        } else {
            null
        }
    }
}

fun IncomingEmail.extractDocument() = EmailAddress(this.recipient).recipient.stripSpacesAndPunctuation()