package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import java.util.*

sealed interface BillPaymentNotification {
    val accountId: AccountId?
    val template: NotificationTemplate
}

data class EmailNotification(
    val receiver: EmailAddress,
    override val accountId: AccountId? = null,
    override val template: NotificationTemplate,
    val parameters: Map<String, String>,
) : BillPaymentNotification

data class WhatsappNotification(
    val receiver: MobilePhone,
    override val accountId: AccountId? = null,
    override val template: NotificationTemplate,
    val parameters: List<String> = listOf(),
    val quickReplyButtonsWhatsAppParameter: List<String> = emptyList(),
    val quickRepliesStartIndex: Int? = null,
    val buttonWhatsAppParameter: ButtonWhatsAppParameter? = null,
    val media: NotificationMedia? = null,
) : BillPaymentNotification {
    constructor(
        account: Account,
        template: NotificationTemplate,
        parameters: List<String> = listOf(),
        quickReplyButtonsWhatsAppParameter: List<String> = emptyList(),
        quickRepliesStartIndex: Int? = null,
        buttonWhatsAppParameter: ButtonWhatsAppParameter? = null,
        media: NotificationMedia? = null,
    ) : this(
        receiver = MobilePhone(account.mobilePhone),
        accountId = account.accountId,
        template = template,
        parameters = parameters,
        quickReplyButtonsWhatsAppParameter = quickReplyButtonsWhatsAppParameter,
        quickRepliesStartIndex = quickRepliesStartIndex,
        buttonWhatsAppParameter = buttonWhatsAppParameter,
        media = media,
    )
}

data class NotificationTemplate(val value: String)

sealed interface ButtonWhatsAppParameter {
    val value: String
}

data class ButtonWhatsAppDeeplinkParameter(private val path: String) : ButtonWhatsAppParameter {
    override val value = "app/$path"
}

data class ButtonWhatsAppTrackedDeeplinkParameter(private val path: String, private val event: String) : ButtonWhatsAppParameter {
    override val value = "app/track/$event/${String(encoder.encode("/$path".toByteArray()))}"

    companion object {
        private val encoder = Base64.getUrlEncoder()
    }
}

data class ButtonWhatsAppWebParameter(private val path: String) : ButtonWhatsAppParameter {
    override val value = "web/$path"
}

data class ButtonWhatsAppRawParameter(private val path: String) : ButtonWhatsAppParameter {
    override val value = path
}

sealed class NotificationMedia(val type: NotificationMediaType) {
    data class Document(val url: String, val filename: String, val documentType: String?) : NotificationMedia(NotificationMediaType.DOCUMENT)
    data class Image(val url: String, val imageType: String?, val title: String? = null, val text: String? = null) : NotificationMedia(NotificationMediaType.IMAGE)
    data class Video(val url: String, val videoType: String?) : NotificationMedia(NotificationMediaType.VIDEO)
}

enum class NotificationMediaType {
    DOCUMENT, IMAGE, VIDEO
}