package ai.friday.billpayment.app.notification

import ai.friday.billpayment.app.account.AccountId
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.net.URI

@Singleton
@Requires(property = "modules.push-notification.enabled", value = "false")
class NoopPushNotificationSenderService : PushNotificationSenderService {
    override fun sendNotification(accountId: AccountId, billPaymentNotification: BillPaymentNotification): Result<Unit> {
        return Result.success(Unit)
    }

    override fun sendNotification(accountId: AccountId, content: PushNotificationContent): Result<SendPushNotificationToAccountIdResult> {
        return Result.success(SendPushNotificationToAccountIdResult(sent = 0, failures = 0, disabled = 0))
    }
}

interface PushNotificationSenderService {
    fun sendNotification(
        accountId: AccountId,
        billPaymentNotification: BillPaymentNotification,
    ): Result<Unit>

    fun sendNotification(accountId: AccountId, content: PushNotificationContent): Result<SendPushNotificationToAccountIdResult>
}

data class PushNotificationContent(val title: String, val body: String, val url: URI?, val imageUrl: URI?)

data class SendPushNotificationToAccountIdResult(val sent: Int, val failures: Int, val disabled: Int)