package ai.friday.billpayment.app.msisdnauth

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountClosureDetails
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.IssueTokenMaxLimitExceeded
import ai.friday.billpayment.app.account.RegisterService
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.TokenChannel
import ai.friday.billpayment.app.account.TokenKey
import ai.friday.billpayment.app.account.TokenService
import ai.friday.billpayment.app.account.TokenStillValidException
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.CrmService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.LoginRepository
import ai.friday.billpayment.app.integrations.MsisdnAuthRepository
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.billpayment.app.login.Login
import ai.friday.billpayment.app.login.ProviderName
import ai.friday.billpayment.app.login.ProviderUser
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.usergroups.AccountGroupSelectorType
import ai.friday.billpayment.app.usergroups.UserGroupsSelectorService
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import java.time.Duration
import java.time.ZonedDateTime
import kotlin.random.Random
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

const val TEMPORARY_NICKNAME = "msisdn-guest"

@FridayMePoupe
open class MsisdnAuthService(
    private val tokenService: TokenService,
    private val loginRepository: LoginRepository,
    private val msisdnAuthRepository: MsisdnAuthRepository,
    private val registerService: RegisterService,
    private val accountRepository: AccountRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val closeAccountService: CloseAccountService,
    private val crmService: CrmService,
    private val userGroupsSelectorService: UserGroupsSelectorService,
    @Property(name = "features.inAppSubscription", defaultValue = "0") private val inAppSubscription: Float,
    @Property(name = "token.onboarding.testMobilePhones") private val signUpMobilePhones: List<String>,
    @Property(name = "token.onboarding.appleMobilePhones") private val appleMobilePhones: List<String>,
) {
    open fun issueToken(
        mobilePhone: MobilePhone,
        channel: TokenChannel,
    ): Either<IssueTokenError, IssuedToken> {
        try {
            val logins =
                loginRepository.findUserLogin(emailAddress = createTemporaryEmail(mobilePhone))

            val login = removeLegacyGuestLogins(logins)

            val (accountId, msisdnId) = if (login == null) {
                val accountId = AccountId()
                val msisdnAuth = createMsisdnLogin(accountId, mobilePhone)

                accountId to msisdnAuth.id
            } else {
                val msisdnAuth = msisdnAuthRepository.findByAccountId(login.accountId)
                login.accountId to msisdnAuth.let {
                    if (it != null) {
                        it.id
                    } else {
                        val msisdnAuthId = createMsisdnAuth(login.accountId).id
                        msisdnAuthId
                    }
                }
            }

            val issuedToken = tokenService.issueToken(mobilePhone, accountId, channel)
                .getOrElse {
                    if (it is TokenStillValidException) {
                        return IssuedToken(msisdnId, duration = it.duration, cooldown = it.cooldown).right()
                    }

                    if (it is IssueTokenMaxLimitExceeded) {
                        return IssuedToken(msisdnId, duration = Duration.ZERO, cooldown = Duration.ZERO).right()
                    } else {
                        return IssueTokenError.Error(it).left()
                    }
                }

            return IssuedToken(msisdnId, duration = issuedToken.duration, cooldown = issuedToken.cooldown).right()
        } catch (e: Exception) {
            return IssueTokenError.Error(e).left()
        }
    }

    private fun removeLegacyGuestLogins(logins: List<Login>): Login? {
        if (logins.isEmpty()) {
            return null
        }
        try {
            val accountId = logins.single().accountId
            val guest = accountRepository.findPartialAccountByIdOrNull(accountId)
            val accountRegister = accountRegisterRepository.findByAccountId(accountId)

            if (guest !== null && guest.role == Role.GUEST && accountRegister.registrationType == RegistrationType.FULL) {
                closeAccountService.closePartialAccount(
                    accountId,
                    AccountClosureDetails.create(
                        reason = null,
                        description = "Cadastro encerrado por ser legado",
                    ),
                )
                logger.info(append("accountId", accountId.value), "removeLegacyGuestLogins")
                return null
            }
        } catch (_: Exception) {
        }
        return logins.single()
    }

    open fun validateToken(validateTokenRequest: ValidateTokenRequest): Either<Exception, Pair<MobilePhone, AccountId>> {
        val msisdnAuth =
            msisdnAuthRepository.find(validateTokenRequest.otpId)
                ?: throw ItemNotFoundException("Invalid OtpTokenKey")

        val tokenKey = TokenKey(accountId = msisdnAuth.accountId, value = validateTokenRequest.token)

        val tokenData = tokenService.validateToken(tokenKey, TokenType.MOBILE_PHONE).getOrElse {
            tokenService.validateToken(tokenKey, TokenType.WHATSAPP).getOrElse {
                return it.left()
            }
        }

        val mobilePhone = MobilePhone(tokenData.value)

        val account = accountRepository.findByIdOrNull(msisdnAuth.accountId)

        if (account != null) {
            crmService.upsertContact(account)
        } else {
            accountRepository.findPartialAccountByIdOrNull(msisdnAuth.accountId)?.let {
                crmService.upsertContact(it)
            }
        }

        try {
            val accountRegister = accountRegisterRepository.findByAccountId(msisdnAuth.accountId)

            accountRegisterRepository.save(accountRegister.copy(mobilePhoneVerified = true))
        } catch (ex: Exception) {
            logger.warn(append("accountId", msisdnAuth.accountId), "MsisdnAuthService#validateToken", ex)
        }

        return (mobilePhone to msisdnAuth.accountId).right()
    }

    private fun checkIsTestAccount(mobilePhone: MobilePhone): Boolean {
        return signUpMobilePhones.contains(mobilePhone.msisdn) || appleMobilePhones.contains(mobilePhone.msisdn)
    }

    private fun createMsisdnAuth(accountId: AccountId): MsisdnAuth {
        val msisdnAuth = MsisdnAuth(
            id = MsisdnAuthId(),
            accountId = accountId,
            createdAt = ZonedDateTime.now(),
        )
        msisdnAuthRepository.save(msisdnAuth)
        return msisdnAuth
    }

    private fun createMsisdnLogin(
        accountId: AccountId,
        mobilePhone: MobilePhone,
    ): MsisdnAuth {
        val temporaryEmailAddress = createTemporaryEmail(mobilePhone)
        loginRepository.createLogin(
            providerUser = ProviderUser(
                id = accountId.value,
                providerName = ProviderName.MSISDN,
                username = TEMPORARY_NICKNAME,
                emailAddress = temporaryEmailAddress,
            ),
            id = accountId,
            role = Role.GUEST,
        )

        loginRepository.createLogin(
            providerUser = ProviderUser(
                id = accountId.value,
                providerName = ProviderName.WHATSAPP,
                username = "",
                emailAddress = createWhatsappEmail(mobilePhone),
            ),
            id = accountId,
            role = Role.GUEST,
        )

        val msisdnAuth = createMsisdnAuth(accountId)
        msisdnAuthRepository.save(msisdnAuth)

        val accountGroups = userGroupsSelectorService.selectGroups(AccountGroupSelectorType.REGISTER).toMutableList()

        /**
         val accountGroups = if (Random.nextFloat() <= 0.5) {
         mutableListOf(AccountGroup.AB_TEST_SIGN_UP)
         } else {
         mutableListOf()
         }

         if (accountGroups.isEmpty() && Random.nextFloat() <= 0.5) {
         accountGroups.add(AccountGroup.IN_APP_SUBSCRIPTION_PAYWALL_TEST)
         }
         */

        val subscriptionType = when {
            checkIsTestAccount(mobilePhone) -> SubscriptionType.IN_APP
            accountGroups.contains(AccountGroup.INVESTMENT_CAMPAIGN) -> SubscriptionType.PIX
            else -> SubscriptionType.random(inAppSubscription)
        }

        accountRepository.create(
            username = TEMPORARY_NICKNAME,
            emailAddress = temporaryEmailAddress,
            accountId = accountId,
            registrationType = RegistrationType.BASIC,
            groups = accountGroups + randomAccountGroup(),
            subscriptionType = subscriptionType,
        )

        registerService.createAccountRegister(
            accountId = accountId,
            emailAddress = temporaryEmailAddress,
            username = TEMPORARY_NICKNAME,
            mobilePhone = mobilePhone,
            registrationType = RegistrationType.BASIC,
        )
        registerService.createLivenessId(accountId).getOrElse {
            logger.error(append("livenessError", it).andAppend("accountId", accountId.value), "createMsisdnLogin")
        }

        return msisdnAuth
    }

    private val logger = LoggerFactory.getLogger(MsisdnAuthService::class.java)

    companion object {
        fun randomAccountGroup(): List<AccountGroup> {
            return if (Random.nextFloat() < 0.5) listOf(AccountGroup.IN_APP_SUBSCRIPTION_PRICE_TEST) else emptyList()
        }
    }
}

sealed class IssueTokenError : PrintableSealedClassV2() {
    class Error(val exception: Exception) : IssueTokenError()
}

data class IssuedToken(
    val otpId: MsisdnAuthId,
    val duration: Duration,
    val cooldown: Duration,
)

data class ValidateTokenRequest(
    val otpId: MsisdnAuthId,
    val token: String,
)