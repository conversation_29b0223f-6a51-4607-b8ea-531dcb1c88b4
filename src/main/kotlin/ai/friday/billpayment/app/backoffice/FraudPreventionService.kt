package ai.friday.billpayment.app.backoffice

import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.balance.DefaultBalanceService
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.cashIn.CreditCardCashIn
import ai.friday.billpayment.app.integrations.AccountRegisterRepository
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BankAccountService
import ai.friday.billpayment.app.integrations.CreditCardChallengeRepository
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.FraudPreventionPaymentOperationDenied
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import io.micronaut.context.annotation.Property
import java.time.Instant
import java.time.LocalDate
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
class FraudPreventionService(
    private val accountRepository: AccountRepository,
    private val accountRegisterRepository: AccountRegisterRepository,
    private val creditCardChallengeRepository: CreditCardChallengeRepository,
    private val transactionRepository: TransactionRepository,
    private val bankAccountService: BankAccountService,
    private val walletRepository: WalletRepository,
    private val balanceService: DefaultBalanceService,
    private val walletService: WalletService,
    private val internalBankRepository: InternalBankRepository,
    @Property(name = "settlementFundsTransfer.fraudFundsAccount") private val fraudFundsAccount: String,
) {
    fun transferAllFundsFromAccount(
        from: AccountId,
        cpf: String,
    ): Either<TransferFundsErrors, BankTransfer> {
        val markers = Markers.append("accountId", from.value)
        return try {
            val account = accountRepository.findById(from)

            if (account.document != cpf) {
                TransferFundsErrors.InvalidCPF.left()
            } else {
                val balance = balanceService.getAccountBalance(account.accountId, walletStatus = null)
                markers.andAppend("balance", balance)

                if (balance.amount <= 0) {
                    TransferFundsErrors.AccountHasNoBalance.left()
                } else {
                    val paymentMethods = accountRepository.findAccountPaymentMethodsByAccountId(from)
                    markers.andAppend("paymentMethods", paymentMethods)

                    val fromAccount = paymentMethods.single {
                        it.method.type == PaymentMethodType.BALANCE
                    }.method as InternalBankAccount
                    markers.andAppend("fromAccount", fromAccount)

                    bankAccountService.transfer(fromAccount.buildFullAccountNumber(), fraudFundsAccount, balance.amount).right()
                }
            }
        } catch (ex: AccountNotFoundException) {
            TransferFundsErrors.AccountNotFound.left()
        } catch (ex: ArbiAccountMissingPermissionsException) {
            TransferFundsErrors.MissingBankAccountPermissions.left()
        } catch (ex: Exception) {
            logger.error(markers, "transferAllFundsFromAccount", ex)
            TransferFundsErrors.UnknownError(ex.message ?: "").left()
        }.also { either ->
            either.map {
                markers.andAppend("result", it)
                logger.info(markers, "transferAllFundsFromAccount")
            }.getOrElse {
                markers.andAppend("result", it)
                logger.warn(markers, "transferAllFundsFromAccount")
            }
        }
    }

    fun captureAllFundsFromAccount(
        from: AccountId,
        cpf: String,
    ): Either<TransferFundsErrors, BankTransfer> {
        val markers = Markers.append("from", from)
        try {
            val account = accountRepository.findById(from)

            markers.andAppend("account", account)

            if (account.document != cpf) {
                return TransferFundsErrors.InvalidCPF.left()
            }

            val (accountNumber, accountPaymentMethodId) = accountRepository.findAccountPaymentMethodsByAccountId(
                accountId = from,
            ).filter {
                it.method is InternalBankAccount
            }.map {
                (it.method as InternalBankAccount).buildFullAccountNumber() to it.id
            }.first()

            markers.andAppend("accountNumber", accountNumber).andAppend("accountPaymentMethodId", accountPaymentMethodId)

            val balance = balanceService.getBalanceFrom(
                accountId = from,
                accountPaymentMethodId = accountPaymentMethodId,
                accountPaymentMethodStatus = null,
            ).amount

            markers.andAppend("balance", balance)

            logger.info(markers, "FraudPreventionService#captureAllFundsFromAccount")

            return bankAccountService.transfer(accountNumber, fraudFundsAccount, balance).right()
        } catch (ex: AccountNotFoundException) {
            logger.error(markers, "FraudPreventionService#captureAllFundsFromAccount", ex)
            return TransferFundsErrors.AccountNotFound.left()
        } catch (ex: Exception) {
            logger.error(markers, "FraudPreventionService#captureAllFundsFromAccount", ex)
            return TransferFundsErrors.UnknownError(ex.message ?: "").left()
        }
    }

    fun accountSummary(accountId: AccountId): String {
        val accountData = StringBuilder()
        val walletData = StringBuilder()
        val creditCardData = StringBuilder()
        val challengeData = StringBuilder()
        val cashInCreditCardData = StringBuilder()
        val cashInData = StringBuilder()
        val walletDate = StringBuilder()
        val cashOutData = StringBuilder()
        val resumoData = StringBuilder()

        accountData.appendLine("AccountId, WalletId, Documento, Numero Conta Arbi, Nome, Telefone, Cidade")
        walletData.appendLine("AccountId, WalletId, PaymentMethodId, Nome, Status")
        creditCardData.appendLine("AccountId, PaymentMethodId, CreditCardToken, ExpiryDate, MaskedPan, Brand, Status, Created")
        challengeData.appendLine("AccountId, PaymentMethodId, Tentativas, Status, Valor cobrado, Created, Updated, Expires")
        cashOutData.appendLine("WalletId, PayerAccountId, PaymentMethodId, Created, BillId, Tipo, Valor, Transaction Status, Bill Status, Data do pagamento, Origem da conta, Documento do pagador, Nome do pagador, Documento do Recebedor, Nome do Recebedor, Instituição do recebedor, Agencia e conta do recebedor, AccountId do recebedor")
        cashInCreditCardData.appendLine("TransactionType, WalletId, PayerAccountId, PaymentMethodId, Created, TransactionId, AcquirerName, AcquirerTid, AcquirerReturnCode, Valor, Status, AcquirerReturnMessage")
        cashInData.appendLine("Amount,CounterPartName,CounterPartDocument,Date,Description,Type")
        resumoData.appendLine("WalletId, Valor de cash-in, Valor de cash-out, Saldo no Arbi")

        val register = try {
            accountRegisterRepository.findByAccountId(accountId)
        } catch (e: Exception) {
            null
        }

        val wallets = walletRepository.findWallets(
            accountId = accountId,
            memberStatus = MemberStatus.ACTIVE,
        ) + walletRepository.findWallets(
            accountId = accountId,
            memberStatus = MemberStatus.REMOVED,
        )

        val founderWallets = wallets.filter {
            it.founder.accountId == accountId
        }

        accountRepository.findAccountPaymentMethodsByAccountId(accountId).filter {
            it.method.type == PaymentMethodType.CREDIT_CARD
        }.sortedByDescending {
            it.created
        }.forEach { paymentMethod ->
            val creditCard = paymentMethod.method as CreditCard
            creditCardData.appendLine("${paymentMethod.accountId.value}, ${paymentMethod.id.value}, ${creditCard.token?.value}, ${creditCard.expiryDate}, ${creditCard.maskedPan}, ${creditCard.brand}, ${paymentMethod.status.name}, ${paymentMethod.created}")

            creditCardChallengeRepository.findAll(paymentMethodId = paymentMethod.id).forEach { challenge ->
                challengeData.appendLine("${paymentMethod.accountId.value}, ${paymentMethod.id.value},${challenge.attempts}, ${challenge.status.name},${challenge.value},${challenge.createdAt},${challenge.updatedAt},${challenge.expiresAt}")
            }
        }

        walletDate.appendLine("=== WALLETS ========")
        founderWallets.forEach { wallet ->
            walletData.appendLine("${wallet.founder.accountId.value}, ${wallet.id.value}, ${wallet.paymentMethodId.value}, ${wallet.name}, ${wallet.status}")

            val bankAccount = accountRepository.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId = wallet.paymentMethodId,
                accountId = wallet.founder.accountId,
            ).method as InternalBankAccount

            accountData.appendLine("${wallet.founder.accountId.value}, ${wallet.id.value}, ${wallet.founder.document}, ${bankAccount.buildFullAccountNumber()}, ${wallet.founder.name}, ${register?.mobilePhone?.msisdn}, ${register?.address?.city}")

            val transactions = transactionRepository.findTransactions(wallet.id)
            transactions.sortedByDescending {
                it.created
            }.forEach { transaction ->
                if (transaction.type == TransactionType.CASH_IN) {
                    val paymentData = transaction.paymentData.toSingle().get<CreditCardAuthorization>()
                    cashInCreditCardData.appendLine("${transaction.type.name}, ${wallet.id.value}, ${transaction.payer.accountId.value}, ${transaction.paymentData.toSingle().accountPaymentMethod.id.value}, ${transaction.created}, ${paymentData.transactionId}, ${paymentData.acquirer.name}, ${paymentData.acquirerTid}, ${paymentData.acquirerReturnCode}, ${paymentData.amount}, ${paymentData.status}, ${paymentData.acquirerReturnMessage}")
                } else {
                    when (transaction.paymentData) {
                        is MultiplePaymentData -> TODO("MultiplePayment não está disponível")
                        NoPaymentData -> throw IllegalStateException("NoPaymentData não pode ser usado aqui")
                        is SinglePaymentData -> {
                            if (transaction.paymentData.details is PaymentMethodsDetailWithCreditCard) {
                                if (transaction.paymentData.payment is CreditCardAuthorization) {
                                    val paymentData = transaction.paymentData.payment as CreditCardAuthorization
                                    cashInCreditCardData.appendLine("${transaction.type.name}, ${wallet.id.value}, ${transaction.payer.accountId.value}, ${transaction.paymentData.toSingle().accountPaymentMethod.id.value}, ${transaction.created}, ${paymentData.transactionId}, ${paymentData.acquirer.name}, ${paymentData.acquirerTid}, ${paymentData.acquirerReturnCode}, ${transaction.paymentData.details.totalAmount}, ${paymentData.status}, ${paymentData.acquirerReturnMessage}")
                                }
                                if (transaction.paymentData.payment is FraudPreventionPaymentOperationDenied) {
                                    val paymentData =
                                        transaction.paymentData.payment as FraudPreventionPaymentOperationDenied
                                    cashInCreditCardData.appendLine("${transaction.type.name}, ${wallet.id.value}, ${transaction.payer.accountId.value}, ${transaction.paymentData.toSingle().accountPaymentMethod.id.value}, ${transaction.created}, BLOQUEADO_NA_FRIDAY, BLOQUEADO_NA_FRIDAY, BLOQUEADO_NA_FRIDAY, BLOQUEADO_NA_FRIDAY, ${transaction.paymentData.details.totalAmount}, ${paymentData.status()}, BLOQUEADO_NA_FRIDAY")
                                }
                            }
                        }
                    }
                }
            }

            transactions.filter { transaction ->
                transaction.type != TransactionType.CASH_IN
            }.sortedByDescending {
                it.created
            }.forEach { transaction ->
                val bill =
                    transaction.settlementData.getTarget<Bill>()
                val recipientDocument = bill.recipient?.document
                val recipientAccountId = recipientDocument?.let {
                    try {
                        accountRegisterRepository.findByDocument(recipientDocument).accountId
                    } catch (e: Exception) {
                        null
                    }
                }

                cashOutData.appendLine(
                    "${wallet.id.value}, ${transaction.payer.accountId.value}, ${transaction.paymentData.toSingle().accountPaymentMethod.id.value}, ${transaction.created}, ${bill.billId.value}, ${bill.billType.name}, ${bill.amountTotal}, ${transaction.status.name}, ${bill.status.name}, ${
                    bill.paidDate?.let {
                        ZonedDateTime.ofInstant(
                            Instant.ofEpochMilli(it),
                            brazilTimeZone,
                        )
                    } ?: ""
                    }, ${bill.source.javaClass.simpleName},${bill.payer?.document ?: ""}, ${bill.payer?.name ?: bill.assignor ?: ""}, ${bill.recipient?.document ?: ""}, ${bill.recipient?.name ?: ""}, ${
                    instituicaoRecebedor(
                        bill.recipient,
                    )
                    }, ${agEContaRecebedor(bill.recipient)}, ${recipientAccountId?.value ?: ""}",
                )
            }

            val statementsData = internalBankRepository.findAllBankStatementCredits(
                wallet.paymentMethodId,
                LocalDate.MIN,
                getZonedDateTime().toLocalDate(),
            )

            statementsData.forEach {
                cashInData.appendLine(
                    "${it.amount},${it.counterpartName},${it.counterpartDocument},${it.date.format(
                        dateFormat,
                    )},${it.description},${it.type.name}",
                )
            }

            val allCashInValue =
                transactions.filter { it.status == TransactionStatus.COMPLETED && it.type == TransactionType.CASH_IN }
                    .sumOf {
                        val settlementData = it.settlementData.getTarget<CreditCardCashIn>()
                        settlementData.amount
                    } + statementsData.sumOf { it.amount }

            val allCashOutValue =
                transactions.filter { it.status == TransactionStatus.COMPLETED && it.type != TransactionType.CASH_IN }
                    .sumOf {
                        val bill =
                            it.settlementData.getTarget<Bill>()
                        bill.amountTotal
                    }

            val arbiBalance = try {
                bankAccountService.getBalance(bankAccount.buildFullAccountNumber())
            } catch (e: Exception) {
                null
            }

            resumoData.appendLine("${wallet.id.value},  $allCashInValue, $allCashOutValue, $arbiBalance")
        }

        val result = StringBuilder()
        result.appendLine("accountData")
        result.appendLine(accountData)

        result.appendLine("walletData")
        result.appendLine(walletData)

        result.appendLine("creditCardData")
        result.appendLine(creditCardData)

        result.appendLine("challengeData")
        result.appendLine(challengeData)

        result.appendLine("cashInData creditCard")
        result.appendLine(cashInCreditCardData)

        result.appendLine("cashInData")
        result.appendLine(cashInData)

        result.appendLine("cashOutData")
        result.appendLine(cashOutData)

        result.appendLine("resumoData")
        result.appendLine(resumoData)

        return result.toString()
    }

    private fun instituicaoRecebedor(recipient: Recipient?): String {
        if (recipient?.pixKeyDetails != null) {
            return "${recipient.pixKeyDetails.holder.institutionName} (${recipient.pixKeyDetails.holder.ispb})"
        }
        if (recipient?.bankAccount != null) {
            return "${recipient.bankAccount.bankNo} (${recipient.bankAccount.ispb})"
        }
        return ""
    }

    private fun agEContaRecebedor(recipient: Recipient?): String {
        if (recipient?.pixKeyDetails != null) {
            return "${recipient.pixKeyDetails.holder.routingNo}/${recipient.pixKeyDetails.holder.accountNo}-${recipient.pixKeyDetails.holder.accountDv}"
        }
        if (recipient?.bankAccount != null) {
            return "${recipient.bankAccount.routingNo}/${recipient.bankAccount.accountNo}-${recipient.bankAccount.accountDv}"
        }
        return ""
    }

    companion object {
        private val logger = LoggerFactory.getLogger(FraudPreventionService::class.java)
    }
}

sealed class TransferFundsErrors() {
    data object AccountNotFound : TransferFundsErrors()
    data object MissingBankAccountPermissions : TransferFundsErrors()
    data object AccountHasNoBalance : TransferFundsErrors()
    data object InvalidCPF : TransferFundsErrors()

    class UnknownError(val message: String) : TransferFundsErrors()
}