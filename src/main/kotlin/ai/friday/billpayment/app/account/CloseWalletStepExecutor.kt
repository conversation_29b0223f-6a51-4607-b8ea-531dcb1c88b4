package ai.friday.billpayment.app.account

import ai.friday.billpayment.PrintableSealedClassV2
import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.adapters.arbi.ArbiInvalidAccountException
import ai.friday.billpayment.adapters.arbi.ArbiInvalidBalanceException
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.backoffice.FraudPreventionService
import ai.friday.billpayment.app.backoffice.TransferFundsErrors
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.PixKeyManagement
import ai.friday.billpayment.app.integrations.UtilityAccountRepository
import ai.friday.billpayment.app.onboarding.OnboardingTestPixService
import ai.friday.billpayment.app.onboarding.RefundOnboardingTestPixError
import ai.friday.billpayment.app.payment.PaymentSchedulingService
import ai.friday.billpayment.app.pix.DeletePixKeyError
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyService
import ai.friday.billpayment.app.recurrence.BackOfficeRecurrenceService
import ai.friday.billpayment.app.utilityaccount.UtilityAccountConnectionStatus
import ai.friday.billpayment.app.wallet.BackOfficeWalletService
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletType
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnore
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

abstract class CloseWalletStepExecutor(
    val stepType: CloseWalletStepType,
) {
    private val logger = LoggerFactory.getLogger(CloseWalletStepExecutor::class.java)

    fun execute(wallet: Wallet): Either<CloseWalletStepError, CloseWalletStepStatus> {
        val markers = append("stepType", this.stepType)
            .andAppend("walletId", wallet.id.value)
            .andAppend("accountId", wallet.founder.accountId.value)

        return try {
            doExecute(wallet, markers)
        } catch (e: Exception) {
            logger.error(markers, "CloseWalletStepExecutor", e)
            CloseWalletStepError.GenericException(e).left()
        }.also {
            markers.andAppend("stepResult", it)
            it.map {
                logger.info(markers, "CloseWalletStepExecutor")
            }.getOrElse {
                logger.warn(markers, "CloseWalletStepExecutor")
            }
        }
    }

    protected abstract fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus>
}

data class CloseWalletStepType(val value: String)

val CloseWalletStepTypeIgnoreRecurrences = CloseWalletStepType("IgnoreRecurrences")
val CloseWalletStepTypeCancelAllSchedules = CloseWalletStepType("CancelAllSchedules")
val CloseWalletStepTypeDeleteWalletPixKey = CloseWalletStepType("DeleteWalletPixKey")
val CloseWalletStepTypeDeleteOldPixKey = CloseWalletStepType("DeleteOldPixKey")
val CloseWalletStepTypeDisconnectUtilityAccounts = CloseWalletStepType("DisconnectUtilityAccounts")
val CloseWalletStepTypeTransferAllFundsFromAccount = CloseWalletStepType("TransferAllFundsFromAccount")
val CloseWalletStepTypeCloseExternalAccountLater = CloseWalletStepType("CloseExternalAccountLater")
val CloseWalletStepTypeRefundOnboardingTestPix = CloseWalletStepType("RefundOnboardingTestPix")
val CloseWalletStepTypeCloseExternalAccountNow = CloseWalletStepType("CloseExternalAccountNow")
val CloseWalletStepTypeDeleteCustomPixKeys = CloseWalletStepType("DeleteCustomPixKeys")
val CloseWalletStepTypeCloseAndRemoveMembers = CloseWalletStepType("CloseAndRemoveMembers")

fun CloseWalletStepType.toStep(status: CloseWalletStepStatus = CloseWalletStepStatus.Pending) = CloseWalletStep(this, status)

data class CloseWalletStep(
    val type: CloseWalletStepType,
    var status: CloseWalletStepStatus = CloseWalletStepStatus.Pending,
    var errorMessage: String? = null,
)

sealed class CloseWalletStepError : PrintableSealedClassV2() {
    data class GenericError(val error: String) : CloseWalletStepError()
    data class GenericException(
        @JsonIgnore // para nao quebrar o log
        val exception: Exception,
    ) : CloseWalletStepError() {
        val reason = exception.javaClass.simpleName
        val message = exception.message
    }
}

enum class CloseWalletStepStatus {
    Pending,
    Success,
    Warning,
    Error,
}

@FridayMePoupe
class DeleteOldPixKeyStepExecutor(
    private val pixKeyManagement: PixKeyManagement,
    private val fingerprintService: DeviceFingerprintService,
    private val walletService: WalletService,
) : CloseWalletStepExecutor(CloseWalletStepTypeDeleteOldPixKey) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        if (wallet.type != WalletType.PRIMARY) {
            markers.andAppend("stepError", "walletTypeNotPrimary")
            return CloseWalletStepStatus.Warning.right()
        }
        val result = fingerprintService.withRealOrTemporaryDeviceId(accountId = wallet.founder.accountId, walletId = wallet.id) { deviceId ->
            pixKeyManagement.deleteKey(
                key = "${wallet.founder.document}@via1.app",
                document = Document(wallet.founder.document),
                deviceId = deviceId,
            )
        }
        return result.map {
            CloseWalletStepStatus.Success.right()
        }.getOrElse {
            markers.andAppend("stepError", it)
            CloseWalletStepStatus.Warning.right()
        }
    }
}

@FridayMePoupe
class DeleteWalletPixKeyStepExecutor(
    private val pixKeyManagement: PixKeyManagement,
    private val walletService: WalletService,
    private val deviceFingerprintService: DeviceFingerprintService,
) : CloseWalletStepExecutor(CloseWalletStepTypeDeleteWalletPixKey) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        val result = deviceFingerprintService.withRealOrTemporaryDeviceId(accountId = wallet.founder.accountId, walletId = wallet.id) { deviceId ->
            pixKeyManagement.deleteKey(
                key = walletService.walletPixKey(wallet).value,
                document = Document(wallet.founder.document),
                deviceId = deviceId,
            )
        }
        return result.map {
            CloseWalletStepStatus.Success.right()
        }.getOrElse {
            markers.andAppend("stepError", it)

            when (it) {
                PixKeyError.KeyNotFound -> CloseWalletStepStatus.Warning.right()
                PixKeyError.KeyNotConfirmed -> CloseWalletStepStatus.Warning.right()
                PixKeyError.MalformedKey -> CloseWalletStepStatus.Warning.right()
                PixKeyError.SystemUnavailable -> CloseWalletStepError.GenericError(it.toString()).left()
                PixKeyError.UnknownError -> CloseWalletStepError.GenericError(it.toString()).left()
                PixKeyError.InvalidQrCode -> CloseWalletStepError.GenericError(it.toString()).left()
            }
        }
    }
}

@FridayMePoupe
class CloseExternalAccountNowStepExecutor(
    private val accountService: AccountService,
    private val externalAccountRegister: ExternalAccountRegister,
    private val backOfficeAccountService: BackOfficeAccountService,
    private val closeExternalAccountLaterStepExecutor: CloseExternalAccountLaterStepExecutor,
) : CloseWalletStepExecutor(CloseWalletStepTypeCloseExternalAccountNow) {
    private val logger = LoggerFactory.getLogger(CloseExternalAccountNowStepExecutor::class.java)

    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        return executeHandlingError(wallet, markers) {
            closeExternalAccountLaterStepExecutor.execute(wallet).map {
                CloseWalletStepStatus.Warning.right()
            }.getOrElse { error ->
                markers.andAppend("stepError", error)
                error.left()
            }
        }
    }

    fun executeWithoutRedrive(wallet: Wallet): Either<CloseWalletStepError, CloseWalletStepStatus> {
        val markers = Markers.append("stepType", this.stepType)
            .andAppend("walletId", wallet.id.value)

        return try {
            executeHandlingError(wallet, markers) {
                CloseWalletStepError.GenericException(it).left()
            }
        } catch (e: Exception) {
            CloseWalletStepError.GenericException(e).left()
        }.also {
            if (it.isRight()) {
                logger.info(markers, "CloseExternalAccountNowStepExecutor#executeWithoutRedrive")
            } else {
                logger.warn(markers, "CloseExternalAccountNowStepExecutor#executeWithoutRedrive")
            }
        }
    }

    private fun executeHandlingError(wallet: Wallet, markers: LogstashMarker, handleError: (exception: Exception) -> Either<CloseWalletStepError, CloseWalletStepStatus>): Either<CloseWalletStepError, CloseWalletStepStatus> {
        val accountPaymentMethod =
            accountService.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId = wallet.paymentMethodId,
                accountId = wallet.founder.accountId,
            )

        val paymentMethod = accountPaymentMethod.method as InternalBankAccount

        if (paymentMethod.bankAccountMode == BankAccountMode.PHYSICAL) {
            val accountNumber = AccountNumber(paymentMethod.buildFullAccountNumber())
            externalAccountRegister.close(accountNumber).getOrElse {
                markers.andAppend("stepError", it.message)
                logger.warn(append("walletId", wallet.id.value), "CloseExternalAccountNowStepExecutor#executeHandlingError", it)

                return handleError(it)
            }
        }

        backOfficeAccountService.closePaymentMethod(wallet.paymentMethodId, wallet.founder.accountId)

        return CloseWalletStepStatus.Success.right()
    }
}

@FridayMePoupe
class DisconnectUtilityAccountsStepExecutor(
    private val utilityAccountRepository: UtilityAccountRepository,
) : CloseWalletStepExecutor(CloseWalletStepTypeDisconnectUtilityAccounts) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        utilityAccountRepository.findAll(wallet.id, UtilityAccountConnectionStatus.CONNECTED).forEach {
            utilityAccountRepository.save(it.copy(status = UtilityAccountConnectionStatus.DISCONNECTED))
        }
        return CloseWalletStepStatus.Success.right()
    }
}

@FridayMePoupe
class CancelAllSchedulesStepExecutor(
    private val schedulingService: PaymentSchedulingService,
) : CloseWalletStepExecutor(CloseWalletStepTypeCancelAllSchedules) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        schedulingService.cancelAllSchedules(wallet.id)
        return CloseWalletStepStatus.Success.right()
    }
}

@FridayMePoupe
class IgnoreRecurrencesStepExecutor(
    private val backOfficeRecurrenceService: BackOfficeRecurrenceService,
) : CloseWalletStepExecutor(CloseWalletStepTypeIgnoreRecurrences) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        backOfficeRecurrenceService.ignoreRecurrences(wallet.id)
        return CloseWalletStepStatus.Success.right()
    }
}

@FridayMePoupe
class TransferAllFundsFromAccountStepExecutor(
    private val fraudPreventionService: FraudPreventionService,
) : CloseWalletStepExecutor(CloseWalletStepTypeTransferAllFundsFromAccount) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        return fraudPreventionService.transferAllFundsFromAccount(
            from = wallet.founder.accountId,
            cpf = wallet.founder.document,
        ).map { bankTransfer ->
            when (bankTransfer.status) {
                BankOperationStatus.SUCCESS -> CloseWalletStepStatus.Success.right()
                else -> CloseWalletStepError.GenericError("${bankTransfer.status.name} - ${bankTransfer.errorDescription}").left()
            }
        }.getOrElse { error ->
            markers.andAppend("stepError", error)
            when (error) {
                TransferFundsErrors.AccountHasNoBalance -> CloseWalletStepStatus.Success.right()
                TransferFundsErrors.AccountNotFound -> CloseWalletStepError.GenericError(error.toString()).left()
                TransferFundsErrors.InvalidCPF -> CloseWalletStepError.GenericError(error.toString()).left()
                is TransferFundsErrors.UnknownError -> CloseWalletStepError.GenericError(error.message).left()
                TransferFundsErrors.MissingBankAccountPermissions -> CloseWalletStepStatus.Warning.right()
            }
        }
    }
}

@FridayMePoupe
class CloseExternalAccountLaterStepExecutor(
    private val backOfficeAccountService: BackOfficeAccountService,
    private val messagePublisher: MessagePublisher,
    @Property(name = "sqs.closeExternalAccountQueueName") private val closeExternalAccountQueueName: String,
) : CloseWalletStepExecutor(CloseWalletStepTypeCloseExternalAccountLater) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        backOfficeAccountService.setAccountPaymentMethodStatus(accountPaymentMethodId = wallet.paymentMethodId, accountId = wallet.founder.accountId, status = AccountPaymentMethodStatus.PENDING_CLOSE)

        messagePublisher.sendMessage(
            closeExternalAccountQueueName,
            CloseExternalAccountEvent(
                wallet.id.value,
            ),
            delaySeconds = 900, // FIXME - somente 15 minutos
        )

        return CloseWalletStepStatus.Success.right()
    }
}

@FridayMePoupe
class RefundOnboardingTestPixStepExecutor(
    private val onboardingTestPixService: OnboardingTestPixService,
    private val balanceService: BalanceService,
    private val accountService: AccountService,
) : CloseWalletStepExecutor(CloseWalletStepTypeRefundOnboardingTestPix) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        val accountPaymentMethod =
            accountService.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId = wallet.paymentMethodId,
                accountId = wallet.founder.accountId,
            )

        val balance = getBalanceOrNull(accountPaymentMethod)
        markers.andAppend("balance", balance)

        if (balance == null || balance == 0L) {
            return CloseWalletStepStatus.Warning.right()
        }

        return onboardingTestPixService.refundFriday(wallet, balance).map {
            CloseWalletStepStatus.Success.right()
        }.getOrElse { error ->
            markers.andAppend("stepError", error)
            when (error) {
                RefundOnboardingTestPixError.CONFLICT -> CloseWalletStepStatus.Warning.right()
                RefundOnboardingTestPixError.FUNDS_NOT_TRANSFERRED -> CloseWalletStepError.GenericError(error.toString()).left()
            }
        }
    }

    private fun getBalanceOrNull(accountPaymentMethod: AccountPaymentMethod): Long? {
        return try {
            balanceService.getBalanceFrom(
                accountId = accountPaymentMethod.accountId,
                accountPaymentMethodId = accountPaymentMethod.id,
                accountPaymentMethodStatus = null,
            ).amount
        } catch (e: ArbiInvalidAccountException) {
            null
        } catch (_: ArbiAccountMissingPermissionsException) {
            null
        } catch (_: ArbiInvalidBalanceException) {
            null
        }
    }
}

@FridayMePoupe
class DeleteCustomWalletPixKeysStepExecutor(
    private val pixKeyService: PixKeyService,
) : CloseWalletStepExecutor(CloseWalletStepTypeDeleteCustomPixKeys) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        pixKeyService.listPixKeys(wallet.id).forEach {
            pixKeyService.deletePixKey(wallet.id, it).getOrElse { error ->
                markers.andAppend("stepError", error)
                return when (error) {
                    DeletePixKeyError.AccountNotFound -> CloseWalletStepStatus.Warning.right()
                    DeletePixKeyError.PixKeyNotFound -> CloseWalletStepStatus.Warning.right()
                    is DeletePixKeyError.ServerError -> CloseWalletStepError.GenericError(error.message).left()
                }
            }
        }

        return CloseWalletStepStatus.Success.right()
    }
}

@FridayMePoupe
class CloseAndRemoveMembersStepExecutor(
    private val backOfficeWalletService: BackOfficeWalletService,
) : CloseWalletStepExecutor(CloseWalletStepTypeCloseAndRemoveMembers) {
    override fun doExecute(wallet: Wallet, markers: LogstashMarker): Either<CloseWalletStepError, CloseWalletStepStatus> {
        backOfficeWalletService.closeAndRemoveMembers(wallet)
        return CloseWalletStepStatus.Success.right()
    }
}