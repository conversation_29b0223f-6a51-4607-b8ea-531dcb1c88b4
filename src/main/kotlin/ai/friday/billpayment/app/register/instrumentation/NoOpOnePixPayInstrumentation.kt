package ai.friday.billpayment.app.register.instrumentation

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.ChatbotType
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.integrations.OnePixPayInstrumentation
import ai.friday.billpayment.app.notification.BillsComingDueLists
import ai.friday.billpayment.app.onepixpay.OnePixPay
import ai.friday.billpayment.app.onepixpay.ProcessScheduleError
import ai.friday.billpayment.app.wallet.Wallet
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton

@Singleton
@Requires(notEnv = [FRIDAY_ENV, ME_POUPE_ENV])
class NoOpOnePixPayInstrumentation : OnePixPayInstrumentation {
    override fun requestedToVerifyBillsComingDueForWallet(wallet: Wallet) {}

    override fun accountIsNotSupported(wallet: Wallet, founder: Account, chatbotType: ChatbotType) {}

    override fun decidedToNotifyBillsComingDue(wallet: Wallet, account: Account, bills: List<BillView>) {}

    override fun decidedNotToNotifyBillsComingDue(wallet: Wallet, account: Account, bills: BillsComingDueLists) {}

    override fun decidedNotToNotifyBillsComingDue(wallet: Wallet, account: Account, bills: List<BillView>) {}

    override fun userReceivedADeposit(accountId: AccountId, deposit: BankStatementItem) {}

    override fun onePixPayFailedToUnscheduleBills(wallet: Wallet, account: Account, bills: List<Bill>) {}

    override fun onePixPayFailed(wallet: Wallet, account: Account, reason: ProcessScheduleError) {}

    override fun onePixPayFullyProcessed(wallet: Wallet, account: Account, onePixPay: OnePixPay, bills: List<Bill>) {}

    override fun onePixPayPartiallyProcessed(wallet: Wallet, account: Account, onePixPay: OnePixPay) {}
}