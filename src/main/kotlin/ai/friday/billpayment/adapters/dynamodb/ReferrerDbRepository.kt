package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.ReferrerRepository
import ai.friday.billpayment.app.referrer.ReferrerRegister
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

private const val ReferrerIndexPartitionKey = "REFERRER_REGISTER"

@FridayMePoupe
class ReferrerDbRepository(private val dynamoDbDAO: DynamoDbDAO) : ReferrerRepository {

    override fun save(referrerRegister: ReferrerRegister) {
        val entity = ReferrerEntity().apply {
            primaryKey = referrerRegister.accountId.value
            scanKey = ReferrerIndexPartitionKey
            accountId = referrerRegister.accountId.value
            created = referrerRegister.created.format(DateTimeFormatter.ISO_DATE_TIME)
            referrer = referrerRegister.referrer
            referrerUrl = referrerRegister.referrerUrl
            platform = referrerRegister.platform
            appVersion = referrerRegister.appVersion
            index1HashKey = ReferrerIndexPartitionKey
            index1RangeKey = referrerRegister.platform
        }
        dynamoDbDAO.save(entity)
    }

    override fun find(accountId: AccountId): ReferrerRegister? {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKey(
            primaryKey = accountId.value,
            scanKey = ReferrerIndexPartitionKey,
            ReferrerEntity::class.java,
        ).firstOrNull()?.toReferrer()
    }
}

private fun ReferrerEntity.toReferrer() = ReferrerRegister(
    accountId = AccountId(this.accountId),
    created = ZonedDateTime.parse(this.created, DateTimeFormatter.ISO_DATE_TIME),
    referrer = this.referrer,
    referrerUrl = this.referrerUrl,
    platform = this.platform,
    appVersion = this.appVersion,
)

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class ReferrerEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // ACCOUNT-ID

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // REFERRER_REGISTER

    @DynamoDBAttribute(attributeName = "AccountId")
    lateinit var accountId: String

    @DynamoDBAttribute(attributeName = "Created")
    lateinit var created: String

    @DynamoDBAttribute(attributeName = "Referrer")
    var referrer: String? = null

    @DynamoDBAttribute(attributeName = "ReferrerUrl")
    var referrerUrl: String? = null

    @DynamoDBAttribute(attributeName = "Platform")
    lateinit var platform: String

    @DynamoDBAttribute(attributeName = "AppVersion")
    lateinit var appVersion: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var index1HashKey: String // REFERRER_REGISTER

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var index1RangeKey: String
}