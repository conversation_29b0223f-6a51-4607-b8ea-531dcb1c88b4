package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.quod.ValidateCreditCardScoreRequestTO
import ai.friday.billpayment.adapters.quod.ValidateCreditCardScoreResponseTO
import ai.friday.billpayment.adapters.quod.ValidateOwnershipRequestTO
import ai.friday.billpayment.adapters.quod.ValidateOwnershipResponseTO
import ai.friday.billpayment.app.FridayMePoupe
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped

@FridayMePoupe
class CreditCardValidationDbRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) {
    private val prefix = "CREDIT_CARD_OWNERSHIP_VALIDATION"
    fun save(creditCardOwnershipValidation: CreditCardOwnershipValidation) {
        val maskedPan =
            "${creditCardOwnershipValidation.request.authReportRequest.searchBy.ccbin}#${creditCardOwnershipValidation.request.authReportRequest.searchBy.creditCardNumber}"
        val entity = CreditCardOwnershipValidationResponseEntity().apply {
            this.partitionKey = "$prefix#${creditCardOwnershipValidation.request.authReportRequest.searchBy.cpf}"
            this.scanKey = "$maskedPan#${creditCardOwnershipValidation.timestamp}"
            this.maskedPan = maskedPan
            this.timestamp = creditCardOwnershipValidation.timestamp.toString()
            this.requestJson = getObjectMapper().writeValueAsString(creditCardOwnershipValidation.request)
            this.responseJson = getObjectMapper().writeValueAsString(creditCardOwnershipValidation.response)
            this.version = creditCardOwnershipValidation.apiVersion
        }

        dynamoDbDAO.save(entity)
    }

    fun find(cpf: String, bin: String?, lastFourDigits: String?): List<CreditCardOwnershipValidation> {
        val entity = bin?.let {
            dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
                partitionKey = "$prefix#$cpf",
                scanKey = "$bin#${lastFourDigits.orEmpty()}",
                type = CreditCardOwnershipValidationResponseEntity::class.java,
                scanIndexForward = false,
            )
        } ?: dynamoDbDAO.queryTableOnHashKey(
            "$prefix#$cpf",
            type = CreditCardOwnershipValidationResponseEntity::class.java,
        )

        return entity.map {
            CreditCardOwnershipValidation(
                apiVersion = it.version,
                request = getObjectMapper().readValue(it.requestJson, ValidateOwnershipRequestTO::class.java),
                response = getObjectMapper().readValue(it.responseJson, ValidateOwnershipResponseTO::class.java),
                timestamp = it.timestamp.toLong(),
            )
        }
    }
}

@FridayMePoupe
class CreditCardScoreDbRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) {
    private val prefix = "CREDIT_CARD_SCORE_VALIDATION"
    fun save(creditCardScoreValidation: CreditCardScoreValidation) {
        val mask =
            "${creditCardScoreValidation.request.authCCScoreRequest.searchBy.ccbin}#${creditCardScoreValidation.request.authCCScoreRequest.searchBy.creditCardNumber}"
        val entity = CreditCardOwnershipValidationResponseEntity().apply {
            this.partitionKey = "$prefix#${creditCardScoreValidation.request.authCCScoreRequest.searchBy.cpf}"
            this.scanKey = "$mask#${creditCardScoreValidation.timestamp}"
            this.maskedPan = mask
            this.timestamp = creditCardScoreValidation.timestamp.toString()
            this.requestJson = getObjectMapper().writeValueAsString(creditCardScoreValidation.request)
            this.responseJson = getObjectMapper().writeValueAsString(creditCardScoreValidation.response)
            this.version = creditCardScoreValidation.apiVersion
        }

        dynamoDbDAO.save(entity)
    }

    fun find(cpf: String, bin: String?, lastFourDigits: String?): List<CreditCardScoreValidation> {
        val entity = bin?.let {
            dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
                partitionKey = "$prefix#$cpf",
                scanKey = "$bin#${lastFourDigits.orEmpty()}",
                type = CreditCardOwnershipValidationResponseEntity::class.java,
                scanIndexForward = false,
            )
        } ?: dynamoDbDAO.queryTableOnHashKey(
            "$prefix#$cpf",
            type = CreditCardOwnershipValidationResponseEntity::class.java,
        )

        return entity.map {
            CreditCardScoreValidation(
                apiVersion = it.version,
                request = getObjectMapper().readValue(it.requestJson, ValidateCreditCardScoreRequestTO::class.java),
                response = getObjectMapper().readValue(it.responseJson, ValidateCreditCardScoreResponseTO::class.java),
                timestamp = it.timestamp.toLong(),
            )
        }
    }
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class CreditCardOwnershipValidationResponseEntity {

    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var maskedPan: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var timestamp: String

    @DynamoDBAttribute(attributeName = "RequestJson")
    lateinit var requestJson: String

    @DynamoDBAttribute(attributeName = "ResponseJson")
    lateinit var responseJson: String

    @DynamoDBAttribute(attributeName = "Version")
    lateinit var version: String
}

data class CreditCardOwnershipValidation(
    val apiVersion: String,
    val request: ValidateOwnershipRequestTO,
    val response: ValidateOwnershipResponseTO,
    val timestamp: Long,
)

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class CreditCardScoreResponseEntity {

    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var maskedPan: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var timestamp: String

    @DynamoDBAttribute(attributeName = "RequestJson")
    lateinit var requestJson: String

    @DynamoDBAttribute(attributeName = "ResponseJson")
    lateinit var responseJson: String

    @DynamoDBAttribute(attributeName = "Version")
    lateinit var version: String
}

data class CreditCardScoreValidation(
    val apiVersion: String,
    val request: ValidateCreditCardScoreRequestTO,
    val response: ValidateCreditCardScoreResponseTO,
    val timestamp: Long,
)