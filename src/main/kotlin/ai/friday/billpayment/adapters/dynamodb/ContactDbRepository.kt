package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.contact.BankAccountId
import ai.friday.billpayment.app.contact.Contact
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.contact.InvalidationCode
import ai.friday.billpayment.app.contact.LastUsed
import ai.friday.billpayment.app.contact.SavedBankAccount
import ai.friday.billpayment.app.contact.SavedPixKey
import ai.friday.billpayment.app.integrations.ContactRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import com.amazonaws.services.dynamodbv2.model.ReturnValue
import com.amazonaws.services.dynamodbv2.model.UpdateItemRequest
import java.time.LocalDateTime

// DO NOT CHANGE: those are serialized as map keys
const val idKey = "Id"
const val typeKey = "Type"
const val bankNoKey = "BankNo"
const val routingNoKey = "RoutingNo"
const val accountNoKey = "AccountNo"
const val accountDvKey = "AccountDv"
const val invalidatedKey = "Invalidated"
const val invalidationMessageKey = "InvalidationMessage"
const val invalidationCodeKey = "InvalidationCode"
const val recipientPrefix = "RECIPIENT#"
const val bankISPBKey = "BankISPB"

private const val billIdsAttributeName = "BillIds"
private const val recurrenceIdsAttributeName = "RecurrenceIds"

@FridayMePoupe
class ContactDbRepository(private val dynamoDbDAO: DynamoDbDAO) : ContactRepository {

    override fun save(recipient: Contact) {
        val entity = ContactEntity().apply {
            contactId = recipient.id.value
            this.accountId = recipient.accountId.value
            gSIndex1PrimaryKey = recipient.accountId.value
            gSIndex1ScanKey = "${recipientPrefix}${recipient.document}"
            document = recipient.document
            name = recipient.name
            alias = recipient.alias
            bankAccounts = convertToMap(recipient.bankAccounts)
            created = recipient.created.format(dateTimeFormat)
            pixKeys = recipient.pixKeys.convertToEntity()
            lastUsed = recipient.lastUsed.convertToEntity()
        }

        dynamoDbDAO.save(entity)
    }

    @Throws(ItemNotFoundException::class)
    override fun delete(accountId: AccountId, contactId: ContactId) {
        dynamoDbDAO.delete(contactId.value, accountId.value)
    }

    @Throws(ItemNotFoundException::class)
    override fun deleteBankAccount(accountId: AccountId, contactId: ContactId, bankAccountId: BankAccountId) {
        val recipientEntity = findRecipient(contactId, accountId)
        recipientEntity.bankAccounts
            .filter { it[idKey] == bankAccountId.value }
            .ifEmpty { throw ItemNotFoundException("") }
            .also { recipientEntity.bankAccounts = recipientEntity.bankAccounts.minus(it) }

        dynamoDbDAO.save(recipientEntity)
    }

    override fun deletePixKey(accountId: AccountId, contactId: ContactId, value: String) {
        val recipientEntity = findRecipient(contactId, accountId)
        recipientEntity.pixKeys
            .filter { it[idKey] == value }
            .ifEmpty { throw ItemNotFoundException("") }
            .also { recipientEntity.pixKeys = recipientEntity.pixKeys.minus(it) }

        dynamoDbDAO.save(recipientEntity)
    }

    private fun findRecipient(contactId: ContactId, accountId: AccountId) =
        dynamoDbDAO.queryTableOnHashKeyAndRangeKey(contactId.value, accountId.value, ContactEntity::class.java)
            .ifEmpty { throw ItemNotFoundException("") }
            .first()

    override fun append(accountId: AccountId, contactId: ContactId, savedBankAccount: SavedBankAccount) {
        val key: MutableMap<String, AttributeValue> = HashMap()
        key[BILL_PAYMENT_PARTITION_KEY] = AttributeValue().withS(contactId.value)
        key[BILL_PAYMENT_RANGE_KEY] = AttributeValue().withS(accountId.value)
        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .withReturnValues(ReturnValue.UPDATED_NEW)
            .withUpdateExpression("SET BankAccounts = list_append(BankAccounts, :i)")
            .withExpressionAttributeValues(
                mapOf(
                    ":i" to AttributeValue().withL(
                        AttributeValue().withM(
                            convertToMutableMap(savedBankAccount),
                        ),
                    ),
                ),
            )
        dynamoDbDAO.updateItem(updateRequest)
    }

    override fun append(accountId: AccountId, contactId: ContactId, savedPixKey: SavedPixKey) {
        val key: MutableMap<String, AttributeValue> = HashMap()
        key[BILL_PAYMENT_PARTITION_KEY] = AttributeValue().withS(contactId.value)
        key[BILL_PAYMENT_RANGE_KEY] = AttributeValue().withS(accountId.value)
        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .withReturnValues(ReturnValue.UPDATED_NEW)
            .withUpdateExpression("SET PixKeys = list_append(if_not_exists(PixKeys, :empty_list), :i)")
            .withExpressionAttributeValues(
                mapOf(
                    ":i" to AttributeValue().withL(AttributeValue().withM(convertToMutableMap(savedPixKey))),
                    ":empty_list" to AttributeValue().withL(listOf<AttributeValue>()),
                ),
            )
        dynamoDbDAO.updateItem(updateRequest)
    }

    override fun updateLastUsed(accountId: AccountId, contactId: ContactId, lastUsed: LastUsed?) {
        val contact = findByIdAndAccountId(accountId = accountId, contactId = contactId)
        save(contact.copy(lastUsed = lastUsed))
    }

    private fun fail(): Nothing {
        throw IllegalArgumentException()
    }

    override fun findRecipientByAccountIDAndDocument(
        accountId: AccountId,
        document: String,
    ): Either<Exception, Contact> {
        return try {
            val entity = dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
                accountId.value,
                "${recipientPrefix}$document",
                ContactEntity::class.java,
            )
            Either.Right(convertToSavedRecipient(entity[0]))
        } catch (e: IndexOutOfBoundsException) {
            Either.Left(ItemNotFoundException("Recipient not found for accountId $accountId and document $document"))
        } catch (e: Exception) {
            Either.Left(e)
        }
    }

    override fun findByAccountId(accountId: AccountId): List<Contact> {
        val recipientEntityList = dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
            accountId.value,
            recipientPrefix,
            ContactEntity::class.java,
        )
        return recipientEntityList.map { recipientEntity -> convertToSavedRecipient(recipientEntity) }.sorted()
    }

    override fun findById(contactId: ContactId): Contact {
        val recipientEntityList = dynamoDbDAO.queryTableOnHashKey(contactId.value, ContactEntity::class.java)
        return recipientEntityList.ifEmpty { throw ItemNotFoundException("Recipient ${contactId.value} not found!") }
            .map { recipientEntity -> convertToSavedRecipient(recipientEntity) }.first()
    }

    override fun findByIdOrNull(contactId: ContactId): Contact? {
        val recipientEntityList = dynamoDbDAO.queryTableOnHashKey(contactId.value, ContactEntity::class.java)
        return recipientEntityList.map { recipientEntity -> convertToSavedRecipient(recipientEntity) }.firstOrNull()
    }

    override fun findByIdAndAccountId(contactId: ContactId, accountId: AccountId): Contact {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKey(contactId.value, accountId.value, ContactEntity::class.java)
            .ifEmpty { throw ItemNotFoundException("Recipient ${contactId.value} not found for account ${accountId.value}!") }
            .map { recipientEntity -> convertToSavedRecipient(recipientEntity) }.first()
    }

    private fun convertToMap(bankAccounts: List<SavedBankAccount>): List<Map<String, String>> {
        return bankAccounts.map { bankAccount -> convertToMap(bankAccount) }
    }

    private fun convertToMap(bankAccount: SavedBankAccount): Map<String, String> {
        val map = mutableMapOf(
            idKey to bankAccount.id.value,
            typeKey to bankAccount.accountType.name,
            bankNoKey to (bankAccount.bankNo?.toString() ?: ""),
            routingNoKey to bankAccount.routingNo.toString(),
            accountNoKey to bankAccount.accountNo.toString(),
            accountDvKey to bankAccount.accountDv,
            invalidatedKey to bankAccount.invalidated.toString(),
            bankISPBKey to (bankAccount.ispb.orEmpty()),
        )
        if (bankAccount.invalidated && bankAccount.invalidationMessage.isNotEmpty()) {
            map[invalidationMessageKey] = bankAccount.invalidationMessage
        }
        if (bankAccount.invalidated && bankAccount.invalidationCode != null) {
            map[invalidationCodeKey] = bankAccount.invalidationCode!!.name
        }
        return map.filterValues { it.isNotEmpty() }
    }

    private fun List<SavedPixKey>.convertToEntity(): List<Map<String, String>> {
        return map {
            val map = mutableMapOf(
                idKey to it.value,
                typeKey to it.type.name,
                invalidatedKey to it.invalidated.toString(),
            )
            if (it.invalidated && it.invalidationCode != null) {
                map[invalidationCodeKey] = it.invalidationCode!!.name
            }
            map
        }
    }

    private fun LastUsed?.convertToEntity(): LastUsedEntity? = this?.let {
        LastUsedEntity(bankAccountId = it.bankAccountId?.value, pixKey = it.pixKey)
    }

    private fun convertToMutableMap(savedPixKey: SavedPixKey): MutableMap<String, AttributeValue> {
        return mutableMapOf(
            idKey to AttributeValue().withS(savedPixKey.value),
            typeKey to AttributeValue().withS(savedPixKey.type.name),
        )
    }

    private fun convertToMutableMap(bankAccount: SavedBankAccount): MutableMap<String, AttributeValue> {
        return mutableMapOf(
            idKey to AttributeValue().withS(bankAccount.id.value),
            typeKey to AttributeValue().withS(bankAccount.accountType.name),
            bankNoKey to AttributeValue().withS(bankAccount.bankNo?.toString()),
            routingNoKey to AttributeValue().withS(bankAccount.routingNo.toString()),
            accountNoKey to AttributeValue().withS(bankAccount.accountNo.toString()),
            accountDvKey to AttributeValue().withS(bankAccount.accountDv),
            bankISPBKey to AttributeValue().withS(bankAccount.ispb),
        ).filterValues {
            it.s != null && it.s.isNotEmpty()
        }.toMutableMap()
    }

    private fun convertToSavedRecipient(contactEntity: ContactEntity): Contact {
        return Contact(
            id = ContactId(contactEntity.contactId),
            accountId = AccountId(contactEntity.accountId),
            alias = contactEntity.alias,
            name = contactEntity.name,
            document = contactEntity.document,
            bankAccounts = convertToSavedBankAccount(contactEntity.bankAccounts),
            pixKeys = convertToSavedPixKey(contactEntity.pixKeys),
            created = LocalDateTime.parse(contactEntity.created, dateTimeFormat).atZone(brazilTimeZone),
            lastUsed = contactEntity.lastUsed?.let { entity ->
                LastUsed(
                    bankAccountId = entity.bankAccountId?.let {
                        BankAccountId(it)
                    },
                    pixKey = entity.pixKey,
                )
            },
        )
    }

    private fun convertToSavedBankAccount(maps: List<Map<String, String>>): List<SavedBankAccount> {
        return maps.map { map -> convertToSavedBankAccount(map) }
    }

    private fun convertToSavedBankAccount(map: Map<String, String>): SavedBankAccount {
        return SavedBankAccount(
            id = BankAccountId(map[idKey] ?: fail()),
            accountType = AccountType.valueOf(map[typeKey] ?: fail()),
            bankNo = map[bankNoKey]?.toLong(),
            routingNo = map[routingNoKey]?.toLong() ?: fail(),
            accountNo = map[accountNoKey]?.toBigInteger() ?: fail(),
            accountDv = map[accountDvKey] ?: fail(),
            ispb = map[bankISPBKey],
        ).apply {
            invalidated = map.getOrDefault(invalidatedKey, false.toString()).toBoolean()
            invalidationMessage = map.getOrDefault(invalidationMessageKey, "")
            if (invalidated) {
                invalidationCode =
                    map[invalidationCodeKey]?.let { InvalidationCode.valueOf(it) } ?: InvalidationCode.INVALID_DATA
            }
        }
    }

    private fun convertToSavedPixKey(pixKeys: List<Map<String, String>>): List<SavedPixKey> {
        return pixKeys.map { map ->
            SavedPixKey(
                value = map[idKey] ?: fail(),
                type = PixKeyType.valueOf(map[typeKey] ?: fail()),
            ).apply {
                invalidated = map.getOrDefault(invalidatedKey, "false").toBoolean()
                if (invalidated) {
                    invalidationCode =
                        map[invalidationCodeKey]?.let { InvalidationCode.valueOf(it) } ?: InvalidationCode.INVALID_DATA
                }
            }
        }
    }
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class ContactEntity {

    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var contactId: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var accountId: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String

    @DynamoDBAttribute(attributeName = "Document")
    lateinit var document: String

    @DynamoDBAttribute(attributeName = "Name")
    lateinit var name: String

    @DynamoDBAttribute(attributeName = "Alias")
    var alias: String? = null

    @DynamoDBAttribute(attributeName = "BankAccounts")
    var bankAccounts: List<Map<String, String>> = listOf()

    @DynamoDBAttribute(attributeName = "PixKeys")
    var pixKeys: List<Map<String, String>> = listOf()

    @DynamoDBAttribute(attributeName = "Created")
    lateinit var created: String

    @DynamoDBAttribute(attributeName = billIdsAttributeName)
    var billIds: Set<String>? = null

    @DynamoDBAttribute(attributeName = recurrenceIdsAttributeName)
    var recurrenceIds: Set<String>? = null

    @DynamoDBAttribute(attributeName = "LastUsed")
    var lastUsed: LastUsedEntity? = null
}

@DynamoDBDocument
data class LastUsedEntity(
    var bankAccountId: String? = null,
    var pixKey: String? = null,
)