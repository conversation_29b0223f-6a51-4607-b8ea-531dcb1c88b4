package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.InvalidTokenException
import ai.friday.billpayment.app.account.InvalidTokenReason
import ai.friday.billpayment.app.account.TokenKey
import ai.friday.billpayment.app.integrations.TokenData
import ai.friday.billpayment.app.integrations.TokenDataWithExpiration
import ai.friday.billpayment.app.integrations.TokenRepository
import ai.friday.billpayment.app.integrations.TokenType
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import jakarta.inject.Singleton

@Singleton
open class TokenDbRepository(private val dynamoDbDAO: DynamoDbDAO) : TokenRepository {

    private val tokenPrefix = "TOKEN#"

    override fun save(tokenKey: TokenKey, tokenData: TokenDataWithExpiration) {
        val tokenEntity = TokenEntity().apply {
            primaryKey = tokenKey.accountId.value
            scanKey = buildScanKey(tokenData)
            this.token = tokenKey.value
            accountId = tokenKey.accountId.value
            this.typeValue = tokenData.value
            this.expiration = tokenData.expiration
        }
        dynamoDbDAO.save(tokenEntity)
    }

    override fun existsToken(
        accountId: AccountId,
        tokenType: TokenType,
    ): Boolean {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = accountId.value,
            scanKey = buildScanKeyPrefix(tokenType),
            type = TokenEntity::class.java,
        )
            .any { it.isExpired().not() }
    }

    override fun retrieveNotExpired(
        accountId: AccountId,
        tokenType: TokenType,
    ): Either<InvalidTokenException, TokenDataWithExpiration> {
        val foundEntity = dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = accountId.value,
            scanKey = buildScanKeyPrefix(tokenType),
            type = TokenEntity::class.java,
        ).firstOrNull { it.isExpired().not() }
            ?: return InvalidTokenException(reason = InvalidTokenReason.NOT_FOUND).left()

        return TokenDataWithExpiration(TokenData.of(foundEntity.typeValue, tokenType), foundEntity.expiration).right()
    }

    override fun retrieveNotExpiredTokenKey(
        accountId: AccountId,
        tokenType: TokenType,
    ): Either<InvalidTokenException, TokenKey> {
        val foundEntity = dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = accountId.value,
            scanKey = buildScanKeyPrefix(tokenType),
            type = TokenEntity::class.java,
        ).firstOrNull { it.isExpired().not() }
            ?: return InvalidTokenException(reason = InvalidTokenReason.NOT_FOUND).left()

        return TokenKey(accountId = accountId, value = foundEntity.token).right()
    }

    override fun retrieveValidated(
        tokenKey: TokenKey,
        tokenType: TokenType,
        errorLimit: Int,
    ): Either<InvalidTokenException, TokenDataWithExpiration> {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = tokenKey.accountId.value,
            scanKey = buildScanKeyPrefix(tokenType),
            type = TokenEntity::class.java,
        ).check(tokenKey, errorLimit).map { foundEntity ->
            TokenDataWithExpiration(TokenData.of(foundEntity.typeValue, tokenType), foundEntity.expiration)
        }
    }

    private fun List<TokenEntity>.check(
        tokenKey: TokenKey,
        errorLimit: Int,
    ): Either<InvalidTokenException, TokenEntity> {
        return try {
            this.ifEmpty {
                throw InvalidTokenException(reason = InvalidTokenReason.NOT_FOUND)
            }.filter {
                it.isExpired().not()
            }.ifEmpty {
                throw InvalidTokenException(reason = InvalidTokenReason.EXPIRED)
            }.filter {
                it.token == tokenKey.value
            }.ifEmpty {
                throw InvalidTokenException(reason = InvalidTokenReason.MISMATCH)
            }.filter {
                it.errorCount < errorLimit
            }.ifEmpty {
                throw InvalidTokenException(reason = InvalidTokenReason.MAX_ATTEMPTS)
            }.first().right()
        } catch (e: InvalidTokenException) {
            e.left()
        }
    }

    override fun delete(tokenKey: TokenKey, tokenData: TokenData) {
        dynamoDbDAO.delete(tokenKey.accountId.value, buildScanKey(tokenData))
    }

    override fun incrementErrorCount(accountId: AccountId): Int {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = accountId.value,
            scanKey = tokenPrefix,
            type = TokenEntity::class.java,
        )
            .filter { it.isExpired().not() }
            .ifEmpty { return 0 }
            .map {
                dynamoDbDAO.incrementItem(
                    mapOf(
                        BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(it.primaryKey),
                        BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(it.scanKey),
                    ),
                    "ErrorCount",
                    1,
                ).n.toInt()
            }.maxByOrNull { it } ?: 0
    }

    fun retrieveErrorCount(accountId: AccountId): Int {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = accountId.value,
            scanKey = tokenPrefix,
            type = TokenEntity::class.java,
        )
            .filter { it.isExpired().not() }
            .ifEmpty { return 0 }
            .first()
            .errorCount
    }

    private fun buildScanKey(tokenData: TokenData): String {
        return "${buildScanKeyPrefix(tokenData.type)}${tokenData.value}"
    }

    private fun buildScanKeyPrefix(type: TokenType): String {
        return "${tokenPrefix}${type.name}#"
    }
}

private fun TokenEntity.isExpired(): Boolean = expiration <= getZonedDateTime().toEpochSecond()

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class TokenEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBAttribute(attributeName = "Token")
    lateinit var token: String

    @DynamoDBAttribute(attributeName = "AccountId")
    lateinit var accountId: String

    @DynamoDBAttribute(attributeName = "TypeValue")
    lateinit var typeValue: String

    @DynamoDBAttribute(attributeName = "ExpirationTTL")
    var expiration: Long = 0

    @DynamoDBAttribute(attributeName = "ErrorCount")
    var errorCount: Int = 0
}