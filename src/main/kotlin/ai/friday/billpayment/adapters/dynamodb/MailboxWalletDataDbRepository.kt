package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.integrations.MailboxWalletDataRepository
import ai.friday.billpayment.app.mailbox.MailboxListType
import ai.friday.billpayment.app.wallet.WalletId
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import jakarta.inject.Singleton

private const val MAILBOX_LIST_KEY = "MAILBOX_WALLET_LIST"

@Singleton
class MailboxWalletDataDbRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) : MailboxWalletDataRepository {

    override fun add(walletId: WalletId, type: MailboxListType, email: EmailAddress) {
        dynamoDbDAO.save(
            UserEmailListDataEntity().apply {
                this.primaryKey = buildPartitionKey(walletId, type)
                this.scanKey = email.value
                this.gSIndex1PrimaryKey = keyName(type)
                this.gSIndex1ScanKey = email.value
                this.walletId = walletId.value
                this.mailboxEmailFilter = email.value
            },
        )
    }

    override fun remove(walletId: WalletId, type: MailboxListType, email: EmailAddress) {
        dynamoDbDAO.delete(
            UserEmailListDataEntity().apply {
                this.primaryKey = buildPartitionKey(walletId, type)
                this.scanKey = email.value
            },
        )
    }

    override fun load(walletId: WalletId, type: MailboxListType): List<EmailAddress> {
        return dynamoDbDAO.queryTableOnHashKey(
            buildPartitionKey(walletId, type),
            UserEmailListDataEntity::class.java,
        ).map { EmailAddress(it.mailboxEmailFilter) }
    }

    override fun has(walletId: WalletId, type: MailboxListType, email: EmailAddress): Boolean {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKey(
            buildPartitionKey(walletId, type),
            email.value,
            UserEmailListDataEntity::class.java,
        ).isNotEmpty()
    }

    private fun keyName(type: MailboxListType) = "$MAILBOX_LIST_KEY#${type.name}"

    private fun buildPartitionKey(walletId: WalletId, type: MailboxListType) = "${walletId.value}#${keyName(type)}"
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class UserEmailListDataEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String

    @DynamoDBAttribute(attributeName = "WalletId")
    lateinit var walletId: String

    @DynamoDBAttribute(attributeName = "MailboxEmailFilter")
    lateinit var mailboxEmailFilter: String
}