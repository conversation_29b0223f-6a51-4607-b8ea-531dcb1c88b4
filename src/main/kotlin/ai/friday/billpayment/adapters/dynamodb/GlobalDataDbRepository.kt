package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import jakarta.inject.Singleton

@Singleton
open class GlobalDataDbRepository(private val dynamoDbDAO: DynamoDbDAO) {

    fun loadGlobalData(primaryKey: String, rangeKey: String) =
        dynamoDbDAO.queryTableOnHashKeyAndRangeKey(primaryKey, rangeKey, GlobalDataEntity::class.java)
            .singleOrNull()

    fun saveGlobalData(primaryKey: String, scanKey: String, list: List<Any>) {
        val entity = GlobalDataEntity().apply {
            this.primaryKey = primaryKey
            this.scanKey = scanKey
            value = getObjectMapper().writeValueAsString(list)
        }
        dynamoDbDAO.save(entity)
    }

    fun saveGlobalData(primaryKey: String, scanKey: String, map: Map<String, Any>) {
        val entity = GlobalDataEntity().apply {
            this.primaryKey = primaryKey
            this.scanKey = scanKey
            value = getObjectMapper().writeValueAsString(map)
        }
        dynamoDbDAO.save(entity)
    }

    fun deleteFromGlobalData(item: Any) = dynamoDbDAO.delete(item)
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class GlobalDataEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBAttribute(attributeName = "GlobalData")
    lateinit var value: String
}