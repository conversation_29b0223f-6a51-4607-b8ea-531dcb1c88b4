package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.LimitRepository
import ai.friday.billpayment.app.limit.Limit
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import java.time.Duration
import java.time.format.DateTimeFormatter

const val LIMIT_PREFIX = "LIMIT"

@FridayMePoupe
class LimitDbRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) : LimitRepository {
    override fun get(accountId: AccountId, key: String): Limit? {
        if (!dynamoDbDAO.hasItem(accountId.value, generateScan(key))) {
            return null
        }

        return dynamoDbDAO.load(accountId.value, generateScan(key), LimitEntity::class.java)
            ?.takeIf { it.expirationTtl > getZonedDateTime().toInstant().epochSecond }
            ?.let { Limit(count = it.count, maxCount = it.maxCount) }
    }

    override fun create(accountId: AccountId, key: String, maxCount: Int, expiration: Duration) {
        dynamoDbDAO.save(
            LimitEntity().apply {
                partitionKey = accountId.value
                scanKey = generateScan(key)
                createdAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                expirationTtl = getZonedDateTime().plus(expiration).toInstant().epochSecond
                count = 0
                this.maxCount = maxCount
            },
        )
    }

    override fun increment(accountId: AccountId, key: String) {
        val pk = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(accountId.value),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(generateScan(key)),
        )

        dynamoDbDAO.incrementItem(pk, "Count", 1)
    }

    private fun generateScan(key: String) = "$LIMIT_PREFIX#$key"
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class LimitEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var partitionKey: String // ACCOUNT_ID

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // PREFIX#KEY

    @DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @DynamoDBAttribute(attributeName = "ExpirationTTL")
    var expirationTtl: Long = 0

    @DynamoDBAttribute(attributeName = "Count")
    var count: Int = 0

    @DynamoDBAttribute(attributeName = "MaxCount")
    var maxCount: Int = 0
}