package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.integrations.ScheduledWalletRepository
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.dateFormat
import java.time.LocalDate
import reactor.core.publisher.Flux
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedAsyncClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@FridayMePoupe
class ScheduledWalletDynamoDAO(cli: DynamoDbEnhancedAsyncClient) : AbstractAsyncDynamoDAO<ScheduledWalletEntity>(cli) {
    override fun args() = BILL_PAYMENT_TABLE_NAME to ScheduledWalletEntity::class.java
}

@FridayMePoupe
class ScheduledWalletDBRepository(
    private val client: ScheduledWalletDynamoDAO,
) : ScheduledWalletRepository {

    private val SCHEDULED_ACCOUNT_PREFIX = "SCHEDULED-ACCOUNT"

    override fun save(walletId: WalletId, minScheduleDate: LocalDate) {
        val entity = ScheduledWalletEntity().apply {
            this.walletId = walletId.value
            metadata = SCHEDULED_ACCOUNT_PREFIX
            minScheduledDate = minScheduleDate.format(dateFormat)
            gSIndex1PrimaryKey = SCHEDULED_ACCOUNT_PREFIX
            gSIndex1ScanKey = minScheduleDate.format(dateFormat)
        }

        client.save(entity).block()
    }

    override fun delete(walletId: WalletId) {
        client.delete(partitionKey = SCHEDULED_ACCOUNT_PREFIX, sortKey = walletId.value).block()
    }

    override fun findAllWalletsWithScheduledBills(): Flux<WalletId> {
        return client.listByPartitionKey(SCHEDULED_ACCOUNT_PREFIX)
            .map {
                WalletId(it.walletId)
            }
    }

    override fun findAllWalletsWithScheduledBillsToDate(scheduledDate: LocalDate): Flux<WalletId> =
        client.findByPrimaryKeyOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = SCHEDULED_ACCOUNT_PREFIX,
            sortKey = scheduledDate.format(dateFormat),
        ).map {
            WalletId(it.walletId)
        }

    override fun findWalletsWithScheduledBillsBetween(startDate: LocalDate, endDate: LocalDate): Flux<WalletId> {
        return client.findByPrimaryKeyAndBetweenSortKeysOnIndex(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = SCHEDULED_ACCOUNT_PREFIX,
            sortKeyFrom = startDate.format(dateFormat),
            sortKeyTo = endDate.format(dateFormat),
        ).map {
            WalletId(it.walletId)
        }
    }

    override fun findWalletsWithScheduledBillsUntil(scheduledDate: LocalDate): Flux<WalletId> =
        client.findByPrimaryKeyAndSortKeyLessThanOrEqualTo(
            index = GlobalSecondaryIndexNames.GSIndex1,
            partitionKey = SCHEDULED_ACCOUNT_PREFIX,
            sortKey = scheduledDate.format(dateFormat),
        ).map {
            WalletId(it.walletId)
        }
}

@DynamoDbBean
class ScheduledWalletEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var metadata: String

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var walletId: String

    @get:DynamoDbAttribute(value = "MinScheduledDate")
    var minScheduledDate: String? = null // TODO - Remover o null apos enriquecer todos os registros

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    var gSIndex1PrimaryKey: String? = null // TODO - Remover o null apos enriquecer todos os registros

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    var gSIndex1ScanKey: String? = null // TODO - Remover o null apos enriquecer todos os registros
}