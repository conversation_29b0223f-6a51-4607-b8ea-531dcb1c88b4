package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.forecast.ForecastPeriod
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.itp.ConsentId
import ai.friday.billpayment.app.itp.PaymentIntent
import ai.friday.billpayment.app.itp.PaymentIntentDetails
import ai.friday.billpayment.app.itp.PaymentIntentId
import ai.friday.billpayment.app.itp.PaymentIntentRepository
import ai.friday.billpayment.app.itp.PaymentIntentStatus
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.wallet.WalletId
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import io.micronaut.context.annotation.Property

const val PAYMENT_INTENT_PREFIX = "PAYMENT_INTENT"

@FridayMePoupe
class PaymentIntentDbRepository(val dynamoDbDAO: DynamoDbDAO) : PaymentIntentRepository {
    @field:Property(name = "accountRegister.pixKey.emailDomain")
    lateinit var defaultPixKeyDomain: String

    override fun save(paymentIntent: PaymentIntent): PaymentIntent {
        val entity = paymentIntent.toPaymentIntentEntity()
        dynamoDbDAO.save(entity)
        return paymentIntent
    }

    override fun find(paymentIntentId: PaymentIntentId): PaymentIntent {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKey(
            primaryKey = paymentIntentId.value,
            scanKey = PAYMENT_INTENT_PREFIX,
            type = PaymentIntentEntity::class.java,
        )
            .ifEmpty { throw ItemNotFoundException("PaymentIntent was not found") }
            .single().toPaymentIntent(defaultPixKeyDomain)
    }

    override fun findByConsentId(consentId: ConsentId): PaymentIntent {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
            primaryKey = consentId.value,
            scanKey = "CONSENT_ID",
            type = PaymentIntentEntity::class.java,
        )
            .ifEmpty { throw ItemNotFoundException("PaymentIntent was not found") }
            .single().toPaymentIntent(defaultPixKeyDomain)
    }
}

private fun PaymentIntent.toPaymentIntentEntity(): PaymentIntentEntity {
    return this.let { paymentIntent ->
        PaymentIntentEntity().apply {
            primaryKey = paymentIntent.paymentIntentId.value
            scanKey = PAYMENT_INTENT_PREFIX
            paymentIntentId = paymentIntent.paymentIntentId.value
            accountId = paymentIntent.accountId.value
            walletId = paymentIntent.walletId.value
            document = paymentIntent.document
            authorizationServerId = paymentIntent.authorizationServerId
            authorizationServerName = paymentIntent.authorizationServerName
            bankNo = paymentIntent.bankNo
            bankISPB = paymentIntent.bankISPB
            routingNo = paymentIntent.routingNo
            accountNo = paymentIntent.accountNo
            accountDv = paymentIntent.accountDv
            accountType = paymentIntent.accountType
            forecastPeriod = paymentIntent.forecastPeriod
            includeScheduledBillsOnly = paymentIntent.includeScheduledBillsOnly
            amount = if (paymentIntent.details is PaymentIntentDetails.WithPixKey) {
                paymentIntent.details.amount
            } else {
                null
            }
            pixKey = if (paymentIntent.details is PaymentIntentDetails.WithPixKey) {
                paymentIntent.details.pixKey.value
            } else {
                null
            }
            qrCode = if (paymentIntent.details is PaymentIntentDetails.WithQRCode) {
                paymentIntent.details.qrCode
            } else {
                null
            }
            paymentIntentStatus = paymentIntent.status
            if (paymentIntent.consentId != null) {
                gSIndex1PrimaryKey = paymentIntent.consentId.value
                gSIndex1ScanKey = "CONSENT_ID"
            }
        }
    }
}

private fun PaymentIntentEntity.toPaymentIntent(defaultPixKeyDomain: String): PaymentIntent {
    return this.let { paymentIntentEntity ->
        PaymentIntent(
            paymentIntentId = PaymentIntentId(paymentIntentEntity.paymentIntentId),
            accountId = AccountId(paymentIntentEntity.accountId),
            walletId = WalletId(paymentIntentEntity.walletId),
            document = paymentIntentEntity.document,
            authorizationServerId = paymentIntentEntity.authorizationServerId,
            authorizationServerName = paymentIntentEntity.authorizationServerName,
            bankNo = paymentIntentEntity.bankNo,
            bankISPB = paymentIntentEntity.bankISPB,
            routingNo = paymentIntentEntity.routingNo,
            accountNo = paymentIntentEntity.accountNo,
            accountDv = paymentIntentEntity.accountDv,
            accountType = paymentIntentEntity.accountType,
            forecastPeriod = paymentIntentEntity.forecastPeriod,
            includeScheduledBillsOnly = paymentIntentEntity.includeScheduledBillsOnly ?: (paymentIntentEntity.forecastPeriod == ForecastPeriod.TODAY),
            details = PaymentIntentDetails.build(
                pixKey = if (paymentIntentEntity.pixKey != null) {
                    PixKey(paymentIntentEntity.pixKey!!, PixKeyType.EMAIL)
                } else {
                    PixKey("${paymentIntentEntity.document}@$defaultPixKeyDomain", PixKeyType.EMAIL)
                },
                amount = paymentIntentEntity.amount,
                qrCode = paymentIntentEntity.qrCode,
            ),
            status = paymentIntentEntity.paymentIntentStatus ?: PaymentIntentStatus.CREATED,
            consentId = if (paymentIntentEntity.gSIndex1PrimaryKey != null) ConsentId(paymentIntentEntity.gSIndex1PrimaryKey!!) else null,
        )
    }
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class PaymentIntentEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBAttribute(attributeName = "PaymentIntentId")
    lateinit var paymentIntentId: String

    @DynamoDBAttribute(attributeName = "AccountId")
    lateinit var accountId: String

    @DynamoDBAttribute(attributeName = "WalletId")
    lateinit var walletId: String

    @DynamoDBAttribute(attributeName = "Document")
    lateinit var document: String

    @DynamoDBAttribute(attributeName = "AuthorizationServerId")
    lateinit var authorizationServerId: String

    @DynamoDBAttribute(attributeName = "AuthorizationServerName")
    lateinit var authorizationServerName: String

    @DynamoDBAttribute(attributeName = "BankNo")
    var bankNo: Long? = null

    @DynamoDBAttribute(attributeName = "BankISPB")
    var bankISPB: String? = null

    @DynamoDBAttribute(attributeName = "RoutingNo")
    var routingNo: Long = 0

    @DynamoDBAttribute(attributeName = "AccountNo")
    var accountNo: Long = 0

    @DynamoDBAttribute(attributeName = "AccountDv")
    var accountDv: String = ""

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "AccountType")
    var accountType: AccountType? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "ForecastPeriod")
    var forecastPeriod: ForecastPeriod? = null

    @DynamoDBAttribute(attributeName = "IncludeScheduledBillsOnly")
    var includeScheduledBillsOnly: Boolean? = null

    @DynamoDBAttribute(attributeName = "Amount")
    var amount: Long? = null

    @DynamoDBAttribute(attributeName = "QRCode")
    var qrCode: String? = null

    @DynamoDBAttribute(attributeName = "PIXKey")
    var pixKey: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "PaymentIntentStatus")
    var paymentIntentStatus: PaymentIntentStatus? = null

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    var gSIndex1PrimaryKey: String? = null // ConsentId

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    var gSIndex1ScanKey: String? = null
}