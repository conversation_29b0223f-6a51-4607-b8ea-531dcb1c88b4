package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BarCode
import ai.friday.billpayment.app.bill.BatchSchedulingId
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.BillView
import ai.friday.billpayment.app.bill.BillViewSchedule
import ai.friday.billpayment.app.bill.ExternalBillId
import ai.friday.billpayment.app.bill.PartnerPayment
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.RefundedBill
import ai.friday.billpayment.app.bill.WarningCode
import ai.friday.billpayment.app.bill.getWarningCode
import ai.friday.billpayment.app.bill.name
import ai.friday.billpayment.app.billcategory.BillCategorySuggestion
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.getOrFalse
import ai.friday.billpayment.app.goal.GoalId
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.FindBillsCriteria
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.DivergentPayment
import ai.friday.billpayment.app.payment.PartialPayment
import ai.friday.billpayment.app.payment.SettlementFundsTransfer
import ai.friday.billpayment.app.payment.SettlementFundsTransferType
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import ai.friday.morning.date.timeFormat
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import com.amazonaws.services.dynamodbv2.model.AttributeValueUpdate
import com.amazonaws.services.dynamodbv2.model.UpdateItemRequest
import com.fasterxml.jackson.core.JsonProcessingException
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import datadog.trace.api.Trace
import io.micronaut.context.annotation.Property
import io.netty.util.internal.StringUtil
import jakarta.inject.Singleton
import java.time.Instant
import java.time.LocalDate
import java.time.LocalDateTime
import java.time.LocalTime
import java.time.Month
import java.time.Year
import java.time.YearMonth
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

enum class ContactIdIndexMetadata {
    RECURRENT_BILL, BILL, RECURRENCE
}

const val REFUNDED_BILL_ENTITY_SCAN_PREFIX = "REFUNDED"

val validStatusToBillComingDue = listOf(BillStatus.ACTIVE, BillStatus.WAITING_APPROVAL).map { it.name }

@Singleton
class DynamoDbBillRepository(private val dynamoDbDAO: DynamoDbDAO) : BillRepository {

    @field:Property(name = "bill.ted.limitTime")
    var tedLimitTime: String = "17:00"

    private val mapper = jacksonObjectMapper()

    private val settlementFundsTransferKey = "SETTLEMENT-FUNDS-TRANSFER"

    override fun save(bill: Bill) {
        val entity = BillEntity().apply {
            primaryKey = bill.billId.value
            scanKey = bill.walletId.value
            gSIndex1PrimaryKey = bill.walletId.value
            gSIndex1ScanKey = "BILL#" + bill.effectiveDueDate + "#" + bill.status.name
            contactId = bill.contactId?.value
            gSIndex2ScanKey = bill.contactId?.let {
                bill.recurrenceRule?.let { ContactIdIndexMetadata.RECURRENT_BILL } ?: ContactIdIndexMetadata.BILL
            }
            amount = bill.amount
            discount = bill.discount
            interest = bill.interest
            fine = bill.fine
            billRecipient = toBillRecipientDocument(bill.recipient)
            amountTotal = bill.amountTotal
            paymentLimitTime = bill.paymentLimitTime.format(timeFormat)
            assignor = bill.assignor.orEmpty()
            created = Instant.ofEpochMilli(bill.created).atZone(brazilTimeZone).format(dateTimeFormat)
            description = bill.description
            status = bill.status.name
            paidDate = bill.paidDate?.let { Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat) }
            amountPaid = bill.amountPaid
            dueDate = bill.dueDate.format(dateFormat)
            effectiveDueDate = bill.effectiveDueDate.format(dateFormat)
            type = bill.billType
            digitableBarCode = bill.barcode?.digitable
            lastSettleDate = bill.lastSettleDate
            expirationDate = bill.expirationDate
            payerDocument = bill.payer?.document
            payerName = bill.payer?.name
            warningCode = bill.getWarningCode().code
            scheduledDate = bill.schedule?.date?.format(dateFormat)
            waitingFunds = bill.schedule?.waitingFunds
            waitingRetry = bill.schedule?.waitingRetry
            batchSchedulingId = bill.schedule?.batchSchedulingId?.value
            payerAlias = bill.payer?.alias
            amountCalculationModel = bill.amountCalculationModel
            amountCalculationOption = bill.amountCalculationOption
            amountCalculationValidUntil = bill.amountCalculationValidUntil?.format(dateFormat)
            recurrenceRule = bill.recurrenceRule?.let {
                RecurrenceRuleEntity().apply {
                    frequency = it.frequency
                    startDate = it.startDate.format(dateFormat)
                    endDate = it.endDate?.format(dateFormat)
                    pattern = it.pattern
                }
            }
            fichaCompensacaoType = bill.fichaCompensacaoType
            visibleBy = bill.visibleBy.map {
                it.value
            }
            fichaCompensacaoIdNumber = bill.idNumber
            markedAsPaidBy = bill.markedAsPaidBy?.let { it.value }
            externalId = bill.externalId?.value
            externalProvider = bill.externalId?.provider?.name
            gsIndex3PartitionKey = bill.status.name
            gSIndex3ScanKey = buildIndex3ScanKey(bill)
            gsIndex4PartitionKey = bill.categoryId?.value
            gSIndex4ScanKey = bill.walletId.value
            partnerPayment = bill.partnerPayment?.let {
                PartnerPaymentDocument().apply {
                    partner = it.partner
                    paymentId = it.partnerPaymentId
                }
            }
            subscriptionFee = bill.subscriptionFee
            securityValidationResult = bill.securityValidationResult
            paymentMethodsDetail = bill.paymentMethodsDetail?.toPaymentMethodsDetailEntity()
            transactionCorrelationId = bill.transactionCorrelationId
            tags = if (bill.tags.isEmpty()) null else bill.tags
            categoryId = bill.categoryId?.value
            categorySuggestions = bill.categorySuggestions.map {
                BillCategorySuggestionDocument().apply {
                    this.categoryId = it.categoryId.value
                    this.probability = it.probability.toLong()
                }
            }
            pixQrCodeData = bill.pixQrCodeData?.let { pixQrCodeData ->
                PixQrCodeDataDocument().apply {
                    type = pixQrCodeData.type
                    info = pixQrCodeData.info
                    additionalInfo = pixQrCodeData.additionalInfo
                    pixId = pixQrCodeData.pixId
                    fixedAmount = pixQrCodeData.fixedAmount
                    expiration = pixQrCodeData.expiration?.format(DateTimeFormatter.ISO_DATE_TIME)
                    originalAmount = pixQrCodeData.originalAmount
                }
            }
            divergentPayment = bill.divergentPayment.toDivergentPaymentDocument()
            partialPayment = bill.partialPayment.toPartialPaymentDocument()
            brand = bill.brand
            scheduledBy = bill.scheduledBy?.value
            paymentWalletId = bill.paymentWalletId?.value
            ignoredAt = bill.ignoredAt?.let { Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat) }
            refundedAt = bill.refundedAt?.let { Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat) }
            markedAsPaidAt = bill.markedAsPaidAt?.let { Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat) }
            goalId = bill.goalId?.value
        }
        try {
            entity.source = mapper.writeValueAsString(bill.source)
        } catch (e: JsonProcessingException) {
            entity.source = ""
            logger.error("DynamoDbBillRepositorySave", e)
        }

        try {
            entity.scheduleSource = bill.scheduleSource?.let { mapper.writeValueAsString(it) }
        } catch (e: JsonProcessingException) {
            logger.error("DynamoDbBillRepositorySave", e)
        }

        dynamoDbDAO.save(entity)
    }

    @Trace
    override fun findOverdueBills(walletId: WalletId): List<BillView> {
        val today = getZonedDateTime()
        val expressionAttributeValues = mapOf(
            ":val1" to AttributeValue(walletId.value),
            ":val2" to AttributeValue("BILL#${today.format(dateFormat)}"),
            ":val3" to AttributeValue(BillStatus.ACTIVE.name),
        )
        val expressionAttributeNames = mapOf("#status" to "Status")
        val queryExpression = DynamoDBQueryExpression<BillEntity>()
            .withConsistentRead(false)
            .withIndexName("GSIndex1")
            .withExpressionAttributeNames(expressionAttributeNames)
            .withExpressionAttributeValues(expressionAttributeValues)
            .withKeyConditionExpression("GSIndex1PrimaryKey = :val1 And GSIndex1ScanKey <= :val2")
            .withFilterExpression("#status = :val3")
        val billList = dynamoDbDAO.queryWithDynamoDBQueryExpression(queryExpression, BillEntity::class.java)
        return billList.map { billEntity: BillEntity -> convertToBill(billEntity) }
    }

    override fun findBillsWaitingFunds(walletId: WalletId): List<BillView> {
        val expressionAttributeValues = mapOf(
            ":val1" to AttributeValue(walletId.value),
            ":val2" to AttributeValue("BILL#"),
            ":val3" to AttributeValue(BillStatus.ACTIVE.name),
            ":val4" to AttributeValue().withN("1"),
        )
        val expressionAttributeNames = mapOf(
            "#status" to "Status",
            "#waitingFunds" to "WaitingFunds",
        )
        val queryExpression = DynamoDBQueryExpression<BillEntity>()
            .withConsistentRead(false)
            .withIndexName("GSIndex1")
            .withExpressionAttributeNames(expressionAttributeNames)
            .withExpressionAttributeValues(expressionAttributeValues)
            .withKeyConditionExpression("GSIndex1PrimaryKey = :val1 And begins_with(GSIndex1ScanKey, :val2)")
            .withFilterExpression("#status = :val3 AND #waitingFunds = :val4")
        val billEntities = dynamoDbDAO.queryWithDynamoDBQueryExpression(queryExpression, BillEntity::class.java)
        return billEntities.map { billEntity: BillEntity -> convertToBill(billEntity) }
    }

    override fun findBillsComingDue(walletId: WalletId): List<BillView> {
        val today = getZonedDateTime()
        val billList = dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
            primaryKey = walletId.value,
            scanKey = "BILL#${today.format(dateFormat)}#",
            type = BillEntity::class.java,
        )
        return billList.filter { it.status in validStatusToBillComingDue }.map { billEntity: BillEntity -> convertToBill(billEntity) }
    }

    override fun findBillsComingDueWithin(walletId: WalletId, periodInDays: Long): List<BillView> {
        val today = getZonedDateTime()
        val endDate = today.plusDays(periodInDays)
        val expressionAttributeValues = mapOf(
            ":val1" to AttributeValue(walletId.value),
            ":val2" to AttributeValue("BILL#${today.format(dateFormat)}"),
            ":val3" to AttributeValue("BILL#${endDate.format(dateFormat)}"),
        )
        val queryExpression = DynamoDBQueryExpression<BillEntity>()
            .withConsistentRead(false)
            .withIndexName("GSIndex1")
            .withExpressionAttributeValues(expressionAttributeValues)
            .withKeyConditionExpression("GSIndex1PrimaryKey = :val1 And GSIndex1ScanKey between :val2 and :val3")
        val billList = dynamoDbDAO.queryWithDynamoDBQueryExpression(queryExpression, BillEntity::class.java)

        return billList.filter { it.status in validStatusToBillComingDue }.map { billEntity: BillEntity -> convertToBill(billEntity) }
    }

    @Trace
    override fun findByWalletAndStatus(walletId: WalletId, status: BillStatus): List<BillView> {
        val expressionAttributeValues = mapOf(
            ":val1" to AttributeValue(walletId.value),
            ":val2" to AttributeValue("BILL#"),
            ":val3" to AttributeValue(status.name),
        )
        val expressionAttributeNames = mapOf("#status" to "Status")
        val queryExpression = DynamoDBQueryExpression<BillEntity>()
            .withConsistentRead(false)
            .withIndexName("GSIndex1")
            .withExpressionAttributeNames(expressionAttributeNames)
            .withExpressionAttributeValues(expressionAttributeValues)
            .withKeyConditionExpression("GSIndex1PrimaryKey = :val1 And begins_with(GSIndex1ScanKey, :val2)")
            .withFilterExpression("#status = :val3")
        val billEntities = dynamoDbDAO.queryWithDynamoDBQueryExpression(queryExpression, BillEntity::class.java)
        return billEntities.map { billEntity: BillEntity -> convertToBill(billEntity) }
    }

    @Trace
    override fun findByWallet(walletId: WalletId): List<BillView> {
        val billList =
            dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(walletId.value, "BILL#", BillEntity::class.java)
        return billList.map { billEntity: BillEntity -> convertToBill(billEntity) }
    }

    @Trace
    override fun findByWallet(walletId: WalletId, criteria: FindBillsCriteria): List<BillView> {
        val from = criteria.from?.let { "BILL#${it.toIsoString()}-01#" }
        val to = criteria.to?.let { "BILL#${it.plusMonths(1).toIsoString()}" }

        val entities: List<BillEntity> = if (from != null && to != null) {
            dynamoDbDAO.queryIndexOnHashKeyAndRangeKeyBetween(walletId.value, from, to, BillEntity::class.java)
        } else if (from != null) {
            dynamoDbDAO.queryIndexOnHashKeyAndRangeKeyGreaterThanOrEqual(walletId.value, from, BillEntity::class.java)
        } else if (to !== null) {
            dynamoDbDAO.queryIndexOnHashKeyAndRangeKeyLessThanOrEqual(walletId.value, to, BillEntity::class.java)
        } else {
            dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(walletId.value, "BILL#", BillEntity::class.java)
        }

        return entities.map { convertToBill(it) }
    }

    @Trace
    override fun findByWalletAndEffectiveDueDate(walletId: WalletId, dueDate: LocalDate): List<BillView> {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
            walletId.value,
            "BILL#${dueDate.format(dateFormat)}",
            BillEntity::class.java,
        ).map(::convertToBill)
    }

    @Trace
    override fun findByWalletAndEffectiveDueDateMonth(walletId: WalletId, year: Year, month: Month): List<BillView> {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
            walletId.value,
            "BILL#${year.value}-${month.value.toString().padStart(2, '0')}",
            BillEntity::class.java,
        ).map(::convertToBill)
    }

    override fun findByContactId(contactId: ContactId): List<BillView> {
        return findByContactId(contactId, ContactIdIndexMetadata.BILL)
    }

    override fun findRecurrenceBillByContactId(contactId: ContactId): List<BillView> {
        return findByContactId(contactId, ContactIdIndexMetadata.RECURRENT_BILL)
    }

    private fun findByContactId(contactId: ContactId, billType: ContactIdIndexMetadata): List<BillView> {
        val expressionAttributeValues =
            mapOf(":val1" to AttributeValue(contactId.value), ":val2" to AttributeValue(billType.name))
        val queryExpression = DynamoDBQueryExpression<BillEntity>()
            .withConsistentRead(false)
            .withIndexName("GSIndex2")
            .withExpressionAttributeValues(expressionAttributeValues)
            .withKeyConditionExpression("GSIndex2PrimaryKey = :val1 And GSIndex2ScanKey = :val2")
        val billEntities = dynamoDbDAO.queryWithDynamoDBQueryExpression(queryExpression, BillEntity::class.java)
        return billEntities.map { billEntity: BillEntity -> convertToBill(billEntity) }
    }

    override fun findBill(billId: BillId, walletId: WalletId): BillView {
        val billList = dynamoDbDAO.queryTableOnHashKeyAndRangeKey(billId.value, walletId.value, BillEntity::class.java)
        val billEntity = billList.stream().findFirst().orElseThrow { IllegalStateException("Bill ${billId.value} not found on wallet ${walletId.value}") }
        return convertToBill(billEntity)
    }

    override fun remove(billId: BillId, walletId: WalletId) {
        dynamoDbDAO.delete(billId.value, walletId.value)
    }

    override fun setBillComingDueMessageSent(billId: BillId, walletId: WalletId, billComingDueMessageSent: Boolean) {
        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(billId.value),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(walletId.value),
        )

        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .addAttributeUpdatesEntry(
                "BillComingDueMessageSent",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withBOOL(billComingDueMessageSent)),
            )
        dynamoDbDAO.updateItem(updateRequest)
    }

    override fun saveSettlementFundsTransfer(settlementFundsTransfer: SettlementFundsTransfer) {
        val entity = SettlementFundsTransferEntity().apply {
            primaryKey = settlementFundsTransfer.billId.value
            scanKey = "$settlementFundsTransferKey#${settlementFundsTransfer.bankOperationId.value}"
            gSIndex1PrimaryKey = settlementFundsTransferKey
            gSIndex1ScanKey = settlementFundsTransfer.created.format(dateTimeFormat)
            amount = settlementFundsTransfer.amount
            status = settlementFundsTransfer.status
            created = settlementFundsTransfer.created.format(dateTimeFormat)
            bankOperationId = settlementFundsTransfer.bankOperationId.value
            transactionId = settlementFundsTransfer.transactionId.value
            sourceAccountType = settlementFundsTransfer.sourceAccountType
        }
        dynamoDbDAO.save(entity)
    }

    override fun findSettlementFundsTransfers(billId: BillId): List<SettlementFundsTransfer> {
        val entities = dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = billId.value,
            scanKey = settlementFundsTransferKey,
            type = SettlementFundsTransferEntity::class.java,
        )

        return entities.map { entity ->
            SettlementFundsTransfer(
                bankOperationId = BankOperationId(value = entity.bankOperationId),
                billId = BillId(value = entity.primaryKey),
                status = entity.status,
                amount = entity.amount,
                created = LocalDateTime.parse(entity.created, dateTimeFormat),
                transactionId = TransactionId(entity.transactionId),
                sourceAccountType = entity.sourceAccountType
                    ?: SettlementFundsTransferType.SETTLEMENT_ACCOUNT,
                sameBank = entity.sameBank,
            )
        }
    }

    override fun getStuckedProcessingBills(seconds: Long): List<BillView> {
        return dynamoDbDAO.queryIndex3OnHashKeyValue(
            partitionKey = BillStatus.PROCESSING.name,
            type = BillEntity::class.java,
        ).filter {
            val processingTime = readIndex3DateTime(it)
            processingTime?.plusSeconds(seconds)?.isBefore(getZonedDateTime()) ?: true
        }.map { convertToBill(it) }
    }

    override fun getPaidBills(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        types: List<BillType>?,
    ): List<BillView> {
        val readValues = dynamoDbDAO.queryIndex3OnHashKeyAndRangeKeyBetween(
            partitionKey = BillStatus.PAID.name,
            initialScanKey = "${walletId.value}#${startDate.format(dateTimeFormat)}",
            finalScanKey = "${walletId.value}#${endDate.format(dateTimeFormat)}",
            type = BillEntity::class.java,
        ).map { convertToBill(it) }

        return types?.let {
            readValues.filter { it.billType in types }
        } ?: readValues
    }

    override fun refund(
        walletId: WalletId,
        billId: BillId,
        transactionId: TransactionId,
        type: BillType,
        amount: Long,
        originalPaidDate: ZonedDateTime,
    ) {
        val refundDate = getZonedDateTime().format(dateTimeFormat)
        dynamoDbDAO.save(
            RefundedBillEntity().apply {
                this.primaryKey = walletId.value
                this.scanKey = "$REFUNDED_BILL_ENTITY_SCAN_PREFIX#$refundDate"
                this.billId = billId.value
                this.amount = amount
                this.transactionId = transactionId.value
                this.refundDate = refundDate
                this.walletId = walletId.value
                this.type = type
                this.originalPaidDate = originalPaidDate.format(dateTimeFormat)
            },
        )
    }

    override fun getRefundedBills(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
    ): List<RefundedBill> {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBetween(
            primaryKey = walletId.value,
            initialScanKey = "$REFUNDED_BILL_ENTITY_SCAN_PREFIX#${startDate.format(dateTimeFormat)}",
            finalScanKey = "$REFUNDED_BILL_ENTITY_SCAN_PREFIX#${endDate.format(dateTimeFormat)}",
            type = RefundedBillEntity::class.java,
        ).map {
            RefundedBill(
                amount = it.amount,
                billId = BillId(it.billId),
                type = it.type,
                transactionId = TransactionId(value = it.transactionId),
                refundDate = ZonedDateTime.parse(it.refundDate, dateTimeFormat),
                originalPaidDate = ZonedDateTime.parse(it.originalPaidDate, dateTimeFormat),
            )
        }
    }

    override fun getTotalPaid(
        walletId: WalletId,
        startDate: ZonedDateTime,
        endDate: ZonedDateTime,
        types: List<BillType>?,
    ): Long {
        val paidBills = getPaidBills(walletId, startDate, endDate, types).filter { it.source !is ActionSource.OpenFinance }
        logger.trace(Markers.append("paidBills", paidBills.map { it.billId }), "getTotalPaid")
        return paidBills.sumOf { it.amountPaid ?: 0 }
    }

    override fun getTotalPaidBillsByScheduleSourceType(walletId: WalletId, startDate: ZonedDateTime, endDate: ZonedDateTime, actionSource: ActionSource): Long {
        return getPaidBills(walletId, startDate, endDate)
            .filter { it.scheduleSource?.name() == actionSource.name() }
            .sumOf { it.amountTotal }
    }

    private fun convertToBill(billEntity: BillEntity): BillView {
        val fixedPaymentLimitTime = if (billEntity.type == BillType.INVOICE) {
            tedLimitTime
        } else {
            billEntity.paymentLimitTime
        }
        val source: ActionSource = try {
            mapper.readerFor(ActionSource::class.java).readValue(billEntity.source)
        } catch (e: JsonProcessingException) {
            logger.error(Markers.append("billEntity", billEntity), "DynamoDbBillRepositoryConvertToBill", e)
            ActionSource.System
        }
        val scheduleSource = billEntity.scheduleSource?.let {
            try {
                mapper.readerFor(ActionSource::class.java).readValue<ActionSource>(it)
            } catch (e: JsonProcessingException) {
                logger.error(Markers.append("billEntity", billEntity), "DynamoDbBillRepositoryConvertToBill", e)
                null
            }
        }

        val pixQrCodeData = billEntity.pixQrCodeData?.let { document ->
            PixQrCodeData(
                type = document.type,
                info = document.info,
                additionalInfo = document.additionalInfo,
                pixId = document.pixId,
                fixedAmount = document.fixedAmount,
                expiration = document.expiration?.let { ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME) },
                originalAmount = document.originalAmount,
            )
        }

        val bill = BillView(
            walletId = WalletId(billEntity.scanKey),
            amount = billEntity.amount,
            discount = billEntity.discount,
            interest = billEntity.interest,
            fine = billEntity.fine,
            amountTotal = billEntity.amountTotal,
            paymentLimitTime = LocalTime.parse(fixedPaymentLimitTime, timeFormat),
            assignor = billEntity.assignor,
            barCode = billEntity.digitableBarCode?.let { BarCode.ofDigitable(it) },
            billId = BillId(billEntity.primaryKey),
            externalId = ExternalBillId.of(billEntity.externalId, billEntity.externalProvider),
            status = BillStatus.fromString(billEntity.status),
            billType = billEntity.type,
            notification = billEntity.notification,
            recipient = toRecipient(billEntity.billRecipient, pixQrCodeData),
            createdOn = toLocalDateTime(billEntity.created),
            isBillComingDueMessageSent = billEntity.billComingDueMessageSent,
            billDescription = billEntity.description ?: StringUtil.EMPTY_STRING,
            amountPaid = billEntity.calculateAmountPaid(),
            paidDate = billEntity.paidDate?.let { tryToLocalDateTime(it) },
            dueDate = LocalDate.parse(billEntity.dueDate, dateFormat),
            effectiveDueDate = billEntity.effectiveDueDate?.let {
                LocalDate.parse(
                    billEntity.effectiveDueDate,
                    dateFormat,
                )
            } ?: LocalDate.parse(billEntity.dueDate, dateFormat),
            lastSettleDate = billEntity.lastSettleDate?.let { LocalDate.parse(it, dateFormat) },
            expirationDate = billEntity.expirationDate?.let { LocalDate.parse(it, dateFormat) },
            payerDocument = billEntity.payerDocument,
            payerName = billEntity.payerName,
            warningCode = WarningCode.getByCode(billEntity.warningCode),
            schedule = billEntity.takeIf { it.scheduledDate != null }?.let {
                BillViewSchedule(
                    batchSchedulingId = it.batchSchedulingId?.let { id -> BatchSchedulingId(id) },
                    date = LocalDate.parse(it.scheduledDate!!, dateFormat),
                    waitingFunds = it.waitingFunds.getOrFalse(),
                    waitingRetry = it.waitingRetry.getOrFalse(),
                )
            },
            payerAlias = billEntity.payerAlias,
            amountCalculationModel = billEntity.amountCalculationModel ?: AmountCalculationModel.UNKNOWN,
            amountCalculationOption = billEntity.amountCalculationOption,
            amountCalculationValidUntil = billEntity.amountCalculationValidUntil?.let { LocalDate.parse(it, dateFormat) },
            recurrenceRule = billEntity.recurrenceRule?.let {
                RecurrenceRule(
                    frequency = it.frequency,
                    startDate = LocalDate.parse(it.startDate, dateFormat),
                    pattern = it.pattern.orEmpty(),
                    endDate = it.endDate?.let { endDate -> LocalDate.parse(endDate, dateFormat) },
                )
            },
            fichaCompensacaoType = billEntity.fichaCompensacaoType,
            visibleBy = billEntity.visibleBy.map {
                AccountId(it)
            },
            source = source,
            idNumber = billEntity.fichaCompensacaoIdNumber,
            markedAsPaidBy = billEntity.markedAsPaidBy?.let { AccountId(it) },
            partnerPayment = billEntity.partnerPayment?.let {
                PartnerPayment(partner = it.partner, partnerPaymentId = it.paymentId)
            },
            subscriptionFee = billEntity.subscriptionFee ?: false,
            securityValidationResult = billEntity.securityValidationResult,
            paymentDetails = billEntity.paymentMethodsDetail?.toPaymentMethodsDetail(),
            transactionCorrelationId = billEntity.transactionCorrelationId,
            tags = billEntity.tags ?: emptySet(),
            categoryId = billEntity.category?.let { PFMCategoryId(billEntity.category!!.billCategoryId) } ?: billEntity.categoryId?.let { PFMCategoryId(value = it) },
            categorySuggestions = billEntity.categorySuggestions.map {
                BillCategorySuggestion(categoryId = PFMCategoryId(it.categoryId), probability = it.probability.toInt())
            },
            pixQrCodeData = pixQrCodeData,
            divergentPayment = billEntity.divergentPayment.toDivergentPayment(),
            partialPayment = billEntity.partialPayment.toPartialPayment(),
            brand = billEntity.brand,
            scheduledBy = billEntity.scheduledBy?.let { AccountId(it) },
            scheduleSource = scheduleSource,
            paymentWalletId = billEntity.paymentWalletId?.let { WalletId(it) },
            refundedAt = billEntity.refundedAt?.let { tryToLocalDateTime(it) },
            markedAsPaidAt = billEntity.markedAsPaidAt?.let { tryToLocalDateTime(it) },
            ignoredAt = billEntity.ignoredAt?.let { tryToLocalDateTime(it) },
            goalId = billEntity.goalId?.let { GoalId(it) },
        )
        return bill
    }

    private fun BillEntity.calculateAmountPaid(): Long? {
        return when (BillStatus.fromString(status)) {
            BillStatus.PAID, BillStatus.ALREADY_PAID, BillStatus.PAID_ON_PARTNER -> amountPaid ?: amountTotal
            else -> null
        }
    }

    private fun toLocalDateTime(date: String): LocalDateTime {
        val dateTime = ZonedDateTime.from(dateTimeFormat.withZone(brazilTimeZone).parse(date))
        return dateTime.toLocalDateTime()
    }

    private fun tryToLocalDateTime(date: String): LocalDateTime {
        return try {
            toLocalDateTime(date)
        } catch (e: Exception) {
            LocalDate.parse(date, dateFormat).atStartOfDay()
        }
    }

    private inline fun buildIndex3ScanKey(bill: Bill): String {
        val suffix = when {
            bill.isPaid() -> bill.paidDate!!
            bill.isProcessing() -> bill.history.lastOrNull { it is PaymentStarted }?.created
            else -> bill.history.lastOrNull()?.created
        }?.let {
            Instant.ofEpochMilli(it).atZone(brazilTimeZone).format(dateTimeFormat)
        } ?: ""

        return "${bill.walletId.value}#$suffix"
    }

    private inline fun readIndex3DateTime(billEntity: BillEntity): ZonedDateTime? {
        val split = billEntity.gSIndex3ScanKey?.split("#") ?: return null

        if (split.size < 2) {
            return null
        }

        return ZonedDateTime.parse(split[1], dateTimeFormat)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(DynamoDbBillRepository::class.java)
    }
}

private fun PartialPayment?.toPartialPaymentDocument(): PartialPaymentDocument? {
    return this?.let {
        PartialPaymentDocument().apply {
            acceptPartialPayment = it.acceptPartialPayment
            qtdPagamentoParcial = it.qtdPagamentoParcial
            qtdPagamentoParcialRegistrado = it.qtdPagamentoParcialRegistrado
            saldoAtualPagamento = it.saldoAtualPagamento
        }
    }
}

private fun DivergentPayment?.toDivergentPaymentDocument(): DivergentPaymentDocument? {
    return this?.let {
        DivergentPaymentDocument().apply {
            minimumAmount = it.minimumAmount
            maximumAmount = it.maximumAmount
            amountType = it.amountType
            minimumPercentage = it.minimumPercentage
            maximumPercentage = it.maximumPercentage
        }
    }
}

private fun PartialPaymentDocument?.toPartialPayment(): PartialPayment? {
    return this?.let {
        PartialPayment(
            acceptPartialPayment = acceptPartialPayment,
            qtdPagamentoParcial = qtdPagamentoParcial,
            qtdPagamentoParcialRegistrado = qtdPagamentoParcialRegistrado,
            saldoAtualPagamento = saldoAtualPagamento,
        )
    }
}

private fun DivergentPaymentDocument?.toDivergentPayment(): DivergentPayment? {
    return this?.let {
        DivergentPayment(
            minimumAmount = minimumAmount,
            maximumAmount = maximumAmount,
            amountType = amountType,
            minimumPercentage = minimumPercentage,
            maximumPercentage = maximumPercentage,
        )
    }
}

private val YearMonthIsoFormatter = DateTimeFormatter.ofPattern("yyyy-MM")

private fun YearMonth.toIsoString(): String = this.format(YearMonthIsoFormatter)

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class RefundedBillEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBAttribute(attributeName = "BillId")
    lateinit var billId: String

    @DynamoDBAttribute(attributeName = "Amount")
    var amount: Long = 0

    @DynamoDBAttribute(attributeName = "TransactionId")
    lateinit var transactionId: String

    @DynamoDBAttribute(attributeName = "RefundDate")
    lateinit var refundDate: String

    @DynamoDBAttribute(attributeName = "OriginalPaidDate")
    lateinit var originalPaidDate: String

    @DynamoDBAttribute(attributeName = "WalletId")
    lateinit var walletId: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "BillType")
    lateinit var type: BillType
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class SettlementFundsTransferEntity {

    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Status")
    lateinit var status: BankOperationStatus

    @DynamoDBAttribute(attributeName = "Amount")
    var amount: Long = 0

    @DynamoDBAttribute(attributeName = "Created")
    lateinit var created: String

    @DynamoDBAttribute(attributeName = "BankOperationId")
    lateinit var bankOperationId: String

    @DynamoDBAttribute(attributeName = "TransactionId")
    lateinit var transactionId: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "SourceAccountType")
    var sourceAccountType: SettlementFundsTransferType? = null

    @DynamoDBAttribute(attributeName = "SameBank")
    var sameBank: Boolean? = null
}