package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.CreditCardChallenge
import ai.friday.billpayment.app.account.CreditCardChallengeStatus
import ai.friday.billpayment.app.integrations.CreditCardChallengeRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

private const val CREDIT_CARD_CHALLENGE_PREFIX = "CREDIT-CARD-CHALLENGE"

@FridayMePoupe
class CreditCardChallengeDbRepository(
    private val dynamoDbDAO: DynamoDbDAO,
) : CreditCardChallengeRepository {

    override fun findAll(
        paymentMethodId: AccountPaymentMethodId,
    ): List<CreditCardChallenge> {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = paymentMethodId.value,
            scanKey = CREDIT_CARD_CHALLENGE_PREFIX,
            type = CreditCardChallengeEntity::class.java,
            scanIndexForward = false,
        ).map { buildCreditCardChallenge(it) }
    }

    override fun find(
        paymentMethodId: AccountPaymentMethodId,
        status: CreditCardChallengeStatus,
    ): CreditCardChallenge? {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = paymentMethodId.value,
            scanKey = CREDIT_CARD_CHALLENGE_PREFIX,
            type = CreditCardChallengeEntity::class.java,
            scanIndexForward = false,
        ).firstOrNull() { it.status == status }?.let { buildCreditCardChallenge(it) }
    }

    override fun save(creditCardChallenge: CreditCardChallenge) {
        dynamoDbDAO.save(
            CreditCardChallengeEntity().apply {
                primaryKey = creditCardChallenge.paymentMethodId.value
                scanKey = "$CREDIT_CARD_CHALLENGE_PREFIX#${creditCardChallenge.createdAt.toInstant().toEpochMilli()}"
                value = creditCardChallenge.value.toString()
                alternativeValue = creditCardChallenge.alternativeValue.toString()
                accountId = creditCardChallenge.accountId.value
                attempts = creditCardChallenge.attempts
                status = creditCardChallenge.status
                createdAt = creditCardChallenge.createdAt.format(DateTimeFormatter.ISO_DATE_TIME)
                updatedAt = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)
                expiresAt = creditCardChallenge.expiresAt.format(DateTimeFormatter.ISO_DATE_TIME)
                authorizationCode = creditCardChallenge.authorizationCode
                acquirerOrderId = creditCardChallenge.acquirerOrderId
                alternativeAcquirerOrderId = creditCardChallenge.alternativeAcquirerOrderId
                gSIndex1PrimaryKey = CREDIT_CARD_CHALLENGE_PREFIX
                gSIndex1ScanKey = creditCardChallenge.status.name
            },
        )
    }

    fun findAll(creditCardChallengeStatus: CreditCardChallengeStatus): List<CreditCardChallenge> =
        dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
            primaryKey = CREDIT_CARD_CHALLENGE_PREFIX,
            scanKey = creditCardChallengeStatus.name,
            type = CreditCardChallengeEntity::class.java,
        ).map { buildCreditCardChallenge(it) }

    private fun buildCreditCardChallenge(entity: CreditCardChallengeEntity): CreditCardChallenge {
        with(entity) {
            return CreditCardChallenge(
                paymentMethodId = AccountPaymentMethodId(primaryKey),
                accountId = AccountId(accountId),
                value = value.toLong(),
                alternativeValue = alternativeValue?.toLong(),
                attempts = attempts,
                status = status,
                createdAt = ZonedDateTime.parse(createdAt, DateTimeFormatter.ISO_DATE_TIME),
                updatedAt = ZonedDateTime.parse(updatedAt, DateTimeFormatter.ISO_DATE_TIME),
                expiresAt = ZonedDateTime.parse(expiresAt, DateTimeFormatter.ISO_DATE_TIME),
                authorizationCode = authorizationCode,
                acquirerOrderId = acquirerOrderId,
                alternativeAcquirerOrderId = alternativeAcquirerOrderId,
            )
        }
    }
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class CreditCardChallengeEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // PAYMENT-METHOD-ID

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // CREDIT-CARD-CHALLENGE#{TIMESTAMP}

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String // CREDIT-CARD-CHALLENGE

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String // STATUS

    @DynamoDBAttribute(attributeName = "RegisterType")
    var registerType: String = "CreditCardChallenge"

    @DynamoDBAttribute(attributeName = "TokenValue")
    lateinit var value: String

    @DynamoDBAttribute(attributeName = "AlternativeTokenValue")
    var alternativeValue: String? = null

    @DynamoDBAttribute(attributeName = "AccountId")
    lateinit var accountId: String

    @DynamoDBAttribute(attributeName = "Attempts")
    var attempts: Int = 0

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Status")
    lateinit var status: CreditCardChallengeStatus

    @DynamoDBAttribute(attributeName = "CreatedAt")
    lateinit var createdAt: String

    @DynamoDBAttribute(attributeName = "UpdatedAt")
    lateinit var updatedAt: String

    @DynamoDBAttribute(attributeName = "ExpiresAt")
    lateinit var expiresAt: String

    @DynamoDBAttribute(attributeName = "AuthorizationCode")
    lateinit var authorizationCode: String

    @DynamoDBAttribute(attributeName = "AcquirerOrderId")
    lateinit var acquirerOrderId: String

    @DynamoDBAttribute(attributeName = "AlternativeAcquirerOrderId")
    var alternativeAcquirerOrderId: String? = null
}