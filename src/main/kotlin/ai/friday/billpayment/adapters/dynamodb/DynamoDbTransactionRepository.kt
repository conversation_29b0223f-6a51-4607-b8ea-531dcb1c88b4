package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.adapters.dynamodb.TransactionEntity.Companion.buildIndexScanKey
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.MultiplePaymentData
import ai.friday.billpayment.app.payment.NoPaymentData
import ai.friday.billpayment.app.payment.Payer
import ai.friday.billpayment.app.payment.PaymentData
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithCreditCard
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.SinglePaymentMethodsDetail
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.removePrefix
import ai.friday.billpayment.app.usage.TransactionAmount
import ai.friday.billpayment.app.usage.TransactionItemAmount
import ai.friday.billpayment.app.usage.sum
import ai.friday.billpayment.app.wallet.WalletId
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

private const val CREDIT_CARD_AUTHORIZATION_KEY = "CREDIT_CARD_AUTHORIZATION"

@FridayMePoupe
class DynamoDbTransactionRepository(
    private val dynamoDbDAO: DynamoDbDAO,
    private val accountRepository: AccountRepository,
    private val transactionEntityConverter: TransactionEntityConverter,
) : TransactionRepository {

    private val mapper by lazy { jacksonObjectMapper() }

    override fun save(transaction: Transaction) {
        val markers = append("transactionId", transaction.id.value)
            .andAppend("transaction", transaction)
        LOG.info(markers, "DynamoDbTransactionRepository#save")

        val transactionSettlementTargetId = transactionEntityConverter.toSettlementTargetId(transaction.settlementData.settlementTarget)

        val indexScanKey = buildIndexScanKey(
            transactionStatus = transaction.status,
            settlementOperation = transaction.settlementData.settlementOperation,
        )

        val entity = TransactionEntity().apply {
            primaryKey = transaction.id.value
            scanKey = transaction.walletId.value
            gSIndex1PrimaryKey = transaction.walletId.value
            gSIndex1ScanKey = indexScanKey
            gSIndex2PrimaryKey = transactionSettlementTargetId
            gSIndex2ScanKey = indexScanKey
            nsu = transaction.nsu
            status = transaction.status
            created = transaction.created.format(DateTimeFormatter.ISO_DATE_TIME)
            type = transaction.type
            settlementTargetId = transactionSettlementTargetId
            totalAmount = transaction.settlementData.totalAmount
            payerAccountId = transaction.payer.accountId.value
            payerDocument = transaction.payer.document
            payerName = transaction.payer.name
            actionSource = mapper.writeValueAsString(transaction.actionSource)
            correlationId = transaction.correlationId
            bankTransactionId = transaction.settlementOperationId() ?: 0
            when (transaction.paymentData) {
                is SinglePaymentData -> {
                    paymentData = PaymentDataDocument().apply {
                        this.paymentMethodId = transaction.paymentData.accountPaymentMethod.id.value
                        this.payment = transaction.paymentData.payment?.let {
                            mapper.writeValueAsString(transactionEntityConverter.toPaymentOperationEntityType(it))
                        }
                        this.infoData = transaction.paymentData.details.toPaymentMethodsDetailEntity()
                    }
                    paymentDataList = null
                    paymentDataNoOp = false
                }

                is MultiplePaymentData -> {
                    paymentData = null
                    paymentDataList = transaction.paymentData.payments.map { currentPaymentData ->
                        PaymentDataDocument().apply {
                            paymentMethodId = currentPaymentData.accountPaymentMethod.id.value
                            this.payment = currentPaymentData.payment?.let {
                                mapper.writeValueAsString(transactionEntityConverter.toPaymentOperationEntityType(it))
                            }
                            infoData = currentPaymentData.details.toPaymentMethodsDetailEntity()
                        }
                    }
                    paymentDataNoOp = false
                }

                NoPaymentData -> {
                    paymentData = null
                    paymentDataList = null
                    paymentDataNoOp = true
                }
            }

            settlementData = SettlementDataDocument().apply {
                targetId = transactionSettlementTargetId
                serviceAmountTax = transaction.settlementData.serviceAmountTax
                totalAmount = transaction.settlementData.totalAmount
                operation =
                    buildSettlementOperation(settlementOperation = transaction.settlementData.settlementOperation)
            }
        }
        val creditCardAuthorizations = transaction.paymentData.retrieveAllCreditCardAuthorization()
        if (creditCardAuthorizations.isNotEmpty()) {
            entity.paymentMethodType = PaymentMethodType.CREDIT_CARD
        }
        dynamoDbDAO.save(entity)

        creditCardAuthorizations.forEach { (details, creditCardAuthorization) ->
            val trackEntity = CreditCardChargebackTrackEntity().apply {
                primaryKey = "$CREDIT_CARD_AUTHORIZATION_KEY#${creditCardAuthorization.tid}"
                scanKey = "#${creditCardAuthorization.authorizationCode}"
                transactionId = transaction.id.value
                payerAccountId = transaction.payer.accountId.value
                walletId = transaction.walletId.value
                paymentMethodId = details.paymentMethodId.value
                settlementTargetId = entity.settlementTargetId
                settlementAmountTotal = entity.totalAmount
                paymentMethodNetAmount = details.netAmount
                paymentMethodFeeAmount = details.feeAmount
                created = transaction.created.format(DateTimeFormatter.ISO_DATE_TIME)
            }
            dynamoDbDAO.save(trackEntity)
        }
    }

    override fun findCreditCardUsage(accountId: AccountId, walletId: WalletId, datePattern: String): TransactionAmount {
        return findUsage(accountId, walletId, datePattern, "PaymentMethodType", "CREDIT_CARD")
    }

    override fun findTransactions(walletId: WalletId): List<Transaction> {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
            primaryKey = walletId.value,
            scanKey = "TRANSACTION",
            type = TransactionEntity::class.java,
        ).map {
            it.toTransaction()
        }
    }

    override fun findTransactions(billId: BillId, status: TransactionStatus?): List<Transaction> {
        val statusString = status?.let { "${it.name}#" } ?: ""
        return dynamoDbDAO.queryIndex2OnHashKeyValueAndRangeKeyBeginsWith(
            primaryKey = billId.value,
            scanKey = "TRANSACTION#$statusString",
            type = TransactionEntity::class.java,
        ).map {
            it.toTransaction()
        }
    }

    private fun findUsage(
        accountId: AccountId,
        walletId: WalletId,
        datePattern: String,
        attributeName: String,
        attributeValue: String,
    ): TransactionAmount {
        val expressionAttributeNames =
            mapOf(
                "#status" to "Status",
                "#created" to "Created",
                "#attributeName" to attributeName,
                "#payerAccountId" to "PayerAccountId",
            )
        val expressionAttributeValues = mapOf(
            ":val1" to AttributeValue(walletId.value),
            ":val2" to AttributeValue("TRANSACTION"),
            ":val3" to AttributeValue(TransactionStatus.COMPLETED.name),
            ":val4" to AttributeValue(TransactionStatus.PROCESSING.name),
            ":val5" to AttributeValue(datePattern),
            ":val6" to AttributeValue(attributeValue),
            ":val7" to AttributeValue(accountId.value),
        )

        val queryExpression = DynamoDBQueryExpression<TransactionEntity>()
            .withIndexName("GSIndex1")
            .withConsistentRead(false)
            .withKeyConditionExpression("GSIndex1PrimaryKey = :val1 and begins_with(GSIndex1ScanKey, :val2)")
            .withFilterExpression("(#status = :val3 or #status = :val4) and begins_with(#created, :val5) and #attributeName = :val6 and #payerAccountId = :val7")
            .withExpressionAttributeNames(expressionAttributeNames)
            .withExpressionAttributeValues(expressionAttributeValues)
        val itemList = dynamoDbDAO.queryWithDynamoDBQueryExpression(queryExpression, TransactionEntity::class.java)
        return itemList.map { transactionEntity: TransactionEntity ->
            transactionEntity.toCreditCardTransactionAmount()
        }.flatten().sum()
    }

    private fun TransactionEntity.toCreditCardTransactionAmount(): List<TransactionAmount> {
        val paymentData = this.paymentData
        return if (paymentData != null) {
            val paymentMethodId = AccountPaymentMethodId(paymentData.paymentMethodId)

            val itemAmount = paymentData.infoData?.let {
                val info = it as PaymentMethodsDetailCreditCardEntity
                TransactionItemAmount(
                    paymentMethodId = AccountPaymentMethodId(it.paymentMethodId),
                    totalAmount = info.totalAmount,
                    feeAmount = info.feeAmount,
                )
            } ?: this.settlementData?.let {
                TransactionItemAmount(
                    paymentMethodId = paymentMethodId,
                    totalAmount = it.totalAmount,
                    feeAmount = it.serviceAmountTax,
                )
            } ?: TransactionItemAmount(
                paymentMethodId = paymentMethodId,
                totalAmount = totalAmount,
                feeAmount = 0,
            )

            listOf(TransactionAmount(itemAmount))
        } else {
            this.paymentDataList!!.mapNotNull {
                val info = it.infoData!!
                if (info is PaymentMethodsDetailCreditCardEntity) {
                    TransactionAmount(
                        TransactionItemAmount(
                            paymentMethodId = AccountPaymentMethodId(it.paymentMethodId),
                            totalAmount = info.totalAmount,
                            feeAmount = info.feeAmount,
                        ),
                    )
                } else {
                    null
                }
            }
        }
    }

    override fun getBankTransactionId(id: TransactionId): Long {
        val transactionEntity = dynamoDbDAO.queryTableOnHashKey(
            id.value,
            TransactionEntity::class.java,
        ).single()
        return transactionEntity.toTransaction().settlementData.getOperation<BoletoSettlementResult>().bankTransactionId.toLong()
    }

    override fun getAuthentication(id: TransactionId): String {
        val transactionEntity = dynamoDbDAO.queryTableOnHashKey(
            id.value,
            TransactionEntity::class.java,
        ).single()

        return transactionEntity.toTransaction().settlementData.getOperation<BankTransfer>().authentication
    }

    override fun findById(id: TransactionId): Transaction {
        val transactionEntities = dynamoDbDAO.queryTableOnHashKey(id.value, TransactionEntity::class.java)
        val transactionEntity =
            transactionEntities.stream().findFirst().orElseThrow { ItemNotFoundException("Transaction $id not found") }
        return transactionEntity.toTransaction()
    }

    override fun findByIdAndWallet(
        id: TransactionId,
        walletId: WalletId,
        transactionType: TransactionType?,
    ): Transaction {
        val transactionEntities =
            dynamoDbDAO.queryTableOnHashKeyAndRangeKey(id.value, walletId.value, TransactionEntity::class.java)
        val transactionEntity =
            transactionEntities.stream().filter { transactionType == null || it.type == transactionType }
                .findFirst()
                .orElseThrow { ItemNotFoundException("transaction $id not found for wallet $walletId${transactionType.let { " with type $it" }}") }
        return transactionEntity.toTransaction()
    }

    /*
        FIXME: remover o beginsWith da busca de transation
        Mudamos o prefixo do BankOperationId de (BANK_OPERATION-$UUID) para (OPERATION-$UUID) porque o arbi só guarda 50 caracteres. Para não gerar bug com as transações em andamento deixamos o BeginsWith por enquanto.
     */
    override fun findByWalletAndStatusAndBankOperationId(
        walletId: WalletId,
        status: TransactionStatus,
        bankOperationId: BankOperationId,
    ): Transaction {
        val transactionEntities = dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
            walletId.value,
            buildIndexScanKey(status, bankOperationId),
            TransactionEntity::class.java,
        )
        val transactionEntity = transactionEntities.stream().findFirst()
            .orElseThrow { ItemNotFoundException("transaction not found for wallet $walletId, status $status and bankOperationId ${bankOperationId.value}") }
        return transactionEntity.toTransaction()
    }

    override fun findTransactionTypeByWalletAndNSU(walletId: WalletId, nsu: Long): Pair<String, String> {
        return dynamoDbDAO.findTransactionTypeByAccountIdAndNSU(walletId.value, nsu)
    }

    override fun findTransactionIdByWalletAndNSU(walletId: WalletId, nsu: Long): TransactionId? =
        dynamoDbDAO.findTransactionTypeByAccountIdAndNSU(walletId.value, nsu)
            .takeIf { it.first.isNotEmpty() }
            ?.let { TransactionId(it.first) }

    override fun findTransactionWalletId(id: TransactionId): WalletId? {
        return dynamoDbDAO.queryTableOnHashKey(id.value, TransactionEntity::class.java).map {
            WalletId(it.scanKey)
        }.firstOrNull()
    }

    override fun findByWalletAndStatusAndType(
        walletId: WalletId,
        transactionStatus: TransactionStatus,
        transactionType: TransactionType,
    ): List<Transaction> {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
            primaryKey = walletId.value,
            scanKey = buildIndexScanKey(transactionStatus),
            type = TransactionEntity::class.java,
        )
            .filter { transactionType == it.type }
            .map { it.toTransaction() }
    }

    override fun hasTransactionByWalletAndStatus(
        walletId: WalletId,
        transactionStatus: TransactionStatus,
    ): Boolean {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
            primaryKey = walletId.value,
            scanKey = buildIndexScanKey(transactionStatus),
            type = TransactionEntity::class.java,
        ).isNotEmpty()
    }

    private fun buildSettlementOperation(settlementOperation: SettlementOperation?): String? {
        return settlementOperation?.let { mapper.writeValueAsString(transactionEntityConverter.toSettlementOperationEntityType(it)) }
    }

    private fun TransactionEntity.toTransaction(): Transaction {
        val payer = Payer(
            AccountId(payerAccountId ?: gSIndex1PrimaryKey),
            payerDocument.orEmpty(),
            payerName.orEmpty(),
        )

        val settlementTarget = transactionEntityConverter.toSettlementTarget(this)

        val settlementOperation = settlementData?.operation?.let {
            val settlementOperationEntityType = mapper.readValue(it, SettlementOperationEntityType::class.java)
            transactionEntityConverter.toSettlementOperation(settlementOperationEntityType, settlementData!!)
        }

        val transactionId = TransactionId(primaryKey)

        return Transaction(
            id = transactionId,
            created = ZonedDateTime.parse(created, DateTimeFormatter.ISO_DATE_TIME),
            walletId = WalletId(gSIndex1PrimaryKey),
            status = status,
            type = type,
            payer = payer,
            paymentData = this.toPaymentData(payer.accountId),
            settlementData = SettlementData(
                settlementTarget = settlementTarget,
                serviceAmountTax = settlementData?.serviceAmountTax ?: 0,
                totalAmount = settlementData?.totalAmount ?: 0,
                settlementOperation = settlementOperation,
            ),
            nsu = nsu,
            actionSource = mapper.readerFor(ActionSource::class.java).readValue(actionSource),
            correlationId = this.correlationId ?: transactionId.removePrefix(),
        )
    }

    private fun TransactionEntity.buildPaymentMethodsDetails(paymentMethodId: AccountPaymentMethodId): SinglePaymentMethodsDetail {
        return when (type) {
            TransactionType.CASH_IN -> {
                val feeAmount = settlementData!!.serviceAmountTax
                val netAmount = settlementData!!.totalAmount - feeAmount

                PaymentMethodsDetailWithCreditCard(
                    paymentMethodId = paymentMethodId,
                    netAmount = netAmount,
                    feeAmount = feeAmount,
                    installments = 1,
                    calculationId = null,
                    fee = 100.0 * feeAmount / netAmount,
                )
            }
            TransactionType.GOAL_REDEMPTION -> throw IllegalStateException("should not happen, GOAL_REDEMPTION does not have payment data")
            TransactionType.BOLETO_PAYMENT,
            TransactionType.INVOICE_PAYMENT,
            TransactionType.DIRECT_INVOICE,
            TransactionType.GOAL_INVESTMENT,
            -> {
                PaymentMethodsDetailWithBalance(
                    amount = totalAmount,
                    paymentMethodId = paymentMethodId,
                    calculationId = null,
                )
            }
        }
    }

    private fun TransactionEntity.toPaymentData(accountId: AccountId): PaymentData {
        fun toSimplePaymentData(paymentData: PaymentDataDocument): SinglePaymentData {
            val paymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
                AccountPaymentMethodId(paymentData.paymentMethodId),
                accountId,
            )

            val paymentOperation = paymentData.payment?.let {
                val paymentEntityType: PaymentOperationEntityType =
                    mapper.readerFor(PaymentOperationEntityType::class.java)
                        .readValue(paymentData.payment)
                transactionEntityConverter.toPaymentOperation(paymentEntityType)
            }

            val details = paymentData.infoData?.let {
                it.toPaymentMethodsDetail() as SinglePaymentMethodsDetail
            } ?: buildPaymentMethodsDetails(paymentMethod.id)

            return SinglePaymentData(
                accountPaymentMethod = paymentMethod,
                payment = paymentOperation,
                details = details,
            )
        }

        return if (paymentData != null) {
            toSimplePaymentData(paymentData!!)
        } else if (paymentDataList != null) {
            MultiplePaymentData(
                payments = paymentDataList!!.map {
                    toSimplePaymentData(it)
                },
            )
        } else if (paymentDataNoOp) {
            NoPaymentData
        } else {
            throw IllegalStateException("Transaction must have payment data or payment data list")
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(DynamoDbTransactionRepository::class.java)
    }
}