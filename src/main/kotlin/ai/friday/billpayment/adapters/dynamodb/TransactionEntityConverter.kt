package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.bill.Bill
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.cashIn.CreditCardCashIn
import ai.friday.billpayment.app.cashIn.FraudPreventionErrors
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BankTransfer
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.CreditCardAuthorization
import ai.friday.billpayment.app.payment.FraudPreventionPaymentOperationDenied
import ai.friday.billpayment.app.payment.PaymentOperation
import ai.friday.billpayment.app.payment.SettlementOperation
import ai.friday.billpayment.app.payment.SettlementTarget
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.wallet.WalletId
import arrow.core.getOrElse
import jakarta.inject.Singleton
import kotlin.reflect.KClass
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

interface TransactionSettlementOperationConverter<D : SettlementOperation, E : SettlementOperationEntityType> {
    val domainType: KClass<D>
    val entityType: KClass<E>
    fun toDomain(element: SettlementOperationEntityType, settlementData: SettlementDataDocument): D
    fun toEntity(element: SettlementOperation): E
}

interface TransactionPaymentOperationConverter<D : PaymentOperation, E : PaymentOperationEntityType> {
    val domainType: KClass<D>
    val entityType: KClass<E>
    fun toDomain(element: PaymentOperationEntityType): D
    fun toEntity(element: PaymentOperation): E
}

interface TransactionSettlementTargetConverter<D : SettlementTarget> {
    val domainType: KClass<D>
    val transactionTypes: List<TransactionType>

    fun toDomain(settlementTargetId: String, transactionEntity: TransactionEntity): D
    fun toSettlementTargetId(element: SettlementTarget): String
}

@Singleton
class TransactionEntityConverter(
    private val settlementOperationConverters: List<TransactionSettlementOperationConverter<out SettlementOperation, out SettlementOperationEntityType>>,
    private val settlementTargetConverters: List<TransactionSettlementTargetConverter<out SettlementTarget>>,
    private val paymentOperationConverters: List<TransactionPaymentOperationConverter<out PaymentOperation, out PaymentOperationEntityType>>,
) {

    fun toSettlementOperationEntityType(settlementOperation: SettlementOperation): SettlementOperationEntityType {
        val converter = settlementOperationConverters.singleOrNull {
            it.domainType == settlementOperation::class
        } ?: throw IllegalArgumentException("No converter found for ${settlementOperation::class}")
        return converter.toEntity(settlementOperation)
    }

    fun toPaymentOperationEntityType(paymentOperation: PaymentOperation): PaymentOperationEntityType {
        val converter = paymentOperationConverters.singleOrNull {
            it.domainType == paymentOperation::class
        } ?: throw IllegalArgumentException("No converter found for ${paymentOperation::class}")
        return converter.toEntity(paymentOperation)
    }

    fun toSettlementTargetId(settlementTarget: SettlementTarget): String {
        val converter = settlementTargetConverters.singleOrNull {
            it.domainType == settlementTarget::class
        } ?: throw IllegalArgumentException("No converter found for ${settlementTarget::class}")
        return converter.toSettlementTargetId(settlementTarget)
    }

    fun toSettlementOperation(settlementOperationEntity: SettlementOperationEntityType, settlementData: SettlementDataDocument): SettlementOperation {
        val converter = settlementOperationConverters.singleOrNull {
            it.entityType == settlementOperationEntity::class
        } ?: throw IllegalArgumentException("No converter found for ${settlementOperationEntity::class}")
        return converter.toDomain(settlementOperationEntity, settlementData)
    }

    fun toPaymentOperation(paymentOperationEntity: PaymentOperationEntityType): PaymentOperation {
        val converter = paymentOperationConverters.singleOrNull {
            it.entityType == paymentOperationEntity::class
        } ?: throw IllegalArgumentException("No converter found for ${paymentOperationEntity::class}")
        return converter.toDomain(paymentOperationEntity)
    }

    fun toSettlementTarget(transactionEntity: TransactionEntity): SettlementTarget {
        val converter = settlementTargetConverters.singleOrNull {
            it.transactionTypes.contains(transactionEntity.type)
        } ?: throw IllegalArgumentException("No converter found for ${transactionEntity.type}")
        return converter.toDomain(transactionEntity.settlementTargetId, transactionEntity)
    }
}

@Singleton
class BoletoSettlementOperationConverter : TransactionSettlementOperationConverter<BoletoSettlementResult, SettlementOperationEntityType.BoletoPayment> {
    override val domainType = BoletoSettlementResult::class
    override val entityType = SettlementOperationEntityType.BoletoPayment::class

    override fun toDomain(element: SettlementOperationEntityType, settlementData: SettlementDataDocument): BoletoSettlementResult {
        return (element as SettlementOperationEntityType.BoletoPayment).toDomain()
    }

    override fun toEntity(element: SettlementOperation): SettlementOperationEntityType.BoletoPayment {
        return (element as BoletoSettlementResult).toEntity()
    }
}

@Singleton
class BankTransferSettlementOperationConverter : TransactionSettlementOperationConverter<BankTransfer, SettlementOperationEntityType.BankTransferSettlement> {
    override val domainType = BankTransfer::class
    override val entityType = SettlementOperationEntityType.BankTransferSettlement::class

    override fun toDomain(element: SettlementOperationEntityType, settlementData: SettlementDataDocument): BankTransfer {
        return (element as SettlementOperationEntityType.BankTransferSettlement).toDomain(settlementData)
    }

    override fun toEntity(element: SettlementOperation): SettlementOperationEntityType.BankTransferSettlement {
        return (element as BankTransfer).toEntity()
    }
}

@Singleton
class FraudPreventionPaymentOperationConverter : TransactionPaymentOperationConverter<FraudPreventionPaymentOperationDenied, PaymentOperationEntityType.FraudPrevention> {
    override val domainType = FraudPreventionPaymentOperationDenied::class
    override val entityType = PaymentOperationEntityType.FraudPrevention::class

    override fun toDomain(element: PaymentOperationEntityType): FraudPreventionPaymentOperationDenied {
        return (element as PaymentOperationEntityType.FraudPrevention).toDomain()
    }

    override fun toEntity(element: PaymentOperation): PaymentOperationEntityType.FraudPrevention {
        return (element as FraudPreventionPaymentOperationDenied).toEntity()
    }
}

@Singleton
class CreditCardAuthorizationPaymentOperationConverter : TransactionPaymentOperationConverter<CreditCardAuthorization, PaymentOperationEntityType.CreditCard> {
    override val domainType = CreditCardAuthorization::class
    override val entityType = PaymentOperationEntityType.CreditCard::class

    override fun toDomain(element: PaymentOperationEntityType): CreditCardAuthorization {
        return (element as PaymentOperationEntityType.CreditCard).toDomain()
    }

    override fun toEntity(element: PaymentOperation): PaymentOperationEntityType.CreditCard {
        return (element as CreditCardAuthorization).toEntity()
    }
}

@Singleton
class BalanceAuthorizationPaymentOperationConverter : TransactionPaymentOperationConverter<BalanceAuthorization, PaymentOperationEntityType.Balance> {
    override val domainType = BalanceAuthorization::class
    override val entityType = PaymentOperationEntityType.Balance::class

    override fun toDomain(element: PaymentOperationEntityType): BalanceAuthorization {
        return (element as PaymentOperationEntityType.Balance).toDomain()
    }

    override fun toEntity(element: PaymentOperation): PaymentOperationEntityType.Balance {
        return (element as BalanceAuthorization).toEntity()
    }
}

@Singleton
class BillSettlementTargetConverter(
    private val billEventRepository: BillEventRepository,
) : TransactionSettlementTargetConverter<Bill> {
    override val domainType = Bill::class
    override val transactionTypes = listOf(
        TransactionType.BOLETO_PAYMENT,
        TransactionType.INVOICE_PAYMENT,
        TransactionType.DIRECT_INVOICE,
        TransactionType.GOAL_INVESTMENT,
    )

    override fun toDomain(settlementTargetId: String, transactionEntity: TransactionEntity): Bill {
        return billEventRepository.getBillById(BillId(settlementTargetId))
            .getOrElse { throw IllegalStateException("settlement target $settlementTargetId not found") }
    }

    override fun toSettlementTargetId(element: SettlementTarget): String {
        return (element as Bill).billId.value
    }
}

@Singleton
class CreditCardCashInSettlementTargetConverter(
    private val walletRepository: WalletRepository,
) : TransactionSettlementTargetConverter<CreditCardCashIn> {
    private val logger = LoggerFactory.getLogger(CreditCardCashInSettlementTargetConverter::class.java)

    override val domainType = CreditCardCashIn::class
    override val transactionTypes = listOf(TransactionType.CASH_IN)

    override fun toDomain(settlementTargetId: String, transactionEntity: TransactionEntity): CreditCardCashIn {
        val walletId = WalletId(transactionEntity.gSIndex1PrimaryKey)
        val bankAccount = walletRepository.findAccountPaymentMethod(walletId)

        if (bankAccount.id != AccountPaymentMethodId(settlementTargetId)) {
            logger.error(
                Markers.append("ACTION", "VERIFY").andAppend("transactionEntity", transactionEntity)
                    .andAppend("bankAccountId", bankAccount.id)
                    .andAppend("settlementTargetId", settlementTargetId)
                    .andAppend(
                        "reason",
                        "bankAccountId != settlementTargetId",
                    ),
                "CreditCardCashInSettlementTargetConverter",
            )
            throw IllegalStateException("Transaction's SettlementTargetId must be the same as Wallet's Bank Account")
        }
        return if (transactionEntity.settlementData != null) {
            CreditCardCashIn(
                amount = transactionEntity.settlementData!!.totalAmount - transactionEntity.settlementData!!.serviceAmountTax,
                bankAccount = bankAccount,
            )
        } else {
            logger.error(
                Markers.append("ACTION", "VERIFY").andAppend("transactionEntity", transactionEntity)
                    .andAppend("bankAccountId", bankAccount.id)
                    .andAppend("settlementTargetId", settlementTargetId)
                    .andAppend(
                        "reason",
                        "settlementData == null",
                    ),
                "CreditCardCashInSettlementTargetConverter",
            )
            CreditCardCashIn(amount = transactionEntity.totalAmount, bankAccount = bankAccount)
        }
    }

    override fun toSettlementTargetId(element: SettlementTarget): String {
        return (element as CreditCardCashIn).bankAccount.id.value
    }
}

fun BoletoSettlementResult.toEntity(): SettlementOperationEntityType.BoletoPayment {
    return SettlementOperationEntityType.BoletoPayment(
        gateway = gateway.name,
        status = status.name,
        bankTransactionId = bankTransactionId,
        externalNsu = externalNsu,
        externalTerminal = externalTerminal,
        errorCode = errorCode,
        errorDescription = errorDescription,
        authentication = authentication,
        paymentPartnerName = paymentPartnerName,
        finalPartnerName = finalPartnerName,
    )
}
fun SettlementOperationEntityType.BoletoPayment.toDomain(): BoletoSettlementResult {
    return BoletoSettlementResult(
        gateway = FinancialServiceGateway.valueOf(
            this.gateway,
        ),
        status = BoletoSettlementStatus.valueOf(this.status),
        bankTransactionId = this.bankTransactionId,
        externalNsu = this.externalNsu,
        externalTerminal = this.externalTerminal,
        errorCode = this.errorCode,
        errorDescription = this.errorDescription,
        authentication = this.authentication,
        paymentPartnerName = this.paymentPartnerName,
        finalPartnerName = this.finalPartnerName,
    )
}

fun BankTransfer.toEntity(): SettlementOperationEntityType.BankTransferSettlement {
    return SettlementOperationEntityType.BankTransferSettlement(
        gateway = gateway.name,
        status = status,
        errorDescription = errorDescription,
        operationID = operationId.value,
        authentication = authentication,
    )
}
fun SettlementOperationEntityType.BankTransferSettlement.toDomain(settlementData: SettlementDataDocument): BankTransfer {
    return BankTransfer(
        operationId = BankOperationId(this.operationID),
        gateway = FinancialServiceGateway.valueOf(this.gateway),
        amount = settlementData.totalAmount,
        status = this.status,
        authentication = this.authentication
            ?: "",
        errorDescription = this.errorDescription ?: "",
    )
}

fun FraudPreventionPaymentOperationDenied.toEntity(): PaymentOperationEntityType.FraudPrevention {
    return PaymentOperationEntityType.FraudPrevention(error.name)
}
fun PaymentOperationEntityType.FraudPrevention.toDomain(): FraudPreventionPaymentOperationDenied {
    return FraudPreventionPaymentOperationDenied(
        error = FraudPreventionErrors.valueOf(this.error),
    )
}

fun CreditCardAuthorization.toEntity(): PaymentOperationEntityType.CreditCard {
    return PaymentOperationEntityType.CreditCard(
        acquirer = acquirer,
        transactionId = transactionId,
        status = status,
        acquirerTid = acquirerTid,
        amount = amount,
        acquirerReturnCode = acquirerReturnCode,
        acquirerReturnMessage = acquirerReturnMessage,
        tid = tid,
        authorizationCode = authorizationCode,
        originalAcquirer = originalAcquirer,
    )
}
fun PaymentOperationEntityType.CreditCard.toDomain(): CreditCardAuthorization {
    return CreditCardAuthorization(
        acquirer = this.acquirer,
        transactionId = this.transactionId,
        status = this.status,
        acquirerTid = this.acquirerTid,
        amount = this.amount,
        acquirerReturnCode = this.acquirerReturnCode,
        acquirerReturnMessage = this.acquirerReturnMessage,
        tid = this.tid,
        authorizationCode = this.authorizationCode,
        originalAcquirer = this.originalAcquirer,
    )
}

fun BalanceAuthorization.toEntity(): PaymentOperationEntityType.Balance {
    return PaymentOperationEntityType.Balance(
        operationID = operationId.value,
        status = status.toString(),
        amount = amount,
        errorDescription = errorDescription,
        timeout = timeout,
        ongoingRefundOperationID = ongoingRefundOperationId?.value,
        paymentGateway = paymentGateway,
    )
}
fun PaymentOperationEntityType.Balance.toDomain(): BalanceAuthorization {
    return BalanceAuthorization(
        operationId = BankOperationId(this.operationID),
        status = BankOperationStatus.valueOf(this.status),
        amount = this.amount,
        errorDescription = this.errorDescription,
        timeout = this.timeout,
        ongoingRefundOperationId = this.ongoingRefundOperationID?.let { BankOperationId(it) },
        paymentGateway = this.paymentGateway ?: FinancialServiceGateway.ARBI,
    )
}