package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.banking.BankStatement
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.banking.BankStatementItemFlow
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.banking.BankStatementMetadata
import ai.friday.billpayment.app.banking.InternalBankStatementItem
import ai.friday.billpayment.app.banking.OmnibusBankStatement
import ai.friday.billpayment.app.banking.OmnibusBankStatementItem
import ai.friday.billpayment.app.integrations.InternalBankRepository
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime

@Singleton
class InternalBankDBRepository(private val dynamoDbDAO: DynamoDbDAO) : InternalBankRepository {

    private val bankStatementScanKeyPrefix = "STATEMENT#"

    override fun findBankStatementItem(
        accountPaymentMethodId: AccountPaymentMethodId,
        date: LocalDate,
        operationNumber: String,
    ): Either<FindError, BankStatementItem> {
        val items = dynamoDbDAO.queryTableOnHashKeyAndRangeKey(
            accountPaymentMethodId.value,
            "$bankStatementScanKeyPrefix${date.format(dateFormat)}#$operationNumber",
            BankStatementItemEntity::class.java,
        )
        val entity = items.ifEmpty { return FindError.NotFound.left() }.first()
        return entity.mapToBankStatementItem().right()
    }

    override fun save(omnibusBankStatementItem: OmnibusBankStatementItem) {
        save(
            omnibusBankStatementItem.omnibusPaymentMethodId,
            omnibusBankStatementItem.bankStatementItem,
            omnibusBankStatementItem.virtualPaymentMethodId,
        )
    }

    override fun create(internalBankStatementItem: InternalBankStatementItem) {
        val items = dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
            primaryKey = internalBankStatementItem.accountPaymentMethodId.value,
            scanKey = internalBankStatementItem.bankStatementItem.operationNumber,
            type = BankStatementItemEntity::class.java,
        )

        if (items.isEmpty()) {
            save(internalBankStatementItem.accountPaymentMethodId, internalBankStatementItem.bankStatementItem)
        } else {
            throw IllegalStateException("Statement already exists.")
        }
    }

    override fun update(internalBankStatementItem: InternalBankStatementItem) {
        save(internalBankStatementItem.accountPaymentMethodId, internalBankStatementItem.bankStatementItem)
    }

    override fun findAllBankStatementItem(accountPaymentMethodId: AccountPaymentMethodId): BankStatement {
        val list = dynamoDbDAO.queryTableOnHashKey(accountPaymentMethodId.value, BankStatementItemEntity::class.java)
            .map { it.mapToBankStatementItem() }
        return BankStatement(list)
    }

    override fun findAllBankStatementItem(
        accountPaymentMethodId: AccountPaymentMethodId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): BankStatement {
        val list = dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBetween(
            primaryKey = accountPaymentMethodId.value,
            initialScanKey = buildScanKey(date = startDate),
            finalScanKey = buildScanKey(date = endDate),
            type = BankStatementItemEntity::class.java,
        ).map { it.mapToBankStatementItem() }
        return BankStatement(list)
    }

    override fun findAllBankStatementCredits(
        accountPaymentMethodId: AccountPaymentMethodId,
        startDate: LocalDate,
        endDate: LocalDate,
    ): List<BankStatementItem> {
        val initialScanKey = buildScanKey(startDate)
        val finalScanKey = buildScanKey(endDate.plusDays(1))
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBetween(
            accountPaymentMethodId.value,
            initialScanKey,
            finalScanKey,
            BankStatementItemEntity::class.java,
        )
            .map { it.mapToBankStatementItem() }.filter { it.flow == BankStatementItemFlow.CREDIT }
    }

    override fun findAllOmnibusBankStatementItem(accountPaymentMethodId: AccountPaymentMethodId): OmnibusBankStatement {
        val list = dynamoDbDAO.queryTableOnHashKey(accountPaymentMethodId.value, BankStatementItemEntity::class.java)
            .map { it.mapToOmnibusBankStatementItem() }
        return OmnibusBankStatement(list)
    }

    private fun save(
        accountPaymentMethodId: AccountPaymentMethodId,
        item: BankStatementItem,
        targetAccountPaymentMethodId: AccountPaymentMethodId? = null,
    ) {
        val entity = BankStatementItemEntity().apply {
            primaryKey = accountPaymentMethodId.value
            scanKey = buildScanKey(item.date, item.operationNumber)
            flow = item.flow
            type = item.type
            amount = item.amount
            operationNumber = item.operationNumber
            date = item.date.format(dateFormat)
            counterpartName = item.counterpartName
            counterpartDocument = item.counterpartDocument
            ref = item.ref.orEmpty()
            target = targetAccountPaymentMethodId?.value.orEmpty()
            description = item.description
            documentNumber = item.documentNumber
            index1HashKey = accountPaymentMethodId.value
            index1RangeKey = item.operationNumber
            metadata = item.metadata.toEntity()
            notificatedAt = item.notificatedAt?.format(dateTimeFormat)
        }

        dynamoDbDAO.save(entity)
    }

    private fun buildScanKey(
        date: LocalDate,
        operationNumber: String? = "",
    ): String {
        return bankStatementScanKeyPrefix + date.format(dateFormat) + "#" + operationNumber
    }
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class BankStatementItemEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var index1HashKey: String // PAYMENT-METHOD-ID

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var index1RangeKey: String // OPERATION-NUMBER

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Flow")
    lateinit var flow: BankStatementItemFlow

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Type")
    lateinit var type: BankStatementItemType

    @DynamoDBAttribute(attributeName = "Amount")
    var amount: Long = 0

    @DynamoDBAttribute(attributeName = "OperationNumber")
    lateinit var operationNumber: String

    @DynamoDBAttribute(attributeName = "Date")
    lateinit var date: String

    @DynamoDBAttribute(attributeName = "CounterpartName")
    var counterpartName: String = ""

    @DynamoDBAttribute(attributeName = "CounterpartDocument")
    var counterpartDocument: String = ""

    @DynamoDBAttribute(attributeName = "DocumentNumber")
    var documentNumber: String = ""

    @DynamoDBAttribute(attributeName = "Ref")
    var ref: String? = null

    @DynamoDBAttribute(attributeName = "Description")
    var description: String = ""

    @DynamoDBAttribute(attributeName = "Target")
    var target: String? = null

    @DynamoDBAttribute(attributeName = "BankStatementMetadata")
    var metadata: BankStatementMetadataEntity? = null

    @DynamoDBAttribute(attributeName = "NotificatedAt")
    var notificatedAt: String? = null
}

fun BankStatementMetadata?.toEntity(): BankStatementMetadataEntity? {
    return this?.let {
        BankStatementMetadataEntity().apply {
            virtualAmount = it.virtualAmount
        }
    }
}

@DynamoDBDocument
class BankStatementMetadataEntity {
    var virtualAmount: Long? = null

    fun toDomainObject(): BankStatementMetadata {
        return BankStatementMetadata(
            virtualAmount = this.virtualAmount,
        )
    }
}

fun BankStatementItemEntity.mapToBankStatementItem() = BankStatementItem(
    date = LocalDate.parse(date, dateFormat),
    amount = amount,
    flow = flow,
    description = description,
    counterpartDocument = counterpartDocument,
    counterpartName = counterpartName,
    documentNumber = documentNumber,
    operationNumber = operationNumber,
    type = type,
    ref = ref,
    metadata = metadata?.toDomainObject(),
    notificatedAt = notificatedAt?.let {
        ZonedDateTime.parse(it, dateTimeFormat)
    },
)

fun BankStatementItemEntity.mapToOmnibusBankStatementItem() = OmnibusBankStatementItem(
    bankStatementItem = mapToBankStatementItem(),
    omnibusPaymentMethodId = AccountPaymentMethodId(primaryKey),
    virtualPaymentMethodId = target?.let { AccountPaymentMethodId(it) },
)