package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.contact.ContactId
import ai.friday.billpayment.app.integrations.BillRecurrenceRepository
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.pfm.PFMCategoryId
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.recurrence.BillRecurrence
import ai.friday.billpayment.app.recurrence.RecurrenceFrequency
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.recurrence.RecurrenceRule
import ai.friday.billpayment.app.recurrence.RecurrenceStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.dateFormat
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBQueryExpression
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.Property
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter

private const val INDEX_1_PRIMARY_KEY = "RECURRENCE"

@FridayMePoupe
class BillRecurrenceDBRepository(
    private val dynamoDbDAO: DynamoDbDAO,
    @Property(name = "recurrence.lastLimitDate") private val lastLimitDateString: String,
) : BillRecurrenceRepository {

    private val mapper = jacksonObjectMapper()

    override fun findAll(status: RecurrenceStatus): List<BillRecurrence> {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
            INDEX_1_PRIMARY_KEY,
            status.name,
            BillRecurrenceEntity::class.java,
        )
            .map { it.toRecurrence() }
    }

    override fun find(recurrenceId: RecurrenceId, walletId: WalletId): BillRecurrence {
        return findOrNull(recurrenceId, walletId)
            ?: throw ItemNotFoundException("Recurrence ${recurrenceId.value} not found for wallet ${walletId.value}")
    }

    override fun findOrNull(recurrenceId: RecurrenceId, walletId: WalletId): BillRecurrence? {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKey(
            recurrenceId.value,
            walletId.value,
            BillRecurrenceEntity::class.java,
        )
            .ifEmpty { return null }
            .first()
            .toRecurrence()
    }

    override fun findByContactId(contactId: ContactId): List<BillRecurrence> {
        val expressionAttributeValues = mapOf(
            ":val1" to AttributeValue(contactId.value),
            ":val2" to AttributeValue(ContactIdIndexMetadata.RECURRENCE.name),
        )
        val queryExpression = DynamoDBQueryExpression<BillRecurrenceEntity>()
            .withConsistentRead(false)
            .withIndexName("GSIndex2")
            .withExpressionAttributeValues(expressionAttributeValues)
            .withKeyConditionExpression("GSIndex2PrimaryKey = :val1 And GSIndex2ScanKey = :val2")
        return dynamoDbDAO.queryWithDynamoDBQueryExpression(queryExpression, BillRecurrenceEntity::class.java)
            .map { it.toRecurrence() }
    }

    override fun findByWalletId(walletId: WalletId): List<BillRecurrence> {
        val expressionAttributeValues = mapOf(
            ":val1" to AttributeValue(INDEX_1_PRIMARY_KEY),
            ":val2" to AttributeValue(walletId.value),
        )
        val queryExpression = DynamoDBQueryExpression<BillRecurrenceEntity>()
            .withIndexName("GSIndex1")
            .withConsistentRead(false)
            .withKeyConditionExpression("GSIndex1PrimaryKey = :val1")
            .withExpressionAttributeValues(expressionAttributeValues)
            .withFilterExpression("$BILL_PAYMENT_RANGE_KEY = :val2")
        return dynamoDbDAO.queryWithDynamoDBQueryExpression(queryExpression, BillRecurrenceEntity::class.java)
            .map { it.toRecurrence() }
    }

    private fun BillRecurrenceEntity.toRecurrence(): BillRecurrence {
        val actionSource: ActionSource.WalletActionSource = if (actionSource.contains("Webapp")) {
            ActionSource.Api(accountId = AccountId(scanKey))
        } else {
            mapper.readerFor(ActionSource::class.java).readValue(actionSource)
        }

        return BillRecurrence(
            id = RecurrenceId(primaryKey),
            walletId = WalletId(scanKey),
            description = description.orEmpty(),
            amount = amount,
            rule = RecurrenceRule(
                frequency = rule.frequency,
                startDate = LocalDate.parse(rule.startDate, dateFormat),
                endDate = rule.endDate?.let { LocalDate.parse(rule.endDate, dateFormat) },
                pattern = rule.pattern.orEmpty(),
            ),
            contactId = contactId?.let { ContactId(it) },
            contactAccountId = contactAccountId?.let { AccountId(it) } ?: AccountId(scanKey),
            recipientName = recipient.name.orEmpty(),
            recipientDocument = recipient.document,
            recipientAlias = recipient.alias.orEmpty(),
            recipientBankAccount = recipient.accountType?.let { recipient.toBankAccount() },
            recipientPixKey = recipient.pixKey?.let {
                PixKey(
                    value = it,
                    type = PixKeyType.valueOf(recipient.pixKeyType!!),
                )
            },
            actionSource = actionSource,
            created = ZonedDateTime.parse(created, DateTimeFormatter.ISO_DATE_TIME),
            status = status,
            bills = billIds.map { BillId(it) },
            billType = billType,
            lastDueDate = LocalDate.parse(lastBillDueDate ?: lastLimitDateString, dateFormat),
            billCategoryId = billCategoryId?.let { PFMCategoryId(it) },
        )
    }

    override fun save(recurrence: BillRecurrence) {
        val entity = BillRecurrenceEntity().apply {
            primaryKey = recurrence.id.value
            scanKey = recurrence.walletId.value
            description = recurrence.description
            amount = recurrence.amount
            rule = RecurrenceRuleEntity().apply {
                frequency = recurrence.rule.frequency
                startDate = recurrence.rule.startDate.format(dateFormat)
                endDate = recurrence.rule.endDate?.format(dateFormat)
                pattern = recurrence.rule.pattern
            }
            contactId = recurrence.contactId?.value
            contactAccountId = recurrence.contactAccountId.value
            gSIndex2ScanKey = recurrence.contactId?.let { ContactIdIndexMetadata.RECURRENCE }
            recipient = toBillRecipientDocument(recurrence)
            actionSource = mapper.writeValueAsString(recurrence.actionSource)
            created = recurrence.created.format(DateTimeFormatter.ISO_DATE_TIME)
            billIds = recurrence.bills.map { it.value }
            status = recurrence.status
            billType = recurrence.billType
            rangeKeyIndex = recurrence.status.name
            lastBillDueDate = recurrence.lastDueDate?.format(dateFormat)
            billCategoryId = recurrence.billCategoryId?.value
        }
        dynamoDbDAO.save(entity)
    }

    private fun toBillRecipientDocument(recurrence: BillRecurrence): BillRecipientDocument {
        return BillRecipientDocument(
            name = recurrence.recipientName,
            document = recurrence.recipientDocument,
            alias = recurrence.recipientAlias,
            accountType = recurrence.recipientBankAccount?.accountType?.name,
            bankNo = recurrence.recipientBankAccount?.bankNo,
            routingNo = recurrence.recipientBankAccount?.routingNo,
            accountNo = recurrence.recipientBankAccount?.accountNo,
            accountDv = recurrence.recipientBankAccount?.accountDv,
            bankISPB = recurrence.recipientBankAccount?.ispb,
            pixKey = recurrence.recipientPixKey?.value,
            pixKeyType = recurrence.recipientPixKey?.type?.name,
            institutionName = null,
        )
    }
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class BillRecurrenceEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    var typeIndex: String = "RECURRENCE"

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var rangeKeyIndex: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex2PrimaryKey", globalSecondaryIndexName = "GSIndex2")
    var contactId: String? = null

    @DynamoDBAttribute(attributeName = "ContactAccountId")
    var contactAccountId: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBIndexRangeKey(attributeName = "GSIndex2ScanKey", globalSecondaryIndexName = "GSIndex2")
    var gSIndex2ScanKey: ContactIdIndexMetadata? = null

    @DynamoDBAttribute(attributeName = "Description")
    var description: String? = null

    @DynamoDBAttribute(attributeName = "Amount")
    var amount: Long = 0

    @DynamoDBAttribute(attributeName = "Rule")
    lateinit var rule: RecurrenceRuleEntity

    @DynamoDBAttribute(attributeName = "Recipient")
    lateinit var recipient: BillRecipientDocument

    @DynamoDBAttribute(attributeName = "Source")
    lateinit var actionSource: String

    @DynamoDBAttribute(attributeName = "Created")
    lateinit var created: String

    @DynamoDBAttribute(attributeName = "BillIds")
    lateinit var billIds: List<String>

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Status")
    lateinit var status: RecurrenceStatus

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "BillType")
    lateinit var billType: BillType

    @DynamoDBAttribute(attributeName = "LastBillDueDate")
    var lastBillDueDate: String? = null

    @DynamoDBAttribute(attributeName = "BillCategoryId")
    var billCategoryId: String? = null
}

@DynamoDBDocument
class RecurrenceRuleEntity {
    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    lateinit var frequency: RecurrenceFrequency
    lateinit var startDate: String
    var endDate: String? = null
    var pattern: String? = null
}