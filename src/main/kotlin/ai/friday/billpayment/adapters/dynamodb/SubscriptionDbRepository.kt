package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.integrations.SubscriptionRepository
import ai.friday.billpayment.app.recurrence.RecurrenceId
import ai.friday.billpayment.app.subscription.Subscription
import ai.friday.billpayment.app.subscription.SubscriptionPaymentStatus
import ai.friday.billpayment.app.subscription.SubscriptionStatus
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.dateFormat
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import java.time.LocalDate

private const val SubcriptionPartitionKey = "SUBSCRIPTION"
private const val SubscriptionOverdueSurveyPartitionKey = "SUBSCRIPTION_OVERDUE_SURVEY"

@FridayMePoupe
class SubscriptionDbRepository(private val dynamoDbDAO: DynamoDbDAO) : SubscriptionRepository {
    override fun save(subscription: Subscription) {
        val entity = SubscriptionEntity().apply {
            primaryKey = SubcriptionPartitionKey
            scanKey = subscription.accountId.value
            gSIndex1PrimaryKey = SubcriptionPartitionKey
            gSIndex1ScanKey = subscription.buildIndex1ScanKey()
            document = subscription.document.value
            status = subscription.status
            amount = subscription.amount
            dayOfMonth = subscription.dayOfMonth
            recurrenceId = subscription.recurrenceId.value
            paymentStatus = subscription.paymentStatus
            walletId = subscription.walletId.value
            nextEffectiveDueDate = subscription.nextEffectiveDueDate.format(dateFormat)
        }
        dynamoDbDAO.save(entity)
    }

    override fun find(accountId: AccountId): Subscription? {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKey(
            primaryKey = SubcriptionPartitionKey,
            scanKey = accountId.value,
            type = SubscriptionEntity::class.java,
        ).singleOrNull()?.toSubscription()
    }

    override fun findAll(): List<Subscription> {
        return dynamoDbDAO.queryTableOnHashKey(
            primaryKey = SubcriptionPartitionKey,
            type = SubscriptionEntity::class.java,
        ).map {
            it.toSubscription()
        }
    }

    override fun find(status: SubscriptionStatus): List<Subscription> {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKeyBeginsWith(
            primaryKey = SubcriptionPartitionKey,
            scanKey = status.name,
            type = SubscriptionEntity::class.java,
        ).map {
            it.toSubscription()
        }
    }

    override fun find(
        subscriptionStatus: SubscriptionStatus,
        paymentStatus: SubscriptionPaymentStatus,
    ): List<Subscription> {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
            primaryKey = SubcriptionPartitionKey,
            scanKey = buildIndex1ScanKey(subscriptionStatus, paymentStatus),
            type = SubscriptionEntity::class.java,
        ).map {
            it.toSubscription()
        }
    }

    private fun SubscriptionEntity.toSubscription() = Subscription(
        accountId = AccountId(scanKey),
        document = Document(document),
        status = status,
        amount = amount,
        dayOfMonth = dayOfMonth,
        recurrenceId = RecurrenceId(recurrenceId),
        paymentStatus = paymentStatus,
        walletId = WalletId(walletId),
        nextEffectiveDueDate = LocalDate.parse(nextEffectiveDueDate, dateFormat),
    )

    private fun Subscription.buildIndex1ScanKey() = buildIndex1ScanKey(status, paymentStatus)
    private fun buildIndex1ScanKey(status: SubscriptionStatus, paymentStatus: SubscriptionPaymentStatus) =
        "$status#$paymentStatus"
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class SubscriptionEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Status")
    lateinit var status: SubscriptionStatus

    @DynamoDBAttribute(attributeName = "Document")
    lateinit var document: String

    @DynamoDBAttribute(attributeName = "Amount")
    var amount: Long = 0

    @DynamoDBAttribute(attributeName = "DayOfMonth")
    var dayOfMonth: Int = 0

    @DynamoDBAttribute(attributeName = "RecurrenceId")
    lateinit var recurrenceId: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "PaymentStatus")
    lateinit var paymentStatus: SubscriptionPaymentStatus

    @DynamoDBAttribute(attributeName = "WalletId")
    lateinit var walletId: String

    @DynamoDBAttribute(attributeName = "NextEffectiveDueDate")
    lateinit var nextEffectiveDueDate: String
}