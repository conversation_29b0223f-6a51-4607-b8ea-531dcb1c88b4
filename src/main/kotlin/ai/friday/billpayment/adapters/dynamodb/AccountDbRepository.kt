package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FindError
import ai.friday.billpayment.app.account.Account
import ai.friday.billpayment.app.account.AccountGroup
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.ACTIVE
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.CLOSED
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.INACTIVE
import ai.friday.billpayment.app.account.AccountPaymentMethodStatus.PENDING
import ai.friday.billpayment.app.account.AccountPaymentStatus
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.CreditCardBinDetails
import ai.friday.billpayment.app.account.CreditCardBrand
import ai.friday.billpayment.app.account.CreditCardConfiguration
import ai.friday.billpayment.app.account.CreditCardExternalId
import ai.friday.billpayment.app.account.CreditCardToken
import ai.friday.billpayment.app.account.DeleteCreditCardResult
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.account.ExternalPaymentMethod
import ai.friday.billpayment.app.account.LegacyAccountConfiguration
import ai.friday.billpayment.app.account.NotificationGateways
import ai.friday.billpayment.app.account.PartialAccount
import ai.friday.billpayment.app.account.PaymentMethodNotFound
import ai.friday.billpayment.app.account.PaymentMethodType.BALANCE
import ai.friday.billpayment.app.account.PaymentMethodType.CREDIT_CARD
import ai.friday.billpayment.app.account.PaymentMethodType.EXTERNAL
import ai.friday.billpayment.app.account.Principal
import ai.friday.billpayment.app.account.RegistrationType
import ai.friday.billpayment.app.account.RiskLevel
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.UpdateCreditCardMethodRiskResult
import ai.friday.billpayment.app.account.UpgradeStatus
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.getOrFalse
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BackOfficeAccountRepository
import ai.friday.billpayment.app.subscription.SubscriptionType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import com.amazonaws.services.dynamodbv2.datamodeling.TransactionWriteRequest
import com.amazonaws.services.dynamodbv2.model.AttributeValue
import com.amazonaws.services.dynamodbv2.model.AttributeValueUpdate
import com.amazonaws.services.dynamodbv2.model.QueryRequest
import com.amazonaws.services.dynamodbv2.model.UpdateItemRequest
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import java.util.*
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

val DEFAULT_LEGACY_ACCOUNT_CONFIGURATION =
    LegacyAccountConfiguration(
        creditCardConfiguration = CreditCardConfiguration(),
        defaultWalletId = null,
        receiveDDANotification = true,
        receiveNotification = true,
    )

private const val accountKey = "ACCOUNT"

private val statusUpdatedDateFormatter = DateTimeFormatter.ISO_DATE_TIME
private val index2RangeKeyDateFormatter = dateFormat

@Singleton
class AccountDbRepository(private val dynamoDbDAO: DynamoDbDAO) : AccountRepository, BackOfficeAccountRepository {

    private val balanceIndexPartitionKey = "BALANCE"

    private val paymentMethodScanKeyPrefix = "PAYMENT-METHOD#"

    private val partialAccountIndexKey = "PARTIAL_ACCOUNT"

    private val externalIdHashKey = "EXTERNAL_ID"

    override fun findAccountByDocument(document: String): Account {
        return findAccountByDocumentOrNull(document) ?: throw AccountNotFoundException(document)
    }

    override fun findAccountByDocumentOrNull(document: String): Account? {
        val userList = dynamoDbDAO.queryIndexOnHashKeyValue(document, AccountEntity::class.java)
        val userEntity = userList.firstOrNull { entity ->
            entity.scanKey == Role.OWNER.name && (entity.status == AccountStatus.ACTIVE || entity.status == AccountStatus.APPROVED || entity.status == AccountStatus.BLOCKED)
        }
        return userEntity?.let { buildAccount(it) }
    }

    override fun listAccountsByDocument(document: String): List<Account> {
        return dynamoDbDAO.queryIndexOnHashKeyValue(document, AccountEntity::class.java)
            .map { buildAccount(it) }
    }

    override fun findById(accountId: AccountId): Account {
        return findByIdOrNull(accountId) ?: throw AccountNotFoundException(accountId.value)
    }

    override fun findByIdOrNull(accountId: AccountId): Account? {
        val accountEntity =
            dynamoDbDAO.queryTableOnHashKeyAndRangeKey(accountId.value, Role.OWNER.name, AccountEntity::class.java)
                .firstOrNull()
        return accountEntity?.let { buildAccount(accountEntity) }
    }

    override fun findByGroup(group: AccountGroup): List<Account> {
        return findAllAccountsActivatedSince(LocalDate.EPOCH)
            .filter { it.configuration.groups.contains(group) }
    }

    override fun findPartialAccountById(accountId: AccountId): PartialAccount {
        return findPartialAccountByIdOrNull(accountId) ?: throw AccountNotFoundException(accountId.value)
    }

    override fun findPartialAccountByIdOrNull(accountId: AccountId): PartialAccount? {
        return dynamoDbDAO.queryTableOnHashKeyAndRangeKey(
            accountId.value,
            Role.GUEST.name,
            PartialAccountEntity::class.java,
        ).firstOrNull()?.toPartialAccount()
    }

    override fun checkAccountRole(accountId: AccountId, role: Role): Role? {
        val expressionAttributeValues = mapOf(
            ":val1" to AttributeValue(accountId.value),
            ":val2" to AttributeValue(role.name),
        )

        val request = QueryRequest()
            .withTableName(BILL_PAYMENT_TABLE_NAME)
            .withConsistentRead(true)
            .withKeyConditionExpression("PrimaryKey = :val1 and ScanKey = :val2")
            .withExpressionAttributeValues(expressionAttributeValues)

        val attributeMap = dynamoDbDAO.query(request).items
            .ifEmpty { return null }

        if (attributeMap.size > 1) {
            logger.error(
                Markers.append("accountId", accountId.value).andAppend("roles", attributeMap),
                "AccountDbRepository",
            )
        }

        return attributeMap.first()["ScanKey"]?.let { Role.valueOf(it.s) }
    }

    override suspend fun findNameAndEmail(
        accountId: AccountId,
        role: Role,
    ): Either<Exception, Pair<String, EmailAddress>> {
        val expressionAttributeValues = mapOf(
            ":val1" to AttributeValue(accountId.value),
            ":val2" to AttributeValue(role.name),
        )

        val request = QueryRequest()
            .withTableName(BILL_PAYMENT_TABLE_NAME)
            .withConsistentRead(true)
            .withKeyConditionExpression("PrimaryKey = :val1 and ScanKey = :val2")
            .withExpressionAttributeValues(expressionAttributeValues)

        val attributeMap = dynamoDbDAO.query(request).items
            .ifEmpty { return AccountNotFoundException(accountId.value).left() }.first()

        return Pair(attributeMap["Name"]!!.s, EmailAddress(attributeMap["Email"]!!.s)).right()
    }

    override fun findPartialAccountByStatus(accountStatus: AccountStatus): List<PartialAccount> {
        return dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
            partialAccountIndexKey,
            accountStatus.name,
            PartialAccountEntity::class.java,
        ).map {
            it.toPartialAccount()
        }
    }

    override fun updatePartialAccountRegistrationType(accountId: AccountId, registrationType: RegistrationType) {
        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(accountId.value),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(Role.GUEST.name),
        )

        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .addAttributeUpdatesEntry(
                "RegistrationType",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withS(registrationType.name)),
            )
        dynamoDbDAO.updateItem(updateRequest)
    }

    override fun updatePartialAccountStatus(accountId: AccountId, newStatus: AccountStatus) {
        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(accountId.value),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(Role.GUEST.name),
        )

        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .addAttributeUpdatesEntry(
                "Status",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withS(newStatus.name)),
            ).addAttributeUpdatesEntry(
                "GSIndex1PrimaryKey",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withS(partialAccountIndexKey)),
            ).addAttributeUpdatesEntry(
                "GSIndex1ScanKey",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withS(newStatus.name)),
            ).addAttributeUpdatesEntry(
                "StatusUpdated",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withS(getZonedDateTime().format(statusUpdatedDateFormatter))),
            )
        dynamoDbDAO.updateItem(updateRequest)
    }

    override fun updateAccountStatus(accountId: AccountId, newStatus: AccountStatus) {
        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(accountId.value),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(Role.OWNER.name),
        )

        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .addAttributeUpdatesEntry(
                "GSIndex1ScanKey",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withS(newStatus.name)),
            )
        dynamoDbDAO.updateItem(updateRequest)
    }

    override fun updatePartialAccountName(accountId: AccountId, newName: String) {
        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(accountId.value),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(Role.GUEST.name),
        )

        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .addAttributeUpdatesEntry(
                "Name",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withS(newName)),
            )
        dynamoDbDAO.updateItem(updateRequest)
    }

    override fun updatePartialAccountEmail(accountId: AccountId, newEmail: EmailAddress) {
        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(accountId.value),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(Role.GUEST.name),
        )

        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .addAttributeUpdatesEntry(
                "Email",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withS(newEmail.value)),
            )
        dynamoDbDAO.updateItem(updateRequest)
    }

    override fun updatePartialAccountGroups(accountId: AccountId, newGroups: List<AccountGroup>): PartialAccount {
        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(accountId.value),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(Role.GUEST.name),
        )

        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .addAttributeUpdatesEntry(
                "Groups",
                AttributeValueUpdate()
                    .withValue(
                        AttributeValue().withL(
                            newGroups.map { AttributeValue().withS(it.value) }
                                .toMutableList(),
                        ),
                    ),
            )

        dynamoDbDAO.updateItem(updateRequest)

        return findPartialAccountById(accountId)
    }

    override fun deletePartialAccount(accountId: AccountId) {
        dynamoDbDAO.delete(accountId.value, Role.GUEST.name)
    }

    override fun findPhysicalAccountPaymentMethod(
        bankNo: Long,
        routingNo: Long,
        accountNo: AccountNumber,
    ): Either<FindError, AccountPaymentMethod> {
        val balanceIndexScanKey = buildBalanceIndexScanKey(bankNo, routingNo, accountNo)
        val paymentList = dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
            balanceIndexPartitionKey,
            balanceIndexScanKey,
            PaymentMethodEntity::class.java,
        )
            .ifEmpty {
                return FindError.NotFound.left()
            }
        if (paymentList.size > 1) {
            return FindError.MultipleItemsFound.left()
        }
        return paymentList.first().toBankAccountPaymentMethod().right()
    }

    override fun deleteAccountPaymentMethod(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ): DeleteCreditCardResult {
        try {
            val foundCreditCardEntity = dynamoDbDAO
                .queryTableOnHashKeyAndRangeKeyBeginsWith(
                    partitionKey = accountId.value,
                    scanKey = paymentMethodScanKeyPrefix,
                    type = PaymentMethodEntity::class.java,
                )
                .filter {
                    it.paymentMethodId == accountPaymentMethodId.value && it.type == CREDIT_CARD
                }
                .ifEmpty {
                    return DeleteCreditCardResult.NotFound
                }
                .first()

            if (foundCreditCardEntity.status !in listOf(ACTIVE, PENDING)) {
                return DeleteCreditCardResult.Deleted(accountPaymentMethodId)
            }

            val (prefix, _, position) = foundCreditCardEntity.scanKey.split("#")

            val updatedPaymentMethod = PaymentMethodEntity().apply {
                primaryKey = accountId.value
                scanKey = "$prefix#${INACTIVE.name}#$position"
                this.paymentMethodId = foundCreditCardEntity.paymentMethodId
                this.status = INACTIVE
                created = foundCreditCardEntity.created
                type = CREDIT_CARD
                this.creditCard = foundCreditCardEntity.creditCard
            }

            val transactionWriteRequest = TransactionWriteRequest()

            transactionWriteRequest.addDelete(foundCreditCardEntity)
            transactionWriteRequest.addPut(updatedPaymentMethod)

            dynamoDbDAO.transactionWrite(transactionWriteRequest)

            return DeleteCreditCardResult.Deleted(accountPaymentMethodId)
        } catch (e: Exception) {
            return DeleteCreditCardResult.ServerError(e)
        }
    }

    override fun updateCreditCardPaymentMethodRisk(accountId: AccountId, accountPaymentMethodId: AccountPaymentMethodId, riskLevel: RiskLevel): UpdateCreditCardMethodRiskResult {
        val foundCreditCardEntity = dynamoDbDAO
            .queryTableOnHashKeyAndRangeKeyBeginsWith(
                partitionKey = accountId.value,
                scanKey = paymentMethodScanKeyPrefix,
                type = PaymentMethodEntity::class.java,
            )
            .filter {
                it.paymentMethodId == accountPaymentMethodId.value && it.type == CREDIT_CARD
            }
            .ifEmpty {
                return UpdateCreditCardMethodRiskResult.NotFound
            }
            .first()

        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(accountId.value),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(foundCreditCardEntity.scanKey),
        )

        val updateRequest = UpdateItemRequest()
            .withTableName(DYNAMODB_TABLE_NAME)
            .withKey(key)
            .addAttributeUpdatesEntry(
                "CreditCardRiskLevel",
                AttributeValueUpdate()
                    .withValue(AttributeValue().withS(riskLevel.name)),
            )
        dynamoDbDAO.updateItem(updateRequest)

        return UpdateCreditCardMethodRiskResult.Updated(accountPaymentMethodId)
    }

    override fun createAccountPaymentMethod(
        accountId: AccountId,
        bankAccount: BankAccount,
        position: Int,
        mode: BankAccountMode,
    ): AccountPaymentMethod {
        return createAccountPaymentMethod(
            accountId = accountId,
            bankAccount = bankAccount,
            position = position,
            paymentMethodId = AccountPaymentMethodId(UUID.randomUUID().toString()),
            mode = mode,
        )
    }

    override fun createAccountPaymentMethod(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
        bankAccount: BankAccount,
        position: Int,
        status: AccountPaymentMethodStatus,
        mode: BankAccountMode,
    ): AccountPaymentMethod {
        val paymentMethod = PaymentMethodEntity().apply {
            primaryKey = accountId.value
            scanKey = "$paymentMethodScanKeyPrefix${status.name}#$position"
            this.paymentMethodId = paymentMethodId.value
            this.status = status
            created = getFormattedZonedDateTime()
            type = BALANCE
            typeIndex = BALANCE
            paymentIdIndex = buildBalanceIndexScanKey(bankAccount)
            this.bankAccount = BankAccountComplexType().apply {
                bankNo = bankAccount.bankNo!!
                bankRoutingNo = bankAccount.routingNo
                bankAccountNo = bankAccount.accountNo.toLong() // TODO: revisar esse caso
                bankAccountType = bankAccount.accountType.name
                bankAccountDv = bankAccount.accountDv
                document = bankAccount.document
                bankAccountMode = mode
            }
        }

        dynamoDbDAO.save(paymentMethod)

        return paymentMethod.toBankAccountPaymentMethod()
    }

    override fun createAccountPaymentMethod(
        accountId: AccountId,
        paymentMethodId: AccountPaymentMethodId,
        creditCard: CreditCard,
        position: Int,
        status: AccountPaymentMethodStatus,
    ): AccountPaymentMethod {
        val now = getFormattedZonedDateTime()

        val activatedAt = if (status == ACTIVE) {
            now
        } else {
            null
        }

        val paymentMethod = PaymentMethodEntity().apply {
            primaryKey = accountId.value
            scanKey = "$paymentMethodScanKeyPrefix${status.name}#$position"
            hmac = creditCard.hmac
            hmacIndex = "CREDIT_CARD#HMAC"
            this.paymentMethodId = paymentMethodId.value
            this.status = status
            created = now
            this.activatedAt = activatedAt
            type = CREDIT_CARD
            this.creditCard = CreditCardComplexType().apply {
                creditCardBrand = creditCard.brand.name
                creditCardPAN = creditCard.maskedPan
                creditCardExpiryDate = creditCard.expiryDate
                token = creditCard.token?.value
                provider = creditCard.binDetails?.provider
                cardType = creditCard.binDetails?.cardType
                foreignCard = creditCard.binDetails?.foreignCard
                corporateCard = creditCard.binDetails?.corporateCard
                issuer = creditCard.binDetails?.issuer
                issuerCode = creditCard.binDetails?.issuerCode
                prepaid = creditCard.binDetails?.prepaid
                this.status = creditCard.binDetails?.status
                bin = creditCard.bin
                riskLevel = creditCard.riskLevel
                mainCard = creditCard.mainCard
                lastFourDigits = creditCard.lastFourDigits
                externalId = creditCard.externalId?.value
                externalProvider = creditCard.externalId?.provider?.name
                hmac = creditCard.hmac
            }
        }

        dynamoDbDAO.save(paymentMethod)

        return paymentMethod.toCreditCardPaymentMethod()
    }

    override fun createExternalAccountPaymentMethod(
        accountId: AccountId,
        providerName: AccountProviderName,
        position: Int,
    ): AccountPaymentMethod {
        val paymentMethodId = UUID.randomUUID().toString()

        val paymentMethod = PaymentMethodEntity().apply {
            primaryKey = accountId.value
            scanKey = "$paymentMethodScanKeyPrefix${ACTIVE.name}#$position"
            this.paymentMethodId = paymentMethodId
            this.status = ACTIVE
            created = getFormattedZonedDateTime()
            type = EXTERNAL
            this.providerName = providerName
        }

        dynamoDbDAO.save(paymentMethod)

        return paymentMethod.toExternalPaymentMethod()
    }

    override fun activateAccountPaymentMethod(
        accountId: AccountId,
        accountPaymentMethodId: AccountPaymentMethodId,
    ) {
        val paymentMethodEntity =
            findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
                .ifEmpty { return }
                .single { it.paymentMethodId == accountPaymentMethodId.value }

        val (prefix, _, position) = paymentMethodEntity.scanKey.split("#")

        val updatedMethodEntity = PaymentMethodEntity().apply {
            primaryKey = paymentMethodEntity.primaryKey
            scanKey = "$prefix#${ACTIVE.name}#$position"
            paymentMethodId = paymentMethodEntity.paymentMethodId
            created = paymentMethodEntity.created
            type = paymentMethodEntity.type
            creditCard = paymentMethodEntity.creditCard
            status = ACTIVE
            activatedAt = getFormattedZonedDateTime()
        }

        val transactionWriteRequest = TransactionWriteRequest()

        transactionWriteRequest.addDelete(paymentMethodEntity)
        transactionWriteRequest.addPut(updatedMethodEntity)

        dynamoDbDAO.transactionWrite(transactionWriteRequest)
    }

    override fun closeCreditCards(accountId: AccountId) {
        this.findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
            .forEach {
                if (it.type == CREDIT_CARD && it.status != CLOSED) {
                    closeAccountPaymentMethod(it)
                }
            }
    }

    override fun closeAccountPaymentMethod(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
    ) {
        this.findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
            .filter {
                it.paymentMethodId == accountPaymentMethodId.value
            }
            .forEach {
                closeAccountPaymentMethod(it)
            }
    }

    override fun setAccountPaymentMethodStatus(accountPaymentMethodId: AccountPaymentMethodId, accountId: AccountId, status: AccountPaymentMethodStatus) {
        this.findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
            .filter {
                it.paymentMethodId == accountPaymentMethodId.value
            }
            .forEach {
                setPaymentMethodStatus(it, status)
            }
    }

    private fun closeAccountPaymentMethod(paymentMethodEntity: PaymentMethodEntity) {
        setPaymentMethodStatus(paymentMethodEntity, CLOSED)
    }

    private fun setPaymentMethodStatus(paymentMethodEntity: PaymentMethodEntity, newStatus: AccountPaymentMethodStatus) {
        dynamoDbDAO.delete(paymentMethodEntity)
        paymentMethodEntity.apply {
            status = newStatus
            scanKey = "$paymentMethodScanKeyPrefix${newStatus.name}#${paymentMethodEntity.scanKey.split("#").last()}"
        }
        dynamoDbDAO.save(paymentMethodEntity)
    }

    override fun create(
        username: String,
        emailAddress: EmailAddress,
        accountId: AccountId,
        registrationType: RegistrationType,
        groups: List<AccountGroup>,
        subscriptionType: SubscriptionType,
    ): PartialAccount {
        val entity = PartialAccountEntity().apply {
            primaryKey = accountId.value
            scanKey = Role.GUEST
            status = AccountStatus.REGISTER_INCOMPLETE
            statusUpdated = getZonedDateTime().format(statusUpdatedDateFormatter)
            email = emailAddress.value
            name = username
            indexHashKey = partialAccountIndexKey
            indexRangeKey = AccountStatus.REGISTER_INCOMPLETE
            this.groups = groups.map { it.value }
            this.registrationType = registrationType
            this.subscriptionType = subscriptionType
        }
        dynamoDbDAO.save(entity)
        return entity.toPartialAccount()
    }

    override fun create(userAccount: Account): Account {
        val now = getZonedDateTime()
        val accountEntity = AccountEntity().apply {
            primaryKey = userAccount.accountId.value
            scanKey = Role.OWNER.name
            documentHash = userAccount.document
            index2HashKey = accountKey
            index2RangeKey = buildIndex2RangeKey(now)
            index3HashKey = userAccount.configuration.externalId?.let { buildExternalIdHashKey(it) }
            index3RangeKey = userAccount.configuration.externalId?.value
            status = AccountStatus.ACTIVE
            email = userAccount.emailAddress.value
            document = userAccount.document
            documentType = userAccount.documentType
            mobilePhone = userAccount.mobilePhone
            created = now.format(DateTimeFormatter.ISO_INSTANT)
            activatedAt = userAccount.activated?.format(DateTimeFormatter.ISO_INSTANT)
            name = userAccount.name
            userAccountType = userAccount.type
            updatedAt = now.format(DateTimeFormatter.ISO_DATE_TIME)
            configuration = userAccount.configuration.toEntity()
            imageUrlSmall = userAccount.imageUrlSmall
            imageUrlLarge = userAccount.imageUrlLarge
            subscriptionType = userAccount.subscriptionType
        }
        dynamoDbDAO.save(accountEntity)

        return buildAccount(accountEntity)
    }

    override fun save(account: Account) {
        val accountEntity = AccountEntity().apply {
            primaryKey = account.accountId.value
            scanKey = Role.OWNER.name
            documentHash = account.document
            status = account.status
            upgradeStatus = account.upgradeStatus
            paymentStatus = account.paymentStatus
            index2HashKey = accountKey
            index2RangeKey = buildIndex2RangeKey(account.created)
            index3HashKey = account.configuration.externalId?.let { buildExternalIdHashKey(it) }
            index3RangeKey = account.configuration.externalId?.let { it.value }
            index4HashKey = account.upgradeStatus?.let { "UPGRADE_STATUS" }
            index4RangeKey = account.upgradeStatus
            email = account.emailAddress.value
            document = account.document
            documentType = account.documentType
            mobilePhone = account.mobilePhone
            created = account.created.format(DateTimeFormatter.ISO_INSTANT)
            name = account.name
            userAccountType = account.type
            updatedAt = account.updated.format(DateTimeFormatter.ISO_DATE_TIME)
            activatedAt = account.activated?.format(DateTimeFormatter.ISO_DATE_TIME)
            configuration = account.configuration.toEntity()
            firstLoginAsOwner = account.firstLoginAsOwner?.format(DateTimeFormatter.ISO_DATE_TIME)
            channel = account.channel
            imageUrlSmall = account.imageUrlSmall
            imageUrlLarge = account.imageUrlLarge
            subscriptionType = account.subscriptionType
        }
        dynamoDbDAO.save(accountEntity)
    }

    override fun findAllAccountsActivatedSince(
        date: LocalDate,
        filterAccountsThatReceiveNotification: Boolean,
    ): List<Account> {
        return dynamoDbDAO.queryIndex2OnHashKeyAndRangeKeyGreaterThanOrEqual(
            primaryKey = accountKey,
            scanKey = date.format(index2RangeKeyDateFormatter),
            type = AccountEntity::class.java,
        )
            .filter {
                !filterAccountsThatReceiveNotification || it.configuration?.receiveNotification == true
            }
            .map {
                buildAccount(it)
            }
    }

    override fun findAccountByUpgradeStatus(status: UpgradeStatus): List<Account> {
        return dynamoDbDAO.queryIndex4OnHashKeyAndRangeKey(
            "UPGRADE_STATUS",
            status.name,
            AccountEntity::class.java,
        ).map { buildAccount(it) }
    }

    override fun findByExternalId(externalId: ExternalId): List<Account> {
        return dynamoDbDAO.queryIndex3OnHashKeyAndRangeKey(
            buildExternalIdHashKey(externalId),
            externalId.value,
            AccountEntity::class.java,
        ).map { buildAccount(it) }
    }

    override fun findAccountPaymentMethodByIdAndAccountId(
        accountPaymentMethodId: AccountPaymentMethodId,
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): AccountPaymentMethod {
        return findAccountPaymentMethodsByAccountIdAndStatus(accountId, status)
            .filter { paymentMethod ->
                paymentMethod.id == accountPaymentMethodId
            }
            .ifEmpty { throw PaymentMethodNotFound(accountPaymentMethodId, accountId) }
            .first()
    }

    override fun findAllPhysicalBalanceAccount(): List<AccountPaymentMethod> {
        return findAllBankAccountByMode(BankAccountMode.PHYSICAL)
    }

    // FIXME walletId - separar metodos de pagamento da carteira (balance) dos metodos de pagamento do usuário (cartao de credito) ???
    override fun findPhysicalBankAccountByAccountId(accountId: AccountId): List<AccountPaymentMethod> {
        return findAccountPaymentMethodsByAccountIdAndStatus(accountId, ACTIVE)
            .filter { accountPaymentMethod -> accountPaymentMethod.method is InternalBankAccount && accountPaymentMethod.method.bankAccountMode == BankAccountMode.PHYSICAL }
            .ifEmpty { throw PaymentMethodNotFound(accountId) }
    }

    override fun findAccountIdByPhysicalBankAccountNo(
        bankNo: Long,
        routingNo: Long,
        accountNo: AccountNumber,
    ): AccountId {
        val primaryKey = dynamoDbDAO.queryIndexOnHashKeyValueAndRangeKey(
            primaryKey = balanceIndexPartitionKey,
            scanKey = buildBalanceIndexScanKey(bankNo, routingNo, accountNo),
            type = PaymentMethodEntity::class.java,
        ).single().primaryKey

        return AccountId(primaryKey)
    }

    override fun findVirtualBankAccountByDocument(document: String): Either<FindError, AccountPaymentMethod> {
        try {
            val accountPaymentMethodsEntities =
                dynamoDbDAO.queryIndexOnHashKeyValue(balanceIndexPartitionKey, PaymentMethodEntity::class.java)
                    .filter { it.bankAccount?.bankAccountMode == BankAccountMode.VIRTUAL && it.bankAccount?.document == document }
                    .ifEmpty { return FindError.NotFound.left() }
            if (accountPaymentMethodsEntities.size > 1) {
                return FindError.MultipleItemsFound.left()
            }
            return accountPaymentMethodsEntities.first().toBankAccountPaymentMethod().right()
        } catch (e: Exception) {
            return FindError.ServerError(e).left()
        }
    }

    override fun findAllVirtualBankAccount(): List<AccountPaymentMethod> {
        return findAllBankAccountByMode(BankAccountMode.VIRTUAL)
    }

    override fun findAccountPaymentMethodsByAccountId(accountId: AccountId): List<AccountPaymentMethod> {
        return findAccountPaymentMethodsByAccountIdAndStatus(accountId = accountId, status = null)
    }

    override fun findAccountPaymentMethodsByHMac(hmac: String): List<AccountPaymentMethod> {
        val paymentMethodEntities = dynamoDbDAO.queryIndex2OnHashKeyValueAndRangeKey(
            primaryKey = hmac,
            scanKey = "CREDIT_CARD#HMAC",
            type = PaymentMethodEntity::class.java,
        )
        return paymentMethodEntities.map { paymentMethodEntity ->
            try {
                when (paymentMethodEntity.type) {
                    CREDIT_CARD -> paymentMethodEntity.toCreditCardPaymentMethod()
                    else -> throw IllegalStateException("Invalid payment method type")
                }
            } catch (e: Exception) {
                logger.error(
                    Markers.append("hmac", hmac)
                        .andAppend("paymentMethodEntity", paymentMethodEntity),
                    "AccountDbRepository",
                    e,
                )
                throw e
            }
        }
    }

    override fun findCreditCardsByAccountIdAndStatus(
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): List<AccountPaymentMethod> {
        return findAccountPaymentMethodsByAccountIdAndStatus(
            accountId = accountId,
            status = status,
        ).filter {
            it.method.type == CREDIT_CARD
        }
    }

    override fun incrementNSU(accountId: AccountId): Int {
        synchronized(accountId) {
            val delta = if (dynamoDbDAO.hasItem(accountId.value, "NSU")) {
                1
            } else {
                incrementBillCount(accountId.value)
            }

            val key = mapOf(
                BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(accountId.value),
                BILL_PAYMENT_RANGE_KEY to AttributeValue().withS("NSU"), // const
            )
            val attributeValue = dynamoDbDAO.incrementItem(key, "NSU", delta)
            return attributeValue.n.toInt()
        }
    }

    private fun incrementBillCount(userId: String): Int {
        val key = mapOf(
            BILL_PAYMENT_PARTITION_KEY to AttributeValue().withS(userId),
            BILL_PAYMENT_RANGE_KEY to AttributeValue().withS(Role.OWNER.name),
        )
        val attributeValue = dynamoDbDAO.incrementItem(key, "BillCount", 1)
        return attributeValue.n.toInt()
    }

    private fun buildIndex2RangeKey(creationDate: ZonedDateTime) = creationDate.format(index2RangeKeyDateFormatter)

    private fun findAllBankAccountByMode(bankAccountMode: BankAccountMode): List<AccountPaymentMethod> {
        return dynamoDbDAO.queryIndexOnHashKeyValue(balanceIndexPartitionKey, PaymentMethodEntity::class.java)
            .filter { it.bankAccount?.bankAccountMode == bankAccountMode }
            .map { balanceEntity -> balanceEntity.toBankAccountPaymentMethod() }
    }

    private fun findPaymentMethodEntitiesByAccountIdAndStatus(
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): List<PaymentMethodEntity> {
        val scanKey = if (status == null) {
            paymentMethodScanKeyPrefix
        } else {
            "$paymentMethodScanKeyPrefix${status.name}"
        }

        return dynamoDbDAO.queryTableOnHashKeyAndRangeKeyBeginsWith(
            partitionKey = accountId.value,
            scanKey = scanKey,
            type = PaymentMethodEntity::class.java,
        )
    }

    // FIXME walletId - separar metodos de pagamento da carteira (balance) dos metodos de pagamento do usuário (cartao de credito) ???
    override fun findAccountPaymentMethodsByAccountIdAndStatus(
        accountId: AccountId,
        status: AccountPaymentMethodStatus?,
    ): List<AccountPaymentMethod> {
        val paymentMethodEntities = findPaymentMethodEntitiesByAccountIdAndStatus(accountId, status)
        return paymentMethodEntities.map { paymentMethodEntity ->
            try {
                when (paymentMethodEntity.type) {
                    CREDIT_CARD -> paymentMethodEntity.toCreditCardPaymentMethod()
                    BALANCE -> paymentMethodEntity.toBankAccountPaymentMethod()
                    EXTERNAL -> paymentMethodEntity.toExternalPaymentMethod()
                }
            } catch (e: Exception) {
                logger.error(
                    Markers.append("accountId", accountId.value).andAppend("status", status)
                        .andAppend("paymentMethodEntity", paymentMethodEntity),
                    "AccountDbRepository",
                    e,
                )
                throw e
            }
        }
    }

    private fun buildAccount(accountEntity: AccountEntity): Account {
        return Account(
            accountId = AccountId(accountEntity.primaryKey),
            document = accountEntity.document,
            documentType = accountEntity.documentType,
            emailAddress = EmailAddress(accountEntity.email),
            mobilePhone = accountEntity.mobilePhone,
            name = accountEntity.name,
            type = accountEntity.userAccountType ?: UserAccountType.FULL_ACCOUNT,
            created = ZonedDateTime.parse(accountEntity.created, DateTimeFormatter.ISO_DATE_TIME)
                .withZoneSameInstant(brazilTimeZone),
            activated = accountEntity.activatedAt?.let {
                ZonedDateTime.parse(it, DateTimeFormatter.ISO_DATE_TIME)
                    .withZoneSameInstant(brazilTimeZone)
            },
            status = accountEntity.status,
            upgradeStatus = accountEntity.upgradeStatus,
            paymentStatus = accountEntity.paymentStatus ?: AccountPaymentStatus.UpToDate,
            configuration = accountEntity.configuration?.let { configEntity ->
                LegacyAccountConfiguration(
                    creditCardConfiguration =
                    CreditCardConfiguration(
                        quota = configEntity.creditCardQuota,
                        allowedRisk = configEntity.creditCardRiskAllowed,
                    ),
                    defaultWalletId = configEntity.defaultWalletId?.let { WalletId(it) },
                    receiveDDANotification = configEntity.receiveDDANotification,
                    receiveNotification = configEntity.receiveNotification,
                    refreshToken = configEntity.refreshToken,
                    accessToken = configEntity.accessToken,
                    groups = configEntity.groups.mapNotNull { AccountGroup.find(it) },
                    externalId = configEntity.externalId?.let {
                        ExternalId(
                            value = it,
                            providerName = AccountProviderName.valueOf(configEntity.externalProvider!!),
                        )
                    },
                    notificationGateway = configEntity.notificationGateway?.let {
                        NotificationGateways.valueOf(it)
                    } ?: NotificationGateways.WHATSAPP,
                )
            } ?: DEFAULT_LEGACY_ACCOUNT_CONFIGURATION,
            firstLoginAsOwner = accountEntity.firstLoginAsOwner?.let { firstLoginAsOwner ->
                ZonedDateTime.parse(
                    firstLoginAsOwner,
                    DateTimeFormatter.ISO_DATE_TIME,
                ).withZoneSameInstant(brazilTimeZone)
            },
            channel = accountEntity.channel,
            imageUrlSmall = accountEntity.imageUrlSmall,
            imageUrlLarge = accountEntity.imageUrlLarge,
            subscriptionType = accountEntity.subscriptionType,
            updated = ZonedDateTime.parse(accountEntity.updatedAt, DateTimeFormatter.ISO_DATE_TIME)
                .withZoneSameInstant(brazilTimeZone),
        )
    }

    private fun buildBalanceIndexScanKey(bankNo: Long, routingNo: Long, accountNo: AccountNumber) =
        "$bankNo#$routingNo#${accountNo.fullAccountNumber}"

    private fun buildBalanceIndexScanKey(bankAccount: BankAccount) = buildBalanceIndexScanKey(
        bankAccount.bankNo!!,
        bankAccount.routingNo,
        AccountNumber(bankAccount.accountNo, bankAccount.accountDv),
    )

    private fun LegacyAccountConfiguration.toEntity(): AccountConfigurationEntity {
        return AccountConfigurationEntity().also {
            it.creditCardQuota = this.creditCardConfiguration.quota
            it.creditCardRiskAllowed = this.creditCardConfiguration.allowedRisk
            it.defaultWalletId = this.defaultWalletId?.value
            it.receiveDDANotification = this.receiveDDANotification
            it.receiveNotification = this.receiveNotification
            it.refreshToken = this.refreshToken
            it.accessToken = this.accessToken
            it.groups = this.groups.map { group -> group.value }
            it.externalId = this.externalId?.value
            it.externalProvider = this.externalId?.providerName?.name
            it.notificationGateway = this.notificationGateway.name
        }
    }

    private fun buildExternalIdHashKey(externalId: ExternalId) = "$externalIdHashKey#${externalId.providerName}"

    override fun saveCreditCardBin(accountId: AccountId, id: AccountPaymentMethodId, bin: String) {
        val paymentMethodEntity =
            findPaymentMethodEntitiesByAccountIdAndStatus(accountId = accountId, status = null)
                .ifEmpty { return }
                .single { it.paymentMethodId == id.value }

        if (paymentMethodEntity.creditCard?.bin != null) {
            return
        }

        if (paymentMethodEntity.status != ACTIVE) {
            return
        }

        val updatedMethodEntity = PaymentMethodEntity().apply {
            primaryKey = paymentMethodEntity.primaryKey
            scanKey = paymentMethodEntity.scanKey
            paymentMethodId = paymentMethodEntity.paymentMethodId
            created = paymentMethodEntity.created
            type = paymentMethodEntity.type
            creditCard = paymentMethodEntity.creditCard
            status = ACTIVE
            activatedAt = getFormattedZonedDateTime()
            this.creditCard!!.bin = bin
        }

        dynamoDbDAO.save(updatedMethodEntity)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AccountDbRepository::class.java)
    }
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class ManualRegisterEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBAttribute(attributeName = "AccountId")
    lateinit var accountId: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Role")
    lateinit var role: Role

    fun toPrincipal() = Principal(id = AccountId(accountId), role = role)
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class PartialAccountEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: Role

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Status")
    lateinit var status: AccountStatus

    @DynamoDBAttribute(attributeName = "Email")
    lateinit var email: String

    @DynamoDBAttribute(attributeName = "Name")
    var name: String = ""

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    var indexHashKey: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    var indexRangeKey: AccountStatus? = null

    @DynamoDBAttribute(attributeName = "StatusUpdated")
    var statusUpdated: String? = null

    @DynamoDBAttribute(attributeName = "Groups")
    var groups: List<String> = emptyList()

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "RegistrationType")
    var registrationType: RegistrationType = RegistrationType.FULL

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "SubscriptionType")
    var subscriptionType: SubscriptionType = SubscriptionType.PIX

    fun toPartialAccount() =
        PartialAccount(
            id = AccountId(primaryKey),
            status = status,
            emailAddress = EmailAddress(email),
            name = name,
            role = scanKey,
            statusUpdated = statusUpdated?.let { ZonedDateTime.parse(it, statusUpdatedDateFormatter) },
            groups = groups.mapNotNull { AccountGroup.find(it) },
            registrationType = registrationType,
            subscriptionType = subscriptionType,
        )
}

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class AccountEntity {

    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var documentHash: String

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var status: AccountStatus

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "UpgradeStatus")
    var upgradeStatus: UpgradeStatus? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "PaymentStatus")
    var paymentStatus: AccountPaymentStatus? = null

    @DynamoDBIndexHashKey(attributeName = "GSIndex2PrimaryKey", globalSecondaryIndexName = "GSIndex2")
    var index2HashKey: String? = null

    @DynamoDBIndexRangeKey(attributeName = "GSIndex2ScanKey", globalSecondaryIndexName = "GSIndex2")
    var index2RangeKey: String? = null

    @DynamoDBIndexHashKey(attributeName = "GSIndex3PrimaryKey", globalSecondaryIndexName = "GSIndex3")
    var index3HashKey: String? = null

    @DynamoDBIndexRangeKey(attributeName = "GSIndex3ScanKey", globalSecondaryIndexName = "GSIndex3")
    var index3RangeKey: String? = null

    @DynamoDBIndexHashKey(attributeName = "GSIndex4PrimaryKey", globalSecondaryIndexName = "GSIndex4")
    var index4HashKey: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBIndexRangeKey(attributeName = "GSIndex4ScanKey", globalSecondaryIndexName = "GSIndex4")
    var index4RangeKey: UpgradeStatus? = null

    @DynamoDBAttribute(attributeName = "Email")
    lateinit var email: String

    @DynamoDBAttribute(attributeName = "Document")
    lateinit var document: String

    @DynamoDBAttribute(attributeName = "DocumentType")
    lateinit var documentType: String

    @DynamoDBAttribute(attributeName = "MobilePhone")
    lateinit var mobilePhone: String

    @DynamoDBAttribute(attributeName = "Created")
    lateinit var created: String

    @DynamoDBAttribute(attributeName = "Name")
    lateinit var name: String

    @DynamoDBAttribute(attributeName = "UserAccountType")
    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    var userAccountType: UserAccountType? = null

    @DynamoDBAttribute(attributeName = "UpdatedAt")
    var updatedAt: String = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)

    @DynamoDBAttribute(attributeName = "ActivatedAt")
    var activatedAt: String? = null

    @DynamoDBAttribute(attributeName = "Configuration")
    var configuration: AccountConfigurationEntity? = null

    @DynamoDBAttribute(attributeName = "FirstLoginAsOwner")
    var firstLoginAsOwner: String? = null

    @DynamoDBAttribute(attributeName = "Channel")
    var channel: String? = null

    @DynamoDBAttribute(attributeName = "ImageUrlSmall")
    var imageUrlSmall: String? = null

    @DynamoDBAttribute(attributeName = "ImageUrlLarge")
    var imageUrlLarge: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "SubscriptionType")
    var subscriptionType: SubscriptionType = SubscriptionType.PIX
}

@DynamoDBDocument
class AccountConfigurationEntity {
    var creditCardQuota: Long = 0

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    var creditCardRiskAllowed: RiskLevel = RiskLevel.LOW
    var defaultWalletId: String? = null
    var receiveDDANotification: Boolean = true
    var receiveNotification: Boolean = true
    var refreshToken: String? = null
    var accessToken: String? = null
    var externalId: String? = null
    var externalProvider: String? = null
    var groups: List<String> = emptyList()
    var notificationGateway: String? = null
}

private fun BankAccountComplexType.toInternalBankAccount() =
    InternalBankAccount(
        accountType = AccountType.valueOf(bankAccountType),
        bankNo = bankNo,
        routingNo = bankRoutingNo,
        accountNo = bankAccountNo,
        accountDv = bankAccountDv,
        document = document,
        bankAccountMode = bankAccountMode,
    )

private fun PaymentMethodEntity.toBankAccountPaymentMethod() =
    AccountPaymentMethod(
        id = AccountPaymentMethodId(paymentMethodId),
        accountId = AccountId(primaryKey),
        method = bankAccount!!.toInternalBankAccount(),
        status = status,
        created = convertToZonedDateTime(created),
    )

private fun PaymentMethodEntity.toExternalPaymentMethod() =
    AccountPaymentMethod(
        id = AccountPaymentMethodId(paymentMethodId),
        accountId = AccountId(primaryKey),
        method = ExternalPaymentMethod(providerName!!),
        status = status,
        created = convertToZonedDateTime(created),
    )

private fun PaymentMethodEntity.toCreditCardPaymentMethod() =
    AccountPaymentMethod(
        id = AccountPaymentMethodId(paymentMethodId),
        accountId = AccountId(primaryKey),
        method = creditCard!!.toCreditCard(),
        status = status,
        created = convertToZonedDateTime(created),
    )

private fun CreditCardComplexType.toCreditCard(): CreditCard {
    val binDetails = provider?.let { provider ->
        CreditCardBinDetails(
            provider = provider,
            cardType = cardType.orEmpty(),
            foreignCard = foreignCard.getOrFalse(),
            corporateCard = corporateCard.orEmpty(),
            issuer = issuer.orEmpty(),
            issuerCode = issuerCode.orEmpty(),
            prepaid = prepaid.orEmpty(),
            status = status.orEmpty(),
        )
    }

    return CreditCard(
        brand = CreditCardBrand.find(creditCardBrand),
        pan = bin?.let { it + creditCardPAN.drop(8) } ?: creditCardPAN,
        expiryDate = creditCardExpiryDate,
        token = token?.let { CreditCardToken(it) },
        binDetails = binDetails,
        riskLevel = riskLevel ?: RiskLevel.LOW,
        mainCard = mainCard,
        hmac = hmac,
        externalId = when {
            externalId.isNullOrEmpty() || !AccountProviderName.entries.any { it.name == externalProvider } -> null

            else -> CreditCardExternalId(
                value = externalId!!,
                provider = AccountProviderName.valueOf(externalProvider!!),
            )
        },
    )
}

fun convertToZonedDateTime(dateOrDateTime: String): ZonedDateTime? {
    val dateTimeFormatters =
        listOf(DateTimeFormatter.ISO_DATE_TIME, dateTimeFormat)

    val dateFormatters =
        listOf(dateFormat)
    dateTimeFormatters.forEach { formatter ->
        try {
            return ZonedDateTime.parse(dateOrDateTime, formatter)
        } catch (e: Exception) {
            // Não há o que fazer
        }
    }

    dateFormatters.forEach { formatter ->
        try {
            return LocalDate.parse(dateOrDateTime, formatter).atStartOfDay(brazilTimeZone)
        } catch (e: Exception) {
            // Não há o que fazer
        }
    }
    return null
}

private fun getFormattedZonedDateTime() = getZonedDateTime().format(DateTimeFormatter.ISO_DATE_TIME)