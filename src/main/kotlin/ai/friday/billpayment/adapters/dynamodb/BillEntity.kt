package ai.friday.billpayment.adapters.dynamodb

import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.BillTag
import ai.friday.billpayment.app.bill.BillTrackingCalculateOptions
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.bill.Recipient
import ai.friday.billpayment.app.payment.AmountCalculationModel
import ai.friday.billpayment.app.payment.FichaCompensacaoType
import ai.friday.billpayment.app.payment.PartialPaymentAmountType
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBAttribute
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBDocument
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexHashKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBIndexRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapperFieldModel
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBRangeKey
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTable
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTypeConverted
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBTyped
import java.math.BigDecimal
import java.math.BigInteger

@DynamoDBTable(tableName = BILL_PAYMENT_TABLE_NAME)
class BillEntity {
    @DynamoDBHashKey(attributeName = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String

    @DynamoDBRangeKey(attributeName = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex1PrimaryKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1PrimaryKey: String

    @DynamoDBIndexRangeKey(attributeName = "GSIndex1ScanKey", globalSecondaryIndexName = "GSIndex1")
    lateinit var gSIndex1ScanKey: String

    @DynamoDBIndexHashKey(attributeName = "GSIndex2PrimaryKey", globalSecondaryIndexName = "GSIndex2")
    var contactId: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBIndexRangeKey(attributeName = "GSIndex2ScanKey", globalSecondaryIndexName = "GSIndex2")
    var gSIndex2ScanKey: ContactIdIndexMetadata? = null

    @DynamoDBIndexHashKey(attributeName = "GSIndex3PrimaryKey", globalSecondaryIndexName = "GSIndex3")
    var gsIndex3PartitionKey: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBIndexRangeKey(attributeName = "GSIndex3ScanKey", globalSecondaryIndexName = "GSIndex3")
    var gSIndex3ScanKey: String? = null

    @DynamoDBIndexHashKey(attributeName = "GSIndex4PrimaryKey", globalSecondaryIndexName = "GSIndex4")
    var gsIndex4PartitionKey: String? = null // BillCategoryId

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBIndexRangeKey(attributeName = "GSIndex4ScanKey", globalSecondaryIndexName = "GSIndex4")
    var gSIndex4ScanKey: String? = null // WalletId

    @DynamoDBAttribute(attributeName = "Assignor")
    var assignor: String? = null

    @DynamoDBAttribute(attributeName = "Description")
    var description: String? = null

    @DynamoDBAttribute(attributeName = "BillRecipient")
    var billRecipient: BillRecipientDocument? = null

    @DynamoDBAttribute(attributeName = "DigitableBarcode")
    var digitableBarCode: String? = null

    @DynamoDBAttribute(attributeName = "DueDate")
    lateinit var dueDate: String

    @DynamoDBAttribute(attributeName = "EffectiveDueDate")
    var effectiveDueDate: String? = null

    @DynamoDBAttribute(attributeName = "PaidDate")
    var paidDate: String? = null

    @DynamoDBAttribute(attributeName = "AmountPaid")
    var amountPaid: Long? = null

    @DynamoDBAttribute(attributeName = "ExpirationDate")
    var expirationDate: String? = null

    @DynamoDBAttribute(attributeName = "Amount")
    var amount: Long = 0

    @DynamoDBAttribute(attributeName = "Discount")
    var discount: Long = 0

    @DynamoDBAttribute(attributeName = "Interest")
    var interest: Long = 0

    @DynamoDBAttribute(attributeName = "Fine")
    var fine: Long = 0

    @DynamoDBAttribute(attributeName = "AmountTotal")
    var amountTotal: Long = 0

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "Type")
    lateinit var type: BillType

    @DynamoDBAttribute(attributeName = "Status")
    lateinit var status: String

    @DynamoDBAttribute(attributeName = "Notification")
    var notification: String? = null

    @DynamoDBAttribute(attributeName = "Created")
    lateinit var created: String

    @DynamoDBAttribute(attributeName = "PaymentLimitTime")
    lateinit var paymentLimitTime: String

    @DynamoDBAttribute(attributeName = "LastSettleDate")
    var lastSettleDate: String? = null

    @DynamoDBAttribute(attributeName = "BillComingDueMessageSent")
    var billComingDueMessageSent: Boolean = false

    @DynamoDBAttribute(attributeName = "Source")
    lateinit var source: String

    @DynamoDBAttribute(attributeName = "PayerDocument")
    var payerDocument: String? = null

    @DynamoDBAttribute(attributeName = "PayerName")
    var payerName: String? = null

    @DynamoDBAttribute(attributeName = "WarningCode")
    var warningCode: Int = 0

    @DynamoDBAttribute(attributeName = "ScheduledDate")
    var scheduledDate: String? = null

    @DynamoDBAttribute(attributeName = "PayerAlias")
    var payerAlias: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "AmountCalculationModel")
    var amountCalculationModel: AmountCalculationModel? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "AmountCalculationOption")
    var amountCalculationOption: BillTrackingCalculateOptions? = null

    @DynamoDBAttribute(attributeName = "AmountCalculationValidUntil")
    var amountCalculationValidUntil: String? = null

    @DynamoDBAttribute(attributeName = "RecurrenceRule")
    var recurrenceRule: RecurrenceRuleEntity? = null

    @DynamoDBAttribute(attributeName = "WaitingFunds")
    var waitingFunds: Boolean? = null

    @DynamoDBAttribute(attributeName = "WaitingRetry")
    var waitingRetry: Boolean? = null

    @DynamoDBAttribute(attributeName = "BatchSchedulingId")
    var batchSchedulingId: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    @DynamoDBAttribute(attributeName = "FichaCompensacaoType")
    var fichaCompensacaoType: FichaCompensacaoType? = null

    @DynamoDBAttribute(attributeName = "VisibleBy")
    var visibleBy: List<String> = listOf()

    @DynamoDBAttribute(attributeName = "FichaCompensacaoIdNumber")
    var fichaCompensacaoIdNumber: String? = null

    @DynamoDBAttribute(attributeName = "ExternalBillId")
    var externalId: String? = null

    @DynamoDBAttribute(attributeName = "ExternalBillProvider")
    var externalProvider: String? = null

    @DynamoDBAttribute(attributeName = "MarkedAsPaidBy")
    var markedAsPaidBy: String? = null

    @DynamoDBAttribute(attributeName = "PartnerPayment")
    var partnerPayment: PartnerPaymentDocument? = null

    @DynamoDBAttribute(attributeName = "FridaySubscription")
    var subscriptionFee: Boolean? = false

    @DynamoDBAttribute(attributeName = "SecurityValidationResult")
    var securityValidationResult: List<String>? = null

    @DynamoDBAttribute(attributeName = "PaymentMethodsDetail")
    @DynamoDBTypeConverted(converter = PaymentMethodsDetailEntityConverter::class)
    var paymentMethodsDetail: PaymentMethodsDetailEntity? = null

    @DynamoDBAttribute(attributeName = "TransactionCorrelationId")
    var transactionCorrelationId: String? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.SS)
    @DynamoDBAttribute(attributeName = "Tags")
    var tags: Set<BillTag>? = null

    @Deprecated("Use categoryId instead")
    @DynamoDBAttribute(attributeName = "Category")
    var category: BillViewCategoryDocument? = null

    @DynamoDBAttribute(attributeName = "CategoryId")
    var categoryId: String? = null

    @DynamoDBAttribute(attributeName = "CategorySuggestions")
    var categorySuggestions: List<BillCategorySuggestionDocument> = listOf()

    @DynamoDBAttribute(attributeName = "PixQrCodeData")
    var pixQrCodeData: PixQrCodeDataDocument? = null

    @DynamoDBAttribute(attributeName = "Brand")
    var brand: String? = null

    @DynamoDBAttribute(attributeName = "ScheduledBy")
    var scheduledBy: String? = null

    @DynamoDBAttribute(attributeName = "ScheduleSource")
    var scheduleSource: String? = null

    @DynamoDBAttribute(attributeName = "PaymentWalletId")
    var paymentWalletId: String? = null

    @DynamoDBAttribute(attributeName = "IgnoredAt")
    var ignoredAt: String? = null

    @DynamoDBAttribute(attributeName = "GoalId")
    var goalId: String? = null

    @DynamoDBAttribute(attributeName = "MarkedAsPaidAt")
    var markedAsPaidAt: String? = null

    @DynamoDBAttribute(attributeName = "RefundedAt")
    var refundedAt: String? = null

    @DynamoDBAttribute(attributeName = "DivergentPayment")
    var divergentPayment: DivergentPaymentDocument? = null

    @DynamoDBAttribute(attributeName = "PartialPayment")
    var partialPayment: PartialPaymentDocument? = null
}

@DynamoDBDocument
class PartialPaymentDocument {
    var acceptPartialPayment: Boolean = false
    var qtdPagamentoParcial: Int = 0
    var qtdPagamentoParcialRegistrado: Int = 0
    var saldoAtualPagamento: Long = 0
}

@DynamoDBDocument
class DivergentPaymentDocument {
    var minimumAmount: Long? = null
    var maximumAmount: Long? = null

    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    var amountType: PartialPaymentAmountType? = null
    var minimumPercentage: BigDecimal? = null
    var maximumPercentage: BigDecimal? = null
}

@DynamoDBDocument
class BillCategorySuggestionDocument {
    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    lateinit var categoryId: String
    var probability: Long = 0
}

@DynamoDBDocument
class PixQrCodeDataDocument {
    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    lateinit var type: PixQrCodeType
    lateinit var info: String
    var additionalInfo: Map<String, String>? = null
    var pixId: String? = null
    var fixedAmount: Long? = null
    var expiration: String? = null
    var originalAmount: Long? = null
}

@DynamoDBDocument
class PartnerPaymentDocument {
    @DynamoDBTyped(DynamoDBMapperFieldModel.DynamoDBAttributeType.S)
    lateinit var partner: AccountProviderName
    var paymentId: String? = null
}

@DynamoDBDocument
class BillViewCategoryDocument {
    lateinit var billCategoryId: String
    lateinit var name: String
    lateinit var icon: String
    var default: Boolean = false
}

@DynamoDBDocument
data class BillRecipientDocument(
    var name: String? = null,
    var document: String? = null,
    var alias: String? = null,
    var accountType: String? = null,
    var bankNo: Long? = null,
    var routingNo: Long? = null,
    var accountNo: BigInteger? = null,
    var accountDv: String? = null,
    var bankISPB: String? = null,
    var pixKey: String? = null,
    var pixKeyType: String? = null,
    var institutionName: String? = null,
)

fun toBillRecipientDocument(recipient: Recipient?): BillRecipientDocument? {
    return recipient?.let {
        BillRecipientDocument(
            name = recipient.name,
            document = recipient.document,
            alias = recipient.alias,
        ).apply {
            recipient.bankAccount?.let {
                accountType = it.accountType.name
                bankNo = it.bankNo
                routingNo = it.routingNo
                accountNo = it.accountNo
                accountDv = it.accountDv
                bankISPB = it.ispb
            }
            recipient.pixKeyDetails?.let {
                name = it.owner.name
                accountType = it.holder.accountType.name
                routingNo = it.holder.routingNo
                accountNo = it.holder.accountNo
                accountDv = it.holder.accountDv
                bankISPB = it.holder.ispb
                pixKey = it.key.value
                pixKeyType = it.key.type.name
                institutionName = it.holder.institutionName
            }
        }
    }
}

fun toRecipient(billRecipientDocument: BillRecipientDocument?, pixQrCodeData: PixQrCodeData?): Recipient? {
    if (billRecipientDocument == null) {
        return null
    }

    val recipient = Recipient(
        name = billRecipientDocument.name.orEmpty(),
        document = billRecipientDocument.document,
        alias = billRecipientDocument.alias.orEmpty(),
        pixQrCodeData = pixQrCodeData,
    )

    return when {
        billRecipientDocument.pixKey != null -> recipient.copy(pixKeyDetails = billRecipientDocument.toPixKeyDetails())
        billRecipientDocument.accountType != null -> recipient.copy(bankAccount = billRecipientDocument.toBankAccount())
        else -> recipient
    }
}

internal fun BillRecipientDocument.toBankAccount(): BankAccount {
    return BankAccount(
        accountType = AccountType.valueOf(accountType!!),
        bankNo = bankNo,
        routingNo = routingNo!!,
        accountNo = accountNo!!,
        accountDv = accountDv!!,
        document = document!!,
        ispb = bankISPB,
    )
}

private fun BillRecipientDocument.toPixKeyDetails(): PixKeyDetails {
    return PixKeyDetails(
        key = PixKey(
            value = pixKey!!,
            type = PixKeyType.valueOf(pixKeyType!!),
        ),
        holder = PixKeyHolder(
            accountNo = accountNo!!,
            accountDv = accountDv!!,
            ispb = bankISPB!!,
            institutionName = institutionName!!,
            accountType = AccountType.valueOf(accountType!!),
            routingNo = routingNo!!,
        ),
        owner = PixKeyOwner(
            name = name!!,
            document = document!!,
        ),
    )
}