package ai.friday.billpayment.adapters.staging

import ai.friday.billpayment.app.account.ExternalAccount
import ai.friday.billpayment.app.account.ExternalRegisterStatus
import ai.friday.billpayment.app.account.RegisterNaturalPersonResponse
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.ExternalAccountRegister
import ai.friday.billpayment.app.integrations.NaturalPersonStatus
import ai.friday.billpayment.app.integrations.NaturalPersonStatusChangeReason
import arrow.core.Either
import arrow.core.right
import io.micronaut.context.annotation.Primary
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

// Esse bean sobe apenas em staging com prioridade para ser utilizado no lugar do ArbiAccountAdapter
// Isso é feito para podermos fazer o cadastro em staging sem chamar o Arbi

@Requires(env = ["staging"])
@Singleton
@Primary
class MockArbiAccountAdapter : ExternalAccountRegister {
    override fun notifyAccountRegisterDocumentsSent(name: String, document: String): Boolean {
        logger.info(
            Markers.append("name", name)
                .andAppend("document", document),
            "$logName#notifyAccountRegisterDocumentsSent",
        )

        return true
    }

    override fun registerNaturalPerson(customer: ExternalAccount): RegisterNaturalPersonResponse {
        logger.info(
            Markers.append("cpf", customer.documentInfo.cpf),
            "$logName#registerNaturalPerson",
        )

        return RegisterNaturalPersonResponse(
            success = true,
            accountNumber = *********, // Conta de staging
            accountDv = "3",
        )
    }

    override fun simpleRegisterNaturalPerson(customer: ExternalAccount): RegisterNaturalPersonResponse {
        logger.info(
            Markers.append("cpf", customer.documentInfo.cpf),
            "$logName#simpleRegisterNaturalPerson",
        )

        return RegisterNaturalPersonResponse(
            success = true,
            accountNumber = *********, // Conta de staging
            accountDv = "3",
        )
    }

    override fun queryExternalRegisterStatus(name: String, document: String): ExternalRegisterStatus? {
        logger.info(
            Markers.append("name", name)
                .andAppend("document", document),
            "$logName#queryExternalRegisterStatus",
        )

        return ExternalRegisterStatus.APPROVED
    }

    override fun close(accountNo: AccountNumber): Either<Exception, Unit> {
        logger.info(
            Markers.append("accountNo", accountNo.fullAccountNumber),
            "$logName#close",
        )

        return Unit.right()
    }

    override fun getNaturalPersonStatus(accountNo: AccountNumber): Either<Exception, NaturalPersonStatus> {
        logger.info(
            Markers.append("accountNo", accountNo.fullAccountNumber),
            "$logName#getNaturalPersonStatus",
        )

        return NaturalPersonStatus.ATI.right()
    }

    override fun getNaturalPersonsStatus(accountNos: List<AccountNumber>): Result<List<Pair<AccountNumber, NaturalPersonStatus>>> {
        logger.info(
            Markers.append("accountNo", accountNos.map { it.fullAccountNumber }),
            "$logName#getNaturalPersonsStatus",
        )

        return Result.success(listOf(AccountNumber("0000000") to NaturalPersonStatus.ATI))
    }

    override fun setNaturalPersonStatus(
        accountNo: AccountNumber,
        status: NaturalPersonStatus,
        reason: NaturalPersonStatusChangeReason,
    ): Either<Exception, Unit> {
        logger.info(
            Markers.append("accountNo", accountNo.fullAccountNumber)
                .andAppend("stastus", status)
                .andAppend("reason", reason),
            "$logName#setNaturalPersonStatus",
        )

        return Unit.right()
    }

    companion object {
        private val logger = LoggerFactory.getLogger(MockArbiAccountAdapter::class.java)
        private val logName = "MockArbiAccountAdapter"
    }
}