package ai.friday.billpayment.adapters.vehicledebts

import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.MessagePublisher
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.VEHICLE_DEBTS_CALLBACK)
@Controller("/vehicle_debts")
@FridayMePoupe
class VehicleDebtsCallbackController(
    private val publisher: MessagePublisher,
    private val messageHandlerConfiguration: SQSMessageHandlerConfiguration,
) {
    private val logger = LoggerFactory.getLogger(VehicleDebtsCallbackController::class.java)

    @Post("/webhook")
    fun vehicleDebtsCallbackController(
        @Body body: String,
    ): HttpResponse<*> {
        val map = parseObjectFrom<Map<String, Any>>(body)
        logger.info(
            Markers
                .append("request", body)
                .andAppend("parsedRequest", map),
            "VehicleDebtsCallbackController",
        )

        publisher.sendMessage(messageHandlerConfiguration.vehicleDebtsQueueName, map)
        return HttpResponse.ok<Unit>()
    }
}