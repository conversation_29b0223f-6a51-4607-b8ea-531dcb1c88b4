package ai.friday.billpayment.adapters.btg

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.and
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.chatbot.Organization
import ai.friday.billpayment.app.itp.ConsentId
import ai.friday.billpayment.app.itp.ITPAdapter
import ai.friday.billpayment.app.itp.ITPServiceError
import ai.friday.billpayment.app.itp.PaymentIntent
import ai.friday.billpayment.app.itp.PaymentIntentDetails
import ai.friday.billpayment.app.itp.PaymentIntentResponse
import ai.friday.billpayment.app.itp.toExternalBankAccount
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import arrow.core.Either
import arrow.core.getOrElse
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientException
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

private const val DOCUMENT_TYPE = "CPF"

@FridayMePoupe
open class BtgITPAdapter(
    @param:Client(value = "\${integrations.btg.host}") private val httpClient: RxHttpClient,
    private val configuration: BtgITPConfiguration,
) : ITPAdapter {
    private val logger = LoggerFactory.getLogger(BtgITPAdapter::class.java)

    fun listResources() {
        val logName = "BtgITPAdapter#listResources"
        val markers = Markers.empty()
        try {
            val accessToken = createAccessToken().getOrElse { return }
            val httpRequest =
                HttpRequest
                    .GET<Unit>("/cm/webhook/resource")
                    .json()
                    .bearer(accessToken)

            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(String::class.java),
                    Argument.of(String::class.java),
                )

            val httpResponse = call.firstOrError().blockingGet()

            markers.andAppend("response", httpResponse)
            logger.info(markers, logName)
        } catch (ex: Exception) {
            when (ex) {
                is HttpClientResponseException -> markers.and("statusCode" to ex.status)
            }

            logger.error(markers.and("ErrorMessage" to ex.message), logName, ex)
        }
    }

    fun listWebhooks() {
        val logName = "BtgITPAdapter#listWebhooks"
        val markers = Markers.empty()
        try {
            val accessToken = createAccessToken().getOrElse { return }
            val httpRequest =
                HttpRequest
                    .GET<Unit>("/cm/webhook/subscription")
                    .json()
                    .bearer(accessToken)

            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(String::class.java),
                    Argument.of(String::class.java),
                )

            val httpResponse = call.firstOrError().blockingGet()

            markers.andAppend("response", httpResponse)
            logger.info(markers, logName)
        } catch (ex: Exception) {
            when (ex) {
                is HttpClientResponseException -> markers.and("statusCode" to ex.status)
            }

            logger.error(markers.and("ErrorMessage" to ex.message), logName, ex)
        }
    }

    fun createWebhook(
        webhookUrl: String,
        resourceId: String,
        events: List<String>,
    ) {
        val logName = "BtgITPAdapter#createWebhook"
        val markers = Markers.empty()
        try {
            val accessToken = createAccessToken().getOrElse { return }
            val httpRequest =
                HttpRequest
                    .POST(
                        "/cm/webhook/subscription",
                        ResourceSubscriptionTO(
                            resources =
                            listOf(
                                ResourceTO(
                                    entity = resourceId,
                                    blockedManyErrors = false,
                                    qtyConsecutiveErrors = 0,
                                    webhookUrl = webhookUrl,
                                    secret = "secret",
                                    eventNames = events,
                                    isMtls = false,
                                ),
                                ResourceTO(
                                    entity = "ReceiverPaymentConsent",
                                    blockedManyErrors = false,
                                    qtyConsecutiveErrors = 0,
                                    webhookUrl = "https://api.friday.ai/btg/paymentConsent",
                                    secret = "secret",
                                    eventNames = listOf(
                                        "ENQUEUED",
                                        "AWAITING_AUTHORISATION",
                                        "AUTHORISED",
                                        "REJECTED",
                                        "PARTIALLY_ACCEPTED",
                                        "CONSUMED",
                                        "ERROR",
                                    ),
                                    isMtls = false,
                                ),
                            ),
                        ),
                    ).json()
                    .bearer(accessToken)

            val call =
                httpClient.exchange(
                    httpRequest,
                    Argument.of(String::class.java),
                    Argument.of(String::class.java),
                )

            val httpResponse = call.firstOrError().blockingGet()

            markers.andAppend("response", httpResponse.body()).andAppend("status", httpResponse.status)
            logger.info(markers, logName)
        } catch (ex: Exception) {
            when (ex) {
                is HttpClientResponseException -> markers.and("statusCode" to ex.status)
            }

            logger.error(markers.and("ErrorMessage" to ex.message), logName, ex)
        }
    }

    fun updateWebhook(
        webhookUrl: String,
        resourceId: String,
        events: List<String>,
    ) {
        val logName = "BtgITPAdapter#updateWebhook"
        val markers = Markers.empty()
        try {
            val accessToken = createAccessToken().getOrElse { return }
            val httpRequest =
                HttpRequest
                    .PUT(
                        "/cm/webhook/subscription",
                        ResourceSubscriptionTO(
                            resources =
                            listOf(
                                ResourceTO(
                                    entity = resourceId,
                                    blockedManyErrors = false,
                                    qtyConsecutiveErrors = 0,
                                    webhookUrl = webhookUrl,
                                    secret = "secret",
                                    eventNames = events,
                                    isMtls = false,
                                ),
                            ),
                        ),
                    ).json()
                    .bearer(accessToken)

            val call =
                httpClient.exchange(
                    httpRequest,
                    Argument.of(String::class.java),
                    Argument.of(String::class.java),
                )

            val httpResponse = call.firstOrError().blockingGet()

            markers.andAppend("response", httpResponse.body()).andAppend("status", httpResponse.status)
            logger.info(markers, logName)
        } catch (ex: Exception) {
            when (ex) {
                is HttpClientResponseException -> markers.and("statusCode" to ex.status)
            }

            logger.error(markers.and("ErrorMessage" to ex.message), logName, ex)
        }
    }

    override fun createPaymentRedirectUrl(
        paymentIntent: PaymentIntent,
    ): Either<ITPServiceError, PaymentIntentResponse> {
        val authorizationServerId = paymentIntent.authorizationServerId
        val externalBankAccount = paymentIntent.toExternalBankAccount()
        val clientRequestId = "${paymentIntent.paymentIntentId.value}#${getZonedDateTime().toInstant().toEpochMilli()}"

        val markers =
            Markers
                .append("authorizationServerId", authorizationServerId)
                .and(
                    "externalBankAccount" to externalBankAccount,
                    "details" to paymentIntent.details,
                    "paymentIntent" to paymentIntent.paymentIntentId,
                    "accountId" to paymentIntent.accountId.value,
                    "walletId" to paymentIntent.walletId.value,
                    "clientRequestId" to clientRequestId,
                )

        try {
            val document = externalBankAccount.document.value

            val paymentDate = getZonedDateTime()

            val redirectUrl = configuration.redirectUrl

            val (payload, url) =
                when (paymentIntent.details) {
                    is PaymentIntentDetails.WithPixKey ->
                        Pair(
                            CreatePaymentITPRequest(
                                amount = paymentIntent.details.amount,
                                pixKey = paymentIntent.details.pixKey.value,
                                clientRequestId = clientRequestId,
                                authorizationServerId = authorizationServerId,
                                redirectUrl = redirectUrl,
                                paymentDate = paymentDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
                                remittanceInformation = null,
                                tags = null,
                                loggedUser = CreatePaymentITPLoggedUser(
                                    identification = document,
                                    type = DOCUMENT_TYPE,
                                ),
                                metadata =
                                CreatePaymentITPMetadata(
                                    platform = "browser",
                                    os = "other",
                                    osVersion = "1.0",
                                ),
                            ),
                            configuration.createPaymentPath,
                        )

                    is PaymentIntentDetails.WithQRCode ->
                        Pair(
                            CreateQrCodePaymentITPRequest(
                                qrCode = paymentIntent.details.qrCode,
                                clientRequestId = clientRequestId,
                                authorizationServerId = authorizationServerId,
                                redirectUrl = redirectUrl,
                                paymentDate = paymentDate.format(DateTimeFormatter.ISO_LOCAL_DATE),
                                remittanceInformation = null,
                                tags = null,
                                loggedUser = CreatePaymentITPLoggedUser(
                                    identification = document,
                                    type = DOCUMENT_TYPE,
                                ),
                                metadata =
                                CreatePaymentITPMetadata(
                                    platform = "browser",
                                    os = "other",
                                    osVersion = "1.0",
                                ),
                            ),
                            configuration.createQrCodePaymentPath,
                        )
                }
            val accessToken = createAccessToken().getOrElse { return it.left() }
            val httpRequest =
                HttpRequest
                    .POST(url, payload)
                    .json()
                    .bearer(accessToken)

            val call =
                httpClient.exchange(
                    httpRequest,
                    Argument.of(CreatePaymentITPResponse::class.java),
                    Argument.of(Map::class.java),
                )

            val httpResponse = call.firstOrError().blockingGet()
            markers.and(
                "pactualId" to httpResponse.body().body.pactualId,
                "institutionName" to paymentIntent.authorizationServerName,
            )
            val response = httpResponse.body()
            if (response.body.body.consentRedirectUrl == null) {
                logger.error(
                    markers.and(
                        "errorCode" to response.body.errorCode,
                        "errorDescription" to response.body.errorDescription,
                        "response" to httpResponse.body,
                    ),
                    "BtgITPAdapter#CreatePayment",
                )
                return ITPServiceError.ITPErrorWithException
                    .ITPProviderError(exception = IllegalStateException("consentRedirectUrl não pode ser vazia"))
                    .left()
            }

            logger.info(markers, "BtgITPAdapter#CreatePayment")
            return PaymentIntentResponse(
                paymentIntentId = paymentIntent.paymentIntentId,
                url = response.body.body.consentRedirectUrl,
                consentId = ConsentId(httpResponse.body().body.pactualId),
            )
                .right()
        } catch (ex: Exception) {
            when (ex) {
                is HttpClientResponseException -> markers.and("statusCode" to ex.status)
            }
            logger.error(markers.and("ErrorMessage" to ex.message), "BtgITPAdapter#CreatePayment", ex)
            return ITPServiceError.ITPErrorWithException.ITPProviderError(exception = ex).left()
        }
    }

    override fun listFinancialInstitutions(): Either<ITPServiceError, List<Organization>> {
        try {
            val accessToken = createAccessToken().getOrElse { return it.left() }
            val httpRequest =
                HttpRequest
                    .GET<ListFinancialInstitutionITPResponse>(configuration.listOrganizationPath)
                    .json()
                    .bearer(accessToken)

            val call =
                httpClient.retrieve(httpRequest, Argument.of(ListFinancialInstitutionITPResponse::class.java))
            val httpResponse = call.firstOrError().blockingGet()

            logger.info(Markers.append("size", httpResponse.body.size), "BtgITPAdapter#listFinancialInstitutions")

            return httpResponse.body
                .map { organization ->
                    Organization(
                        authorizationServerId = organization.authorizationServerId,
                        ispb = organization.ispb,
                        friendlyName = organization.customerFriendlyName,
                    )
                }.right()
        } catch (ex: Exception) {
            val log = Markers.append("ErrorMessage", ex.message)
            when (ex) {
                is HttpClientResponseException -> log.and("statusCode" to ex.status)
            }

            logger.error(log, "BtgITPAdapter#listFinancialInstitutions", ex)
            return ITPServiceError.ITPErrorWithException.ITPProviderError(exception = ex).left()
        }
    }

    override fun createPaymentRedirectURL(
        url: String,
        description: String,
    ): Either<ITPServiceError, Unit> {
        val logName = "BtgITPAdapter#createPaymentRedirectURL"
        val payload =
            mapOf(
                "redirectUrl" to url,
                "description" to description,
            )
        val markers = Markers.append("payload", payload)
        try {
            val accessToken = createAccessToken().getOrElse { return it.left() }
            val httpRequest =
                HttpRequest
                    .POST(configuration.createConsentRedirectUrlPath, payload)
                    .json()
                    .bearer(accessToken)

            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(Map::class.java),
                    Argument.of(String::class.java),
                )

            val httpResponse = call.firstOrError().blockingGet()

            markers.andAppend("response", httpResponse)
            logger.info(markers, logName)
            return Unit.right()
        } catch (ex: Exception) {
            when (ex) {
                is HttpClientResponseException -> markers.and("statusCode" to ex.status)
            }

            logger.error(markers.and("ErrorMessage" to ex.message), logName, ex)
            return ITPServiceError.ITPErrorWithException.ITPProviderError(exception = ex).left()
        }
    }

    override fun listPaymentRedirectURLs(): Either<ITPServiceError, List<Pair<String, String>>> =
        internalListPaymentRedirectURLs().map {
            it.map { body ->
                Pair(body.body.redirectUrl, body.body.description)
            }
        }

    override fun deletePaymentRedirectURL(url: String): Either<ITPServiceError, Unit> {
        val logName = "BtgITPAdapter#deletePaymentRedirectURLs"

        val markers = Markers.empty()
        try {
            val urlFound =
                internalListPaymentRedirectURLs()
                    .getOrElse { return it.left() }
                    .firstOrNull { redirectUrlTO ->
                        url == redirectUrlTO.body.redirectUrl
                    } ?: return ITPServiceError.WalletNotFound().left() // TODO - melhorar o erro

            val accessToken = createAccessToken().getOrElse { return it.left() }
            val httpRequest =
                HttpRequest
                    .DELETE<Unit>("${configuration.createConsentRedirectUrlPath}/${urlFound.pactualId}")
                    .json()
                    .bearer(accessToken)

            val call =
                httpClient.exchange(
                    httpRequest,
                    Argument.of(Unit::class.java),
                    Argument.of(String::class.java),
                )

            val httpResponse = call.firstOrError().blockingGet()
            markers.andAppend("response", httpResponse)
            logger.info(markers, logName)

            if (httpResponse.status in listOf(HttpStatus.NO_CONTENT, HttpStatus.OK)) {
                return Unit.right()
            }
            throw IllegalStateException("Erro ao deletar o redirectUrl. Codigo de resposta deveria ser 204")
        } catch (ex: Exception) {
            when (ex) {
                is HttpClientResponseException -> markers.and("statusCode" to ex.status)
            }

            logger.error(markers.and("ErrorMessage" to ex.message), logName, ex)
            return ITPServiceError.ITPErrorWithException.ITPProviderError(exception = ex).left()
        }
    }

    override fun callbackForPaymentInitiationConsent(
        state: String,
        code: String?,
        idToken: String?,
        error: String?,
        errorDescription: String?,
    ): Either<ITPServiceError, Unit> {
        val logName = "BtgITPAdapter#callbackForPaymentInitiationConsent"
        val payload =
            mapOf(
                "state" to state,
                "idToken" to idToken,
                "code" to code,
                "error" to error,
                "errorDescription" to errorDescription,
            )

        val markers = Markers.append("payload", payload)
        try {
            val accessToken = createAccessToken().getOrElse { return it.left() }
            val httpRequest =
                HttpRequest
                    .POST(configuration.paymentConsentCallbackPath, payload)
                    .json()
                    .bearer(accessToken)

            val call =
                httpClient.exchange(
                    httpRequest,
                    Argument.of(String::class.java),
                    Argument.of(String::class.java),
                )

            val httpResponse = call.firstOrError().blockingGet()
            val responseBody = httpResponse.body.get()

            markers.andAppend("response", getObjectMapper().writeValueAsString(responseBody)).andAppend("headers", httpResponse.headers.asMap())
            logger.info(markers, logName)
            return Unit.right()
        } catch (ex: Exception) {
            when (ex) {
                is HttpClientResponseException -> markers.and("statusCode" to ex.status)
            }

            logger.error(markers.and("ErrorMessage" to ex.message), logName, ex)
            return ITPServiceError.ITPErrorWithException.ITPProviderError(exception = ex).left()
        }
    }

    private fun internalListPaymentRedirectURLs(): Either<ITPServiceError, List<RedirectUrlTO>> {
        val logName = "BtgITPAdapter#listPaymentRedirectURLs"
        val markers = Markers.empty()
        try {
            val accessToken = createAccessToken().getOrElse { return it.left() }
            val httpRequest =
                HttpRequest
                    .GET<Unit>(configuration.createConsentRedirectUrlPath)
                    .json()
                    .bearer(accessToken)

            val call =
                httpClient.retrieve(
                    httpRequest,
                    Argument.of(ListGetConsentRedirectResponseTO::class.java),
                    Argument.of(String::class.java),
                )

            val httpResponse = call.firstOrError().blockingGet()

            markers.andAppend("response", httpResponse)
            logger.info(markers, logName)
            return httpResponse.body.right()
        } catch (ex: Exception) {
            when (ex) {
                is HttpClientResponseException -> markers.and("statusCode" to ex.status)
            }

            logger.error(markers.and("ErrorMessage" to ex.message), logName, ex)
            return ITPServiceError.ITPErrorWithException.ITPProviderError(exception = ex).left()
        }
    }

    private fun createAccessToken(): Either<ITPServiceError, String> {
        try {
            val getAccessTokenRequest =
                with(configuration) {
                    mapOf("client_id" to clientId, "client_secret" to clientSecret, "grant_type" to grantType)
                }
            val httpRequest =
                HttpRequest
                    .POST(configuration.accessTokenPath, getAccessTokenRequest)
                    .contentType(MediaType.APPLICATION_FORM_URLENCODED_TYPE)
                    .accept(MediaType.APPLICATION_JSON_TYPE)

            val call = httpClient.retrieve(httpRequest, Argument.of(CreateAccessTokenResponse::class.java))
            val httpResponse = call.firstOrError().blockingGet()

            logger.info("BtgITPAdapter#createAccessToken")

            return httpResponse.accessToken.right()
        } catch (ex: HttpClientException) {
            val log = Markers.append("ErrorMessage", ex.message)
            when (ex) {
                is HttpClientResponseException -> log.and("statusCode" to ex.status)
            }

            logger.error(log.andAppend("ACTION", "VERIFY"), "BtgITPAdapter#createAccessToken", ex)
            return ITPServiceError.ITPErrorWithException.ITPProviderError(exception = ex).left()
        }
    }

    fun <T> MutableHttpRequest<T>.json() =
        this.accept(MediaType.APPLICATION_JSON).contentType(MediaType.APPLICATION_JSON)

    fun <T> MutableHttpRequest<T>.bearer(token: String): MutableHttpRequest<T> =
        this.header("Authorization", "Bearer $token")
}

data class CreateAccessTokenResponse(
    @JsonProperty("expire_in") val expireIn: Int,
    @JsonProperty("access_token") val accessToken: String,
)

enum class FinancialInstitutionITPStatus {
    ACTIVE,
    PENDING,
    WITHDRAWN,
}

data class ListFinancialInstitutionITPResponse(
    val version: String,
    val status: Int,
    val body: List<OrganizationTO>,
)

data class ListGetConsentRedirectResponseTO(
    val version: String,
    val status: Int,
    val body: List<RedirectUrlTO>,
)

data class RedirectUrlTO(
    val pactualId: String,
    val clientRequestId: String?,
    val status: String,
    val body: RedirectUrlBodyTO,
)

data class RedirectUrlBodyTO(
    val redirectUrl: String,
    val description: String,
)

data class OrganizationTO(
    val organizationId: String,
    val authorizationServerId: String,
    val status: FinancialInstitutionITPStatus,
    val customerFriendlyName: String,
    val customerFriendlyDescription: String?,
    val customerFriendlyLogoUri: String?,
    val organizationName: String,
    val legalEntityName: String,
    val ispb: String?,
    val clearingCode: String?,
    val cnpj: String,
    val apiFamilyTypes: List<String>,
)

data class CreatePaymentITPResponse(
    val version: String,
    val status: Int,
    val body: CreatePaymentITPBodyResponse,
)

data class CreatePaymentITPRequest(
    val pixKey: String,
    val clientRequestId: String,
    val authorizationServerId: String,
    val redirectUrl: String,
    val amount: Long,
    val paymentDate: String?, // RFC-3339
    val remittanceInformation: String?,
    val tags: List<String>?,
    val loggedUser: CreatePaymentITPLoggedUser,
    val debtorAccount: CreatePaymentITPDebtorAccount? = null,
    val metadata: CreatePaymentITPMetadata,
)

data class CreatePaymentITPCreditAccount(
    val personType: String,
    val cpfCnpj: String,
    val name: String,
    val ispb: String,
    val issuer: String,
    val number: String,
    val accountType: String,
)

data class CreateQrCodePaymentITPRequest(
    val qrCode: String,
    val clientRequestId: String,
    val authorizationServerId: String,
    val redirectUrl: String,
    val paymentDate: String?, // RFC-3339
    val remittanceInformation: String?,
    val tags: List<String>?,
    val loggedUser: CreatePaymentITPLoggedUser,
    val debtorAccount: CreatePaymentITPDebtorAccount? = null,
    val metadata: CreatePaymentITPMetadata,
)

data class CreatePaymentITPMetadata(
    val platform: String, // "browser" "app"
    val os: String, // "android" "ios" "windows" "linux" "macos" "unix" "other"
    val osVersion: String,
)

data class CreatePaymentITPLoggedUser(
    val identification: String,
    val type: String, // CPF ou CNPJ
)

data class CreatePaymentITPDebtorAccount(
    val ispb: String,
    val issuer: String,
    val number: String,
    val accountType: String,
)

data class CreatePaymentITPBodyResponse(
    val pactualId: String,
    val pactualBulkId: String?,
    val clientRequestId: String,
    val createTimestamp: String,
    val lastUpdateTimestamp: String,
    val entity: String,
    val status: String,
    val body: ResultITPBody,
    val tags: List<String>?,
    val errorCode: String? = null,
    val errorDescription: String? = null,
)

data class ResultITPBody(
    val consentRedirectUrl: String?,
)

@ConfigurationProperties("integrations.btg")
class BtgITPConfiguration {
    lateinit var host: String
    lateinit var clientId: String
    lateinit var clientSecret: String
    lateinit var grantType: String
    lateinit var accessTokenPath: String
    lateinit var createPaymentPath: String
    lateinit var createQrCodePaymentPath: String
    lateinit var createConsentRedirectUrlPath: String
    lateinit var paymentConsentCallbackPath: String
    lateinit var listOrganizationPath: String
    lateinit var redirectUrl: String
}

data class ResourceSubscriptionTO(
    val resources: List<ResourceTO>,
)

data class ResourceTO(
    val entity: String,
    val blockedManyErrors: Boolean,
    val qtyConsecutiveErrors: Int,
    val webhookUrl: String,
    val secret: String,
    val eventNames: List<String>,
    val isMtls: Boolean,
)