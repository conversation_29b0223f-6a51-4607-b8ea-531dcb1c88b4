package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillComingDue
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.UpdatedRegisterData
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.BillComingDueRepository
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.brazilTimeZone
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.LocalDate
import java.time.LocalTime
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
@Requires(property = "features.billComingDueHandler.enabled", value = "true")
open class BillComingDueHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
    private val billComingDueRepository: BillComingDueRepository,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.billComingDueQueueName,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter =
    EventTypeFilter(
        eventTypes =
        listOf(
            BillEventType.ADD,
            BillEventType.REGISTER_UPDATED,
        ),
    ),
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val markers = append("event", m.body())

        val billComingDue =
            when (val event = parseEvent(m)) {
                is FichaCompensacaoAdded ->
                    if (!event.isOverdue()) {
                        createBillComingDue(event)
                    } else {
                        null
                    }

                is RegisterUpdated -> processRegisterUpdated(event)
                is BillPaymentScheduled -> setIsScheduled(billId = event.billId, isScheduled = true)
                is BillPaymentScheduleCanceled -> setIsScheduled(billId = event.billId, isScheduled = false)
                else -> null
            }

        billComingDue?.let {
            billComingDueRepository.save(it)
        }

        logger.debug(markers.andAppend("billComingDue", billComingDue), "BillComingDueHandler")

        return SQSHandlerResponse(true)
    }

    private fun createBillComingDue(event: FichaCompensacaoAdded) =
        BillComingDue(
            billId = event.billId,
            walletId = event.walletId,
            dueDate = event.effectiveDueDate,
            paymentLimitTime = event.paymentLimitTime,
            isScheduled = false,
            created = getZonedDateTime(),
        )

    private fun createBillComingDue(event: BillAdded) =
        BillComingDue(
            billId = event.billId,
            walletId = event.walletId,
            dueDate = event.effectiveDueDate ?: event.dueDate,
            paymentLimitTime = event.paymentLimitTime,
            isScheduled = false,
            created = getZonedDateTime(),
        )

    private fun processRegisterUpdated(event: RegisterUpdated) =
        when (event.updatedRegisterData) {
            is UpdatedRegisterData.NewDueDate -> updateDueDate(event.billId, event.updatedRegisterData.effectiveDueDate)
            else -> null
        }

    private fun setIsScheduled(
        billId: BillId,
        isScheduled: Boolean,
    ) =
        billComingDueRepository
            .findById(billId)
            ?.copy(isScheduled = isScheduled)

    private fun updateDueDate(
        billId: BillId,
        effectiveDueDate: LocalDate,
    ) =
        billComingDueRepository.findById(billId)?.copy(dueDate = effectiveDueDate)

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("event", m), "BillComingDueHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BillComingDueHandler::class.java)
    }
}

private fun FichaCompensacaoAdded.isOverdue(): Boolean {
    val time = paymentLimitTime
    val zonedDueDateTime = ZonedDateTime.of(effectiveDueDate.atTime(LocalTime.from(time)), brazilTimeZone)
    return getZonedDateTime().isAfter(zonedDueDateTime)
}

private fun BillAdded.isOverdue(): Boolean {
    val time = paymentLimitTime
    val dueDate = effectiveDueDate ?: dueDate
    val zonedDueDateTime = ZonedDateTime.of(dueDate.atTime(LocalTime.from(time)), brazilTimeZone)
    return getZonedDateTime().isAfter(zonedDueDateTime)
}