package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.banking.SynchronizeBankAccountTO
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

open class SQSPixDepositNotificationHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sqs.queues.pixDepositNotification") private val queueName: String,
    private val internalBankService: InternalBankService,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    queueName,
    consumers = 1,
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val message = parseObjectFrom<SynchronizeBankAccountTO>(m.body())
        val marker = Markers.append("body", message)

        logger.info(marker, "SQSPixDepositNotificationHandler")

        val synchronizedAnyStatement =
            internalBankService.synchronizeBankAccount(
                bankNo = message.bankNo,
                routingNo = message.routingNo,
                accountNumber = AccountNumber(message.fullAccountNumber),
            )

        if (!synchronizedAnyStatement && m.receiveCount() >= MAX_RETRIES) {
            logger.error(marker, "SQSPixDepositNotificationHandler#MaxRetriesExceeded")
            return SQSHandlerResponse(true)
        }

        return SQSHandlerResponse(synchronizedAnyStatement)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(Markers.append("body", m.body()), "SQSPixDepositNotificationHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSPixDepositNotificationHandler::class.java)
        const val MAX_RETRIES = 6
    }
}