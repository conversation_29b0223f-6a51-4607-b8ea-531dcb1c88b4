package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.dynamodb.toBillEventDetailEntity
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.app.account.AccountEvent
import ai.friday.billpayment.app.bill.BillAdded
import ai.friday.billpayment.app.bill.BillEvent
import ai.friday.billpayment.app.bill.BillPaymentScheduleCanceled
import ai.friday.billpayment.app.bill.BillPaymentScheduled
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.FichaCompensacaoAdded
import ai.friday.billpayment.app.bill.PaymentFailed
import ai.friday.billpayment.app.bill.PaymentRefunded
import ai.friday.billpayment.app.bill.RegisterUpdated
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.eventbus.EventBus
import ai.friday.billpayment.app.integrations.EventPublisher
import ai.friday.billpayment.app.metrics.AccountEvents
import ai.friday.billpayment.app.metrics.BillEvents
import ai.friday.billpayment.app.metrics.DynamicMetric
import ai.friday.billpayment.app.metrics.WalletEvents
import ai.friday.billpayment.app.metrics.metricRegister
import ai.friday.billpayment.app.metrics.push
import ai.friday.billpayment.app.wallet.WalletEvent
import ai.friday.billpayment.log
import ai.friday.billpayment.markers
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sns.model.MessageAttributeValue
import software.amazon.awssdk.services.sns.model.PublishRequest
import software.amazon.awssdk.services.sns.model.PublishResponse

@Singleton
class SNSEventPublisher(
    private val snsClient: SnsClient,
    @Property(name = "features.eventBus.publisher.sns.enabled", defaultValue = "false") private val eventBusEnabled: Boolean,
    @Property(name = "sns.arnPrefix") private val snsArnPrefix: String,
) : EventPublisher, EventBus.Publisher {
    @field:Property(name = billEventTopicArnPropertyName)
    lateinit var billTopicArn: String

    @field:Property(name = walletEventTopicArnPropertyName)
    lateinit var walletTopicArn: String

    @field:Property(name = accountEventTopicArnPropertyName)
    lateinit var accountTopicArn: String

    @field:Property(name = eventBusTopicArnPropertyName)
    lateinit var eventBusTopicArn: String

    private val mapper = getObjectMapper()

    override fun publish(topic: String, message: Any, attributes: Map<String, String>) {
        val attrs = attributes.mapValues { (_, value) -> snsMessageAttributeValue(value) }

        val publishRequest = PublishRequest.builder().topicArn(snsArnPrefix + topic)
            .message(mapper.writeValueAsString(message))
            .messageAttributes(attrs).build()

        val publishResponse: PublishResponse = snsClient.publish(publishRequest)

        LOG.info(
            log(
                "event" to message,
                "eventName" to message::class.simpleName,
                "messageId" to publishResponse.messageId(),
            ),
            "SNSEventPublisher",
        )
    }

    override fun publish(event: BillEvent, billType: BillType) {
        val attributes = mapOf(
            accountIdAttributeName to MessageAttributeValue.builder().dataType("String")
                .stringValue(event.walletId.value).build(),
            eventTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                .stringValue(event.eventType.name).build(),
            billTypeAttributeName to MessageAttributeValue.builder().dataType("String").stringValue(billType.name)
                .build(),
        )
        val publishRequest = PublishRequest.builder().topicArn(billTopicArn)
            .message(mapper.writeValueAsString(event.toBillEventDetailEntity()))
            .messageAttributes(attributes).build()
        val publishResponse: PublishResponse = snsClient.publish(publishRequest)

        sendEvents(event, billType)

        LOG.info(
            log(
                "event" to event,
                "eventName" to event::class.simpleName,
                "messageId" to publishResponse.messageId(),
            ),
            "SNSEventPublisher",
        )
    }

    override fun publish(event: WalletEvent) {
        val attributes = mapOf(
            eventTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                .stringValue(event.eventType.name).build(),
        )
        val publishRequest = PublishRequest.builder().topicArn(walletTopicArn)
            .message(mapper.writeValueAsString(event))
            .messageAttributes(attributes)
            .build()
        val publishResponse: PublishResponse = snsClient.publish(publishRequest)

        metricRegister(
            WalletEvents(),
            "event_type" to event.eventType,
            "action_source" to event.actionSource::class.simpleName,
        )

        LOG.info(append("event", event).and(append("messageId", publishResponse.messageId())), "SNSEventPublisher")
    }

    override fun publish(event: AccountEvent) {
        val attributes = mapOf(
            eventTypeAttributeName to MessageAttributeValue.builder().dataType("String")
                .stringValue(event.eventType.name).build(),
        )
        val publishRequest = PublishRequest.builder().topicArn(accountTopicArn)
            .message(mapper.writeValueAsString(event))
            .messageAttributes(attributes)
            .build()
        val publishResponse: PublishResponse = snsClient.publish(publishRequest)

        metricRegister(
            AccountEvents(),
            "event_type" to event.eventType,
        )

        LOG.info(append("event", event).and(append("messageId", publishResponse.messageId())), "SNSEventPublisher")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SNSEventPublisher::class.java)
    }

    override fun publish(message: EventBus.Message): Result<EventBus.MessageId> {
        if (!eventBusEnabled) return Result.success(EventBus.MessageId("disabled"))

        val markers = markers("event" to message)
        return runCatching {
            val attributes = mapOf(
                eventTypeAttributeName to snsMessageAttributeValue(message.eventType.name),
            )

            val publishRequest = PublishRequest.builder().topicArn(eventBusTopicArn)
                .message(mapper.writeValueAsString(message))
                .messageAttributes(attributes)
                .build()

            EventBus.MessageId(snsClient.publish(publishRequest).messageId()).also {
                LOG.info(markers.andAppend("messageId", it.value), "SNSEventPublisher")
            }
        }.onFailure { e ->
            LOG.error(markers, "SNSEventPublisher#error", e)
        }
    }
}

private fun essentialTags(event: BillEvent): Map<String, String> {
    val commonTags = mapOf(
        "event_type" to event.eventType.name,
        "action_source" to event.actionSource.javaClass.simpleName,
    )

    val customTags = runCatching { // just to avoid any unexpected behaviour on this non essential scope
        when (event) {
            is PaymentFailed -> mapOf(
//                "error_description" to event.errorDescription.lowercase().take(35), // limit to 35 chars to avoid long descriptions and high cardinalities
                "error_source" to event.errorSource.name,
            )

            is BillPaymentScheduled -> mapOf(
                "internal_settlement" to event.infoData.toSchedulePaymentDetails().internalSettlement().toString(),
            )

            is BillPaymentScheduleCanceled -> mapOf("reason" to event.reason.name)

            else -> emptyMap()
        }
    }.getOrElse {
        LoggerFactory.getLogger(BillEvents::class.java).error("BillEvents#error", it)
        return commonTags
    }

    return commonTags + customTags
}

private fun specificEventTags(event: BillEvent, billType: BillType): Map<String, String?> {
    val commonTags = essentialTags(event).plus("bill_type" to billType.name)

    return runCatching { // just to avoid any unexpected behaviour on this non essential scope
        when (event) {
            is PaymentFailed -> mapOf("retryable" to event.retryable.toString())

            is BillPaymentScheduled -> mapOf(
                "os" to event.fingerprint?.os?.name,
                "payment_method" to event.infoData.toSchedulePaymentDetails().javaClass.simpleName,
            )

            is BillAdded -> mapOf("calculation_model" to event.amountCalculationModel?.name)

            is FichaCompensacaoAdded -> mapOf("type" to event.fichaCompensacaoType.name)

            is PaymentRefunded -> mapOf("reason" to event.reason.name, "gateway" to event.gateway.name)

            is RegisterUpdated -> mapOf("register_type" to event.updatedRegisterData.javaClass.simpleName)

            else -> emptyMap()
        }
    }.getOrElse {
        LoggerFactory.getLogger(BillEvents::class.java).error("BillEvents#error", it)
        return commonTags
    }.plus(commonTags).filter { it.value != null }
}

private fun sendEvents(event: BillEvent, billType: BillType) = CoroutineScope(Dispatchers.IO).launch {
    BillEvents.push(essentialTags(event))
    DynamicMetric.count(event.javaClass.simpleName, specificEventTags(event, billType))
}

private fun snsMessageAttributeValue(value: String) = MessageAttributeValue.builder()
    .dataType("String")
    .stringValue(value).build()