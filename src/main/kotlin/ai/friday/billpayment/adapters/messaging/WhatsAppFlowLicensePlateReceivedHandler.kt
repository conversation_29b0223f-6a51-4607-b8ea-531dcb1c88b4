package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.security.EncryptionService
import ai.friday.billpayment.app.vehicledebts.EnrollVehicleSource
import ai.friday.billpayment.app.vehicledebts.LicensePlate
import ai.friday.billpayment.app.vehicledebts.VehicleDebts
import ai.friday.billpayment.app.vehicledebts.VehicleDebtsService
import ai.friday.billpayment.app.whatsapp.WhatsAppFlowConfiguration
import ai.friday.billpayment.plus
import ai.friday.morning.json.parseObjectFrom
import io.micronaut.context.annotation.Property
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

const val eventType = "register_license_plate"

// FIXME não necessariamente necessita de vahicle debts
@VehicleDebts
open class WhatsAppFlowLicensePlateReceivedHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sns.arnPrefix") private val arnPrefix: String,
    @Property(name = "sns.topics.whatsapp-flow.name") private val topicName: String,
    private val encryptionService: EncryptionService,
    private val vehicleDebtsService: VehicleDebtsService,
    private val flowConfiguration: WhatsAppFlowConfiguration,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = "whatsapp-flow-$eventType",
    amazonSNS = amazonSNS,
    topicArn = arnPrefix + topicName,
    filter = EventTypeFilter(eventTypes = listOf(eventType)),
) {
    private val logger = LoggerFactory.getLogger(WhatsAppFlowLicensePlateReceivedHandler::class.java)

    override fun handleMessage(message: Message): SQSHandlerResponse {
        val markers = message.markers()
        val request = parseObjectFrom<LicensePlateReceivedRequest>(message.body())

        val accountId = encryptionService.decrypt(request.id, flowConfiguration.salt)

        markers.plus("accountId" to accountId)

        runCatching {
            vehicleDebtsService.enrollByVehicle(
                accountId = AccountId(accountId),
                licensePlate = LicensePlate(request.licensePlate),
                sync = true,
                description = null,
                source = EnrollVehicleSource.FLOW,
            )
        }.getOrElse { ex ->
            markers.plus("licensePlate" to request.licensePlate)
            logger.error(markers, "WhatsAppFlowLicensePlateReceivedHandler", ex)
            return SQSHandlerResponse(false)
        }

        logger.info(markers, "WhatsAppFlowLicensePlateReceivedHandler")

        return SQSHandlerResponse(true)
    }
}

internal data class LicensePlateReceivedRequest(val id: String, val licensePlate: String)