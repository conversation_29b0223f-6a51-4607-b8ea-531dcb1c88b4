package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.NotificationAdapter
import io.micronaut.context.annotation.Property
import jakarta.inject.Singleton
import kotlin.contracts.ExperimentalContracts
import kotlin.contracts.contract
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSBillPaymentScheduledHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
    private val systemActivityService: SystemActivityService,
    private val notificationAdapter: NotificationAdapter,
    private val accountService: AccountService,
) : AbstractSQSHandler(
    amazonSQS = amazonSQS,
    configuration = configuration,
    queueName = configuration.billPaymentScheduledQueueName,
    amazonSNS = amazonSNS,
    topicArn = topicArn,
    filter = EventTypeFilter(eventTypes = listOf(BillEventType.PAYMENT_SCHEDULED)),
) {
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val event = parseEvent(m)

        val markers = append("queue_message", m)

        val actionSource = event.actionSource
        if (isUserScheduled(actionSource)) {
            markers.andAppend("accountId", actionSource.accountId)

            if (!systemActivityService.hasScheduledBill(actionSource.accountId)) {
                val account = accountService.findAccountById(actionSource.accountId)
                notificationAdapter.notifyFirstBillScheduled(account)

                systemActivityService.setHasScheduledBill(actionSource.accountId)
                markers.andAppend("notificationSent", true)
            }
        }

        logger.info(markers, "BillPaymentScheduledHandler")

        return SQSHandlerResponse(shouldDeleteMessage = true)
    }

    @OptIn(ExperimentalContracts::class)
    private fun isUserScheduled(actionSource: ActionSource): Boolean {
        contract {
            returns(true) implies (actionSource is ActionSource.Api)
        }
        return actionSource is ActionSource.Api
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        logger.error(append("queue_message", m), "BillPaymentScheduledHandler", e)
        return SQSHandlerResponse(shouldDeleteMessage = false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSBillPaymentScheduledHandler::class.java)
    }
}