package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.account.SystemActivityService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillEventType
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.journey.UserJourneyEvent
import ai.friday.billpayment.app.journey.UserJourneyService
import ai.friday.billpayment.app.wallet.WalletService
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSBillPaidActivityHandler(
    amazonSQS: SqsClient,
    amazonSNS: SnsClient,
    private val systemActivityService: SystemActivityService,
    private val walletService: WalletService,
    private val billRepository: BillRepository,
    private val userJourneyService: UserJourneyService,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = billEventTopicArnPropertyName) private val topicArn: String,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    configuration.billPaidActivityQueueName,
    amazonSNS,
    topicArn,
    filter =
    EventTypeFilter(
        eventTypes =
        listOf(
            BillEventType.PAID,
        ),
    ),
) {
    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val billEvent = parseEvent(m)
        if (billEvent !is BillPaid || billEvent.actionSource is ActionSource.OpenFinance) {
            return SQSHandlerResponse(true)
        }

        val wallet = walletService.findWallet(billEvent.walletId)
        systemActivityService.setBillPaidOnOwnWallet(wallet.founder.accountId)

        val bill = billRepository.findBill(billId = billEvent.billId, walletId = billEvent.walletId)

        if (bill.source is ActionSource.DDA) {
            systemActivityService.setDDABillPaid(bill.source.accountId)
        }

        when (bill.billType) {
            BillType.CONCESSIONARIA -> UserJourneyEvent.CollectBillPaid
            BillType.FICHA_COMPENSACAO -> UserJourneyEvent.RegularBillPaid
            BillType.INVOICE -> UserJourneyEvent.InvoiceBillPaid
            BillType.PIX -> UserJourneyEvent.PixBillPaid
            BillType.INVESTMENT -> UserJourneyEvent.InvestmentRequested
            BillType.OTHERS -> null
        }?.let {
            userJourneyService.trackEventAsync(wallet.founder.accountId, it)
            userJourneyService.trackEventAsync(wallet.founder.accountId, UserJourneyEvent.BillPaid)
        }

        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("eventBody", m.body()).andAppend("exceptionMessage", e.message)
        LOG.error(markers, "SQSBillPaidActivityHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSBillPaidActivityHandler::class.java)
    }
}