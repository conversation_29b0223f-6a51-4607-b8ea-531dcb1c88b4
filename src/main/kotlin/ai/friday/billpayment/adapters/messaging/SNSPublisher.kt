package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.and
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.e
import ai.friday.billpayment.elapsed
import ai.friday.billpayment.i
import ai.friday.billpayment.log
import ai.friday.billpayment.measure
import ai.friday.billpayment.withErrorResult
import ai.friday.billpayment.withSuccessResult
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient

interface SNSPublisher<T> {
    fun publish(arn: String, body: T): Result<Unit>
}

@FridayMePoupe
class DefaultSNSPublisher<T>(private val cli: SnsClient) : SNSPublisher<T> {
    private val logger = LoggerFactory.getLogger(DefaultSNSPublisher::class.java)

    override fun publish(arn: String, body: T): Result<Unit> {
        val markers = log("topic_arn" to arn, "message_body" to body)

        val (result, elapsed) = measure {
            runCatching { cli.publish { it.topicArn(arn).message(getObjectMapper().writeValueAsString(body)) } }
        }

        markers.elapsed(elapsed)

        val response = result.getOrElse {
            logger.e(markers.withErrorResult(it), ex = it)
            return Result.failure(it)
        }

        logger.i(markers.withSuccessResult().and("message_id" to response.messageId()))

        return Result.success(Unit)
    }
}