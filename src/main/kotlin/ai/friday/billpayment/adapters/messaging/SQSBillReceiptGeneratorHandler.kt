package ai.friday.billpayment.adapters.messaging

import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

@Singleton
open class SQSBillReceiptGeneratorHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    private val notifyReceiptService: NotifyReceiptService,
) : AbstractSQSHandler(amazonSQS, configuration, configuration.billReceiptGeneratorQueueName) {
    @NewSpan
    override fun handleMessage(m: Message): SQSHandlerResponse {
        val billEvent = parseEvent(m)
        if (billEvent !is BillPaid || billEvent.actionSource is ActionSource.OpenFinance) {
            return SQSHandlerResponse(true)
        }

        if (!billEvent.syncReceipt) {
            notifyReceiptService.notify(billEvent)
        } else {
            logger.warn(append("billPaid", billEvent).andAppend("context", "ignorando envio de recibo pois foi feito sincronamente"), "SQSBillReceiptGeneratorHandler")
        }

        return SQSHandlerResponse(true)
    }

    override fun handleError(
        m: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("eventBody", m.body()).andAppend("exceptionMessage", e.message)
        logger.error(markers, "SQSBillReceiptGeneratorHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(SQSBillReceiptGeneratorHandler::class.java)
    }
}