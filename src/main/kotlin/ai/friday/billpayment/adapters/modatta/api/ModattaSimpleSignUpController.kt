package ai.friday.billpayment.adapters.modatta.api

import ai.friday.billpayment.IpBlockingFilter
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.api.StandardHttpResponses
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountNotFoundException
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.modatta.UserId
import ai.friday.billpayment.app.modatta.register.Enrollment
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUp
import ai.friday.billpayment.app.modatta.register.ModattaSimpleSignUpService
import ai.friday.billpayment.app.modatta.register.SimpleSignUpStatus
import ai.friday.billpayment.app.modatta.register.UserContract
import ai.friday.billpayment.app.modatta.register.UserData
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.morning.date.dateFormat
import arrow.core.Either
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Filter
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.server.util.HttpClientAddressResolver
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.security.rules.SecurityRule
import io.micronaut.validation.Validated
import java.time.Duration
import java.time.LocalDate
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Filter(value = ["/simpleSignUp", "/simpleSignUp/status", "/simpleSignUp/userData"])
@Requires(env = [MODATTA_ENV])
class SimpleSignUpIpBlockingFilter(addressResolver: HttpClientAddressResolver, configuration: BaasConfiguration) :
    IpBlockingFilter(addressResolver, configuration.allowedIps)

@Secured(Role.Code.MODATTA_CORP_CALLBACK)
@Controller("/simpleSignUp")
@Validated
@Requires(env = [MODATTA_ENV])
class ModattaSimpleSignUpController(
    private val simpleSignUpService: ModattaSimpleSignUpService,
    private val publicHttpLinkGenerator: PublicHttpLinkGenerator,
    @Property(name = "accountRegister.user_files.contractLinkDuration") private val s3LinkDuration: Duration,
    @Property(name = "urls.termsOfUse") private val termsOfUse: String,
) {

    @Get("/{userId}")
    fun get(@PathVariable userId: String, request: HttpRequest<*>): HttpResponse<*> {
        return simpleSignUpService.find(
            userId = UserId(userId),
        ).toHttpResponse(
            userId = UserId(userId),
            logMessage = "ModattaSimpleSignUpController#get",
        )
    }

    @Get("/{userId}/status")
    fun getStatus(@PathVariable userId: String, request: HttpRequest<*>): HttpResponse<*> {
        val markers = Markers.append("accountId", userId)

        val status = simpleSignUpService.getSimpleSignUpStatus(UserId(userId)).getOrElse {
            LOG.error(markers, "ModattaSimpleSignUpController#getStatus", it)
            return StandardHttpResponses.serverError(it.message.orEmpty())
        }

        markers.andAppend("status", status)

        LOG.info(markers, "ModattaSimpleSignUpController#getStatus")
        return HttpResponse.ok(ModattaSimpleSignUpStatusTO(status))
    }

    @Post("/userData")
    fun postUserData(@Body body: UserDataTO): HttpResponse<*> {
        val userId = UserId(body.userId!!)

        return simpleSignUpService.updateUserData(
            userId = userId,
            userData = body.toUserData(),
        ).toHttpResponse(
            userId = userId,
            logMessage = "ModattaSimpleSignUpController#postUserData",
        )
    }

    private fun Either<Exception, ModattaSimpleSignUp>.toHttpResponse(
        userId: UserId,
        logMessage: String,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", userId)

        return this.map {
            markers.andAppend("response", it)
            LOG.info(markers, logMessage)
            HttpResponse.ok(it.toModattaSimpleSignUpTO())
        }.getOrElse {
            if (it is AccountNotFoundException) {
                LOG.warn(markers, logMessage)
                HttpResponse.notFound(ResponseTO(code = "4004", message = "Account Register not found"))
            } else {
                LOG.error(markers, logMessage, it)
                HttpResponse.serverError(ResponseTO(code = "5000", message = it.message.orEmpty()))
            }
        }
    }

    private fun ModattaSimpleSignUp.toModattaSimpleSignUpTO() = ModattaSimpleSignUpTO(
        userData = userData?.toUserDataTO(),
        enrollment = enrollment?.toEnrollmentTO(),
        userContract = userContract?.toUserContractTO(),
        termsOfUse = termsOfUse,
        validationStatus = validationStatus.name,
    )

    private fun Enrollment.toEnrollmentTO() = EnrollmentTO(
        livenessId = livenessId.value,
        done = done,
    )

    private fun UserData.toUserDataTO() = UserDataTO(
        name = name,
        birthDate = birthDate.format(dateFormat),
        document = document.value,
        mobilePhone = mobilePhone.msisdn,
        email = email.value,
        userId = "",
    )

    private fun UserContract.toUserContractTO() = UserContractTO(
        hasAccepted = hasAccepted,
        contract = contract.let {
            publicHttpLinkGenerator.generate(
                storedObject = it,
                duration = s3LinkDuration,
                contentType = MediaType.APPLICATION_PDF,
            )
        },
    )

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaSimpleSignUpController::class.java)
    }
}

@Secured(SecurityRule.IS_AUTHENTICATED)
@Controller("/simpleSignUp/agreement")
@Requires(env = [MODATTA_ENV])
class ModattaSimpleSignUpAgreementController(
    private val simpleSignUpService: ModattaSimpleSignUpService,
    private val addressResolver: HttpClientAddressResolver,
) {

    @Post
    fun postAgreement(request: HttpRequest<*>, authentication: Authentication): HttpResponse<*> {
        val logMessage = "ModattaSimpleSignUpAgreementController#postAgreement"

        val userId = authentication.toUserId()
        val clientIp = addressResolver.resolve(request) ?: ""

        val markers = Markers.append("accountId", userId.value)

        simpleSignUpService.acceptAgreement(
            userId = userId,
            clientIP = clientIp,
        ).mapLeft {
            return if (it is AccountNotFoundException) {
                LOG.warn(markers, logMessage)
                HttpResponse.notFound(ResponseTO(code = "4004", message = "Account Register not found"))
            } else {
                LOG.error(markers, logMessage, it)
                HttpResponse.serverError(ResponseTO(code = "5000", message = it.message.orEmpty()))
            }
        }

        return HttpResponse.ok<Unit>()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaSimpleSignUpAgreementController::class.java)
    }
}

internal fun UserDataTO.toUserData() = UserData(
    name = name,
    birthDate = LocalDate.parse(birthDate, dateFormat),
    document = Document(document),
    mobilePhone = MobilePhone(msisdn = mobilePhone),
    email = EmailAddress(email = email),
)

data class ModattaSimpleSignUpTO(
    val userData: UserDataTO?,
    val enrollment: EnrollmentTO?,
    val userContract: UserContractTO?,
    val termsOfUse: String,
    val validationStatus: String,
)

data class EnrollmentTO(
    val livenessId: String,
    val done: Boolean,
)

data class UserDataTO(
    val name: String,
    val userId: String? = null,
    val birthDate: String,
    val document: String,
    val mobilePhone: String,
    val email: String,
)

data class UserContractTO(
    val hasAccepted: Boolean,
    val contract: String?,
)

data class ModattaSimpleSignUpStatusTO(
    val status: SimpleSignUpStatus,
)