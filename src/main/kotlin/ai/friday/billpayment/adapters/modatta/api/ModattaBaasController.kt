package ai.friday.billpayment.adapters.modatta.api

import ai.friday.billpayment.IpBlockingFilter
import ai.friday.billpayment.adapters.api.AmountBalanceTO
import ai.friday.billpayment.adapters.arbi.ArbiAdapter
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.BankAccount
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintService
import ai.friday.billpayment.app.integrations.ModattaBassConfiguration
import ai.friday.billpayment.app.integrations.PixCommand
import ai.friday.billpayment.app.integrations.PixPaymentService
import ai.friday.billpayment.app.payment.PixPaymentStatus
import ai.friday.billpayment.app.pix.PixKey
import io.micronaut.context.annotation.ConfigurationInject
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.context.annotation.Requires
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Filter
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.server.util.HttpClientAddressResolver
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@ConfigurationProperties("baas")
class BaasConfiguration @ConfigurationInject constructor(
    override var name: String,
    override var document: String,
    override var bankNo: Long,
    override var routingNo: Long,
    override var allowedIps: List<String>,
) : ModattaBassConfiguration

@Filter(value = ["/baas/**"])
@Requires(env = [MODATTA_ENV])
class BaasIpBlockingFilter(addressResolver: HttpClientAddressResolver, configuration: BaasConfiguration) :
    IpBlockingFilter(addressResolver, configuration.allowedIps)

@Controller("/baas")
@Requires(env = [MODATTA_ENV])
@Secured(Role.Code.MODATTA_CORP_CALLBACK)
class ModattaBaasController(
    private val arbiAdapter: ArbiAdapter,
    private val arbiPixPaymentAdapter: PixPaymentService,
    private val configuration: BaasConfiguration,
    private val deviceFingerprintService: DeviceFingerprintService,
) {

    @Post("/transfer")
    fun transfer(@Body body: ModattaBankTransferTO): HttpResponse<*> {
        return try {
            val bankTransfer = arbiAdapter.transfer(
                originAccountNo = body.originAccountNo,
                targetAccountNo = body.destinationAccountNo,
                amount = body.amount,
                operationId = BankOperationId(value = body.operationId),
            )
            HttpResponse.ok(bankTransfer)
        } catch (e: Exception) {
            LOG.error(
                Markers.append("body", body),
                "ModattaBaasController#transfer",
                e,
            )
            return HttpResponse.serverError<Unit>()
        }
    }

    @Get("/{accountNo}/balance")
    fun getBalance(@PathVariable accountNo: String): HttpResponse<*> {
        return try {
            val balance = arbiAdapter.getBalance(accountNo)
            HttpResponse.ok(AmountBalanceTO(amount = balance))
        } catch (e: Exception) {
            LOG.error(
                Markers.append("accountNo", accountNo),
                "ModattaBaasController#getBalance",
                e,
            )
            return HttpResponse.serverError<Unit>()
        }
    }

    @Post("/pix")
    fun createPix(@Body body: ModattaCreatePixTO): HttpResponse<*> {
        val logName = "ModattaBaasController#createPix"
        val markers = Markers.append("body", body)

        return try {
            val command = PixCommand(
                payerName = configuration.name,
                recipientName = body.recipientName,
                recipientDocument = body.recipientDocument,
                recipientBankAccount = body.recipientBankAccount,
                recipientPixKey = body.recipientPixKey,
                originBankAccount = InternalBankAccount(
                    accountType = AccountType.CHECKING,
                    bankNo = configuration.bankNo,
                    routingNo = configuration.routingNo,
                    accountNo = body.originBankAccountNo,
                    accountDv = body.originBankAccountDv,
                    document = configuration.document,
                    bankAccountMode = BankAccountMode.PHYSICAL,
                ),
                description = body.description,
                amount = body.amount,
                pixQrCodeId = null,
            )
            val deviceId = deviceFingerprintService.getInternalDevicesAccount()?.deviceIds?.get(AccountNumber("${body.originBankAccountNo}${body.originBankAccountDv}"))
            val result = arbiPixPaymentAdapter.initPayment(command, BankOperationId(body.operationId), deviceId = deviceId)
            LOG.info(markers.andAppend("result", result), logName)
            HttpResponse.created(
                ModattaPixResponseTO(
                    status = result.status.name,
                    operationId = body.operationId,
                    endToEnd = result.endToEnd,
                    error = result.error?.code,
                ),
            )
        } catch (e: Exception) {
            LOG.error(
                markers,
                logName,
                e,
            )
            HttpResponse.serverError<Unit>()
        }
    }

    @Get("/pix/{operationId}")
    fun checkStatusPix(@PathVariable operationId: String): HttpResponse<*> {
        val logName = "ModattaBaasController#checkStatusPix"
        val markers = Markers.append("operationId", operationId)
        val result = arbiPixPaymentAdapter.checkPaymentStatus(
            bankAccount = null,
            bankOperationId = BankOperationId(operationId),
        )
        if (result.status == PixPaymentStatus.UNKNOWN) {
            LOG.error(markers.andAppend("result", result), logName)
            return HttpResponse.serverError<Unit>()
        }
        LOG.info(markers.andAppend("result", result), logName)
        return HttpResponse.ok(result.status.name)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(ModattaBaasController::class.java)
    }
}

data class ModattaCreatePixTO(
    val operationId: String,
    val amount: Long,
    val description: String,
    val recipientPixKey: PixKey?,
    val recipientName: String,
    val recipientDocument: String,
    val recipientBankAccount: BankAccount?,
    val originBankAccountNo: Long,
    val originBankAccountDv: String,
)

data class ModattaPixResponseTO(
    val status: String,
    val operationId: String,
    val endToEnd: String,
    val error: String? = null,
)

data class ModattaBankTransferTO(
    val operationId: String,
    val originAccountNo: String,
    val destinationAccountNo: String,
    val amount: Long,
)