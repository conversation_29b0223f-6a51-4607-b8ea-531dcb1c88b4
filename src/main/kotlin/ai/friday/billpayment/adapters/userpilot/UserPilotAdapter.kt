package ai.friday.billpayment.adapters.userpilot

import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.UserAccountType
import ai.friday.billpayment.app.account.UserJourneyRegisterException
import ai.friday.billpayment.app.account.UserJourneyTrackEventException
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.UserJourneyAdapter
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.fasterxml.jackson.annotation.JsonProperty
import io.micronaut.context.annotation.ConfigurationProperties
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.time.ZonedDateTime
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@FridayMePoupe
class UserPilotAdapter(
    @param:Client(value = "\${integrations.userpilot.host}") private val httpClient: RxHttpClient,
    private val configuration: UserPilotConfiguration,
) : UserJourneyAdapter {
    override fun register(accountId: AccountId, name: String, email: EmailAddress, accountType: UserAccountType, createdAt: ZonedDateTime) {
        val registerRequestTO = UserPilotRegisterRequestTO(
            userId = accountId.value,
            metadata = UserPilotRegisterMetadata(
                name = name,
                email = email.toString(),
                accountType = accountType.name,
                createdAt = createdAt.toEpochSecond().toString(),
            ),
        )

        val markers = Markers.append("request", registerRequestTO)

        val request = HttpRequest.POST(configuration.identifyPath, registerRequestTO)
            .header("X-API-Version", configuration.apiVersion)
            .header("Authorization", "Token ${configuration.token}")
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.exchange(
            request,
            Argument.of(Unit::class.java),
        )

        try {
            call.firstOrError().blockingGet().body()
            LOG.info(markers, "UserPilotRegister")
        } catch (e: HttpClientResponseException) {
            LOG.info(markers.andAppend("response", e.response), "UserPilotRegister")
            throw UserJourneyRegisterException(e)
        }
    }

    override fun trackEvent(accountId: AccountId, eventName: String) {
        val trackEventRequestTO = UserPilotTrackEventRequestTO(
            userId = accountId.value,
            eventName = eventName,
            metadata = mapOf(
                "created_at" to getZonedDateTime().toEpochSecond().toString(),
            ),
        )

        val markers = Markers.append("request", trackEventRequestTO)

        val request = HttpRequest.POST(configuration.trackEventPath, trackEventRequestTO)
            .header("X-API-Version", configuration.apiVersion)
            .header("Authorization", "Token ${configuration.token}")
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.exchange(
            request,
            Argument.of(Unit::class.java),
        )

        try {
            call.firstOrError().blockingGet().body()
            LOG.info(markers, "UserPilotTrackEvent")
        } catch (e: HttpClientResponseException) {
            LOG.info(markers.andAppend("response", e.response), "UserPilotTrackEvent")
            throw UserJourneyTrackEventException(e)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(UserPilotAdapter::class.java)
    }
}

@ConfigurationProperties("integrations.userpilot")
class UserPilotConfiguration {
    lateinit var host: String
    lateinit var token: String
    lateinit var apiVersion: String
    lateinit var identifyPath: String
    lateinit var trackEventPath: String
}

data class UserPilotRegisterRequestTO(
    @JsonProperty("user_id")
    val userId: String,
    val metadata: UserPilotRegisterMetadata,
)

data class UserPilotRegisterMetadata(
    val name: String,
    val email: String,
    @JsonProperty("account_type")
    val accountType: String,
    @JsonProperty("created_at")
    val createdAt: String,
)

data class UserPilotTrackEventRequestTO(
    @JsonProperty("user_id")
    val userId: String,

    @JsonProperty("event_name")
    val eventName: String,

    val metadata: Map<String, String> = mapOf(),
)