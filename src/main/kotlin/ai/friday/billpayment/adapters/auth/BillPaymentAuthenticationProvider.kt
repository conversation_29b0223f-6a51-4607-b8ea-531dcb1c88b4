package ai.friday.billpayment.adapters.auth

import ai.friday.billpayment.app.bill.andAppend
import io.micronaut.http.HttpRequest
import io.micronaut.security.authentication.AuthenticationFailed
import io.micronaut.security.authentication.AuthenticationProvider
import io.micronaut.security.authentication.AuthenticationRequest
import io.micronaut.security.authentication.AuthenticationResponse
import io.reactivex.Flowable
import net.logstash.logback.marker.Markers
import org.reactivestreams.Publisher
import org.slf4j.LoggerFactory

abstract class FridayFixedIdentityAuthenticationProvider(
    private val fixedIdentity: String,
    private val fixedSecret: String,
    private val fixedRole: String,
) : BillPaymentAuthenticationProvider() {

    final override fun billPaymentAuthenticate(
        httpRequest: HttpRequest<*>?,
        authenticationRequest: AuthenticationRequest<*, *>,
    ): Pair<String, AuthenticationResponse> {
        val authenticatorName = this::class.java.simpleName
        return if (authenticationRequest.identity == fixedIdentity && authenticationRequest.secret == fixedSecret) {
            Pair(
                authenticatorName,
                AuthenticationResponse.success(
                    authenticationRequest.identity as String,
                    listOf(fixedRole),
                ),
            )
        } else {
            Pair(authenticatorName, AuthenticationFailed())
        }
    }
}

abstract class BillPaymentAuthenticationProvider : AuthenticationProvider<HttpRequest<*>> {
    companion object {
        private val logger = LoggerFactory.getLogger(BillPaymentAuthenticationProvider::class.java)
    }

    abstract fun billPaymentAuthenticate(
        httpRequest: HttpRequest<*>?,
        authenticationRequest: AuthenticationRequest<*, *>,
    ): Pair<String, AuthenticationResponse>

    final override fun authenticate(
        httpRequest: HttpRequest<*>?,
        authenticationRequest: AuthenticationRequest<*, *>,
    ): Publisher<AuthenticationResponse> {
        val (authName, authResponse) = billPaymentAuthenticate(httpRequest, authenticationRequest)

        if (authResponse.isAuthenticated) {
            logSuccess(authName)
            return Flowable.just(authResponse)
        }
        return Flowable.just(authResponse)
    }

    private fun logSuccess(authName: String) {
        logger.info(Markers.append("authenticate", "success").andAppend("authenticatorName", authName), "BillPaymentAuthenticationProvider")
    }
}