package ai.friday.billpayment.adapters.s3

import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.ME_POUPE_ENV
import ai.friday.billpayment.app.MODATTA_ENV
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.billpayment.app.utils.PublicHttpLinkGeneratorRetryConfiguration
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import com.amazonaws.HttpMethod
import com.amazonaws.auth.DefaultAWSCredentialsProviderChain
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import com.amazonaws.services.dynamodbv2.datamodeling.DynamoDBMapper
import com.amazonaws.services.s3.model.GeneratePresignedUrlRequest
import com.amazonaws.services.s3.model.ResponseHeaderOverrides
import io.micronaut.context.annotation.Requires
import jakarta.inject.Singleton
import java.time.Duration
import java.util.Date
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@Requires(env = [FRIDAY_ENV, MODATTA_ENV, ME_POUPE_ENV])
open class S3LinkGenerator(private val amazonDynamoDB: AmazonDynamoDB) : PublicHttpLinkGenerator {

    private val credentialsProvider: DefaultAWSCredentialsProviderChain =
        DefaultAWSCredentialsProviderChain.getInstance()
    private val mapper: DynamoDBMapper = DynamoDBMapper(amazonDynamoDB, credentialsProvider)

    override fun generate(storedObject: StoredObject, duration: Duration, contentType: String?, retryConfiguration: PublicHttpLinkGeneratorRetryConfiguration?): String? {
        val logName = "S3LinkGenerator#generate"
        if (retryConfiguration == null) {
            return doGenerate(storedObject, duration, contentType)
        }

        var attempt = 1
        var delayMillis = retryConfiguration.initialDelay.toMillis()

        while (attempt <= retryConfiguration.maxRetries) {
            val markers = append("attempt", attempt)
                .andAppend("retryConfiguration", retryConfiguration)
                .andAppend("storedObject", storedObject)
            try {
                val result = doGenerate(storedObject, duration, contentType)
                if (result != null) {
                    return result
                }
                logger.warn(markers, logName)
            } catch (e: Exception) {
                if (attempt == retryConfiguration.maxRetries - 1) {
                    logger.error(markers, logName, e)
                    throw e
                }
                logger.warn(markers, logName, e)
            }

            try {
                Thread.sleep(delayMillis)
            } catch (interrupted: InterruptedException) {
                Thread.currentThread().interrupt()
                logger.error(markers, logName, interrupted)
                throw interrupted
            }

            delayMillis = (delayMillis * 2).coerceAtMost(retryConfiguration.maxDelay.toMillis())
            attempt++
        }
        return null
    }

    private fun doGenerate(storedObject: StoredObject, duration: Duration, contentType: String?): String? {
        try {
            val s3Client = mapper.s3ClientCache.getClient(storedObject.region)

            val expiration = getZonedDateTime().plus(duration)

            val expirationDate = Date.from(expiration.toInstant())

            if (s3Client.doesObjectExist(storedObject.bucket, storedObject.key)) {
                val generatePresignedUrlRequest = GeneratePresignedUrlRequest(storedObject.bucket, storedObject.key)
                    .withMethod(HttpMethod.GET)
                    .withExpiration(expirationDate)
                contentType?.let {
                    generatePresignedUrlRequest.withResponseHeaders(ResponseHeaderOverrides().withContentType(it))
                }
                return s3Client.generatePresignedUrl(generatePresignedUrlRequest).toString()
            }
        } catch (e: Exception) {
            // FIXME se RegisterController precisa tratar a exceção  e assim nao precisaria nem do log de do try/catch
            //  outra opção seria retornar um either
            logger.error("S3LinkGenerator", e)
            throw e
        }
        return null
    }

    companion object {
        private val logger = LoggerFactory.getLogger(S3LinkGenerator::class.java)
    }
}