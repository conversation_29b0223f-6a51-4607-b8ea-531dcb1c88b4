package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.bill.andAppend
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpVersion
import io.micronaut.http.MediaType
import io.micronaut.http.client.HttpClientConfiguration
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.http.ssl.ClientAuthentication
import io.micronaut.http.ssl.ClientSslConfiguration
import io.micronaut.runtime.ApplicationConfiguration
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.Duration
import java.time.LocalDateTime
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Singleton
@Requires(beans = [ArbiAdapter::class])
class NewArbiHttpConfiguration(
    applicationConfiguration: ApplicationConfiguration,
    val configuration: HttpClientConfiguration,
) : HttpClientConfiguration(applicationConfiguration) {
    init {
        setReadTimeout(Duration.ofMinutes(1))
        sslConfiguration = ClientSslConfiguration().apply {
            isEnabled = true
            setClientAuthentication(ClientAuthentication.WANT)
            isInsecureTrustAllCertificates = true
        }
        httpVersion = HttpVersion.HTTP_1_1
    }

    override fun getConnectionPoolConfiguration(): ConnectionPoolConfiguration =
        configuration.connectionPoolConfiguration
}

@Singleton
@Requires(beans = [ArbiAdapter::class])
open class NewArbiAuthenticationManager(
    @param:Client(
        id = "arbi",
    )
    private val httpClient: RxHttpClient,
    private val arbiConfiguration: ArbiConfiguration,
) {

    private var token: String? = null
    private var expiresIn: LocalDateTime? = null
    private var refreshToken: String? = null
    private val expireOffset = 1L

    open fun getToken(): String {
        return expiresIn?.let {
            return if (it.minusMinutes(expireOffset).isAfter(getZonedDateTime().toLocalDateTime())) {
                token?.let { token -> return token } ?: getAccessToken()
            } else {
                refreshToken?.let { return refresh() } ?: getAccessToken()
            }
        } ?: getAccessToken()
    }

    private fun refresh(): String {
        val httpRequest = HttpRequest.POST(
            arbiConfiguration.accessTokenPath,
            mapOf("grant_type" to "refresh_token", "refresh_token" to refreshToken),
        )
            .basicAuth(arbiConfiguration.clientId, arbiConfiguration.clientSecret)
            .contentType(MediaType.APPLICATION_FORM_URLENCODED_TYPE)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(AccessTokenTO::class.java),
        )
        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(
                Markers.append("response", response)
                    .and<LogstashMarker>(Markers.append("request", httpRequest.toString())),
                "AuthenticationRefresh",
            )
            expiresIn = getZonedDateTime().toLocalDateTime().plusSeconds(response.expiresIn)
            refreshToken = response.refreshToken
            token = response.accessToken
            response.accessToken
        } catch (e: HttpClientResponseException) {
            logger.warn(
                Markers.append("error", "Error on Arbi refresh token API")
                    .and<LogstashMarker>(Markers.append("status", e.status)),
                "ArbiRefresh",
                e,
            )
            return getAccessToken()
        }
    }

    private fun getAccessToken(): String {
        val grantCode = getGrantCode()
        val httpRequest = HttpRequest.POST(
            arbiConfiguration.accessTokenPath,
            mapOf("grant_type" to "authorization_code", "code" to grantCode),
        )
            .basicAuth(arbiConfiguration.clientId, arbiConfiguration.clientSecret)
            .contentType(MediaType.APPLICATION_FORM_URLENCODED_TYPE)
            .accept(MediaType.APPLICATION_JSON_TYPE)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(AccessTokenTO::class.java),
        )
        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(
                Markers.append("response", response)
                    .and<LogstashMarker>(Markers.append("request", httpRequest.toString())),
                "AuthenticationManagerAccessToken",
            )
            expiresIn = getZonedDateTime().toLocalDateTime().plusSeconds(response.expiresIn)
            refreshToken = response.refreshToken
            token = response.accessToken
            response.accessToken
        } catch (e: HttpClientResponseException) {
            logger.error(
                Markers.append("error", "Error on Arbi access token API")
                    .and<LogstashMarker>(Markers.append("status", e.status)),
                "ArbiAccessToken",
                e,
            )
            cleanTokens()
            throw ArbiLoginException()
        }
    }

    private fun getGrantCode(): String {
        val markers = Markers.append("path", arbiConfiguration.grantCodePath)
        val httpRequest: HttpRequest<GrantCodeTO> = HttpRequest.POST(
            arbiConfiguration.grantCodePath,
            GrantCodeTO(arbiConfiguration.clientId, "http://localhost/"),
        )
            .contentType(MediaType.APPLICATION_JSON_TYPE)
            .accept(MediaType.APPLICATION_JSON_TYPE)

        val call = httpClient.retrieve(
            httpRequest,
            Argument.of(GrantCodeResponseTO::class.java),
        )
        markers.andAppend("request", httpRequest.toString())
        return try {
            val response = call.firstOrError().blockingGet()
            logger.info(
                markers.andAppend("response", response),
                "AuthenticationManagerGrantCode",
            )
            if (response.redirectUri.startsWith("http://localhost/?code=")) {
                response.redirectUri.drop("http://localhost/?code=".length)
            } else {
                throw ArbiLoginException()
            }
        } catch (e: HttpClientResponseException) {
            logger.error(
                markers.andAppend("error", "Error on Arbi grant code API")
                    .andAppend("response", e.response.getBody(String::class.java).orElse(""))
                    .andAppend("status", e.status),
                "ArbiGrantCode",
                e,
            )
            throw ArbiLoginException()
        }
    }

    fun cleanTokens() {
        token = null
        expiresIn = null
        refreshToken = null
    }

    companion object {
        private val logger = LoggerFactory.getLogger(NewArbiAuthenticationManager::class.java)
    }
}