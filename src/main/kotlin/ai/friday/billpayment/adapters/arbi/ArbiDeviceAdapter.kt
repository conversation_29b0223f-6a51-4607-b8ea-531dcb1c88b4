package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.fingerprint.CreateDeviceRequest
import ai.friday.billpayment.app.fingerprint.DeviceFingerprintAdapter
import ai.friday.billpayment.app.fingerprint.DeviceId
import ai.friday.billpayment.app.fingerprint.DevicePerson
import ai.friday.billpayment.app.fingerprint.DevicePspInformation
import ai.friday.billpayment.app.fingerprint.DeviceScreenResolution
import ai.friday.billpayment.app.fingerprint.DeviceType
import ai.friday.billpayment.app.isValidCpf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientException
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@FridayMePoupe
open class ArbiDeviceAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: ArbiConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
) : DeviceFingerprintAdapter {

    override fun createDevice(request: CreateDeviceRequest): DeviceId {
        val logName = "ArbiCreateDevice"
        val token = authenticationManager.getToken()
        val idRequisicao = UUID.randomUUID().toString()
        val marker = append("requestBody", request)
        val mobileDeviceDetails = request.deviceDetails
        val httpRequest =
            HttpRequest
                .POST(
                    configuration.devicesPath,
                    CreateDeviceRequestTO(
                        account = AccountTO(
                            number = request.account.accountNumber.fullAccountNumber,
                            pspInformation = PspInformationTO(
                                code = request.account.psp.code,
                                agencyCode = request.account.psp.agencyCode,
                            ),
                        ),
                        alias = request.deviceDetails.alias,
                        person = PersonTO(
                            phoneNumber = request.person.phoneNumber.msisdn,
                            documentNumber = request.person.document.value,
                            fullName = request.person.fullName,
                            documentType = request.person.document.type.value,
                            type = if (request.person.document.isValidCpf()) "NATURAL_PERSON" else "LEGAL_PERSON",
                            email = request.person.email.value,
                        ),
                        type = request.deviceDetails.type.name,
                        uuid = request.deviceDetails.uuid.toString(),
                        screenResolution = request.deviceDetails.screenResolution.toScreenResolutionString(),
                        fingerPrint = request.deviceDetails.fingerprint,
                        storageCapacity = mobileDeviceDetails.storageCapacity.toString(),
                        densityDPI = mobileDeviceDetails.dpi.toString(),
                        manufacturer = mobileDeviceDetails.manufacturer,
                        model = mobileDeviceDetails.model,
                        androidId = mobileDeviceDetails.takeIf { it.type == DeviceType.ANDROID }?.osId,
                        idfv = mobileDeviceDetails.takeIf { it.type == DeviceType.IOS }?.osId,
                        isRooted = mobileDeviceDetails.rooted,

                    ),
                )
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .header("x-client-request-id", idRequisicao)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)
        marker.andAppend("x-client-request-id", idRequisicao)
        val call =
            httpClient.retrieve(
                httpRequest,
                Argument.of(DevicesResponseTO::class.java),
                Argument.STRING,
            )
        try {
            val response = call.firstOrError().blockingGet()

            logger.info(marker, logName)
            return DeviceId(response.id)
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)

            val responseBody = e.response.getBody(String::class.java).get()

            val invalidAccount =
                e.status == HttpStatus.UNPROCESSABLE_ENTITY &&
                    responseBody
                        .contains("Conta origem não cadastrado e/ou invalido.")

            val accountNotAllowed =
                e.status == HttpStatus.FORBIDDEN &&
                    responseBody
                        .contains("Usuario/Modulo/transacao/Conta sem permissão.")

            val response = e.response.getBody(String::class.java).get()
            if (invalidAccount) {
                logger.warn(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiInvalidAccountException()
            } else if (accountNotAllowed) {
                logger.warn(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiAccountMissingPermissionsException()
            } else {
                logger.error(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiAdapterException()
            }
        } catch (e: ArbiInvalidBalanceException) {
            throw e
        } catch (e: HttpClientResponseException) {
            logger.error(
                marker
                    .andAppend("status", e.status)
                    .andAppend("response", e.response.getBody(String::class.java)),
                logName,
            )
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(marker.andAppend("ACTION", "VERIFY"), logName, e)
            throw ArbiAdapterException()
        }
    }

    override fun removeDeviceId(deviceId: DeviceId): Boolean {
        val logName = "ArbiRemoveDeviceId"
        val token = authenticationManager.getToken()
        val idRequisicao = UUID.randomUUID().toString()
        val httpRequest =
            HttpRequest
                .DELETE<Unit>("${configuration.devicesPath}/${deviceId.value}")
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .header("x-client-request-id", idRequisicao)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)
        val marker = append("x-client-request-id", idRequisicao)

        val call =
            httpClient.exchange(
                httpRequest,
                Argument.of(Unit::class.java),
                Argument.STRING,
            )
        try {
            val response = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("status", response.status), logName)
            return true
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)
            logger.error(
                marker.andAppend("status", e.status)
                    .andAppend("response", e.response.getBody(String::class.java).getOrNull()),
                logName,
            )
            return false
        } catch (e: Exception) {
            logger.error(marker, logName, e)
            return false
        }
    }

    fun getDevice(deviceId: DeviceId): String {
        val logName = "ArbiIsDeviceEnabled"
        val token = authenticationManager.getToken()
        val idRequisicao = UUID.randomUUID().toString()
        val httpRequest =
            HttpRequest
                .GET<Unit>("${configuration.devicesPath}/${deviceId.value}")
                .header("client_id", configuration.clientId)
                .header("access_token", token)
                .header("x-client-request-id", idRequisicao)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON_TYPE)
        val marker = append("x-client-request-id", idRequisicao)
        val call =
            httpClient.retrieve(
                httpRequest,
                Argument.of(RegisteredDeviceTO::class.java),
                Argument.STRING,
            )

        try {
            val response = call.firstOrError().blockingGet()

            logger.info(marker.andAppend("response", response), logName)
            TODO()
        } catch (e: HttpClientResponseException) {
            checkUnauthorized(e)

            val responseBody = e.response.getBody(String::class.java).get()

            val invalidAccount =
                e.status == HttpStatus.UNPROCESSABLE_ENTITY &&
                    responseBody
                        .contains("Conta origem não cadastrado e/ou invalido.")

            val accountNotAllowed =
                e.status == HttpStatus.FORBIDDEN &&
                    responseBody
                        .contains("Usuario/Modulo/transacao/Conta sem permissão.")

            val response = e.response.getBody(String::class.java).get()
            if (invalidAccount) {
                logger.warn(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiInvalidAccountException()
            } else if (accountNotAllowed) {
                logger.warn(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiAccountMissingPermissionsException()
            } else {
                logger.error(
                    marker.andAppend("response", response),
                    logName,
                    e,
                )
                throw ArbiAdapterException()
            }
        } catch (e: ArbiInvalidBalanceException) {
            throw e
        } catch (e: HttpClientException) {
            logger.error(marker, logName, e)
            throw ArbiAdapterException()
        } catch (e: Exception) {
            logger.error(marker.andAppend("ACTION", "VERIFY"), logName, e)
            throw ArbiAdapterException()
        }
    }

    private fun checkUnauthorized(e: HttpClientResponseException) {
        if (e.status == HttpStatus.UNAUTHORIZED) {
            logger.warn(
                append("error_message", "Token is expired")
                    .andAppend("response", e.response.getBody(String::class.java)),
                "ArbiDeviceAdapter",
            )
            authenticationManager.cleanTokens()
            throw ArbiLoginException()
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiDeviceAdapter::class.java)
    }
}

data class CreateDeviceRequestTO(
    val account: AccountTO,
    val alias: String,
    val person: PersonTO,
    val type: String,
    val uuid: String,
    val screenResolution: String,
    val fingerPrint: String?,
    val storageCapacity: String?,
    val densityDPI: String?,
    val manufacturer: String?,
    val model: String?,
    val androidId: String?,
    val idfv: String?,
    val isRooted: Boolean?,
)

fun PersonTO.toDomain() =
    DevicePerson(
        phoneNumber = MobilePhone(phoneNumber),
        document = Document(documentNumber),
        fullName = fullName,
        email = EmailAddress(email),
    )

fun PspInformationTO.toDomain() =
    DevicePspInformation(
        code = code,
        agencyCode = agencyCode,
    )

fun ScreenResolutionTO.toDomain() =
    DeviceScreenResolution(
        width = width,
        height = height,
    )

private fun DeviceScreenResolution.toScreenResolutionString() = "${width}x$height"

data class RegisteredDeviceTO(
    val account: AccountTO,
    val alias: String,
    val person: PersonTO,
    val type: DeviceType,
    val status: String,
    val creationDate: String,
)

data class PersonTO(
    val phoneNumber: String,
    val documentType: String,
    val documentNumber: String,
    val fullName: String,
    val type: String,
    val email: String,
)

data class AccountTO(
    val number: String,
    val pspInformation: PspInformationTO,
)

data class PspInformationTO(
    val code: String,
    val agencyCode: String,
)

data class ScreenResolutionTO(
    val width: Int,
    val height: Int,
)

data class DevicesResponseTO(
    val id: String,
)