package ai.friday.billpayment.adapters.arbi

import ai.friday.billpayment.adapters.parsers.parseListFrom
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.banking.AccountNumber
import ai.friday.billpayment.app.bill.PixQrCodeData
import ai.friday.billpayment.app.bill.PixQrCodeType
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.PixQRCodeParserService
import ai.friday.billpayment.app.pix.PixKey
import ai.friday.billpayment.app.pix.PixKeyDetails
import ai.friday.billpayment.app.pix.PixKeyDetailsResult
import ai.friday.billpayment.app.pix.PixKeyError
import ai.friday.billpayment.app.pix.PixKeyHolder
import ai.friday.billpayment.app.pix.PixKeyOwner
import ai.friday.billpayment.app.pix.PixKeyType
import arrow.core.Either
import arrow.core.left
import arrow.core.right
import com.fasterxml.jackson.annotation.JsonIgnoreProperties
import com.fasterxml.jackson.annotation.JsonInclude
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.cache.annotation.CacheConfig
import io.micronaut.cache.annotation.Cacheable
import io.micronaut.context.annotation.Requires
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpMethod
import io.micronaut.http.HttpRequest
import io.micronaut.http.MediaType
import io.micronaut.http.client.annotation.Client
import io.micronaut.http.client.exceptions.HttpClientResponseException
import io.micronaut.rxjava2.http.client.RxHttpClient
import jakarta.inject.Singleton
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Singleton
@CacheConfig("arbiPix")
@Requires(beans = [ArbiAdapter::class])
open class ArbiPixQRCodeAdapter(
    @param:Client(
        id = "arbi",
    ) private val httpClient: RxHttpClient,
    private val configuration: NewArbiPixKeyManagementConfiguration,
    private val authenticationManager: NewArbiAuthenticationManager,
) : PixQRCodeParserService {

    @Cacheable(parameters = ["qrCodeValue", "document"])
    override fun parseQRCodeCacheable(qrCodeValue: String, document: String): Either<PixKeyError, PixKeyDetailsResult> {
        return parseQRCode(qrCodeValue, document)
    }

    override fun parseQRCode(qrCodeValue: String, document: String): Either<PixKeyError, PixKeyDetailsResult> {
        val requestMap = QRCodeValidateRequestTO(qrCodeValue = qrCodeValue, cpfCnpj = document, indConsultaDICT = true)
        val marker = append("request", requestMap)
        val httpRequest = createHttpRequest<QRCodeValidateRequestTO>(HttpMethod.POST, configuration.qrCodeProcessPath).body(requestMap)
        val call = httpClient.retrieve(
            httpRequest,
            Argument.listOf(QRCodeResponseWrapperTO::class.java),
            Argument.STRING,
        )
        try {
            val response = call.firstOrError().blockingGet()
            logger.info(marker.andAppend("response", response), "processQRCode")
            val qrCodeDetailsResponse = jacksonObjectMapper().readerFor(QRCodeDetailsResponseTO::class.java).readValue<QRCodeDetailsResponseTO>(response[0].response)
            with(qrCodeDetailsResponse) {
                val pixKey = PixKey(
                    value = chaveEnderecamento,
                    type = PixKeyType.valueOf(tipoChave),
                )
                val accountNumber = AccountNumber(nroConta)
                val pixKeyHolder = PixKeyHolder(
                    accountNo = accountNumber.number,
                    accountDv = accountNumber.dv,
                    ispb = codInstituicao,
                    institutionName = nomePsp.trim(),
                    accountType = fromArbiAccountType(tipoConta),
                    routingNo = codAgencia.toLong(),
                )

                val validName: (String?) -> Boolean = { it != null && it.length > 1 }

                val pixKeyOwner = PixKeyOwner(
                    name = (recebedorNomeFantasia.takeIf(validName) ?: recebedorNome.takeIf(validName) ?: nome.takeIf(validName)).orEmpty().trim(),
                    document = cpfCnpj,
                )
                if (tipoPix != "NORMAL") {
                    logger.error(marker.andAppend("tipoPix", tipoPix), "processQRCode")
                    return PixKeyError.InvalidQrCode.left()
                }

                return PixKeyDetailsResult(
                    e2e = qrCodeDetailsResponse.endToEnd,
                    pixKeyDetails = PixKeyDetails(
                        key = pixKey,
                        holder = pixKeyHolder,
                        owner = pixKeyOwner,
                    ),
                    qrCodeInfo = PixQrCodeData(
                        pixId = referencia ?: identificador,
                        fixedAmount = valorFinal?.let { convertToLong(it) },
                        type = when (tipoQRCode) {
                            "DINAMICO" -> PixQrCodeType.DYNAMIC
                            "ESTATICO" -> PixQrCodeType.STATIC
                            else -> return Either.Left(PixKeyError.InvalidQrCode)
                        },
                        info = info,
                        additionalInfo = infoAdicional?.mapNotNull {
                            if (it["nome"] != null) {
                                Pair(it["nome"]!!, it["valor"].orEmpty())
                            } else {
                                null
                            }
                        }?.associate { it },
                        originalAmount = valor?.let { convertToLong(it) },
                        expiration = calculateExpiration(calendarioCriacao, calendarioExpiracaoSegundos),
                    ),
                ).right()
            }
        } catch (e: HttpClientResponseException) {
            val response = e.response.getBody(String::class.java).get()
            val error = handleError(response, marker)
            logger.error(
                marker.andAppend("status", e.response.status)
                    .andAppend("response", response)
                    .andAppend("error", error),
                "processQRCode",
                e,
            )

            return error.left()
        } catch (e: Exception) {
            logger.error(marker, "processQRCode", e)
            return PixKeyError.UnknownError.left()
        }
    }

    private fun handleError(
        response: String,
        markers: LogstashMarker,
    ): PixKeyError {
        try {
            val wrapper = parseListFrom<QRCodeErrorResponseWrapperTO>(response).first()
            val error = parseObjectFrom<QRCodeErrorResponseTO>(wrapper.response)

            markers.andAppend("errorWrapper", wrapper)
                .andAppend("error", error)

            logger.error(markers, "processQRCode#handleError")
            return when (error.erro.codigoErro) {
                QrCodeError.INVALID_QR_CODE.code -> PixKeyError.InvalidQrCode
                else -> PixKeyError.UnknownError
            }
        } catch (ex: Exception) {
            logger.error(markers, "processQRCode#handleError", ex)
            return PixKeyError.UnknownError
        }
    }

    private fun calculateExpiration(calendarioCriacao: String?, calendarioExpiracaoSegundos: Long?): ZonedDateTime? {
        if (calendarioCriacao == null || calendarioExpiracaoSegundos == null) {
            return null
        }
        val creationTime = ZonedDateTime.parse(calendarioCriacao, DateTimeFormatter.ISO_ZONED_DATE_TIME)
        return creationTime.plusSeconds(calendarioExpiracaoSegundos)
    }

    private fun <T> createHttpRequest(httpMethod: HttpMethod, uri: String) = HttpRequest.create<T>(httpMethod, uri)
        .header("client_id", configuration.clientId)
        .header("access_token", authenticationManager.getToken())
        .contentType(MediaType.APPLICATION_JSON)
        .accept(MediaType.APPLICATION_JSON_TYPE)

    companion object {
        private val logger = LoggerFactory.getLogger(ArbiPixQRCodeAdapter::class.java)
    }
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class QRCodeValidateRequestTO(
    @JsonProperty("qrCodeValue") val qrCodeValue: String,
    @JsonProperty("cpfCnpj") val cpfCnpj: String,
    @JsonProperty("indConsultaDICT") val indConsultaDICT: Boolean,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class QRCodeRequestTO(
    @JsonProperty("codUsuario") val document: String,
    @JsonProperty("chaveEnderecamento") val key: String,
    @JsonProperty("valor") val amount: Float,
    @JsonProperty("nomeBeneficiario") val recipientName: String,
    @JsonProperty("cidade") val recipientCity: String,
    @JsonProperty("identificador") val txId: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class QRCodeResponseWrapperTO(
    @JsonProperty("response") val response: String? = null,
)

data class QRCodeResponseTO(
    val qrCodeValue: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class QRCodeDetailsResponseTO(
    val nome: String,
    val cpfCnpj: String,
    val nomePsp: String,
    val codInstituicao: String,
    val codAgencia: String,
    val nroConta: String,
    val tipoConta: String,
    val referencia: String?,
    val info: String,
    val tipoQRCode: String,
    val endToEnd: String,
    val tipoChave: String,
    val chaveEnderecamento: String,
    val identificador: String?,
    val tipoPix: String,
    val valor: String?,
    val valorFinal: String?,
    val infoAdicional: List<Map<String, String>>?,
    val calendarioCriacao: String?,
    val recebedorNomeFantasia: String?,
    val recebedorNome: String?,
    val calendarioExpiracaoSegundos: Long?,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class QRCodeErrorResponseWrapperTO(
    val status: String,
    val response: String,
)

@JsonIgnoreProperties(ignoreUnknown = true)
data class QRCodeErrorResponseTO(
    val erro: QRCodeErrorTO,
)

data class QRCodeErrorTO(
    val codigoErro: String,
    val descricaoErro: String,
)

enum class QrCodeError(val code: String) {
    INVALID_QR_CODE("QRCD29"),
}