package ai.friday.billpayment.adapters.sns

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.SmsSender
import io.micronaut.context.annotation.Property
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sns.SnsClient
import software.amazon.awssdk.services.sns.model.MessageAttributeValue
import software.amazon.awssdk.services.sns.model.PublishRequest
import software.amazon.awssdk.services.sns.model.PublishResponse

@FridayMePoupe
class AwsSmsSender(private val amazonSNS: SnsClient) : SmsSender {

    @field:Property(name = "sns.sms.maxPrice")
    private lateinit var smsMaxPrice: String

    override fun send(phoneNumber: MobilePhone, message: String) {
        try {
            val smsAttributes: MutableMap<String, MessageAttributeValue> = HashMap()
            smsAttributes["AWS.SNS.SMS.MaxPrice"] = MessageAttributeValue.builder()
                .stringValue(smsMaxPrice)
                .dataType("Number").build()
            smsAttributes["AWS.SNS.SMS.SMSType"] = MessageAttributeValue.builder()
                .stringValue("Transactional")
                .dataType("String").build()
            val result: PublishResponse = amazonSNS.publish(
                PublishRequest.builder()
                    .message(message)
                    .phoneNumber(phoneNumber.msisdn)
                    .messageAttributes(smsAttributes).build(),
            )
            logger.info(
                append("phoneNumber", phoneNumber.msisdn)
                    .and<LogstashMarker>(append("result", result.sdkHttpResponse().statusCode())),
                "AwsSmsSender",
            )
        } catch (e: Exception) {
            logger.error(append("phoneNumber", phoneNumber.msisdn).andAppend("message", message), "AwsSmsSender", e)
            throw e
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(AwsSmsSender::class.java)
    }
}