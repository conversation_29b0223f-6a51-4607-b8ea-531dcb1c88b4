package ai.friday.billpayment.adapters.circuitbreaker

import ai.friday.billpayment.adapters.arbi.ArbiAccountMissingPermissionsException
import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import ai.friday.billpayment.adapters.arbi.ArbiInvalidAccountException
import ai.friday.billpayment.app.bill.andAppend
import io.github.resilience4j.circuitbreaker.CircuitBreaker
import io.github.resilience4j.circuitbreaker.CircuitBreakerConfig
import io.github.resilience4j.circuitbreaker.CircuitBreakerRegistry
import io.github.resilience4j.circuitbreaker.event.CircuitBreakerEvent
import io.github.resilience4j.decorators.Decorators
import io.micronaut.aop.Around
import io.micronaut.aop.InterceptorBean
import io.micronaut.aop.MethodInterceptor
import io.micronaut.aop.MethodInvocationContext
import io.micronaut.context.annotation.Factory
import jakarta.inject.Singleton
import java.time.Duration
import java.util.function.Supplier
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Factory
class CircuitBreakerFactory {
    @Singleton
    fun circuitBreakerRunner(): CircuitBreakerManager {
        return CircuitBreakerManager()
    }
}

open class CircuitBreakerManager {
    private val registry = CircuitBreakerRegistry.ofDefaults()
    private val logger = LoggerFactory.getLogger(CircuitBreakerManager::class.java)

    fun getRegistry(): CircuitBreakerRegistry {
        return registry
    }

    fun <T> decorate(
        circuitBreakerName: String,
        minimumNumberOfCalls: Int,
        slidingWindowSize: Int,
        permittedNumberOfCallsInHalfOpenState: Int,
        waitDurationInOpenState: Int,
        failureRateThreshold: Float,
        method: Supplier<T>,
    ): Supplier<T> {
        val circuitBreaker: CircuitBreaker = registry.allCircuitBreakers.find { it.name == circuitBreakerName } ?: run {
            val config = CircuitBreakerConfig
                .custom()
                .slidingWindowType(CircuitBreakerConfig.SlidingWindowType.COUNT_BASED)
                .minimumNumberOfCalls(minimumNumberOfCalls)
                .slidingWindowSize(slidingWindowSize)
                .permittedNumberOfCallsInHalfOpenState(permittedNumberOfCallsInHalfOpenState)
                .waitDurationInOpenState(Duration.ofSeconds(waitDurationInOpenState.toLong()))
                .failureRateThreshold(failureRateThreshold)
                .writableStackTraceEnabled(false)
                .recordException { exception ->
                    exception is ArbiAdapterException && exception !is ArbiInvalidAccountException && exception !is ArbiAccountMissingPermissionsException
                }
                .build()
            val newCircuitBreaker = registry.circuitBreaker(circuitBreakerName, config)
            val function: (CircuitBreakerEvent) -> Unit = { e -> logger.info(append("event", e.circuitBreakerName).andAppend("eventType", e.eventType), "CircuitBreakerManager#decorate") }
            newCircuitBreaker.eventPublisher
                .onReset(function)
                .onStateTransition(function)
                .onError(function)
                .onCallNotPermitted(function)
                .onFailureRateExceeded(function)
            newCircuitBreaker
        }

        return Decorators
            .ofSupplier(method)
            .withCircuitBreaker(circuitBreaker)
            .decorate()
    }
}

const val minimumNumberOfCallsDefault = 6
const val slidingWindowSizeDefault = 10
const val permittedNumberOfCallsInHalfOpenStateDefault = 4
const val waitDurationInOpenStateDefault = 10
const val failureRateThresholdDefault = 50.0f

@Retention(AnnotationRetention.RUNTIME)
@Target(AnnotationTarget.FUNCTION)
@Around
annotation class CircuitBreakerHoF(
    val name: String,
    val minimumNumberOfCalls: Int = minimumNumberOfCallsDefault,
    val slidingWindowSize: Int = slidingWindowSizeDefault,
    val permittedNumberOfCallsInHalfOpenState: Int = permittedNumberOfCallsInHalfOpenStateDefault,
    val waitDurationInOpenState: Int = waitDurationInOpenStateDefault,
    val failureRateThreshold: Float = failureRateThresholdDefault,
)

@Singleton
@InterceptorBean(CircuitBreakerHoF::class)
class CircuitBreakerAspect(private val circuitBreakerManager: CircuitBreakerManager) : MethodInterceptor<Any, Any> {

    override fun intercept(context: MethodInvocationContext<Any, Any>): Any {
        val values = context.getAnnotation(CircuitBreakerHoF::class.java).values
        val circuitBreakerName = values.getValue("name") ?: throw RuntimeException("CircuitBreakerHoF annotation must have a name")
        val minimumNumberOfCalls = values.getOrDefault("minimumNumberOfCalls", minimumNumberOfCallsDefault)
        val slidingWindowSize = values.getOrDefault("slidingWindowSize", slidingWindowSizeDefault)
        val permittedNumberOfCallsInHalfOpenState = values.getOrDefault("permittedNumberOfCallsInHalfOpenState", permittedNumberOfCallsInHalfOpenStateDefault)
        val waitDurationInOpenState = values.getOrDefault("waitDurationInOpenState", waitDurationInOpenStateDefault)
        val failureRateThreshold = values.getOrDefault("failureRateThreshold", failureRateThresholdDefault)

        return circuitBreakerManager.decorate(
            circuitBreakerName = circuitBreakerName as String,
            minimumNumberOfCalls = minimumNumberOfCalls as Int,
            slidingWindowSize = slidingWindowSize as Int,
            permittedNumberOfCallsInHalfOpenState = permittedNumberOfCallsInHalfOpenState as Int,
            waitDurationInOpenState = waitDurationInOpenState as Int,
            failureRateThreshold = failureRateThreshold as Float,
            method = { context.proceed() },
        ).get()
    }
}