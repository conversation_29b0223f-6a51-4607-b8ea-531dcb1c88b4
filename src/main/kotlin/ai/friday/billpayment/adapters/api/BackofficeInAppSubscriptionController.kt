package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.inappsubscription.BACKOFFICE_IN_APP_SUBSCRIPTION_PRODUCT_ID
import ai.friday.billpayment.app.inappsubscription.InAppSubscription
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionAccessConcessionId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionProductId
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionReason
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionRepository
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionService
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStatus
import ai.friday.billpayment.app.inappsubscription.InAppSubscriptionStore
import ai.friday.billpayment.app.inappsubscription.RefundResult
import ai.friday.billpayment.app.inappsubscription.TransferFromAnonymousIdResult
import ai.friday.morning.date.brazilTimeZone
import ai.friday.morning.date.dateTimeFormat
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import java.time.Instant
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Validated
@Secured(BACKOFFICE)
@Controller("/backoffice/in-app-subscription")
@FridayMePoupe
class BackofficeInAppSubscriptionController(
    private val inAppSubscriptionService: InAppSubscriptionService,
    private val inAppSubscriptionRepository: InAppSubscriptionRepository,
) {
    @Get("/{?status,endDate}")
    fun getBy(@QueryValue status: String?, @QueryValue endDate: String?): HttpResponse<*> {
        val markers = Markers.append("status", status).andAppend("endDate", endDate)

        try {
            if (status == null && endDate == null) {
                LOG.info(markers, "BackofficeInAppSubscriptionController#getBy")
                return HttpResponse.badRequest("status or endDate is required")
            }

            val subscriptions = if (status != null) {
                inAppSubscriptionRepository.findByStatus(status = InAppSubscriptionStatus.valueOf(status))
            } else {
                val endDateParsed = ZonedDateTime.parse(endDate, DateTimeFormatter.ISO_DATE_TIME)
                inAppSubscriptionRepository.findByEndDate(endsAt = endDateParsed)
            }

            LOG.info(markers, "BackofficeInAppSubscriptionController#getBy")
            return HttpResponse.ok(subscriptions.map { it.toBackofficeInAppSubscriptionTO() })
        } catch (ex: Exception) {
            LOG.error(markers, "BackofficeInAppSubscriptionController#getBy", ex)
            return HttpResponse.badRequest<Unit>()
        }
    }

    @Get("/{accountId}")
    fun getSubscriptions(@PathVariable accountId: String): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)

        try {
            return HttpResponse.ok(
                BackofficeInAppSubscriptionsTO(
                    current = inAppSubscriptionService.getSubscription(accountId = AccountId(accountId))?.toBackofficeInAppSubscriptionTO(),
                    history = inAppSubscriptionService.getStoreSubscriptions(accountId = AccountId(accountId)).getOrThrow().map { it.toBackofficeInAppSubscriptionTO() },
                ),
            ).also {
                LOG.info(markers, "BackofficeInAppSubscriptionController#getSubscription")
            }
        } catch (ex: Exception) {
            LOG.error(markers, "BackofficeInAppSubscriptionController#getSubscription", ex)
            return HttpResponse.badRequest<Unit>()
        }
    }

    @Post("/{accountId}/{productId}/refund")
    fun refundSubscription(
        authentication: Authentication,
        @PathVariable accountId: String,
        @PathVariable productId: String,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("productId", productId)

        val result = if (authentication.roles.contains(Role.ADMIN.name) || authentication.roles.contains(Role.BACKOFFICE_ADMIN.name)) {
            inAppSubscriptionService.refundAndCancelSubscription(accountId = AccountId(accountId), productId = InAppSubscriptionProductId(productId))
        } else {
            inAppSubscriptionService.refundAndCancelGuestSubscription(accountId = AccountId(accountId), productId = InAppSubscriptionProductId(productId))
        }

        return result.fold(
            {
                LOG.info(markers.andAppend("result", it).andAppend("name", it::class.simpleName), "BackofficeInAppSubscriptionController#refundSubscription")

                when (it) {
                    is RefundResult.AccountMustBeGuest -> HttpResponse.badRequest(it)
                    is RefundResult.AccountNotFound -> HttpResponse.notFound(it)
                    is RefundResult.NotAllowedByStore -> HttpResponse.badRequest(it)
                    is RefundResult.Requested -> HttpResponse.accepted()
                    is RefundResult.SubscriptionNotFound -> HttpResponse.notFound(it)
                }
            },
            {
                LOG.error(markers, "BackofficeInAppSubscriptionController#refundSubscription", it)
                HttpResponse.serverError()
            },
        )
    }

    @Post("/")
    fun createSubscription(@Body body: BackofficeInAppSubscriptionTO): HttpResponse<*> {
        val markers = Markers.append("body", body)

        try {
            inAppSubscriptionRepository.save(
                InAppSubscription(
                    accountId = AccountId(value = body.accountId),
                    status = InAppSubscriptionStatus.valueOf(body.status),
                    endsAt = ZonedDateTime.parse(body.endsAt, dateTimeFormat),
                    store = InAppSubscriptionStore.valueOf(body.store!!),
                    reason = InAppSubscriptionReason.valueOf(body.reason),
                    inAppSubscriptionAccessConcessionId = body.inAppSubscriptionAccessConcessionId?.let { InAppSubscriptionAccessConcessionId(it) },
                    autoRenew = body.autoRenew,
                    price = body.price,
                    productId = BACKOFFICE_IN_APP_SUBSCRIPTION_PRODUCT_ID,
                    offStoreProductId = null,
                ),
            )

            LOG.info(markers, "BackofficeInAppSubscriptionController#createSubscription")
            return HttpResponse.created(Unit)
        } catch (ex: Exception) {
            LOG.error(markers, "BackofficeInAppSubscriptionController#createSubscription", ex)
            return HttpResponse.badRequest<Unit>()
        }
    }

    @Post("/processCreateSubscriptionEvent")
    fun processCreateSubscriptionEvent(@Body body: ProcessCreateSubscriptionEventTO): HttpResponse<*> {
        val markers = Markers.append("body", body)

        try {
            inAppSubscriptionService.createSubscription(
                inAppSubscription = body.toInAppSubscription(),
            )

            LOG.info(markers, "BackofficeInAppSubscriptionController#processCreateSubscriptionEvent")
            return HttpResponse.created(Unit)
        } catch (ex: Exception) {
            LOG.error(markers, "BackofficeInAppSubscriptionController#processCreateSubscriptionEvent", ex)
            return HttpResponse.badRequest<Unit>()
        }
    }

    @Post("/transfer-from-external-id")
    fun transferFromExternalId(@Body body: List<String>): HttpResponse<*> {
        val logName = "BackofficeInAppSubscriptionController#handleTransferAnonymousIdSubscription"
        val markers = Markers.append("externalAccountIds", body)

        return body.associateWith { sourceExternalId ->
            inAppSubscriptionService.transferSubscriptionFromExternalId(sourceExternalId)
                .fold(
                    onFailure = {
                        LOG.error(markers, logName, it)
                        "Falha inesperada. Verifique os logs: ${it.message}"
                    },
                    onSuccess = {
                        when (it) {
                            is TransferFromAnonymousIdResult.Failed -> {
                                LOG.error(markers.andAppend("reason", it), logName)
                                it.toString()
                            }

                            is TransferFromAnonymousIdResult.InvalidExternalId,
                            is TransferFromAnonymousIdResult.InvalidTargetAccount,
                            is TransferFromAnonymousIdResult.TargetAccountIdNotFound,
                            -> {
                                LOG.warn(markers.andAppend("reason", it), logName)
                                it.toString()
                            }

                            is TransferFromAnonymousIdResult.Transferred -> {
                                it.targetAccountId.value
                            }
                        }
                    },
                )
        }.let {
            LOG.info(markers, logName)
            HttpResponse.ok(it)
        }
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeInAppSubscriptionController::class.java)
    }
}

fun InAppSubscription.toBackofficeInAppSubscriptionTO(): BackofficeInAppSubscriptionTO {
    return BackofficeInAppSubscriptionTO(
        accountId = accountId.value,
        status = status.name,
        endsAt = endsAt.format(dateTimeFormat),
        store = store?.name,
        reason = reason.name,
        inAppSubscriptionAccessConcessionId = inAppSubscriptionAccessConcessionId?.value,
        autoRenew = autoRenew,
        productId = productId?.value,
        price = price,
    )
}

data class BackofficeInAppSubscriptionsTO(
    val current: BackofficeInAppSubscriptionTO?,
    val history: List<BackofficeInAppSubscriptionTO>,
)

data class BackofficeInAppSubscriptionTO(
    val accountId: String,
    val status: String,
    val endsAt: String,
    val store: String?,
    val reason: String,
    val inAppSubscriptionAccessConcessionId: String?,
    val autoRenew: Boolean?,
    val productId: String?,
    val price: Long,
)

fun ProcessCreateSubscriptionEventTO.toInAppSubscription(): InAppSubscription {
    return InAppSubscription(
        accountId = AccountId(value = accountId),
        status = InAppSubscriptionStatus.valueOf(status),
        endsAt = ZonedDateTime.ofInstant(Instant.ofEpochMilli(endsAt), brazilTimeZone),
        store = InAppSubscriptionStore.valueOf(store),
        reason = InAppSubscriptionReason.valueOf(reason),
        inAppSubscriptionAccessConcessionId = null,
        autoRenew = autoRenew,
        price = price,
        productId = null,
        offStoreProductId = null,
    )
}

data class ProcessCreateSubscriptionEventTO(
    val accountId: String,
    val status: String,
    val endsAt: Long,
    val store: String,
    val reason: String,
    val price: Long,
    val autoRenew: Boolean?,
)