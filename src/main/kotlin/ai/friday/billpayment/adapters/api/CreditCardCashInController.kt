package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountPaymentMethodId
import ai.friday.billpayment.app.account.PaymentMethodType
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.cashIn.CashInResult
import ai.friday.billpayment.app.cashIn.CreditCardCashIn
import ai.friday.billpayment.app.cashIn.CreditCardCashInCommand
import ai.friday.billpayment.app.cashIn.CreditCardCashInService
import ai.friday.billpayment.app.integrations.ItemNotFoundException
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionService
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.uuidSize
import ai.friday.billpayment.app.pix.uuidRegexMatcher
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.core.annotation.Introspected
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Error
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import jakarta.validation.ConstraintViolationException
import jakarta.validation.Valid
import jakarta.validation.constraints.Min
import jakarta.validation.constraints.NotNull
import jakarta.validation.constraints.Pattern
import jakarta.validation.constraints.PositiveOrZero
import java.util.UUID
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/transaction")
@Version("2")
@FridayMePoupe
class CreditCardCashInController(
    private val creditCardCashInService: CreditCardCashInService,
    private val transactionService: TransactionService,
) {

    @Post("/cash-in")
    fun postCashIn(
        @Body @Valid
        cashInRequestTO: CashInRequestTO,
        authentication: Authentication,
    ): HttpResponse<ResponseTO> {
        if (cashInRequestTO.paymentMethod.type != PaymentMethodType.CREDIT_CARD) {
            return HttpResponse.badRequest(CashInErrors.PAYMENT_METHOD_NOT_CREDITCARD.responseTO)
        }

        val markers = append("accountId", authentication.toAccountId().value)
            .andAppend("walletId", authentication.toWalletId().value)
            .andAppend("request", cashInRequestTO)

        val command = CreditCardCashInCommand(
            transactionId = TransactionId.build(cashInRequestTO.id),
            netAmount = cashInRequestTO.netAmount,
            feeAmount = cashInRequestTO.feeAmount,
            paymentMethodId = AccountPaymentMethodId(value = cashInRequestTO.paymentMethod.id),
            actionSource = ActionSource.Api(accountId = authentication.toAccountId()),
            walletId = authentication.toWalletId(),
            accountId = authentication.toAccountId(),
        )

        return try {
            val result = creditCardCashInService.process(command)
            LOG.info(markers.and(append("result", result.javaClass.simpleName)), "PostCashIn")
            when (result) {
                CashInResult.InvalidPaymentMethod -> HttpResponse.badRequest(CashInErrors.PAYMENT_METHOD_NOT_CREDITCARD.responseTO)
                CashInResult.PaymentMethodNotFound -> HttpResponse.badRequest(CashInErrors.PAYMENT_METHOD_NOT_FOUND.responseTO)
                is CashInResult.Unknown -> {
                    LOG.error(markers, "PostCashIn", result.error)
                    HttpResponse.serverError(CashInErrors.UNKNOWN.responseTO)
                }

                CashInResult.DuplicatedTransactionId -> HttpResponse.accepted()
                CashInResult.InvalidTransactionId -> HttpResponse.badRequest(CashInErrors.INVALID_TRANSACTION_ID.responseTO)
                CashInResult.OtherCashInIsProcessing -> HttpResponse.badRequest(CashInErrors.CASH_IN_IN_PROGRESS.responseTO)
                CashInResult.MissingInternalBankAccount -> HttpResponse.badRequest(CashInErrors.INVALID_TARGET_ACCOUNT.responseTO)
                CashInResult.InvalidFeeAmount -> {
                    LOG.error(markers, "PostCashIn")
                    HttpResponse.badRequest(CashInErrors.INVALID_FEE.responseTO)
                }

                CashInResult.LimitReached -> {
                    LOG.info(markers.andAppend("limitReached", true), "PostCashIn")
                    HttpResponse.badRequest(CashInErrors.CASH_IN_LIMIT_REACHED.responseTO)
                }

                CashInResult.QuotaLimitReached -> HttpResponse.badRequest(CashInErrors.QUOTA_LIMIT_REACHED.responseTO)
                CashInResult.Success -> HttpResponse.accepted()
            }
        } catch (e: Exception) {
            LOG.error(markers, "PostCashIn", e)
            HttpResponse.serverError(CashInErrors.UNKNOWN.responseTO)
        }
    }

    @Get("/cash-in/{transactionId}")
    fun getCashIn(@PathVariable transactionId: String, authentication: Authentication): HttpResponse<*> {
        return try {
            val transaction = transactionService.findTransactionByIdAndWalletIdAndAccountId(
                transactionId = TransactionId.build(UUID.fromString(transactionId)),
                walletId = authentication.toWalletId(),
                accountId = authentication.toAccountId(),
                transactionType = TransactionType.CASH_IN,
            )
            HttpResponse.ok(transaction.toCashInResponseTO())
        } catch (e: ItemNotFoundException) {
            LOG.warn(
                append("accountId", authentication.name)
                    .and<LogstashMarker>(append("errorMessage", e.message))
                    .and<LogstashMarker>(append("transactionId", transactionId))
                    .and(append("httpStatus", HttpStatus.NOT_FOUND)),
                "GetCashIn",
            )
            StandardHttpResponses.notFound(CashInErrors.TRANSACTION_NOT_FOUND.responseTO)
        }
    }

    private fun Transaction.toCashInResponseTO() = CashInResponseTO(
        id = id.toUUID().toString(),
        status = status,
        errorDetail = null,
        authorizationNumber = null,
        amount = settlementData.getTarget<CreditCardCashIn>().amount,
        serviceAmountTax = settlementData.serviceAmountTax,
        totalAmount = settlementData.totalAmount,
        paymentMethod = CashInPaymentMethodTO(id = paymentData.toSingle().accountPaymentMethod.id.value),
    )

    @Error
    fun handleConstraintViolationException(
        request: HttpRequest<*>,
        exception: ConstraintViolationException,
    ): HttpResponse<ResponseTO> {
        return HttpResponse.badRequest(
            ResponseTO(
                message = exception.constraintViolations.first().message,
                code = defaultErrorCode,
            ),
        )
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(CreditCardCashInController::class.java)
    }
}

@Introspected
data class CashInRequestTO(
    @field:Pattern(regexp = uuidRegexMatcher, message = invalidUUIDErrorMessage) val id: String,
    val paymentMethod: TransactionPaymentMethodTO,
    @field:Min(message = invalidAmountErrorMessage, value = 100) val netAmount: Long,
    @field:PositiveOrZero(message = invalidAmountErrorMessage) val feeAmount: Long,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class CashInResponseTO(
    val id: String,
    val status: TransactionStatus,
    val errorDetail: String?,
    val authorizationNumber: String?,
    val amount: Long,
    val serviceAmountTax: Long,
    val totalAmount: Long,
    val paymentMethod: CashInPaymentMethodTO,
)

data class CashInPaymentMethodTO(
    val id: String,
)

data class TransactionPaymentMethodTO(@NotNull val id: String, @NotNull val type: PaymentMethodType)

const val defaultErrorCode = "4000"
const val invalidUUIDErrorMessage = "id must be a valid UUID"
const val invalidAmountErrorMessage = "amount must be greater than R$ 1,00"

enum class CashInErrors(val responseTO: ResponseTO) {
    INVALID_UUID(ResponseTO(message = invalidUUIDErrorMessage, code = defaultErrorCode)),
    INVALID_AMOUNT(ResponseTO(message = invalidAmountErrorMessage, code = defaultErrorCode)),
    PAYMENT_METHOD_NOT_CREDITCARD(ResponseTO(message = "Payment method type should be CREDIT_CARD", code = "4000")),
    PAYMENT_METHOD_NOT_FOUND(ResponseTO(message = "Payment method type not found", code = "4001")),
    UNKNOWN(ResponseTO(message = "Unknown error", code = "4002")),
    INVALID_TRANSACTION_ID(ResponseTO(message = "Invalid transactionid", code = "4003")),
    CASH_IN_IN_PROGRESS(ResponseTO(message = "Another cash-in transaction is in progress", code = "4004")),
    INVALID_TARGET_ACCOUNT(ResponseTO(message = "User does not have a valid target account", code = "4005")),
    TRANSACTION_NOT_FOUND(ResponseTO(message = "Transaction not found", code = "4006")),
    INVALID_FEE(ResponseTO(message = "fee amount differs from expected", code = "4009")),
    CASH_IN_LIMIT_REACHED(ResponseTO(message = "Cash in limit reached", code = "4010")),
    QUOTA_LIMIT_REACHED(ResponseTO(message = "Quota limit reached", code = "4011")),
}

private fun TransactionId.toUUID(): UUID = UUID.fromString(this.value.takeLast(uuidSize))