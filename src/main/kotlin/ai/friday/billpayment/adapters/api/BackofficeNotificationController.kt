package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.notification.CommCentreAdapter
import ai.friday.billpayment.adapters.parsers.getObjectMapper
import ai.friday.billpayment.adapters.parsers.parseObjectFrom
import ai.friday.billpayment.app.EmailAddress
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.MobilePhone
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.account.StoredObject
import ai.friday.billpayment.app.account.UserFilesConfiguration
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.chatbot.OpenFinanceIncentiveService
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillRepository
import ai.friday.billpayment.app.integrations.MessagePublisher
import ai.friday.billpayment.app.integrations.WelcomeMessageType
import ai.friday.billpayment.app.message.QueueMessageBatch
import ai.friday.billpayment.app.notification.ChatBotMessagePublisher
import ai.friday.billpayment.app.notification.NotificationMedia
import ai.friday.billpayment.app.notification.NotificationMediaType
import ai.friday.billpayment.app.notification.NotifyBillComingDueMessage
import ai.friday.billpayment.app.notification.NotifyBillComingDueType
import ai.friday.billpayment.app.notification.PaymentCanceledDueToAmountChangeNotificationService
import ai.friday.billpayment.app.onepixpay.OnePixPayService
import ai.friday.billpayment.app.subscription.SubscriptionService
import ai.friday.billpayment.app.utils.PublicHttpLinkGenerator
import ai.friday.billpayment.app.wallet.Member
import ai.friday.billpayment.app.wallet.MemberPermissions
import ai.friday.billpayment.app.wallet.MemberStatus
import ai.friday.billpayment.app.wallet.MemberType
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.getOrElse
import io.micronaut.context.annotation.Property
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.via1.communicationcentre.app.integrations.CheckableNotificationService
import io.via1.communicationcentre.app.notification.ChatState
import java.time.Duration
import java.time.ZonedDateTime
import java.util.UUID
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/notify")
@FridayMePoupe
class BackofficeNotificationController(
    private val commCentreAdapter: CommCentreAdapter,
    private val walletService: WalletService,
    private val billRepository: BillRepository,
    private val accountRepository: AccountRepository,
    private val onePixPayService: OnePixPayService,
    private val subscriptionService: SubscriptionService,
    private val paymentCanceledDueToAmountChangeNotificationService: PaymentCanceledDueToAmountChangeNotificationService,
    private val messagePublisher: MessagePublisher,
    private val whatsAppSender: CheckableNotificationService,
    private val chatBotMessagePublisher: ChatBotMessagePublisher,
    private val publicHttpLinkGenerator: PublicHttpLinkGenerator,
    private val userFilesConfiguration: UserFilesConfiguration,
    private val openFinanceIncentiveService: OpenFinanceIncentiveService,
    @Property(name = "sqs.queues.notifyBillComingDue") private val billComingDueExecutionQueueName: String,
) {

    private val logger = LoggerFactory.getLogger(this::class.java)

    @Post("/message/{message}")
    fun notifyMessage(
        @Body rawBody: String,
        @PathVariable message: String,
    ): HttpResponse<*> {
        try {
            when (message) {
                "insufficient-balance-today" -> sendInsufficientBalanceToday(rawBody)
                "one-pix-pay" -> sendOnePixPay(rawBody)
                "credit_card_enabled" -> sendCreditCardEnabled(rawBody)
                "pix-not-received-failure" -> sendPixNotReceivedFailure(rawBody)
                "bill-schedule-canceled-due-amount-changed" -> sendBillScheduleCanceledDueAmountChanged(rawBody)
                "schedule-bill-canceled-due-credit-card-denied" -> sendScheduleBillCanceledDueCreditCardDenied(rawBody)
                "notifyRegisterDenied" -> sendNotifyRegisterDenied(rawBody)
                "notifyRegisterUpgraded" -> sendNotifyRegisterUpgraded(rawBody)
                "notifyUpgradeCompleted" -> sendNotifyUpgradeCompleted(rawBody)
                "notifyHumanChatWelcome" -> sendHumanChatWelcome(rawBody)
                "notifyWelcomeChatBotOnboardingSinglePix" -> sendWelcomeChatBotOnboardingSinglePix(rawBody)
                "notifyBasicSignUpReopened" -> sendBasicSignUpReopened(rawBody)
                "notify-welcome-account-created-without-chatbot" -> sendWelcome(rawBody)
                "subscription_overdue_day01" -> sendSubscriptionOverdue(rawBody, false)
                "subscription_overdue_day02" -> sendSubscriptionOverdue(rawBody, true)
                "subscription_overdue_day04" -> sendSubscriptionOverdueWarningDetailedNotificationsShutdown(rawBody, true)
                "subscription_overdue_close_account" -> sendSubscriptionOverdueCloseAccount(rawBody)
                "custom" -> sendCustom(rawBody)
                "custom_email" -> sendCustomEmail(rawBody)
                "register_completed" -> sendChatbotRegisterCompleted(rawBody)
                "of_incentive_dda" -> sendOpenFinanceIncentiveDDA(rawBody)
                else -> throw Exception("Message not found")
            }
        } catch (e: Exception) {
            LOG.error(
                append("rawBody", rawBody).andAppend("path_message", message),
                "NotificationController#notifyMessage",
                e,
            )
            return HttpResponse.serverError<Unit>()
        }

        return HttpResponse.ok("")
    }

    @Post("/publishNotifyComingDueMessage/{message}")
    fun publishNotifyMessage(
        @Body body: PublishNotifyTO,
        @PathVariable message: String,
    ): HttpResponse<*> {
        try {
            val wallet = walletService.findWallet(WalletId(body.walletId))

            val notify = NotifyBillComingDueMessage(
                walletId = wallet.id,
                type = NotifyBillComingDueType.valueOf(message),
            )

            val messageString = getObjectMapper().writeValueAsString(notify)

            messagePublisher.sendMessageBatch(
                messageBatch = QueueMessageBatch(
                    queueName = billComingDueExecutionQueueName,
                    messages = listOf(messageString),
                    delaySeconds = null,
                ),
            )
        } catch (e: Exception) {
            LOG.error(
                append("body", body).andAppend("path_message", message),
                "NotificationController#publishNotifyMessage",
                e,
            )
            return HttpResponse.serverError<Unit>()
        }

        return HttpResponse.ok("")
    }

    @Post("/chatState/{mobilePhone}/{state}")
    fun notifyChatState(
        @PathVariable mobilePhone: String,
        @PathVariable state: ChatState,
    ): HttpResponse<*> {
        val markers = append("mobilePhone", mobilePhone)
            .andAppend("state", state)

        try {
            whatsAppSender.sendChatState(state, mobilePhone, UUID.randomUUID().toString())
        } catch (e: Exception) {
            LOG.error(markers, "NotificationController#notifyChatState", e)
            return HttpResponse.serverError<Unit>()
        }

        return HttpResponse.ok("")
    }

    private fun sendCreditCardEnabled(rawBody: String) {
        val body = parseObjectFrom<CreditCardEnabledTO>(rawBody)
        val account = accountRepository.findById(AccountId(body.accountId))
        commCentreAdapter.notifyCreditCardEnabled(
            account.accountId,
            quota = body.quota,
        )
    }

    private fun sendOnePixPay(rawBody: String) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val wallet = walletService.findWallet(WalletId(body.walletId))
        val notScheduledBills = billRepository.findBillsComingDue(wallet.id).filter { it.isOpen() }

        val onePixPay = onePixPayService.create(notScheduledBills, false).getOrElse {
            throw Exception(it::class.java.simpleName)
        }

        commCentreAdapter.notifyOnePixPay(
            members = listOf(wallet.founder),
            walletName = wallet.name,
            onePixPayId = onePixPay.id,
            delay = Duration.ofSeconds(1),
            bills = notScheduledBills,
        )
    }

    private fun sendInsufficientBalanceToday(rawBody: String) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        commCentreAdapter.notifyInsufficientBalanceToday(
            members = listOf(
                Member(
                    accountId = AccountId(body.accountId),
                    document = "",
                    name = "",
                    emailAddress = EmailAddress(email = ""),
                    type = MemberType.FOUNDER,
                    permissions = MemberPermissions.of(MemberType.FOUNDER),
                    status = MemberStatus.ACTIVE,
                    created = ZonedDateTime.now(),
                    updated = ZonedDateTime.now(),
                ),
            ),
            pendingScheduledAmountToday = 5000,
            pendingAmountTotal7Days = 10000,
            pendingAmountTotal15Days = 15000,
            walletId = WalletId(body.walletId),
            walletName = body.walletName,
            isAfterHours = body.isAfterHours ?: false,
        )
    }

    private fun sendHumanChatWelcome(rawBody: String) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val account = accountRepository.findById(AccountId(body.accountId))
        commCentreAdapter.notifyHumanChatWelcome(
            accountId = account.accountId,
            mobilePhone = MobilePhone(account.mobilePhone),
        )
    }

    private fun sendWelcomeChatBotOnboardingSinglePix(rawBody: String) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val account = accountRepository.findById(AccountId(body.accountId))
        chatBotMessagePublisher.publishWelcomeMessage(
            account = account,
            chatBotWelcomeMessageType = WelcomeMessageType.WELCOME_CHATBOT_ONBOARDING_SINGLE_PIX,
        )
    }

    private fun sendChatbotRegisterCompleted(rawBody: String) {
        val body = parseObjectFrom<AccountIdTO>(rawBody)
        val account = accountRepository.findById(AccountId(body.accountId))
        chatBotMessagePublisher.publishRegisterCompleted(account)
    }

    private fun sendWelcome(rawBody: String) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        val account = accountRepository.findById(AccountId(body.accountId))
        commCentreAdapter.notifyUserActivated(
            account = account,
        )
    }

    private fun sendBasicSignUpReopened(rawBody: String) {
        val body = parseObjectFrom<NotifyAccountPhoneTO>(rawBody)
        val account = accountRepository.findById(AccountId(body.accountId))
        commCentreAdapter.notifyBasicSignUpReopened(AccountId(body.accountId), MobilePhone(body.mobilePhone))
    }

    private fun sendSubscriptionOverdue(rawBody: String, alternativeVersion: Boolean) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        commCentreAdapter.notifySubscriptionOverdue(
            accountId = AccountId(body.accountId),
            effectiveDueDate = getLocalDate(),
            amount = 990,
            alternativeVersion = alternativeVersion,
        )
    }

    private fun sendSubscriptionOverdueWarningDetailedNotificationsShutdown(rawBody: String, alternativeVersion: Boolean) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        commCentreAdapter.notifySubscriptionOverdueWarningDetailedNotificationsShutdown(
            accountId = AccountId(body.accountId),
            effectiveDueDate = getLocalDate(),

        )
    }

    private fun sendSubscriptionOverdueCloseAccount(rawBody: String) {
        val body = parseObjectFrom<NotifyMessageTO>(rawBody)
        commCentreAdapter.notifySubscriptionOverdueCloseAccount(
            accountId = AccountId(body.accountId),
        )
    }

    private fun sendPixNotReceivedFailure(rawBody: String) {
        val body = parseObjectFrom<NotifyPixReceivedFailureTO>(rawBody)
        val wallet = walletService.findWallet(walletId = WalletId(body.walletId))

        commCentreAdapter.notifyPixNotReceivedFailure(
            accountId = wallet.founder.accountId,
            wallet = wallet,
            amount = body.amount,
            senderName = body.senderName,
            senderDocument = body.senderDocument,
        )
    }

    private fun sendBillScheduleCanceledDueAmountChanged(rawBody: String) {
        val body = parseObjectFrom<WalletIdTO>(rawBody)
        val wallet = walletService.findWallet(walletId = WalletId(body.walletId))

        paymentCanceledDueToAmountChangeNotificationService.notify(
            members = listOf(wallet.founder),
            walletId = WalletId(),
            billId = BillId(),
            payee = "Recebedor da conta com valor alterado",
            oldAmount = 1234_56,
            currentAmount = 1235_67,
        )
    }

    private fun sendScheduleBillCanceledDueCreditCardDenied(rawBody: String) {
        val body = parseObjectFrom<WalletIdTO>(rawBody)
        val wallet = walletService.findWallet(walletId = WalletId(body.walletId))

        commCentreAdapter.notifyScheduleBillCanceledDueCreditCardDenied(
            members = listOf(wallet.founder),
            walletId = WalletId(),
            billId = BillId(),
            payee = "pagador da conta",
            amount = 1234_56,

        )
    }

    private fun sendOpenFinanceIncentiveDDA(rawBody: String) {
        val body = parseObjectFrom<NotifyBillIdTO>(rawBody)
        openFinanceIncentiveService.processDDA(billId = BillId(body.billId))
    }

    private fun sendCustom(rawBody: String) {
        val body = parseObjectFrom<CustomWhatsappMessageTO>(rawBody)

        val media = if (body.media != null) {
            val publicLink = body.media.mediaS3File?.let { mediaS3File -> publicHttpLinkGenerator.generate(StoredObject.fromS3Path(userFilesConfiguration.region, mediaS3File), Duration.ofMinutes(1), body.media.mimeType) } ?: body.media.mediaPublicUrl!!

            when (body.media.mediaType) {
                NotificationMediaType.DOCUMENT -> NotificationMedia.Document(
                    url = publicLink,
                    filename = body.media.filename ?: "no_name",
                    documentType = body.media.mimeType,
                )

                NotificationMediaType.IMAGE -> NotificationMedia.Image(
                    url = publicLink,
                    imageType = body.media.mimeType,
                    title = null,
                    text = null,
                )

                NotificationMediaType.VIDEO -> NotificationMedia.Video(
                    url = publicLink,
                    videoType = body.media.mimeType,
                )
            }
        } else { null }

        logger.info(append("media", media), "BackofficeNotificationController#sendCustom")

        commCentreAdapter.notifyCustom(AccountId(body.accountId), body.template, body.parameters, body.quickReplyButtonsWhatsAppParameter, body.buttonWhatsAppParameter, media)
    }

    private fun sendCustomEmail(rawBody: String) {
        val body = parseObjectFrom<CustomEmailMessageTO>(rawBody)
        commCentreAdapter.notifyCustomEmail(AccountId(body.accountId), body.template, body.parameters)
    }

    private fun sendNotifyRegisterDenied(rawBody: String) {
        val body = parseObjectFrom<AccountIdTO>(rawBody)
        val account = accountRepository.findById(AccountId(body.accountId))
        commCentreAdapter.notifyRegisterDenied(account.accountId, MobilePhone(account.mobilePhone))
    }

    private fun sendNotifyRegisterUpgraded(rawBody: String) {
        val body = parseObjectFrom<AccountIdTO>(rawBody)
        val account = accountRepository.findById(AccountId(body.accountId))
        commCentreAdapter.notifyRegisterUpgraded(account.accountId, MobilePhone(account.mobilePhone))
    }

    private fun sendNotifyUpgradeCompleted(rawBody: String) {
        val body = parseObjectFrom<AccountIdTO>(rawBody)
        val account = accountRepository.findById(AccountId(body.accountId))
        commCentreAdapter.notifyUpgradeCompleted(account)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeNotificationController::class.java)
    }
}

private data class CustomWhatsappMessageTO(
    val accountId: String,
    val template: String,
    val parameters: List<String>,
    val quickReplyButtonsWhatsAppParameter: List<String>,
    val buttonWhatsAppParameter: String?,
    val media: CustomWhatsappMessageMediaTO?,
)

private data class CustomWhatsappMessageMediaTO(
    val mediaPublicUrl: String?,
    val mediaS3File: String?,
    val filename: String?,
    val mediaType: NotificationMediaType,
    val mimeType: String,
)

private data class CustomEmailMessageTO(
    val accountId: String,
    val template: String,
    val parameters: Map<String, String>,
)

private data class CreditCardEnabledTO(
    val accountId: String,
    val quotaType: String,
    val quota: Long,
)

private data class NotifyMessageTO(
    val accountId: String,
    val walletId: String,
    val walletName: String,
    val isAfterHours: Boolean?,
    val daysOverdue: Long?,
)

private data class NotifyBillIdTO(
    val billId: String,
)

private data class NotifyPixReceivedFailureTO(
    val walletId: String,
    val amount: Long,
    val senderName: String,
    val senderDocument: String,
)

private data class NotifyAccountPhoneTO(
    val accountId: String,
    val mobilePhone: String,
)

private data class WalletIdTO(
    val walletId: String,
)

private data class AccountIdTO(
    val accountId: String,
)

data class PublishNotifyTO(
    val walletId: String,
)