package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountPaymentMethod
import ai.friday.billpayment.app.account.AccountService
import ai.friday.billpayment.app.account.CreditCard
import ai.friday.billpayment.app.account.ExternalPaymentMethod
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.banking.InternalBankAccount
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.CreditCardService
import ai.friday.billpayment.app.pix.PixKeyType
import ai.friday.billpayment.app.pix.emailPixKeySanitize
import ai.friday.billpayment.app.usage.CreditCardUsage
import ai.friday.billpayment.app.usage.CreditCardUsageService
import ai.friday.billpayment.app.wallet.Wallet
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.billpayment.app.wallet.WalletType
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.context.annotation.Property
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import io.micronaut.validation.Validated
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Validated
@Secured(Role.Code.OWNER)
@Controller("/wallet")
@FridayMePoupe
@Version("2")
class WalletPaymentMethodController(
    private val creditCardUsageService: CreditCardUsageService,
    private val accountService: AccountService,
    private val creditCarService: CreditCardService,
    private val balanceService: BalanceService,
    private val walletService: WalletService,
) {

    @field:Property(name = "accountRegister.pixKey.emailDomain")
    lateinit var pixKeyEmailDomain: String

    @Get("/{walletId}/payment-method")
    fun retrievePaymentMethods(authentication: Authentication, @PathVariable walletId: String): HttpResponse<*> {
        val accountId = authentication.toAccountId()
        val wallet = authentication.getWallet()
        val walletMember = authentication.asWalletMember()

        val markers = append("accountId", accountId.value)
            .andAppend("walletId", walletId)

        return try {
            val usage = creditCardUsageService.calculateCreditCardUsage(accountId)
            val balance =
                if (walletMember.permissions.viewBalance) {
                    balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)
                } else {
                    null
                }

            val responseTO = WalletPaymentMethodsResponseTO(
                usage = usage.toCreditCardUsageTO(),
                creditCards = getCreditCards(accountId, usage),
                pixKey = getPixKey(wallet),
                evpPixKey = getEvpPixKey(wallet.id),
                bankDetails = getBankDetails(wallet),
                balance = WalletPaymentBalanceResponseTO(
                    paymentMethodId = wallet.paymentMethodId.value,
                    amount = balance?.amount,
                ),
            )

            HttpResponse.ok(responseTO)
        } catch (e: Exception) {
            LOG.error(
                markers.andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR.code),
                "retrivePaymentMethods",
                e,
            )
            return StandardHttpResponses.serverError()
        }
    }

    private fun getPixKey(wallet: Wallet) = when (wallet.type) {
        WalletType.PRIMARY -> PixKeyRequestTO(
            value = "${wallet.founder.document}@$pixKeyEmailDomain",
            type = PixKeyType.EMAIL,
        )

        WalletType.SECONDARY -> PixKeyRequestTO(
            value = emailPixKeySanitize("${wallet.name}.${wallet.founder.document}@$pixKeyEmailDomain"),
            type = PixKeyType.EMAIL,
        )
    }

    private fun getEvpPixKey(walletId: WalletId): PixKeyRequestTO? {
        val key = walletService.walletEvpPixKey(walletId) ?: return null

        return PixKeyRequestTO(
            value = key.value,
            type = key.type,
        )
    }

    private fun getBankDetails(wallet: Wallet): WalletPaymentMethodBankAccountTO? {
        val accountPaymentMethod = accountService.findAccountPaymentMethodByIdAndAccountId(
            wallet.paymentMethodId,
            wallet.founder.accountId,
        )
        return accountPaymentMethod.toBankDetails(wallet)
    }

    private fun getCreditCards(
        accountId: AccountId,
        creditCardUsage: CreditCardUsage,
    ) = creditCarService.findActiveAndPendingCreditCards(accountId)
        .map {
            val creditCard = (it.method as CreditCard)
            WalletPaymentMethodCreditCardTO(
                id = it.id.value,
                type = "CREDIT_CARD",
                creditCard = creditCard.toCreditCardTO(it.status.name),
                usage = creditCard.toCreditCardUsageResponseTO(creditCardUsage),
            )
        }

    private fun AccountPaymentMethod.toBankDetails(wallet: Wallet) = when (val paymentMethod = this.method) {
        is InternalBankAccount -> WalletPaymentMethodBankAccountTO(
            paymentMethodId = wallet.paymentMethodId.value,
            accountType = paymentMethod.accountType,
            bankNo = paymentMethod.bankNo,
            routingNo = paymentMethod.routingNo,
            accountNo = paymentMethod.accountNo,
            accountDv = paymentMethod.accountDv,
            mode = paymentMethod.bankAccountMode,
            name = wallet.founder.name,
            document = wallet.founder.document,
        )

        is CreditCard -> throw IllegalStateException("Cartao de Credito nao deveria ser o metodo de pagamento principal da carteira")

        is ExternalPaymentMethod -> null

        else -> throw IllegalStateException("Metodo de pagamento desconhecido")
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(WalletPaymentMethodController::class.java)
    }
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class WalletPaymentMethodsResponseTO(
    val usage: CreditCardUsageTO,
    val creditCards: List<WalletPaymentMethodCreditCardTO>,
    val pixKey: PixKeyRequestTO?,
    val evpPixKey: PixKeyRequestTO?,
    val bankDetails: WalletPaymentMethodBankAccountTO?,
    val balance: WalletPaymentBalanceResponseTO,
)

@JsonInclude(JsonInclude.Include.ALWAYS)
data class WalletPaymentBalanceResponseTO(
    val paymentMethodId: String,
    val amount: Long?,
)

data class WalletPaymentMethodCreditCardTO(
    val id: String,
    val type: String,
    val creditCard: CreditCardTO,
    val usage: CreditCardUsageResponseTO,
)

data class CreditCardUsageResponseTO(
    val usage: Long,
    val quota: Long,
)

fun CreditCard.toCreditCardUsageResponseTO(creditCardUsage: CreditCardUsage): CreditCardUsageResponseTO {
    val riskUsage = creditCardUsage.riskUsages[this.riskLevel]!!
    return CreditCardUsageResponseTO(usage = riskUsage.usage, quota = riskUsage.quota)
}

data class WalletPaymentMethodBankAccountTO(
    val paymentMethodId: String,
    val accountType: AccountType,
    val bankNo: Long,
    val routingNo: Long,
    val accountNo: Long,
    val accountDv: String,
    val mode: BankAccountMode,
    val name: String,
    val document: String,
)