package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.arbi.ArbiAdapterException
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.balance.BalanceService
import ai.friday.billpayment.app.banking.AccountType
import ai.friday.billpayment.app.banking.BankAccountMode
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.forecast.BalanceForecast
import ai.friday.billpayment.app.forecast.ForecastService
import ai.friday.billpayment.app.forecast.WalletBalanceForecast
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.HttpStatus
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Secured(Role.Code.OWNER)
@Controller
@Version("2")
@FridayMePoupe
class BalanceController(private val balanceService: BalanceService) {

    private val logger = LoggerFactory.getLogger(BalanceController::class.java)

    @Get("/balance/amount")
    fun getAmountBalance(authentication: Authentication): HttpResponse<*> {
        val wallet = authentication.getWallet()
        val walletMember = authentication.asWalletMember()

        val markers = append("accountId", walletMember.accountId.value)
            .andAppend("walletId", wallet.id.value)

        try {
            if (!walletMember.permissions.viewBalance) {
                logger.error(
                    markers.andAppend("reason", "member has viewBalance false")
                        .andAppend("httpStatus", HttpStatus.FORBIDDEN.code),
                    "GetCashinAmount",
                )
                return HttpResponse.status<Any>(HttpStatus.FORBIDDEN)
            }

            val balance = balanceService.getBalanceFrom(wallet.founder.accountId, wallet.paymentMethodId)

            return HttpResponse.ok(AmountBalanceTO(amount = balance.amount))
        } catch (e: Exception) {
            return handleError(markers, e, "GetCashinAmount")
        }
    }

    private fun handleError(markers: LogstashMarker, e: Exception, message: String): HttpResponse<ResponseTO> {
        val httpStatus = when (e) {
            is ArbiAdapterException -> HttpStatus.BAD_GATEWAY
            else -> HttpStatus.INTERNAL_SERVER_ERROR
        }

        markers.andAppend("httpStatus", httpStatus.code).also {
            logger.error(it, message, e)
        }

        return HttpResponse.status<ResponseTO>(httpStatus)
            .body(
                ResponseTO(
                    code = httpStatus.code.toString(),
                    message = e.message ?: "Internal Server Error. Please try again later.",
                ),
            )
    }
}

@Secured(Role.Code.OWNER)
@Controller
@Version("2")
@FridayMePoupe
class ForecastBalanceController(private val forecastService: ForecastService) {
    @Get("/balance/cashin")
    fun tryGetCashinBalance(authentication: Authentication): HttpResponse<*> {
        return try {
            val wallet = authentication.getWallet()
            if (!authentication.asWalletMember().permissions.viewBalance) {
                LOG.error(
                    append("accountId", authentication.toAccountId().value)
                        .and<LogstashMarker>(append("walletId", authentication.toWalletId()))
                        .and<LogstashMarker>(append("reason", "member has viewBalance false"))
                        .and(append("httpStatus", HttpStatus.FORBIDDEN.code)),
                    "GetCashinBalance",
                )
                return HttpResponse.status<Any>(HttpStatus.FORBIDDEN)
            }

            val walletBalanceForecast = forecastService.calculateWalletBalanceForecast(wallet)

            LOG.info(
                append("forecastBills", walletBalanceForecast)
                    .andAppend("walletId", wallet.id)
                    .andAppend("accountId", authentication.toAccountId()),
                "TryGetCashinBalance",
            )

            HttpResponse.ok(walletBalanceForecast.toWalletBalanceForecastTO())
        } catch (e: Exception) {
            handleError(authentication.toAccountId(), e, "GetCashinBalance")
        }
    }

    @Get("/balance/checkout")
    fun tryGetCheckoutBalance(authentication: Authentication): HttpResponse<*> {
        return try {
            if (!authentication.asWalletMember().permissions.viewBalance) {
                LOG.error(
                    append("accountId", authentication.toAccountId().value)
                        .andAppend("walletId", authentication.toWalletId())
                        .andAppend("reason", "member has viewBalance false")
                        .andAppend("httpStatus", HttpStatus.FORBIDDEN.code),
                    "GetCheckoutBalance",
                )
                return HttpResponse.status<Any>(HttpStatus.FORBIDDEN)
            }

            val walletForecast = forecastService.calculateWalletBalanceForecast(authentication.getWallet())
            HttpResponse.ok(
                CheckoutBalanceTO(
                    amount = walletForecast.amount,
                    scheduledToDateAmount = walletForecast.scheduled.amountToday,
                ),
            )
        } catch (e: Exception) {
            handleError(authentication.toAccountId(), e, "GetCheckoutBalance")
        }
    }

    private fun handleError(accountId: AccountId, e: Exception, message: String): HttpResponse<ResponseTO> {
        append("accountId", accountId.value)
            .andAppend("httpStatus", HttpStatus.INTERNAL_SERVER_ERROR.code).also {
                LOG.error(it, message, e)
            }
        return if (e.message != null) StandardHttpResponses.serverError(e.message!!) else StandardHttpResponses.serverError()
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BalanceController::class.java)
    }
}

fun WalletBalanceForecast.toWalletBalanceForecastTO() = WalletBalanceForecastTO(
    walletId = walletId.value,
    amount = amount,
    open = open.toBalanceForecastTO(),
    scheduled = scheduled.toBalanceForecastTO(),
    dates = BalanceForecastDatesTO(
        today = dates.today,
        week = dates.week,
        fifteenDays = dates.fifteenDays,
        thirtyDays = dates.thirtyDays,
        month = dates.month,
        nextMonth = dates.nextMonth,
    ),
    overdueAmount = overdueAmount,
)

data class AmountBalanceTO(
    val amount: Long,
)

data class WalletBalanceForecastTO(
    val walletId: String,
    val amount: Long,
    val open: BalanceForecastTO,
    val scheduled: BalanceForecastTO,
    val dates: BalanceForecastDatesTO,
    val overdueAmount: Long,
)

data class BalanceForecastDatesTO(
    val today: String,
    val week: String,
    val fifteenDays: String,
    val thirtyDays: String,
    val month: String,
    val nextMonth: String,
)

data class BalanceForecastTO(
    val amountToday: Long,
    val amountWeek: Long,
    val amountFifteenDays: Long,
    val amountThirtyDays: Long,
    val amountMonth: Long,
    val amountNextMonth: Long,
)

fun BalanceForecast.toBalanceForecastTO() = BalanceForecastTO(
    amountToday = amountToday,
    amountWeek = amountWeek,
    amountFifteenDays = amountFifteenDays,
    amountThirtyDays = amountThirtyDays,
    amountMonth = amountMonth,
    amountNextMonth = amountNextMonth,
)

data class CheckoutBalanceTO(
    val amount: Long,
    val scheduledToDateAmount: Long,
)

data class InternalBankAccountTO(
    val accountType: AccountType,
    val bankNo: Long,
    val routingNo: Long,
    val accountNo: Long,
    val accountDv: String,
    val mode: BankAccountMode,
)