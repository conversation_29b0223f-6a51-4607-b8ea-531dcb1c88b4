package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.cielo.CieloAdapter
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.CreditCardPaymentStatus
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.feature.RequiresCielo
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.ADMIN)
@Controller("/backoffice")
@FridayMePoupe
@RequiresCielo
class CieloBackofficeCreditCardController(
    private val cieloAdapter: CieloAdapter,
) {
    private val logger = LoggerFactory.getLogger(CieloBackofficeCreditCardController::class.java)

    @Get("/credit-card/order/{orderId}")
    fun getOrder(@PathVariable orderId: String): HttpResponse<*> {
        val markers = Markers.append("orderId", orderId)
        return try {
            val order = cieloAdapter.checkStatus(orderId)
            markers.andAppend("order", order)

            logger.info(markers, "BackofficeCreditCardController#getOrder")
            HttpResponse.ok(order)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeCreditCardController#getOrder", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("/credit-card/order/{orderId}/cancelPending")
    fun cancelPending(@PathVariable orderId: String): HttpResponse<*> {
        val markers = Markers.append("orderId", orderId)
        return try {
            val order = cieloAdapter.checkStatus(orderId)
            markers.andAppend("order", order)

            return when (order.status) {
                CreditCardPaymentStatus.AUTHORIZED -> {
                    cieloAdapter.cancel(orderId)

                    logger.info(markers, "BackofficeCreditCardController#cancel")
                    HttpResponse.accepted<Unit>()
                }

                CreditCardPaymentStatus.PAYMENT_CONFIRMED,
                CreditCardPaymentStatus.DENIED,
                CreditCardPaymentStatus.VOIDED,
                CreditCardPaymentStatus.REFUNDED,
                CreditCardPaymentStatus.ABORTED,
                -> {
                    logger.warn(markers, "BackofficeCreditCardController#cancel")
                    HttpResponse.badRequest("Apenas transações pendentes (autorizadas e não confirmadas) podem ser desfeitas. Estado atual: ${order.status}")
                }
            }
        } catch (e: Exception) {
            logger.error(markers, "BackofficeCreditCardController#cancel", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }
}