package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.Role
import ai.friday.billpayment.app.auth.toAccountId
import ai.friday.billpayment.app.banking.BankOperationId
import ai.friday.billpayment.app.banking.BankOperationStatus
import ai.friday.billpayment.app.banking.FinancialServiceGateway
import ai.friday.billpayment.app.banking.InternalBankService
import ai.friday.billpayment.app.bill.ActionSource
import ai.friday.billpayment.app.bill.BillId
import ai.friday.billpayment.app.bill.BillPaid
import ai.friday.billpayment.app.bill.BillStatus
import ai.friday.billpayment.app.bill.BillType
import ai.friday.billpayment.app.bill.PaymentStarted
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.BillEventPublisher
import ai.friday.billpayment.app.integrations.BillEventRepository
import ai.friday.billpayment.app.integrations.TransactionRepository
import ai.friday.billpayment.app.integrations.WalletRepository
import ai.friday.billpayment.app.payment.BalanceAuthorization
import ai.friday.billpayment.app.payment.BoletoSettlementResult
import ai.friday.billpayment.app.payment.BoletoSettlementStatus
import ai.friday.billpayment.app.payment.PaymentMethodsDetailWithBalance
import ai.friday.billpayment.app.payment.SettlementData
import ai.friday.billpayment.app.payment.SinglePaymentData
import ai.friday.billpayment.app.payment.Transaction
import ai.friday.billpayment.app.payment.TransactionId
import ai.friday.billpayment.app.payment.TransactionStatus
import ai.friday.billpayment.app.payment.TransactionType
import ai.friday.billpayment.app.payment.receipt.NotifyReceiptService
import ai.friday.billpayment.app.payment.removePrefix
import ai.friday.billpayment.app.payment.toPayer
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import arrow.core.getOrElse
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.security.annotation.Secured
import io.micronaut.security.authentication.Authentication
import java.time.ZonedDateTime
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(Role.Code.ADMIN)
@Controller("/backoffice")
@FridayMePoupe
class BackofficeBillPaymentController(
    private val billEventRepository: BillEventRepository,
    private val walletRepository: WalletRepository,
    private val accountRepository: AccountRepository,
    private val internalBankService: InternalBankService,
    private val billEventPublisher: BillEventPublisher,
    private val transactionRepository: TransactionRepository,
    private val notifyReceiptService: NotifyReceiptService,
) {
    private val logger = LoggerFactory.getLogger(BackofficeBillPaymentController::class.java)

    @Post("wallet/{walletId}/bill/{billId}/captureFunds{/amount}")
    fun captureFunds(authentication: Authentication, @PathVariable walletId: String, @PathVariable billId: String, @PathVariable amount: String?): HttpResponse<*> {
        val markers = Markers.append("adminAccountId", authentication.name)
            .andAppend("walletId", walletId)
            .andAppend("billId", billId)
            .andAppend("amount", amount)
        return try {
            val bill = billEventRepository.getBill(
                walletId = WalletId(walletId),
                billId = BillId(billId),
            ).getOrElse {
                throw it
            }

            val amountToCapture = if (amount != null) {
                amount.toLong()
            } else {
                bill.amount
            }

            if (amountToCapture > bill.amount) {
                logger.warn(markers, "BackofficeBillPaymentController#registerExternalPayment")
                HttpResponse.badRequest("Invalid amount: $amount. Max amount allowed: ${bill.amount}")
            }

            val wallet = walletRepository.findWallet(bill.walletId)

            val accountPaymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId = wallet.paymentMethodId,
                accountId = wallet.founder.accountId,
            )
            markers.andAppend("accountPaymentMethod", accountPaymentMethod)

            val bankTransfer = internalBankService.captureFunds(
                accountId = accountPaymentMethod.accountId,
                accountPaymentMethodId = accountPaymentMethod.id,
                paymentMethod = accountPaymentMethod.method,
                amount = amountToCapture,
            )
            markers.andAppend("bankTransfer", bankTransfer)

            if (bankTransfer.status == BankOperationStatus.SUCCESS) {
                logger.info(markers, "BackofficeBillPaymentController#captureFunds")
                HttpResponse.ok(bankTransfer)
            } else {
                logger.warn(markers, "BackofficeBillPaymentController#captureFunds")
                HttpResponse.badRequest(bankTransfer)
            }
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBillPaymentController#captureFunds", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Get("wallet/{walletId}/checkCaptureFunds/{operationId}/{operationDate}")
    fun checkCaptureFunds(authentication: Authentication, @PathVariable walletId: String, @PathVariable operationId: String, @PathVariable operationDate: String): HttpResponse<*> {
        val markers = Markers.append("adminAccountId", authentication.name)
            .andAppend("walletId", walletId)
            .andAppend("operationId", operationId)
            .andAppend("operationDate", operationDate)

        return try {
            val paymentMethod = walletRepository.findAccountPaymentMethod(
                walletId = WalletId(walletId),
            )

            val response = internalBankService.checkCaptureFunds(
                accountId = paymentMethod.accountId,
                paymentMethod = paymentMethod.method,
                operationId = BankOperationId(operationId),
                operationDate = BrazilZonedDateTimeSupplier.parseDate(operationDate),
            )

            logger.info(markers, "BackofficeBillPaymentController#checkCaptureFunds")
            HttpResponse.ok(response)
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBillPaymentController#checkCaptureFunds", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }

    @Post("wallet/{walletId}/bill/{billId}/markAsPaidManually")
    fun markAsPaidManually(authentication: Authentication, @PathVariable walletId: String, @PathVariable billId: String, @Body request: MarkAsPaidExternallyTO): HttpResponse<*> {
        val markers = Markers.append("adminAccountId", authentication.name)
            .andAppend("walletId", walletId)
            .andAppend("billId", billId)
            .andAppend("request", request)
        return try {
            val bill = billEventRepository.getBill(
                walletId = WalletId(walletId),
                billId = BillId(billId),
            ).getOrElse {
                throw it
            }

            if (bill.status == BillStatus.PAID) {
                logger.warn(markers, "BackofficeBillPaymentController#markAsPaidManually")
                HttpResponse.badRequest("Invalid bill status: ${bill.status}")
            }

            if (bill.billType !in listOf(BillType.CONCESSIONARIA, BillType.FICHA_COMPENSACAO)) {
                logger.warn(markers, "BackofficeBillPaymentController#markAsPaidManually")
                HttpResponse.badRequest("Invalid bill type: ${bill.billType}")
            }

            val wallet = walletRepository.findWallet(bill.walletId)

            val accountPaymentMethod = accountRepository.findAccountPaymentMethodByIdAndAccountId(
                accountPaymentMethodId = wallet.paymentMethodId,
                accountId = wallet.founder.accountId,
            )
            markers.andAppend("accountPaymentMethod", accountPaymentMethod)

            val now = ZonedDateTime.parse(request.date, DateTimeFormatter.ISO_DATE_TIME)

            val transactionId = TransactionId.build()
            markers.andAppend("transactionId", transactionId.value)

            val actionSource = ActionSource.Api(accountId = authentication.toAccountId())
            val nsu = accountRepository.incrementNSU(wallet.founder.accountId).toLong()

            val transaction = Transaction(
                id = transactionId,
                created = now,
                walletId = bill.walletId,
                type = TransactionType.BOLETO_PAYMENT,
                payer = wallet.founder.toPayer(),
                paymentData = SinglePaymentData(
                    accountPaymentMethod = accountPaymentMethod,
                    details = PaymentMethodsDetailWithBalance(
                        amount = request.amountPaid,
                        paymentMethodId = accountPaymentMethod.id,
                        calculationId = null,
                    ),
                    payment = BalanceAuthorization(
                        operationId = BankOperationId(request.paymentOperationId),
                        status = BankOperationStatus.SUCCESS,
                        amount = request.amountPaid,
                        paymentGateway = FinancialServiceGateway.ARBI,
                    ),
                ),
                settlementData = SettlementData(
                    settlementTarget = bill,
                    serviceAmountTax = 0,
                    totalAmount = request.amountPaid,
                    settlementOperation = BoletoSettlementResult(
                        gateway = FinancialServiceGateway.FRIDAY,
                        status = BoletoSettlementStatus.CONFIRMED,
                        bankTransactionId = transactionId.value,
                        externalNsu = nsu,
                        externalTerminal = wallet.founder.accountId.value,
                        errorCode = "",
                        errorDescription = null,
                        authentication = request.settlementAuthentication,
                        paymentPartnerName = request.settlementPartnerName,
                        finalPartnerName = FinancialServiceGateway.FRIDAY,
                    ),
                ),
                nsu = nsu,
                actionSource = actionSource,
                status = TransactionStatus.COMPLETED,
                correlationId = transactionId.removePrefix(),
            )
            markers.andAppend("transaction", transaction)
            transactionRepository.save(transaction)

            val paymentStarted = PaymentStarted(
                walletId = bill.walletId,
                billId = bill.billId,
                created = now.toInstant().toEpochMilli(),
                actionSource = actionSource,
                transactionId = transaction.id,
                correlationId = transaction.correlationId,
            )
            bill.apply(paymentStarted)

            billEventRepository.save(paymentStarted)

            val billPaid = BillPaid(
                walletId = bill.walletId,
                billId = bill.billId,
                created = now.plusSeconds(1).toInstant().toEpochMilli(),
                actionSource = actionSource,
                transactionId = transaction.id,
                syncReceipt = true,
            )
            billEventPublisher.publish(
                bill = bill,
                event = billPaid,
            )

            notifyReceiptService.notifyWithAsyncRetry(billPaid)

            logger.info(markers, "BackofficeBillPaymentController#markAsPaidManually")
            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            logger.error(markers, "BackofficeBillPaymentController#markAsPaidManually", e)
            StandardHttpResponses.serverError(e.message.orEmpty())
        }
    }
}

data class MarkAsPaidExternallyTO(
    val date: String,
    val amountPaid: Long,
    val paymentOperationId: String,
    val settlementAuthentication: String,
    val settlementPartnerName: String,
)