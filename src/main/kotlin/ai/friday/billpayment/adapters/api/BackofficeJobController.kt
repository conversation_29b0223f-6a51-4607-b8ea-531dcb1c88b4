package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.integrations.JobRepository
import ai.friday.billpayment.app.job.Job
import ai.friday.billpayment.app.job.JobManager
import ai.friday.billpayment.app.reports.ArbiAccountsReportSummary
import ai.friday.billpayment.app.reports.BankAccountReconciliationReportService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getZonedDateTime
import ai.friday.morning.date.dateFormat
import ai.friday.morning.date.dateTimeFormat
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.scheduling.TaskExecutors
import io.micronaut.scheduling.TaskScheduler
import io.micronaut.security.annotation.Secured
import jakarta.inject.Named
import java.time.Duration
import java.time.LocalDate
import java.util.Optional
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/job")
@FridayMePoupe
class BackofficeJobController(
    private val jobManager: Optional<JobManager>,
    private val jobRepository: JobRepository,
    @param:Named(TaskExecutors.SCHEDULED) private val taskScheduler: TaskScheduler,
) {

    @Get("/")
    fun listJobs(): HttpResponse<*> {
        val jobs = jobRepository.findAll()

        LOG.info(Markers.append("jobsSize", jobs.size), "BackofficeJobController#listJobs")

        return HttpResponse.ok(jobs.map { it.toJobTO() })
    }

    @Get("/{jobName}")
    fun getJobByName(@PathVariable jobName: String): HttpResponse<*> {
        val job = jobRepository.findAll().single { it.name == jobName }

        LOG.info(Markers.append("jobName", jobName), "BackofficeJobController#getJobByName")

        return HttpResponse.ok(job.toJobTO())
    }

    @Post("/{jobName}/execute/")
    fun executeJob(@PathVariable jobName: String): HttpResponse<*> {
        val markers = Markers.append("jobName", jobName)

        val abstractJob = jobManager.get().listJobs().single { it.jobName == jobName }
        markers.andAppend("previousLastStartTime", abstractJob.lastStartTime)

        val now = getZonedDateTime()

        taskScheduler.schedule(Duration.ZERO, abstractJob)

        while (abstractJob.lastStartTime?.isBefore(now) != false) {
            Thread.sleep(500)
        }
        markers.andAppend("currentLastStartTime", abstractJob.lastStartTime)

        LOG.info(markers, "BackofficeJobController#executeJob")
        return HttpResponse.ok(abstractJob.toJob().toJobTO())
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeJobController::class.java)
    }
}

data class ArbiAccountReportRequestTO(
    val accountId: String,
)

data class ArbiAccountReportRequestIntervalTO(
    val accountId: String,
    val lastDate: String,
    val daysBefore: Int,
)

@Secured(BACKOFFICE)
@Controller("/backoffice/conciliation")
@FridayMePoupe
class BackofficeConciliationController(
    private val reconciliationReportService: BankAccountReconciliationReportService,
    private val accountRepository: AccountRepository,
) {

    @Post("/arbiAccountsReport/{date}")
    fun arbiAccountsReport(@PathVariable date: String, @Body body: ArbiAccountReportRequestTO): HttpResponse<*> {
        val markers = Markers.append("date", date)
        return try {
            val accountPaymentMethod = accountRepository.findAccountPaymentMethodsByAccountId(AccountId(body.accountId)).firstOrNull {
                reconciliationReportService.checkIsEligible(it)
            }

            if (accountPaymentMethod == null) {
                return HttpResponse.badRequest("AccountId ${body.accountId} does not have an eligible payment method")
            }

            val report = reconciliationReportService.generateArbiAccountsReport(
                date = LocalDate.parse(date, dateFormat),
                cutOffTime = 24,
                accountPaymentMethod = accountPaymentMethod,
                previouslyCheckedTimes = 0,
            )

            markers.andAppend("report", report)
            LOG.info(markers, "BackofficeConciliationController#arbiAccountsReport")
            HttpResponse.ok(report)
        } catch (e: Exception) {
            LOG.error(markers, "BackofficeConciliationController#arbiAccountsReport", e)
            HttpResponse.serverError(e.message.orEmpty())
        }
    }

    @Post("/arbiAccountsReport/interval")
    fun arbiAccountsReportInterval(@Body body: ArbiAccountReportRequestIntervalTO): HttpResponse<*> {
        val markers = Markers.append("body", body)
        val lastDate = LocalDate.parse(body.lastDate, dateFormat)

        if (body.daysBefore < 0 || body.daysBefore > 30) {
            return HttpResponse.badRequest("daysBefore must be between 0 and 30")
        }

        val results = mutableMapOf<String, ArbiAccountsReportSummary?>()

        repeat(times = body.daysBefore) { index ->
            val currentDate = lastDate.minusDays(index.toLong())
            try {
                val accountPaymentMethod = accountRepository.findAccountPaymentMethodsByAccountId(AccountId(body.accountId)).firstOrNull {
                    reconciliationReportService.checkIsEligible(it)
                }

                if (accountPaymentMethod == null) {
                    return HttpResponse.badRequest("AccountId ${body.accountId} does not have an eligible payment method")
                }

                val report = reconciliationReportService.generateArbiAccountsReport(
                    date = currentDate,
                    cutOffTime = 24,
                    accountPaymentMethod = accountPaymentMethod,
                    previouslyCheckedTimes = 0,
                )
                results[currentDate.format(dateFormat)] = report
            } catch (e: Exception) {
                LOG.error(markers, "BackofficeConciliationController#arbiAccountsReportInterval", e)
            }
        }
        LOG.info(markers.andAppend("results", results), "BackofficeConciliationController#arbiAccountsReportInterval")
        return HttpResponse.ok(results)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(BackofficeConciliationController::class.java)
    }
}

data class JobTO(
    val name: String,
    val crons: List<String>,
    val fixedDelay: String?,
    val running: Boolean,
    val lastStartTime: String?,
    val lastElapsedMinutes: Long?,
    val shouldLock: Boolean,
    val lockAtLeastFor: String,
    val lockAtMostFor: String,
    val shutdownGracefully: Boolean,
    val shutdownGracefullyMaxWaitTime: Int,
)

private fun Job.toJobTO() = JobTO(
    name = this.name,
    crons = this.crons,
    fixedDelay = this.fixedDelay?.toString(),
    running = this.running,
    lastStartTime = this.lastStartTime?.format(dateTimeFormat),
    lastElapsedMinutes = this.lastElapsedMinutes,
    shouldLock = this.shouldLock,
    lockAtLeastFor = this.lockAtLeastFor.toString(),
    lockAtMostFor = this.lockAtMostFor.toString(),
    shutdownGracefully = this.shutdownGracefully,
    shutdownGracefullyMaxWaitTime = this.shutdownGracefullyMaxWaitTime,
)