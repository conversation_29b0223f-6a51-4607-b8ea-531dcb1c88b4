package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.arbi.ArbiAccountStatementAdapter
import ai.friday.billpayment.adapters.arbi.NewArbiPixPaymentAdapter
import ai.friday.billpayment.app.Document
import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.account.AccountConfigurationService
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.Role.Code.BACKOFFICE
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.integrations.NotificationAdapter
import ai.friday.billpayment.app.integrations.StatementItemConverter
import ai.friday.billpayment.app.pix.PixStatement
import ai.friday.billpayment.app.statement.StatementError
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.app.wallet.WalletService
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getLocalDate
import arrow.core.getOrElse
import io.micronaut.core.annotation.Nullable
import io.micronaut.http.HttpResponse
import io.micronaut.http.MediaType
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Get
import io.micronaut.http.annotation.PathVariable
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.QueryValue
import io.micronaut.security.annotation.Secured
import java.time.LocalDate
import java.time.format.DateTimeFormatter
import net.logstash.logback.marker.Markers
import org.slf4j.LoggerFactory

@Secured(BACKOFFICE)
@Controller("/backoffice/statement")
@FridayMePoupe
class BackofficeStatementController(
    private val walletService: WalletService,
    private val statementService: StatementService,
    private val accountConfigurationService: AccountConfigurationService,
    private val statementItemConverter: StatementItemConverter,
    private val arbiAccountStatementAdapter: ArbiAccountStatementAdapter, // FIXME remover apos teste
    private val notificationAdapter: NotificationAdapter, // FIXME remover apos teste,
    private val newArbiPixPaymentAdapter: NewArbiPixPaymentAdapter,
) {

    @Get("pix{?codIspb,codAgencia,nroConta,qtdDias,endToEnd,endToEndOriginal,origemMovimento,dataDe,dataAte,cpfCnpjTitular}")
    fun getPixStatement(
        @QueryValue codIspb: String?,
        @QueryValue codAgencia: String?,
        @QueryValue nroConta: String?,
        @QueryValue qtdDias: String?,
        @QueryValue endToEnd: String?,
        @QueryValue endToEndOriginal: String?,
        @QueryValue origemMovimento: String?,
        @QueryValue dataDe: String?,
        @QueryValue dataAte: String?,
        @QueryValue cpfCnpjTitular: String?,
    ): HttpResponse<List<PixStatement>> {
        val statement = newArbiPixPaymentAdapter.getStatementTest(
            buildMap {
                codIspb?.let { put("codIspb", it) }
                codAgencia?.let { put("codAgencia", it) }
                nroConta?.let { put("nroConta", it) }
                qtdDias?.let { put("qtdDias", it) }
                endToEnd?.let { put("endToEnd", it) }
                endToEndOriginal?.let { put("endToEndOriginal", it) }
                origemMovimento?.let { put("origemMovimento", it) }
                dataDe?.let { put("dataDe", it) }
                dataAte?.let { put("dataAte", it) }
                cpfCnpjTitular?.let { put("cpfCnpjTitular", it) }
            },
        )

        return HttpResponse.ok(statement)
    }

    @Get("/wallet/{walletId}{?startDate,endDate}", produces = [MediaType.TEXT_CSV])
    fun getBankStatement(
        @QueryValue @Nullable
        startDate: String,
        @QueryValue @Nullable
        endDate: String,
        @PathVariable walletId: String,
    ): HttpResponse<String> {
        val markers = Markers.append("walletId", walletId)
            .andAppend("startDate", startDate).andAppend("endDate", endDate)
        try {
            val parsedStartDate = LocalDate.parse(startDate, DateTimeFormatter.ISO_DATE)
            val parsedEndDate = LocalDate.parse(endDate, DateTimeFormatter.ISO_DATE)

            val statements =
                statementService.findAllStatementsByDate(WalletId(walletId), parsedStartDate, parsedEndDate)
                    .getOrElse {
                        logger.error(markers, "BackofficeController#getBankStatement", it)

                        return when (it) {
                            is StatementError.WalletNotFound ->
                                HttpResponse.badRequest(ResponseTO("4001", "Wallet não encontrado").toString())

                            else -> HttpResponse.serverError(ResponseTO("5001", "Erro inesperado.").toString())
                        }
                    }

            val csvData = statementItemConverter.convertToCsv(statements.statementItems)

            return HttpResponse.ok(csvData.toString())
        } catch (e: Exception) {
            logger.error(markers, "BackofficeController#getBankStatement", e)
            return HttpResponse.serverError(ResponseTO("5001", "Erro inesperado.").toString())
        }
    }

    @Post("/account/{accountId}/sendNow")
    fun sendBankStatement(
        @PathVariable accountId: String,
        @Body body: SendStatementReport,
    ): HttpResponse<*> {
        val startDate = body.startDate
        val endDate = body.endDate

        val markers = Markers.append("accountId", accountId)
            .andAppend("startDate", startDate).andAppend("endDate", endDate)

        try {
            val parsedStartDate = LocalDate.parse(startDate, DateTimeFormatter.ISO_DATE)
            val parsedEndDate = LocalDate.parse(endDate, DateTimeFormatter.ISO_DATE)

            if (parsedStartDate.isAfter(parsedEndDate)) {
                return HttpResponse.badRequest(ResponseTO("4002", "Data inicial não pode ser maior que a data final."))
            }

            val wallet = walletService.findWallet(WalletId(body.walletId))

            statementService.requestStatement(
                walletId = wallet.id,
                accountId = AccountId(accountId),
                startDate = parsedStartDate,
                endDate = parsedEndDate,
            ).getOrElse {
                logger.error(markers, "BackofficeController#sendBankStatement", it)
                return HttpResponse.serverError(ResponseTO("5001", "Erro inesperado."))
            }

            return HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            logger.error(markers, "BackofficeController#sendBankStatement", e)
            return HttpResponse.badRequest(ResponseTO("4001", e.message ?: "Erro inesperado."))
        }
    }

    @Post("/account/{accountId}/monthlyReport/sendNow")
    fun sendMonthlyReport(
        @PathVariable accountId: String,
        @Body body: SendMonthlyStatementReport,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId)
        try {
            val wallet = walletService.findWallet(WalletId(body.walletId))
            val startDate = getLocalDate().minusMonths(1).withDayOfMonth(1)
            val endDate = getLocalDate().withDayOfMonth(1).minusDays(1)

            statementService.requestStatement(
                walletId = wallet.id,
                accountId = AccountId(accountId),
                startDate = startDate,
                endDate = endDate,
            ).map {
                return HttpResponse.noContent<Unit>()
            }.getOrElse {
                logger.error(markers, "BackofficeController#sendMonthlyReport", it)
                return HttpResponse.badRequest(ResponseTO("4001", it.toString()))
            }
        } catch (e: Exception) {
            logger.error(markers, "BackofficeController#sendMonthlyReport", e)
            return HttpResponse.badRequest(ResponseTO("4001", e.message ?: "Erro inesperado."))
        }
    }

    @Post("/account/{accountId}/monthlyReport/sendMonthly")
    fun optMonthlyReportInOrOut(
        @PathVariable accountId: String,
        @Body body: GenerateMonthlyReport,
    ): HttpResponse<*> {
        val markers = Markers.append("accountId", accountId).andAppend("body", body)
        return try {
            accountConfigurationService.updateReceiveMonthlyStatement(AccountId(accountId), body.enabled)
            HttpResponse.noContent<Unit>()
        } catch (e: Exception) {
            logger.error(markers, "BackofficeController#generateMonthlyReport", e)
            HttpResponse.badRequest(ResponseTO("4001", e.message ?: "Erro inesperado."))
        }
    }

    @Get("/accountNo/{accountNo}/{initialDate}/{endDate}/{sortType}")
    fun getArbiAccountStatement(
        @PathVariable accountNo: String,
        @PathVariable initialDate: String,
        @PathVariable endDate: String,
        @PathVariable sortType: String,
    ): HttpResponse<*> {
        val response = arbiAccountStatementAdapter.getStatement(
            accountNo = accountNo,
            document = Document("***********"),
            initialDate = LocalDate.parse(initialDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")),
            endDate = LocalDate.parse(endDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")),
            sortType = sortType, // Movement ou InsertDate
            logBody = true,
        )
        return HttpResponse.ok(response)
    }

    companion object {
        private val logger = LoggerFactory.getLogger(BackofficeStatementController::class.java)
    }
}

data class GenerateMonthlyReport(
    val enabled: Boolean,
)

data class SendStatementReport(
    val startDate: String,
    val endDate: String,
    val walletId: String,
)

data class SendMonthlyStatementReport(
    val walletId: String,
)