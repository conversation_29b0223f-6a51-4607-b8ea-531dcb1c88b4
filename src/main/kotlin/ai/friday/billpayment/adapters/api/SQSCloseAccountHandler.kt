package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.adapters.messaging.AbstractSQSHandler
import ai.friday.billpayment.adapters.messaging.SQSHandlerResponse
import ai.friday.billpayment.adapters.messaging.SQSMessageHandlerConfiguration
import ai.friday.billpayment.app.account.CloseAccountMessage
import ai.friday.billpayment.app.account.CloseAccountService
import ai.friday.billpayment.app.account.CloseAccountStepType
import ai.friday.billpayment.app.account.CloseWalletStepType
import ai.friday.billpayment.app.bill.andAppend
import com.fasterxml.jackson.core.JsonParser
import com.fasterxml.jackson.databind.DeserializationContext
import com.fasterxml.jackson.databind.DeserializationFeature
import com.fasterxml.jackson.databind.JsonDeserializer
import com.fasterxml.jackson.databind.JsonNode
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule
import com.fasterxml.jackson.module.kotlin.jacksonObjectMapper
import io.micronaut.context.annotation.Property
import io.micronaut.tracing.annotation.NewSpan
import jakarta.inject.Singleton
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.Message

class CloseAccountStepTypeDeserializer : JsonDeserializer<CloseAccountStepType>() {
    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): CloseAccountStepType {
        val node: JsonNode = p.codec.readTree(p)
        return when {
            node.isTextual -> CloseAccountStepType(node.asText())
            node.has("value") -> CloseAccountStepType(node.get("value").asText())
            else -> throw IllegalArgumentException("Formato inválido para CloseAccountStepType: $node")
        }
    }
}

val closeAccountStepTypeModule = SimpleModule().apply {
    addDeserializer(CloseAccountStepType::class.java, CloseAccountStepTypeDeserializer())
}

class CloseWalletStepTypeDeserializer : JsonDeserializer<CloseWalletStepType>() {
    override fun deserialize(p: JsonParser, ctxt: DeserializationContext): CloseWalletStepType {
        val node: JsonNode = p.codec.readTree(p)
        return when {
            node.isTextual -> CloseWalletStepType(node.asText())
            node.has("value") -> CloseWalletStepType(node.get("value").asText())
            else -> throw IllegalArgumentException("Formato inválido para CloseAccountStepType: $node")
        }
    }
}

val closeWalletStepTypeModule = SimpleModule().apply {
    addDeserializer(CloseWalletStepType::class.java, CloseWalletStepTypeDeserializer())
}

inline fun <reified T> parseObjectFromCloseAccount(string: String): T {
    return jacksonObjectMapper()
        .registerModule(JavaTimeModule())
        .registerModule(closeAccountStepTypeModule)
        .registerModule(closeWalletStepTypeModule)
        .disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS)
        .disable(DeserializationFeature.ADJUST_DATES_TO_CONTEXT_TIME_ZONE)
        .disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES)
        .readerFor(T::class.java).readValue(string)
}

@Singleton
open class SQSCloseAccountHandler(
    amazonSQS: SqsClient,
    configuration: SQSMessageHandlerConfiguration,
    @Property(name = "sqs.closeAccountQueueName") private val queueName: String,
    private val closeAccountService: CloseAccountService,
) : AbstractSQSHandler(
    amazonSQS,
    configuration,
    queueName,
) {
    @NewSpan
    override fun handleMessage(message: Message): SQSHandlerResponse {
        val event = parseObjectFromCloseAccount<CloseAccountMessage>(message.body())
        val markers = append("event", event)

        if (event.tryCount < 60) {
            val result =
                closeAccountService.executeCloseAccountStepsWithRetry(
                    accountId = event.accountId,
                    steps = event.steps,
                    tryCount = event.tryCount,
                )
            markers.andAppend("result", result)
            LOG.info(markers, "SQSCloseAccountHandler")
        } else {
            markers.andAppend("ACTION", "VERIFY")
            LOG.warn(markers, "SQSCloseAccountHandler")
        }

        return SQSHandlerResponse(true)
    }

    override fun handleError(
        message: Message,
        e: Exception,
    ): SQSHandlerResponse {
        val markers = append("eventBody", message.body())

        LOG.error(markers, "SQSCloseAccountHandler", e)
        return SQSHandlerResponse(false)
    }

    companion object {
        private val LOG = LoggerFactory.getLogger(SQSCloseAccountHandler::class.java)
    }
}