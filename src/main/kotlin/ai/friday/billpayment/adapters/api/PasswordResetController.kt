package ai.friday.billpayment.adapters.api

import ai.friday.billpayment.app.FridayMePoupe
import ai.friday.billpayment.app.bill.andAppend
import ai.friday.billpayment.app.liveness.LivenessId
import ai.friday.billpayment.app.password.PasswordErrors
import ai.friday.billpayment.app.password.PasswordService
import arrow.core.getOrElse
import io.micronaut.core.annotation.Introspected
import io.micronaut.core.version.annotation.Version
import io.micronaut.http.HttpResponse
import io.micronaut.http.annotation.Body
import io.micronaut.http.annotation.Controller
import io.micronaut.http.annotation.Post
import io.micronaut.http.annotation.Put
import io.micronaut.security.annotation.Secured
import io.micronaut.security.rules.SecurityRule
import io.micronaut.validation.Validated
import jakarta.validation.Valid
import jakarta.validation.constraints.Pattern
import net.logstash.logback.marker.LogstashMarker
import net.logstash.logback.marker.Markers.append
import org.slf4j.LoggerFactory

@Validated
@Controller("/password")
@Secured(SecurityRule.IS_ANONYMOUS)
@Version("2")
@FridayMePoupe
open class PasswordResetController(
    private val passwordService: PasswordService,
) {

    @Put("/liveness/forgot")
    fun forgotPassword(
        @Body @Valid
        body: ForgotPasswordRequestTO,
    ): HttpResponse<*> {
        val logName = "PasswordResetController#forgotPassword"
        val markers = append("document", body.document)

        return try {
            passwordService.createOTP(body.document).map {
                logger.info(markers, logName)
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                handleError(markers, it, logName)
            }
        } catch (e: Exception) {
            handleError(markers, PasswordErrors.Error(e), logName)
        }
    }

    @Post("/liveness/verify")
    fun verifyOtp(@Body body: VerifyOTPRequestTO): HttpResponse<*> {
        val logName = "PasswordResetController#verifyOtp"
        val markers = append("body", body)

        return try {
            passwordService.verifyOTPAndCreateMatchLivenessId(document = body.document, token = body.otp).map {
                logger.info(markers, logName)
                HttpResponse.ok(LivenessValidatedOTPResponseTO(it.value))
            }.getOrElse {
                handleError(markers, it, logName)
            }
        } catch (e: Exception) {
            handleError(markers, PasswordErrors.Error(e), logName)
        }
    }

    @Post("/liveness/reset")
    fun resetPassword(@Body body: UpdatePasswordRequestTO): HttpResponse<*> {
        val logName = "PasswordResetController#resetPassword"
        val markers = append("livenessId", body.livenessId)
        return try {
            passwordService.resetPassword(LivenessId(body.livenessId), body.password).map {
                markers.andAppend("document", it)
                logger.info(markers, logName)
                HttpResponse.noContent<Unit>()
            }.getOrElse {
                handleError(markers, it, logName)
            }
        } catch (e: Exception) {
            handleError(markers, PasswordErrors.Error(e), logName)
        }
    }

    private fun handleError(
        markers: LogstashMarker,
        passwordErrors: PasswordErrors,
        logName: String,
    ): HttpResponse<*> {
        markers.andAppend("passwordErrors", passwordErrors::class.java.simpleName)
        return when (passwordErrors) {
            PasswordErrors.AccountNotFound, PasswordErrors.TokenStillValid -> {
                logger.warn(markers, logName)
                HttpResponse.noContent<Unit>()
            }

            PasswordErrors.CantUseLiveness -> {
                logger.warn(markers, logName)
                StandardHttpResponses.conflict(
                    ResponseTO(
                        code = "4091",
                        message = "can't use liveness to reset password",
                    ),
                )
            }

            is PasswordErrors.Error -> {
                logger.error(markers, logName, passwordErrors.e)
                StandardHttpResponses.serverError()
            }

            PasswordErrors.AccountWithoutPassword -> {
                logger.warn(markers, logName)
                StandardHttpResponses.conflict(
                    ResponseTO(
                        code = "4092",
                        message = "account doesn't have password yet",
                    ),
                )
            }

            PasswordErrors.LivenessValidationIncomplete -> {
                logger.error(markers, logName)
                StandardHttpResponses.conflict(
                    ResponseTO(
                        code = "4093",
                        message = "liveness validation is incomplete",
                    ),
                )
            }

            PasswordErrors.InvalidOTPToken -> {
                logger.warn(markers, logName)
                StandardHttpResponses.conflict(
                    ResponseTO(
                        code = "4094",
                        message = "Invalid OTP Token",
                    ),
                )
            }
        }
    }

    companion object {
        private val logger = LoggerFactory.getLogger(PasswordResetController::class.java)
    }
}

@Introspected
data class ForgotPasswordRequestTO(
    @field:Pattern(
        regexp = "^\\d{11}|\\d{14}$",
        message = "document must be 11 digits for CPF or 14 digits for CNPJ",
    ) val document: String,
)

data class UpdatePasswordRequestTO(
    val livenessId: String,
    val password: String,
)

data class LivenessValidatedOTPResponseTO(
    val livenessId: String,
)

data class VerifyOTPRequestTO(
    val otp: String,
    val document: String,
)