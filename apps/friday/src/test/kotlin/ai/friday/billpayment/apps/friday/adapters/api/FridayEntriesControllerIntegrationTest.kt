package ai.friday.billpayment.apps.friday.adapters.api

import ai.friday.billpayment.adapters.api.DefaultCreditTO
import ai.friday.billpayment.adapters.api.EntriesTO
import ai.friday.billpayment.adapters.api.ResponseTO
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.adapters.dynamodb.WalletDbRepository
import ai.friday.billpayment.app.FRIDAY_ENV
import ai.friday.billpayment.app.statement.StatementService
import ai.friday.billpayment.app.wallet.WalletId
import ai.friday.billpayment.bankStatementItem
import ai.friday.billpayment.integration.WalletFixture
import ai.friday.billpayment.integration.buildCookie
import ai.friday.billpayment.integration.createBillPaymentTable
import arrow.core.right
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.shouldBe
import io.kotest.matchers.types.shouldBeTypeOf
import io.micronaut.core.type.Argument
import io.micronaut.http.HttpRequest
import io.micronaut.http.HttpStatus
import io.micronaut.http.MutableHttpRequest
import io.micronaut.http.cookie.Cookie
import io.micronaut.runtime.server.EmbeddedServer
import io.micronaut.rxjava2.http.client.RxHttpClient
import io.micronaut.test.annotation.MockBean
import io.micronaut.test.extensions.junit5.annotation.MicronautTest
import io.mockk.every
import io.mockk.mockk
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test

@MicronautTest(environments = [FRIDAY_ENV])
class FridayEntriesControllerIntegrationTest(
    embeddedServer: EmbeddedServer,
    val dynamoDB: AmazonDynamoDB,
) {

    private val walletFixture = WalletFixture()
    private val wallet = walletFixture.buildWallet()
    private val client = embeddedServer.applicationContext.createBean(RxHttpClient::class.java, embeddedServer.url)

    private val dynamoDbDAO = DynamoDbDAO(dynamoDB)
    private val accountRepository = AccountDbRepository(dynamoDbDAO)
    private val walletRepository = WalletDbRepository(dynamoDbDAO, mockk())

    private val statementService: StatementService = mockk() {
        every {
            findAllCredits(any(), any())
        } returns listOf(bankStatementItem).right()
    }

    @MockBean(StatementService::class)
    fun statementService() = statementService

    @BeforeEach
    fun setup() {
        createBillPaymentTable(dynamoDB)
        accountRepository.create(walletFixture.founderAccount)
        walletRepository.save(wallet)
    }

    @Test
    fun `deve listar creditos, bills e entradas manuais`() {
        val request = buildRequest(
            buildCookie(wallet.founder),
            walletId = wallet.id,
        )

        val response = client.toBlocking().exchange(request, Argument.of(EntriesTO::class.java), Argument.of(ResponseTO::class.java))
        response.status shouldBe HttpStatus.OK

        with(response.body().credits) {
            size shouldBe 1
            single().shouldBeTypeOf<DefaultCreditTO>()
        }
    }

    private fun buildRequest(
        cookie: Cookie,
        walletId: WalletId,
    ): MutableHttpRequest<*> {
        return HttpRequest.GET<Unit>("/entries")
            .cookie(cookie).header("X-API-VERSION", "2")
            .header("X-WALLET-ID", walletId.value)
    }
}