package ai.friday.billpayment.apps.mepoupe.adapters.api

import ai.friday.billpayment.adapters.api.BankStatementItemConverter
import ai.friday.billpayment.adapters.api.TimelineCreditTO
import ai.friday.billpayment.app.banking.BankStatementItem
import ai.friday.billpayment.app.banking.BankStatementItemType
import ai.friday.billpayment.app.sanitizeDocumentNumber
import ai.friday.billpayment.apps.mepoupe.app.MePoupe
import com.fasterxml.jackson.annotation.JsonInclude
import io.micronaut.context.annotation.Primary
import jakarta.inject.Singleton
import java.time.format.DateTimeFormatter

@Singleton
@Primary
@MePoupe
class MePoupeBankStatementItemConverter : BankStatementItemConverter {
    override fun convert(item: BankStatementItem): TimelineCreditTO {
        return if (item.type == BankStatementItemType.INVESTMENT_REDEMPTION) {
            MePoupeCreditTO(
                counterPartName = "Meta resgatada",
                counterPartDocument = item.counterpartDocument.sanitizeDocumentNumber(),
                type = item.type.name,
                amount = item.amount,
                date = item.date.format(DateTimeFormatter.ISO_LOCAL_DATE),
            )
        } else {
            MePoupeCreditTO(
                counterPartName = item.counterpartName,
                counterPartDocument = item.counterpartDocument.sanitizeDocumentNumber(),
                type = item.type.name,
                amount = item.amount,
                date = item.date.format(DateTimeFormatter.ISO_LOCAL_DATE),
            )
        }
    }
}

@JsonInclude(JsonInclude.Include.ALWAYS)
data class MePoupeCreditTO(
    override val counterPartName: String,
    override val counterPartDocument: String,
    val type: String,
    override val amount: Long,
    override val date: String,
) : TimelineCreditTO {
    override val timelineEntryType = "SETTLEMENT_CREDIT_ENTRY"
}