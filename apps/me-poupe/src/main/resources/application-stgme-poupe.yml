application:
  region: us-east-1
  accountNumber: ************

BILL_PAYMENT_SECRET: "PleaseChangeThisSecretForANewOne"

micronaut:
  http:
    services:
      arbi:
        url: https://gaph.bancoarbi.com.br
        read-timeout: 60s
        pool:
          max-connections: 30
          enabled: true
        ssl:
          enabled: true
          isInsecureTrustAllCertificates: true
      celcoin:
        url: ${configuration.staging.celcoin.url}
        read-timeout: 10s
        pool:
          max-connections: 5
          enabled: true
      investment-manager:
        url: ${integrations.investment-manager.host}
        read-timeout: 60s
        pool:
          max-connections: 15
          enabled: true

redis:
  uri: "redis://me-poupe-bill-payment-cache.6114ao.0001.use1.cache.amazonaws.com:6379"

ALLOWED_ORIGINS_REGEX: '^https:\/\/use-me-poupe-contas\.meupagador\.com\.br(|.)$'
LOGIN_SUCCESS_TARGET_URL: "https://use-me-poupe-contas.meupagador.com.br/"
LOGIN_FAILURE_TARGET_URL: "https://use-me-poupe-contas.meupagador.com.br/falha-ao-autenticar"
JWKS_COGNITO_ME_POUPE_URL: 'https://cognito-idp.${application.region}.amazonaws.com/${integrations.cognito.userPoolId}/.well-known/jwks.json'
COOKIE_DOMAIN: '.meupagador.com.br'

ME_POUPE_API_CLIENT_ID: "me-poupe-api-client-id-STG"
ME_POUPE_API_CLIENT_SECRET: "af5c16fe-1876-11ef-8bd7-cbee17805a9d-STG"

subscription:
  amount: 990
  dayOfMonth: 10
  description: "%s"
  recipientName: Assinatura Me Poupe!
  # Rever os dados abaixo
  recipientDocument: **************
  bankAccount:
    accountType: CHECKING
    bankNo: 213
    routingNo: 1
    accountNo: *********
    accountDv: 2
    ispb: ********

# Rever esses dados
msisdn-authentication:
  jwt:
    audience: use-me-poupe-contas.meupagador.com.br
    issuer: https://use-me-poupe-contas.meupagador.com.br
    duration: 12h
    secret: ${micronaut.security.token.jwt.signatures.secret.generator.secret}

jwtValidation:
  providers:
    msisdn:
      audience:
        - ${msisdn-authentication.jwt.audience}
      issuer: ${msisdn-authentication.jwt.issuer}

integrations:
  cognito:
    userPoolId: ${application.region}_A7lfQFouu
    jwtCookie:
      userPoolClientId: 10k5k2uv90dlbvmj0ku2lsj1h8
  investment-manager:
    host: "https://investment-api.meupagador.com.br"
    clientId: "INTERNAL_STG_b366d0b6-7d09-11ef-86ad-77322bc27d20"
    clientSecret: "INTERNAL_STG_c0d303a0-7d09-11ef-9dd7-efbc07508854"

receipt:
  bucketName: stg-me-poupe-contas-bill-receipts

disable:
  export-s3: true

modules:
  investment-goals:
    enabled: true
