package ai.friday.billpayment.apps.motorola.adapters.auth

import ai.friday.billpayment.adapters.dynamodb.AbstractDynamoDAO
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_PARTITION_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_RANGE_KEY
import ai.friday.billpayment.adapters.dynamodb.BILL_PAYMENT_TABLE_NAME
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.apps.motorola.Motorola
import ai.friday.billpayment.apps.motorola.adapters.auth.ExternalAuth.SCAN_KEY
import software.amazon.awssdk.enhanced.dynamodb.DynamoDbEnhancedClient
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbAttribute
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbBean
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondaryPartitionKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSecondarySortKey
import software.amazon.awssdk.enhanced.dynamodb.mapper.annotations.DynamoDbSortKey

@Motorola
internal class ExternalAuthDAO(client: DynamoDbEnhancedClient) : AbstractDynamoDAO<ExternalAuthTokenEntity>(client) {
    override fun args() = BILL_PAYMENT_TABLE_NAME to ExternalAuthTokenEntity::class.java
}

@DynamoDbBean
internal class ExternalAuthTokenEntity {

    @get:DynamoDbPartitionKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_PARTITION_KEY)
    lateinit var primaryKey: String // AccountId

    @get:DynamoDbSortKey
    @get:DynamoDbAttribute(value = BILL_PAYMENT_RANGE_KEY)
    lateinit var scanKey: String // ExternalAuth.SCAN_KEY

    @get:DynamoDbAttribute(value = "Provider")
    lateinit var provider: String

    @get:DynamoDbAttribute(value = "Token")
    lateinit var token: String

    @get:DynamoDbAttribute(value = "ExternalUserId")
    lateinit var externalUserId: String

    @get:DynamoDbAttribute(value = "Nonce")
    lateinit var nonce: String

    @get:DynamoDbAttribute(value = "Expiration")
    var expiration: Long = 0

    @get:DynamoDbSecondaryPartitionKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1PrimaryKey")
    lateinit var index1HashKey: String

    @get:DynamoDbSecondarySortKey(indexNames = ["GSIndex1"])
    @get:DynamoDbAttribute(value = "GSIndex1ScanKey")
    lateinit var index1RangeKey: String
}

internal data class ExternalAuthToken(
    val accountId: AccountId,
    val externalUserId: String,
    val token: String,
    val provider: String,
    val nonce: String,
    val expiration: Long,
)

internal data class DecryptedExternalAuthToken(
    val accountId: AccountId,
    val decryptedToken: String,
    val encryptedToken: String,
    val expiration: Long,
    val externalUserId: String,
    val provider: String,
)

@Motorola
internal class ExternalAuthDbRepository(private val client: ExternalAuthDAO) {
    fun save(authToken: ExternalAuthToken) {
        client.save(
            ExternalAuthTokenEntity().apply {
                primaryKey = authToken.accountId.value
                scanKey = SCAN_KEY
                provider = authToken.provider
                nonce = authToken.nonce
                token = authToken.token // TODO encrypt
                expiration = authToken.expiration
                externalUserId = authToken.externalUserId
                index1HashKey = authToken.externalUserId
                index1RangeKey = SCAN_KEY
            },
        )
    }

    fun findByAccountId(accountId: AccountId): Result<ExternalAuthTokenEntity?> = runCatching {
        client.findByPrimaryKey(accountId.value, SCAN_KEY)
    }
}

private object ExternalAuth {
    const val SCAN_KEY = "EXTERNAL_AUTH_REGISTER"
}