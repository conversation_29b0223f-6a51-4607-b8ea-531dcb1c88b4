package ai.friday.billpayment.apps.motorola.adapters.auth

import DynamoDBUtils.setupDynamoDB
import ai.friday.billpayment.ACCOUNT
import ai.friday.billpayment.EncryptUtil
import ai.friday.billpayment.adapters.dynamodb.AccountDbRepository
import ai.friday.billpayment.adapters.dynamodb.DynamoDbDAO
import ai.friday.billpayment.app.account.AccountId
import ai.friday.billpayment.app.account.AccountProviderName
import ai.friday.billpayment.app.account.AccountStatus
import ai.friday.billpayment.app.account.ExternalId
import ai.friday.billpayment.app.integrations.AccountRepository
import ai.friday.billpayment.app.security.DefaultEncryptionService
import ai.friday.billpayment.apps.motorola.adapters.auth.MotorolaAuthenticationServiceTest.Setup.createContext
import ai.friday.billpayment.integration.LocalDbCreationRule
import ai.friday.morning.date.BrazilZonedDateTimeSupplier
import ai.friday.morning.date.BrazilZonedDateTimeSupplier.getEpochMilli
import com.amazonaws.services.dynamodbv2.AmazonDynamoDB
import io.kotest.matchers.nulls.shouldNotBeNull
import io.kotest.matchers.result.shouldBeSuccess
import io.kotest.matchers.shouldBe
import io.mockk.every
import io.mockk.mockk
import io.mockk.mockkObject
import io.mockk.spyk
import io.mockk.verify
import org.junit.jupiter.api.Test

class MotorolaAuthenticationServiceTest {

    @Test
    fun `should return account when nonce was exchanged by jwt with success`() {
        val c = createContext()

        // scenario values
        val accountId = AccountId()
        val externalId = ExternalId("123456", AccountProviderName.MOTOROLA)

        every {
            c.accountRepository.findByExternalId(any())
        } returns listOf(
            ACCOUNT.copy(
                accountId = accountId,
                status = AccountStatus.ACTIVE,
                configuration = ACCOUNT.configuration.copy(externalId = externalId),
            ),
        )

        val exchangeResponse = JazzAuthenticationAdapter.NonceExchangeResponse(
            jwtToken = "user_jwt_token",
            expiresIn = 11235,
            refreshToken = null,
            userId = "123456",
        )

        every { c.jazzAdapter.exchangeToken("nonce_success") } returns Result.success(exchangeResponse)

        mockkObject(BrazilZonedDateTimeSupplier, EncryptUtil)
        every { getEpochMilli() } returns 1 // current time in millis to calculate expiration
        every { EncryptUtil.encrypt("masterKey", exchangeResponse.jwtToken, any()) } returns "encrypted_token"

        val account = c.service.authenticate("nonce_success").shouldBeSuccess()

        verify {
            c.authRepository.save(withArg { it.accountId shouldBe accountId })
            c.accountRepository.findByExternalId(externalId)
        }

        c.authRepository.findByAccountId(account.accountId).shouldBeSuccess().let {
            it.shouldNotBeNull().primaryKey shouldBe account.accountId.value
            it.token shouldBe "encrypted_token"
            it.nonce shouldBe "nonce_success"
            it.expiration shouldBe 11235 * 1000 + 1 // 11235 seconds + 1s
            it.provider shouldBe "MOTOROLA"
            it.scanKey shouldBe "EXTERNAL_AUTH_REGISTER"
            it.externalUserId shouldBe "123456"

            it.index1HashKey shouldBe it.externalUserId
            it.index1RangeKey shouldBe it.scanKey
        }
    }

    private object Setup {
        val dynamoDB: AmazonDynamoDB = LocalDbCreationRule.getDynamoDBProxyServer()
        val dynamoDAO: DynamoDbDAO = DynamoDbDAO(dynamoDB)
        val enhancedClient = setupDynamoDB()
        val externalAuthDAO = ExternalAuthDAO(enhancedClient)

        fun createContext(): TestContext {
            val authRepository = spyk(ExternalAuthDbRepository(externalAuthDAO))
            val accountRepository = spyk(AccountDbRepository(dynamoDAO))

            val jazzAdapter = mockk<JazzAuthenticationAdapter>()
            val encryptionService = spyk(DefaultEncryptionService("masterKey"))

            val service = MotorolaAuthenticationService(
                authRepository,
                accountRepository,
                jazzAdapter,
                encryptionService,
            )

            return TestContext(service, authRepository, accountRepository, jazzAdapter)
        }
    }

    private data class TestContext(
        val service: MotorolaAuthenticationService,
        val authRepository: ExternalAuthDbRepository,
        val accountRepository: AccountRepository,
        val jazzAdapter: JazzAuthenticationAdapter,
    )
}